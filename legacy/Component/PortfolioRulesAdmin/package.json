{"name": "@reddeer/portfolio-rules-admin-js-client", "version": "1.0.1", "description": "Portfolio Rules Administration JS Client", "keywords": [], "main": "build/index.js", "files": ["build"], "scripts": {"start": "raid-build start", "build-typescript": "yarn g:tsc", "build": "yarn build-typescript", "build:watch": "tsc --watch", "prettier": "prettier --write \"src/**/*.{ts,tsx,css}\""}, "dependencies": {"@firefly-trading-labs/portfolio-metadata-api": "1.6.1589", "@reddeer/firefly-common-components": "workspace:*", "@reddeer/firefly-shared-controls": "workspace:*", "@reddeer/raid-theme": "workspace:*", "@tradinglabs/components": "workspace:*", "@tradinglabs/connected-components": "workspace:*", "@tradinglabs/contracts": "workspace:*", "@tradinglabs/primitive-components": "workspace:*", "@tradinglabs/theming": "workspace:*", "@tradinglabs/transport-gateway": "workspace:*", "@tradinglabs/utilities": "workspace:*", "@tradinglabs/viewserver-core": "workspace:*", "@tradinglabs/viewserver-redux": "workspace:*", "ag-grid-community": "22.1.1", "ag-grid-enterprise": "22.1.1", "ag-grid-react": "22.1.1", "antd": "3.26.20", "lodash": "~4.17.21", "moment": "2.29.4", "react": "18.2.0", "react-dom": "18.2.0", "react-grid-layout": "^0.17.1", "react-json-view": "^1.21.3", "react-redux": "7.2.9", "redux": "4.2.1", "styled-components": "5.3.11"}, "license": "UNLICENSED", "devDependencies": {"@tradinglabs/raid-build": "workspace:*", "@types/lodash": "~4.14.202", "@types/moment": "^2.13.0", "@types/react": "18.0.25", "@types/react-dom": "18.0.9", "@types/react-grid-layout": "^0.17.2", "@types/react-redux": "^7.1.33", "@types/styled-components": "5.1.29", "prettier": "3.0.3", "typescript": "5.2.2"}}