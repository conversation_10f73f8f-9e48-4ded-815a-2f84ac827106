import React, { useCallback, useState } from "react";
import { Icon, Modal } from "antd";

import { getContainer, DataLoader } from "@reddeer/raid-theme";
import { PortfolioDto } from "@firefly-trading-labs/portfolio-metadata-api";

import { Panel } from "./components/Panel";
import { PortfolioForm } from "./components/PortfolioForm";
import { PortfolioData } from "./data/usePortfolioData";

enum PanelTitle {
    Edit = "Edit Portfolio",
    New = "Create New Portfolio"
}

type Props = PortfolioData & {
    isVisible: boolean;
    portfolio: PortfolioDto;
    onConfirm: (portfolioId: number | undefined) => void;
    onCancel: () => void;
};

export const PortfolioModal = ({
    isVisible,
    onConfirm,
    onCancel,
    portfolio,
    mappingData,
    profileDataStatus,
    updatePortfolio
}: Props) => {
    const [formValues, setFormValues] = useState<PortfolioDto>({
        ...portfolio,
        PortfolioRules: undefined
    });

    const isExistingPortfolio: boolean = portfolio && !!portfolio.Id;

    const sendUpdatedPortfolio = useCallback(
        async (portfolioToUpdate: PortfolioDto) => {
            const updatedPortfolio = await updatePortfolio(portfolioToUpdate);
            if (!!updatedPortfolio)
                onConfirm(updatedPortfolio.IsArchived ? undefined : updatedPortfolio.Id);
        },
        [onConfirm, portfolio]
    );

    return (
        <Modal
            width={700}
            closable={false}
            getContainer={getContainer}
            destroyOnClose={true}
            bodyStyle={{ padding: 0 }}
            style={{ backgroundColor: "initial", top: 20 }}
            visible={isVisible}
            //cancelButtonProps={{ style: { display: "none" } }}
            onCancel={() => onCancel()}
            onOk={() => sendUpdatedPortfolio(formValues)}
            okText={"Submit"}
            maskClosable={false}
        >
            <Panel
                header={
                    <span style={{ color: "#ffffff" }}>
                        {isExistingPortfolio ? PanelTitle.Edit : PanelTitle.New}
                    </span>
                }
                isCollapsable={false}
                contextMenu={
                    <Icon
                        type="close"
                        onClick={() => onCancel()}
                    />
                }
            >
                <DataLoader status={profileDataStatus}>
                    <PortfolioForm {...{ formValues, setFormValues, mappingData }} />
                    {/* <ReactJSON iconStyle={"square"} collapsed={0} src={{mappingData, portfolio, formValues}} theme={"ocean"} displayDataTypes={false}/> */}
                </DataLoader>
            </Panel>
        </Modal>
    );
};
