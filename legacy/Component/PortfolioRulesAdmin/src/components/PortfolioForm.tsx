import React, { useState, use<PERSON>emo, Dispatch, SetStateAction } from "react";
import styled from "styled-components";
import { Checkbox, Radio, Input } from "antd";
import type { RadioChangeEvent } from "antd/lib/radio";
import type { CheckboxChangeEvent } from "antd/lib/checkbox";

import {
    HierarchyLevel,
    PositionInclusionType,
    PortfolioDto,
    PortfolioMappingDto
} from "@firefly-trading-labs/portfolio-metadata-api";
import { apiGatewaySpec, createConfig, withCommandState } from "TransportGateway/index";
import type { ApiGatewayService } from "Contracts/index";
import { Flex } from "PrimitiveComponents/index";

import { PortfolioMappingCheckbox } from "./PortfolioMappingCheckbox";
import { MappingOption } from "../data/usePortfolioData";
import { StyledLabel, POSITION_TYPE_OPTIONS, GROUP_TYPE_OPTIONS, HierarchyLabel } from "./Shared";

export const RadioGroup = styled(Radio.Group)`
    &&&&&&& {
        margin-bottom: 0;
    }
`;

type Props = {
    formValues: PortfolioDto;
    setFormValues: Dispatch<SetStateAction<PortfolioDto>>;
    mappingData: MappingOption[];
};
export const PortfolioForm = ({ formValues, setFormValues, mappingData }: Props) => {
    const updateFormValue = (input: { name: string; value: any }) =>
        setFormValues(prev => ({ ...prev, [input.name]: input.value }));
    const onFieldChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>
        updateFormValue(event.target);
    const onCheckBoxChange = (event: CheckboxChangeEvent) =>
        updateFormValue({ name: event.target.name!, value: event.target.checked });

    return (
        <form>
            <Flex
                direction="column"
                gap="x-large"
            >
                <Flex direction="column">
                    <StyledLabel>Portfolio Type:</StyledLabel>
                    <RadioGroup
                        name={"HierarchyLevelId"}
                        onChange={({ target: { value } }: RadioChangeEvent) => {
                            setFormValues(prev => ({
                                ...prev,
                                HierarchyLevelId: value,
                                PortfolioMappings: [] as PortfolioMappingDto[]
                            }));
                        }}
                        value={formValues.HierarchyLevelId}
                        options={GROUP_TYPE_OPTIONS}
                    />
                </Flex>

                <Flex direction="column">
                    <StyledLabel>Position Inclusion Type:</StyledLabel>
                    <RadioGroup
                        name={"PositionInclusionTypeId"}
                        onChange={({
                            target: { name = "PositionInclusionTypeId", value }
                        }: RadioChangeEvent) => updateFormValue({ name, value })}
                        value={formValues.PositionInclusionTypeId}
                        options={POSITION_TYPE_OPTIONS}
                    />
                </Flex>

                <Flex
                    direction="column"
                    gap="small"
                >
                    <StyledLabel>Portfolio Name:</StyledLabel>
                    <Input
                        name="Name"
                        type="text"
                        value={formValues.Name}
                        onChange={onFieldChange}
                    />
                </Flex>

                <Flex
                    direction="column"
                    gap="small"
                >
                    <StyledLabel>Description:</StyledLabel>
                    <Input.TextArea
                        name="Description"
                        value={formValues.Description}
                        onChange={onFieldChange}
                        autoSize={{ minRows: 3, maxRows: 8 }}
                    />
                </Flex>

                <Flex
                    gap
                    items="center"
                >
                    <StyledLabel>Is Offering Memorandum:</StyledLabel>
                    <Checkbox
                        name={"IsOfferingMemorandum"}
                        checked={formValues.IsOfferingMemorandum}
                        onChange={onCheckBoxChange}
                    />
                </Flex>

                <PortfolioMappingCheckbox
                    name={`PortfolioMappings`}
                    onChange={updateFormValue}
                    mappingOptions={mappingData}
                    formValues={formValues}
                />
            </Flex>
        </form>
    );
};
