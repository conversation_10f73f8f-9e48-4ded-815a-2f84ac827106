import styled from "styled-components";
import {
    HierarchyLevel,
    PositionInclusionType
} from "@firefly-trading-labs/portfolio-metadata-api";

export const StyledLabel = styled.label`
    color: #06c6dd;
    font-family: <PERSON>o;
    font-size: 12px;
    line-height: 16px;
    white-space: nowrap;
`;

export const HierarchyLabel = {
    Firm: "Firm",
    FundGroup: "Fund Group",
    Fund: "Fund",
    Trader: "Trader",
    FundGroupAndTrader: "Fund Group and Trader",
    Strategy: "Strategy"
};

export const GROUP_TYPE_OPTIONS = Object.keys(HierarchyLevel)
    .map((key: any) => ({
        label: HierarchyLabel[HierarchyLevel[key]],
        value: Number(key)
    }))
    .filter(({ value }: any) => !isNaN(value))
    //always exclude Firm
    .filter(({ value }: any) => value !== HierarchyLevel.Firm);

export const POSITION_TYPE_OPTIONS = Object.keys(PositionInclusionType)
    .filter((h: any) => !isNaN(h))
    .map((key: any) => ({ label: PositionInclusionType[key], value: Number(key) }));
