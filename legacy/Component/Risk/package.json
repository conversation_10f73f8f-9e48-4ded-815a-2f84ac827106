{"name": "@reddeer/risk-utilisation-js-client", "version": "1.0.1", "description": "Risk Utilisation JS Client", "keywords": [], "main": "build/index.js", "files": ["build"], "scripts": {"start": "raid-build start", "build-typescript": "yarn g:tsc", "build": "yarn build-typescript"}, "dependencies": {"@firefly-trading-labs/portfolio-metadata-api": "1.6.1589", "@firefly-trading-labs/position-lifecycle-api": "1.6.1582", "@firefly-trading-labs/raid-trading-analytics-api": "1.6.1577", "@firefly-trading-labs/raiddata-api": "1.6.1596", "@firefly-trading-labs/risk-api": "1.6.1585", "@firefly-trading-labs/risk-model-api": "1.6.1578", "@reddeer/firefly-common-components": "workspace:*", "@reddeer/firefly-shared-controls": "workspace:*", "@reddeer/raid-theme": "workspace:*", "@tradinglabs/component-library": "workspace:*", "@tradinglabs/primitive-components": "workspace:*", "@tradinglabs/theming": "workspace:*", "@tradinglabs/utilities": "workspace:*", "@tradinglabs/viewserver-core": "workspace:*", "@tradinglabs/viewserver-redux": "workspace:*", "antd": "3.26.20", "lodash": "~4.17.21", "moment": "2.29.4", "react": "18.2.0", "react-datepicker": "^4.16.0", "react-dom": "18.2.0", "react-redux": "7.2.9", "redux": "4.2.1", "styled-components": "5.3.11"}, "license": "UNLICENSED", "devDependencies": {"@tradinglabs/raid-build": "workspace:*", "@types/lodash": "~4.14.202", "@types/moment": "^2.13.0", "@types/react": "18.0.25", "@types/react-dom": "18.0.9", "@types/react-redux": "^7.1.33", "@types/styled-components": "5.1.29", "typescript": "5.2.2"}}