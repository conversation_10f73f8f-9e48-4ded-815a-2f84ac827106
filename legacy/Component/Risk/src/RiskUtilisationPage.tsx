import React, { useState, useEffect } from "react";
import { Switch } from "antd";
import { DataLoader, DataRequestStatus } from "@reddeer/raid-theme";
import { Selector } from "@reddeer/firefly-shared-controls";
import { Content, Layout } from "@reddeer/firefly-common-components";
import { BaseStyles } from "Theming/index";
import { formatLabel } from "Utilities/index";
import { Radio } from "PrimitiveComponents/index";

import { useUserSetting, PortfolioType, RuleType, PageProps } from "./model";
// eslint-disable-next-line import/extensions
import { RiskGrid } from "./RiskGrid";

export const ConnectedRiskUtilisationPage = (props: PageProps) => {
    const [showAllExposures, setShowAllExposures] = useState<boolean>(false);
    const [portfolioType, setPortfolioType, portfolioTypeStatus] = useUserSetting<PortfolioType>(
        "fundOrTrader",
        PortfolioType.Trader
    );
    const [ruleType, setRuleType, ruleTypeStatus] = useUserSetting<RuleType>(
        "positionOrPortfolio",
        RuleType.Portfolio
    );
    const selectorsReady =
        portfolioTypeStatus === DataRequestStatus.Ready &&
        ruleTypeStatus === DataRequestStatus.Ready;

    useEffect(() => {
        document.title = "Risk Utilisation";
    }, []);

    return (
        <BaseStyles global={false}>
            <Layout style={{ height: `calc(100vh - 67px)` }}>
                <Content>
                    <div style={{ display: "flex" }}>
                        <div
                            style={{
                                width: 400,
                                margin: "auto",
                                marginBottom: 15,
                                display: "flex",
                                flexDirection: "row",
                                alignItems: "center",
                                justifyContent: "space-between"
                            }}
                        >
                            <Radio
                                size="x-large"
                                value={portfolioType}
                                onChange={setPortfolioType}
                                options={Object.entries(PortfolioType).map(([label, value]) => ({
                                    label: formatLabel(label),
                                    value
                                }))}
                            />
                            <Radio
                                size="x-large"
                                value={ruleType}
                                onChange={setRuleType}
                                options={Object.entries(RuleType).map(([label, value]) => ({
                                    label: formatLabel(label),
                                    value
                                }))}
                            />
                        </div>

                        <div title="Show exposure for rules where a limit has not been set">
                            Show all exposures{" "}
                            <Switch
                                defaultChecked={showAllExposures}
                                onChange={(checked: boolean) => setShowAllExposures(checked)}
                            />
                        </div>
                    </div>
                    <DataLoader status={[portfolioTypeStatus, ruleTypeStatus]}>
                        <RiskGrid
                            {...{
                                ...props,
                                portfolioType,
                                ruleType,
                                showAllExposures,
                                selectorsReady
                            }}
                        />
                    </DataLoader>
                </Content>
            </Layout>
        </BaseStyles>
    );
};
