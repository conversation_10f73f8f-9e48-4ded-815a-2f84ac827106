import { useCallback, useEffect, useState } from "react";
import { ParameterTypeDto, ParameterDataType } from "@firefly-trading-labs/portfolio-metadata-api";
import { PageProps, SelectorProps, RowData, PortfolioRowData } from "../model";
import { useRiskUtilData, UtilsByParam } from "../data/useRiskUtilData";
import { usePortfolioData } from "../data/usePortfolioData";
import { useParameterData } from "../data/useParameterData";
import { ParameterSeverity, UtilisationDto } from "@firefly-trading-labs/risk-api";
import { orderBy } from "lodash";
import {
    formatCurrentValueColumn,
    formatCurrentValuePercentColumn,
    formatDollarLimitColumn,
    formatPercentLimitColumn
} from "../data/formatting";
import { PortfolioType } from "..";
import { CloseToLimitThreshold } from "../common/NestedDataPanel";

const getEntitiesSortedByKey = (param: ParameterTypeDto) =>
    !param.TableEntities
        ? [["single-value", param.Name]]
        : [
              ["_category", param.Name],
              ...Object.entries(param.TableEntities).sort(([a], [b]) =>
                  a > b ? 1 : a < b ? -1 : 0
              )
          ];
const isTableEntity = (type: ParameterDataType) =>
    [ParameterDataType.TablePercentage, ParameterDataType.TablePercentageOfAum].includes(type);

const getUtilData = (
    utils: UtilisationDto[],
    param: ParameterTypeDto,
    portfolioId: number,
    portfolioName: string,
    entityId,
    entityKey,
    showAllExposures = false,
    showHighValuesOnly = false
): PortfolioRowData => {
    const paramType = param.ParameterDataTypeId;
    if (isTableEntity(paramType)) {
        utils = [...utils, ...utils.flatMap(r => r.AllUtilisations)].filter(
            (util: UtilisationDto) =>
                util?.PortfolioKey === entityId || util?.PortfolioKey === entityKey
        );
    }
    if (!showAllExposures) {
        utils = utils?.filter(
            row => row.CurrentValue !== 0 && (row.PercentLimit !== 0 || row.DollarLimit !== 0)
        );
    }
    if (showHighValuesOnly) {
        utils = utils?.filter(
            row => row.IsBreaching || row.CurrentUtilisation > CloseToLimitThreshold
        );
    }

    const Hard = utils.find(util => util.Severity === ParameterSeverity.Hard);
    const Warn = utils.find(util => util.Severity === ParameterSeverity.Warning);
    const Hardest = !!Warn && (!Warn.IsBreaching || !Hard) ? Warn : Hard;
    if (!Hardest) return undefined;
    const {
        DollarLimit,
        PercentLimit,
        CurrentValue,
        CurrentValuePercent,
        CurrentUtilisation,
        PortfolioKey,
        Severity
    } = Hardest;

    const nestedData: UtilisationDto[] =
        !Hardest || !Hardest?.AllUtilisations
            ? []
            : orderBy(
                  [{ ...Hardest, AllUtilisations: null }, ...(Hardest?.AllUtilisations ?? [])],
                  ["CurrentUtilisation", "CurrentValue"],
                  ["desc", "desc"]
              );

    return !Hardest
        ? undefined
        : {
              Id: portfolioId,
              Name: portfolioName,
              Severity,

              DollarLimit,
              DollarLimitFormatted: formatDollarLimitColumn(DollarLimit, paramType),

              PercentLimit,
              PercentLimitFormatted: formatPercentLimitColumn(PercentLimit, paramType),

              CurrentValue,
              CurrentValueFormatted: formatCurrentValueColumn(CurrentValue, paramType),

              CurrentValuePercent,
              CurrentValuePercentFormatted: formatCurrentValuePercentColumn(
                  CurrentValuePercent,
                  paramType
              ),

              CurrentUtilisation,

              ExpectationPercentOfTotal: Hardest.ExpectationPercentOfTotal,
              HardBreach: !!Hard?.IsBreaching,
              WarnBreach: !!Warn?.IsBreaching,
              PortfolioKey,
              NestedData: nestedData
                  .map(util =>
                      getUtilData(
                          [util],
                          param,
                          portfolioId,
                          portfolioName,
                          entityId,
                          entityKey,
                          true
                      )
                  )
                  .filter(util => !!util)
          };
};

export const useRowData = (
    props: PageProps & SelectorProps & { selectorsReady: boolean; showHighValuesOnly: boolean }
) => {
    const {
        traderList,
        fundList,
        fundId,
        fundGroupId,
        traderIds,
        portfolioType,
        ruleType,
        showAllExposures,
        selectorsReady,
        showHighValuesOnly
    } = props;

    const { portfolios } = usePortfolioData({
        traderList,
        fundList,
        fundId,
        fundGroupId,
        traderIds,
        portfolioType
    });
    const { activeParams, countryMap } = useParameterData({
        portfolioType,
        ruleType,
        selectorsReady
    });
    const { riskUtilRequestState, lastUpdated, utilDataByParamByPortfolio } = useRiskUtilData({
        activeParams,
        portfolios
    });
    const [rowData, setRowData] = useState([]);

    const getRowData = useCallback(
        (results: UtilsByParam): RowData[] => {
            const rowData = activeParams
                .reduce((rows: RowData[], param) => {
                    //const showCategory = filteredResults.length > 0;
                    const utilsForParam = results?.[param.Id];
                    return [
                        ...rows,
                        ...getEntitiesSortedByKey(param).reduce(
                            (entities: RowData[], [EntityKey, EntityId]) => {
                                const IsTableCategory = EntityKey === "_category";
                                const IsTableRow =
                                    !IsTableCategory && isTableEntity(param.ParameterDataTypeId);
                                return [
                                    ...entities,
                                    {
                                        ...param,
                                        rowId: `${param.Id}-${EntityId}`,
                                        RuleName: `${
                                            IsTableRow ? EntityKey : param.DisplayName ?? param.Name
                                        }`,
                                        IsTableCategory,
                                        IsTableRow,
                                        ShowTableCategory: IsTableCategory,
                                        Path: IsTableRow
                                            ? [param.Name, EntityKey]
                                            : [param.DisplayName ?? param.Name],
                                        ByPortfolio: portfolios.reduce(
                                            (acc: RowData["ByPortfolio"], { Id, Name }) => {
                                                const portfolioData = !!utilsForParam?.[Id]
                                                    ? getUtilData(
                                                          utilsForParam[Id],
                                                          param,
                                                          Id,
                                                          Name,
                                                          EntityId,
                                                          EntityKey,
                                                          showAllExposures,
                                                          showHighValuesOnly
                                                      )
                                                    : undefined;
                                                if (!!portfolioData) {
                                                    acc[Id] = portfolioData;
                                                }
                                                return acc;
                                            },
                                            {}
                                        )
                                    }
                                ];
                            },
                            []
                        )
                    ];
                }, [])
                .filter(
                    row =>
                        ([PortfolioType.Trader, PortfolioType.Strategy].includes(portfolioType) &&
                            !!row?.ShowTableCategory) ||
                        !!Object.keys(row?.ByPortfolio).length
                );

            return rowData;
        },
        [
            activeParams,
            portfolios,
            traderList,
            traderIds,
            fundId,
            fundGroupId,
            showAllExposures,
            showHighValuesOnly
        ]
    );

    useEffect(() => {
        setRowData(getRowData(utilDataByParamByPortfolio));
    }, [utilDataByParamByPortfolio, getRowData]);

    return { rowData, requestState: riskUtilRequestState, lastUpdated, countryMap, portfolios };
};
