import { Store, createStore, applyMiddleware, Middleware, combineReducers } from "redux";
import { subscriptionMiddleware, connectionMiddleware, connectionReducer as ConnectionStatusReducer, subscriptionReducer } from "@tradinglabs/viewserver-redux";
import { IServerConnection, ISubscriptionClient } from "@tradinglabs/viewserver-core";
export interface IRootState {
}

export function configureStore(connection: IServerConnection, subscriptionClient: ISubscriptionClient): Store<IRootState> {
    connection.connect();
    const middleware = applyMiddleware(connectionMiddleware(connection), subscriptionMiddleware(subscriptionClient, undefined));
    const store = createStore(combineReducers({ connection: ConnectionStatusReducer, subscriptions: subscriptionReducer }), undefined, middleware) as Store<IRootState>;
    return store;
}
