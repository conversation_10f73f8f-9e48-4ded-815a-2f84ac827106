import React from "react";
import moment from "moment";
import { TickerWithTooltip } from "PrimitiveComponents/index";
import { withCommandStateDepreciated as withCommandState, Table, TableDefinition, TableColumnAlignType, ChangedValue } from "@reddeer/firefly-shared-controls";
import * as styles from "./Styles.scss";
import { subscriptionCommandSpec, createConfig } from "./types";

const winnerLoserTableDefinition: TableDefinition<any> = {
    columns: [
        {
            align: TableColumnAlignType.Left,
            header: "Ticker",
            getCellValue: row => <span className={styles.stockCell}><TickerWithTooltip bbgTicker={row.bloombergTicker} /></span>,
        },
        {
            align: TableColumnAlignType.Right,
            header: "Date",
            getCellValue: row => moment(row.date).format("DD/MM/YYYY")
        },
        {
            align: TableColumnAlignType.Right,
            header: "Return",
            getCellValue: row => <ChangedValue value={row.return} formatType="percent2dp" />
        },
        {
            align: TableColumnAlignType.Right,
            header: "Volatility",
            getCellValue: row => <ChangedValue value={row.volatility} formatType="number2dp" />
        }
    ],
    getRowKey: (row, index) => String(index)
}

const TopWinnersTableBase = ({ dataRows = [] }) => {
    return <Table definition={winnerLoserTableDefinition} data={dataRows} />;
};

const TopLosersTableBase = ({ dataRows = [] }) => {
    return <Table definition={winnerLoserTableDefinition} data={dataRows} />;
};

const responseMapper = (data = []) => (data);
const requestMapper = ({ backtestId: Id }) => ({ Id })
export const TopWinnersTable = withCommandState(TopWinnersTableBase, createConfig({ dataRows: subscriptionCommandSpec("TableRequest_TopWinnerDto_Subscription_Handler", requestMapper, responseMapper) }));
export const TopLosersTable = withCommandState(TopLosersTableBase, createConfig({ dataRows: subscriptionCommandSpec("TableRequest_BottomLoserDto_Subscription_Handler", requestMapper, responseMapper) }));
//export const TopLosersTable = withCommandState(TopLosersTableBase, createConfig({ dataRows: subscriptionCommandSpec("TopLosersRequestHandler", mapWinnerLoserData, ({ backtestId: Id }) => ({ Id })) }));
