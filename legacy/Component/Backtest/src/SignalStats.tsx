import React from "react";
import { ChangedValue, numberFormats, withCommandStateDepreciated as withCommandState, formatNumber, getDirectionalCss } from "@reddeer/firefly-shared-controls";
import * as styles from "./Styles.scss";
import { subscriptionCommandSpec, createConfig } from "./types";

const Stat = ({ title, children }) => (
    <div className={styles.signalStat__stat}>
        <div className={styles.signalStat__title}>{title}</div>
        <div className={styles.signalStat__value}>{children}</div>
    </div>
);

const SignalStatsContent = ({ data }: { data: any }) => (
    <div className={styles.signalStat__container} style={{flex: 1}}>
        <Stat title="Hit Ratio">
            <ChangedValue
                value={data ? data.hitRatio : null}
                classNameFn={value => getDirectionalCss(value - 0.5)}
                formatType="number2dp"
                hidePositiveSign
            />
        </Stat>
        <Stat title="Avg Daily Hits">
            {data ? formatNumber(data.averageDailyHits, numberFormats.number2dp) : null}
        </Stat>
        <Stat title="Avg Monthly Ret">
            <ChangedValue value={data ? data.averageMonthlyReturn : null} formatType="percent2dp" />
        </Stat>
        <Stat title="Abs Ret">
            <ChangedValue value={data ? data.absoluteReturn : null} formatType="percent2dp" />
        </Stat>
        {/* <Stat title="Avg Ann Vol" value={data ? data.averageAnnualVolatility : null} /> */}
        {/* <Stat title="Significance" value={data ? data.significance : null} /> */}
        {/* <Stat title="Rel Ret" value={data ? data.relativeReturn : null} numberFormat={numberFormats.percent2dp} /> */}
    </div>
);


const requestMapper = ({ backtestId: Id }) => ({ Id })
export const SignalStats = withCommandState(SignalStatsContent, createConfig({ data: subscriptionCommandSpec("TableRequest_SummaryDto_Subscription_Handler", requestMapper, (data = []) => data[0] ) }));
