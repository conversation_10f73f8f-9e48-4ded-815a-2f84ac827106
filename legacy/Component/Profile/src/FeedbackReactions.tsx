import React from "react";
import styled from "styled-components";
import { PercentageBarChart } from "@reddeer/firefly-shared-controls";
import { PrimaryTitle } from "./Components";

const ChartTitle = styled(PrimaryTitle)`
  font-weight: 300 !important;
  margin: 24px 18px !important;
`;

const TitleSecondary = styled.span`
  color: #778a93;
`;

const ChartSection = ({ titleA, titleB, valueA, valueB }: any) => (
  <>
    <ChartTitle>
      {titleA} <TitleSecondary>vs {titleB}</TitleSecondary>
    </ChartTitle>
    <PercentageBarChart
      width={400}
      height={100}
      model={{
        series: [
          { label: titleA, value: valueA, fillColor: "#04AEF5" },
          { label: titleB, value: valueB, fillColor: "#045571" }
        ]
      }}
    />
  </>
);

export const FeedbackReactions = () => {
  return (
    <div>
      <ChartSection titleA="Cool Headed" valueA={0.58} titleB="Defensive" valueB={0.41} />
      <ChartSection titleA="Acceptance" valueA={0.83} titleB="Denial" valueB={0.18} />
      <ChartSection titleA="Genuine" valueA={0.97} titleB="Superficial" valueB={0.09} />
    </div>
  );
};
