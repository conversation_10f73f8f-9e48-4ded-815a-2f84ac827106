import React, { useState, useCallback, useMemo }  from "react";
import { Widget, useElementSize, InsightsFilterIds } from "@reddeer/firefly-shared-controls";
import { Selector } from "@reddeer/firefly-shared-controls";
import { AnalyticCardGroup } from "../AnalyticCardGroup";
import { PnlType, AnalyticsSectionTitles, Direction, ThemeSortFieldValue, ConnectedTheme, TraderAnalyticsThemeSummary } from "../../model";
import { CardSortField } from "../common/CardSortField";
import { BackButton } from "../common/BackButton";
import { WidgetHeader, WidgetHeaderLeft, WidgetHeaderRight, WidgetInner, WidgetHeaderCenter } from "../common/WidgetHeader";
import { DirectionSelector } from "../common/DirectionSelector";
import { Dictionary } from "lodash";

type Props = InsightsFilterIds & {
    data: Dictionary<TraderAnalyticsThemeSummary[]>;
};

export const decisionTradingThemes = {
    positionManagement: [
        {id: 3, title: "Initiating"},
        {id: 6, title: "AddingToWinners"},
        {id: 7, title: "AddingToLosers"},
        {id: 5, title: "CuttingLosers"},
        {id: 2, title: "ActionedStopLoss"},
        {id: 1, title: "UnactionedStopLoss"},
        {id: 4, title: "ProfitTaking"},
    ],
    behaviour: [
        {id: 8, title: "MorningTrading"},
        {id: 9, title: "AfternoonTrading"},
        {id: 10, title: "OnResultsDay"},
        {id: 11, title: "BeforeResultsDay"},
        {id: 12, title: "AfterResultsDay"},
        {id: 13, title: "RevisitedWithin1Week"},
        {id: 14, title: "RevisitedBetween1WeekAnd1Month"},
        {id: 15, title: "RevisitedBetween1MonthAnd3Months"}

    ],
    reactiveTrading: [],
};
export const DecisionTradingAnalyticsComponent = ({data, ...rest}: Props) => {
    const container = React.useRef(null);
    const [containerWidth] = useElementSize(container);
    const [direction, setDirection] = useState<Direction>(Direction.Overall);
    const [pnlType, setPnlType] = useState<PnlType>(PnlType.Dollar);
    const [showRelative, setShowRelative] = useState<boolean>(false);
    const [selectedTheme, setSelectedTheme] = useState<ConnectedTheme | undefined>();
    const [sortField, setSortField] = useState<ThemeSortFieldValue>(ThemeSortFieldValue.Default);

    const isCentered = useMemo(() => containerWidth < 1000 ? 1 : 0, [containerWidth]);

    const backToSummary = () => {
        setSelectedTheme(undefined);
    };

    const onClick = useCallback((theme: ConnectedTheme, _: string | null) => {
        setSelectedTheme(theme);
    }, []);

    const positionManagementThemes: ConnectedTheme[] = decisionTradingThemes.positionManagement.map(({ id, title }) => {
        return {
            id,
            title,
            summaries: data[id] ?? []
        };
    });

    const behaviourThemes: ConnectedTheme[] = decisionTradingThemes.behaviour.map(({ id, title }) => {
        return {
            id,
            title,
            summaries: data[id] ?? []
        };
    });

    return (
        <Widget title={AnalyticsSectionTitles.DecisionTrading}>
            <WidgetInner>
                <WidgetHeader ref={container} isCentered={isCentered}>
                    <WidgetHeaderLeft isCentered={isCentered}>
                        <BackButton isVisible={!!selectedTheme} onClick={backToSummary} />
                    </WidgetHeaderLeft>
                    <WidgetHeaderCenter>
                        <DirectionSelector {...{direction, setDirection}} />
                    </WidgetHeaderCenter>
                    <WidgetHeaderRight>
                        <div style={{width: 150}}>
                            <CardSortField onChange={setSortField} value={sortField} />
                        </div>
                        <div style={{width: 100, marginLeft: 8}}>
                            <Selector onChange={setPnlType} options={[
                                {label: "%", value: PnlType.Percent, active: pnlType === PnlType.Percent},
                                {label: "$", value: PnlType.Dollar, active: pnlType === PnlType.Dollar}
                            ]}/>
                        </div>
                        <div style={{width: 100, marginLeft: 8}}>
                            <Selector onChange={v => setShowRelative(v)} options={[
                                {label: "Abs", value: false, active: !showRelative},
                                {label: "Rel", value: true, active: showRelative}
                            ]}/>
                        </div>
                    </WidgetHeaderRight>
                </WidgetHeader>
                <AnalyticCardGroup {...{sortField, selectedTheme, pnlType, direction, onClick, showRelative }} title="Position Management" themes={positionManagementThemes}  {...rest} />
                <AnalyticCardGroup {...{sortField, selectedTheme, pnlType, direction, onClick, showRelative }} title="Behaviour" themes={behaviourThemes}  {...rest} />
            </WidgetInner>
        </Widget>
    );
};
