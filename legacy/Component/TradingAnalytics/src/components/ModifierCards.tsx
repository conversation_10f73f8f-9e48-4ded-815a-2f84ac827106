import React, { useState, useMemo } from "react";
import { Carousel } from "antd";
import styled from "styled-components";
import { chunk, groupBy } from "lodash";
import FlipMove from "react-flip-move";

import { PnlType, Direction, ThemeSortFieldValue, ConnectedTheme, selectDirectionalData, sortThemeSummaries} from "../model";
import { AnalyticCard } from "./AnalyticCard";

const CardContainer = styled(FlipMove)`display: flex; flex-wrap: wrap; justify-content: center; align-items: flex-start;`;
const CARDS_PER_SLIDE = 5;

const StyledCarousel = styled(Carousel)`
    && .slick-dots {
        height: 8px;
    }

    && .slick-dots li.slick-active button {
        background: #0B80B3;
        width: 40px;
    }
    && .slick-dots li button {
        height: 8px;
        width: 40px;
        opacity: 0.4;
        background: #0B80B3;
    }
    & .slick-slide {
        text-align: center;
        overflow: hidden;
    }
    & .slick-list {
        height: 150px;
    }
`
const CarouselContainer = styled.div`
    width: 988px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding-top: 16px;
`;
const CarouselTitleContainer = styled.div`display: flex;`;
const CarouselTitleIndicatorLeft = styled.span<{count: number}>`
    transition: all 0.3s ease;
    flex: 1; border-top: 1px solid #424860;
    margin-left: ${({count}) => CARDS_PER_SLIDE + 1 - count}00px;
    margin-top: 10px; border-left: 1px solid #424860;`;
const CarouselTitleIndicatorRight = styled.span<{count: number}>`
    transition: all 0.3s ease;
    flex: 1; border-top: 1px solid #424860;
    margin-right: ${({count}) => CARDS_PER_SLIDE + 1 - count}00px;
    margin-top: 10px; border-right: 1px solid #424860;`;
const CarouselTitleText = styled.span`padding: 0px 10px 6px 10px; color: #FFFFFF;`;
const CarouselTitle = ({children, count}: {children: any, count: number}) => (
    <CarouselTitleContainer>
        <CarouselTitleIndicatorLeft count={Math.max(count, 2)} />
        <CarouselTitleText>
            {children}
        </CarouselTitleText>
        <CarouselTitleIndicatorRight count={Math.max(count, 2)}/>
    </CarouselTitleContainer>
);

type Props = {
    selectedTheme: ConnectedTheme;
    direction: Direction;
    sortField: ThemeSortFieldValue;
    showRelative: boolean;
    pnlType: PnlType;
    onSelect?: (modifier: string) => void;
    selectedModifier: string | undefined;
};

export const ModifierCards = ({ pnlType, direction, sortField, selectedTheme, onSelect, selectedModifier, showRelative }: Props) => {
    const byModifier = groupBy(selectedTheme.summaries.filter(d => !!d.modifier), d => d.modifier);

    const orderedModifiers = useMemo(() => {
        return Object.keys(byModifier).sort((a, b) => {
            const aData = selectDirectionalData(byModifier[a], direction, a);
            const bData = selectDirectionalData(byModifier[b], direction, b);
            
            return sortThemeSummaries(aData, bData, sortField, showRelative);
        });
    }, [selectedTheme, direction, showRelative, selectedTheme, sortField]);

    const [visibleSlideIndex, setVisibleSlideIndex] = useState<number>(0);
    const toggleSelection = (_: ConnectedTheme, modifier: string) => onSelect(modifier === selectedModifier ? undefined : modifier);

    const groupedModifierData = chunk(orderedModifiers, CARDS_PER_SLIDE);
    const numOfSlides = groupedModifierData?.length;

    return (
        <CarouselContainer>
            <CarouselTitle count={groupedModifierData[visibleSlideIndex]?.length}>Modifiers{numOfSlides > 1 ? ` ${visibleSlideIndex + 1} / ${numOfSlides}` : ""}</CarouselTitle>
            <StyledCarousel initialSlide={visibleSlideIndex} dotPosition={"bottom"} afterChange={nextIndex => setVisibleSlideIndex(nextIndex)} >
                {groupedModifierData.map((group, i) => <div key={i}>
                    <CardContainer /* appearAnimation="elevator" leaveAnimation="elevator" */ duration={300} easing="ease-out">
                        {(group || []).map((m) =>
                            <div key={m}>
                                <AnalyticCard
                                    onClick={toggleSelection}
                                    plain
                                    showRelative={showRelative}
                                    direction={direction}
                                    modifier={m}
                                    selected={(selectedModifier === m)}
                                    theme={selectedTheme}
                                    pnlType={pnlType}
                                />
                            </div>)}
                    </CardContainer></div>)}
            </StyledCarousel>
        </CarouselContainer>
    );
};