import React, { useMemo, useState } from "react";
import { Direction, PnlType, TradingAnalyticsRequestProps, ThemeSortFieldValue, ConnectedTheme, selectDirectionalData, sortThemeSummaries } from "../model";
import { isEqual } from "lodash";
import { ModifierCards } from "./ModifierCards";
import { AnalyticCardContainer, AnalyticCard } from "./AnalyticCard";
import { ThemeDeepDiveConnected } from "./DecisionTrading/ThemeDeepDive";
import styled from "styled-components";

type AnalyticCardGroupProps = TradingAnalyticsRequestProps & {
    title: string;
    direction: Direction;
    pnlType: PnlType;
    showRelative: boolean;
    selectedTheme: ConnectedTheme | undefined;
    sortField: ThemeSortFieldValue;
    onClick?: (theme: ConnectedTheme, modifier: string | null) => void;
};

type ConnectedAnalyticCardGroupProps = AnalyticCardGroupProps & {
    themes: ConnectedTheme[];
};

const CardWrapper = styled.div<{showselectedcard: 1 | 0}>`
    ${({showselectedcard}) => showselectedcard ? "width: 100%; display: flex; justify-content: center;" : ""}
`;

export const AnalyticCardGroup = React.memo(({ title, themes, selectedTheme, onClick, direction, sortField, showRelative, ...rest }: ConnectedAnalyticCardGroupProps) => {
    const orderedThemes = useMemo(() => {
        return selectedTheme ? themes.filter(t => t.id === selectedTheme.id) : [...themes].sort((a, b) => {
            if (a.summaries.length === 0 && b.summaries.length === 0) {
                return 0;
            }

            if (a.summaries.length === 0) {
                return 1;
            }

            if (b.summaries.length === 0) {
                return -1;
            }

            const aData = selectDirectionalData(a.summaries, direction);
            const bData = selectDirectionalData(b.summaries, direction);
            
            return sortThemeSummaries(aData, bData, sortField, showRelative);
        });
    }, [themes, direction, selectedTheme, showRelative, sortField]);

    const showSelectedTheme = selectedTheme && orderedThemes.length === 1 ? 1 : 0;
    const [selectedModifier, setSelectedModifier] = useState<string | undefined>(undefined);

    return (
        <div>
            { !selectedTheme ? <div>{title}</div> : null }
            <AnalyticCardContainer showselectedcard={showSelectedTheme} duration={300} easing="ease-out">
                {
                    orderedThemes.map((t: ConnectedTheme) => {
                        return (
                            <CardWrapper key={t.id} showselectedcard={showSelectedTheme}>
                                <AnalyticCard theme={t as any} direction={direction} onClick={onClick} showRelative={showRelative} {...rest} modifier={null} />
                            </CardWrapper>
                        );
                    })
                }
                <div>
                    {
                        showSelectedTheme ? <ModifierCards
                            showRelative={showRelative}
                            direction={direction} 
                            onSelect={m => setSelectedModifier(m)}
                            pnlType={rest.pnlType}
                            selectedTheme={selectedTheme}
                            selectedModifier={selectedModifier}
                            sortField={sortField}
                        /> : null
                    }
                </div>
            </AnalyticCardContainer>
            {
                showSelectedTheme ? 
                    <ThemeDeepDiveConnected modifier={selectedModifier} direction={direction} theme={selectedTheme} showRelative={showRelative} {...rest} /> :
                    null
            }
        </div>
    );
}, isEqual);