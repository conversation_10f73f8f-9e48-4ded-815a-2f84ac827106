import React, { useState, useContext, useEffect }  from "react";
import { FlexRow, FlexItem, withCommandStateDepreciated as withCommandState } from "@reddeer/firefly-shared-controls";
import FlipMove from "react-flip-move";
import { Tabs, Spin } from "antd";
import { RangeTableRow, RANGE_TABLE_ROW_WIDTH, RANGE_COLUMN_FLEX, MIN_NUM_RANGES, TITLE_COLUMN_FLEX } from "./RangeTableRow";
import { RangeTitle } from "./RangeTitle";
import { TradingAnalyticsRequestProps, RangeDataCollection, createConfig, SubscriptionCommands, requestMapper, subscriptionRequestResponse, StockCharacteristicCategory, RootState, Direction } from "../../model";
import { rangeDataMapper } from "./rangeDataMapper";
import styled from "styled-components";
import { RangeTableContext } from "./rangeTableContext";
import { useSelector } from "react-redux";
import { get } from "lodash";

const categories = Object.keys(StockCharacteristicCategory).map(key => StockCharacteristicCategory[key]);

const {TabPane} = Tabs;
const RowContainer = styled.div`
    overflow-x: auto;
    ::-webkit-scrollbar-thumb {
        background-color: #13455D !important;
    }
    ::-webkit-scrollbar-track {
        background-color: #131415 !important;
    }
`;
const StyledTabs = styled(Tabs)<{hideBar: 1 | 0}>` && {
    .ant-tabs-bar {
        width: 100%;
        border-bottom: none;
        display: ${({hideBar}) => hideBar === 1 ? "none" : "inherit"};
    }
    .ant-tabs-ink-bar {
        display: none;
    }
    .ant-tabs-nav-container {
        text-align: center;
    }
    && .ant-tabs-tab {
        border-radius: 2px 0 0 2px;
        border: 1px solid #383C41;
        width: 99px;
        height: 28px;
        margin-right: 0px;
        color: #ffffff;
        padding: 2px;
        overflow: hidden;
    }
    && .ant-tabs-tab-active {
        border: 1px solid #13455D;
        background-color: #13455D;
    }
    && .ant-tabs-ink-bar {
        background-color: inherit;
    }
}`;

export const RangeTable = (props: TradingAnalyticsRequestProps & {direction: Direction, showRelative: boolean}) => {
    const [category, setCategory] = useState<StockCharacteristicCategory>(StockCharacteristicCategory.Technical);
    const [selectedCell] = useContext(RangeTableContext);
    const isCellSelected: boolean = !!selectedCell;

    const categories: StockCharacteristicCategory[] = Object.keys(StockCharacteristicCategory).map(key => StockCharacteristicCategory[key]);
    const isReady: {[key: string]: boolean} = categories.reduce((acc, category) => {
        acc[category] = useSelector((state: RootState) =>
            get(state, `subscriptions.${window.TE_CONNECTION_NAME}[${SubscriptionCommands.StockAnalytics[category]}].complete`));
        return acc;
    }, {});

    useEffect(() => {
        if (!!selectedCell) {
            setCategory(selectedCell.category);
        }
    }, [selectedCell]);

    return <StyledTabs activeKey={category} onTabClick={setCategory} hideBar={isCellSelected ? 1 : 0}>
        {categories.map(category =>
            <TabPane 
                tab={<Spin size="small" spinning={!isReady[category]}>{category}</Spin>}
                key={category}
                forceRender={true}>
                {React.createElement(RangeDataConnected[category], {...props, category})}
            </TabPane>
        )}
    </StyledTabs>;
};

const RangeTableRows = ({data = [], category, direction, showRelative}: {data: RangeDataCollection[], category: StockCharacteristicCategory, direction: Direction, showRelative: boolean}) => {
    const [selectedCell, setSelectedCell] = useContext(RangeTableContext);
    const filteredData = !selectedCell ? data : data.filter((row) => row.name === selectedCell.name);

    useEffect(() => {
        if (!!selectedCell && filteredData.length === 1) {
            //when we change from long to short we need to reselect the correct cell
            setSelectedCell(
                filteredData[0].directionData[direction].find(
                    ({name, rangeTitle}) => name === selectedCell.name && rangeTitle === selectedCell.rangeTitle));
        }
    }, [direction]);

    return <div style={{ overflow: "auto" }}><div style={{width: RANGE_TABLE_ROW_WIDTH, minHeight:100, margin: "auto"}}>
        {filteredData && <>
            {!filteredData.length ? <div style={{textAlign: "center"}}>No Data</div> : 
            <FlexRow>
                <FlexItem flex={TITLE_COLUMN_FLEX}>{selectedCell ? category : ""}</FlexItem>
                <FlexItem flex={RANGE_COLUMN_FLEX * MIN_NUM_RANGES}>
                    <RangeTitle>Range</RangeTitle>
                </FlexItem>
            </FlexRow>}
            <FlipMove duration={300} easing="ease-out">
                {filteredData.map((data: RangeDataCollection) => <RowContainer key={data.name}>
                    <RangeTableRow data={data.directionData[direction].map(range => ({
                        ...range, 
                        hitRate: showRelative ? range.relativeHitRate : range.hitRate,
                        pnLDollar: showRelative ? range.relativePnLDollar : range.pnLDollar,
                    }))} /></RowContainer>)}
                </FlipMove>
            </>}
    </div></div>
};

const RangeDataConnected = categories.reduce((acc, category) => ({
    ...acc,
    [category]: withCommandState(RangeTableRows, createConfig({data: subscriptionRequestResponse(SubscriptionCommands.StockAnalytics[category], requestMapper, rangeDataMapper(StockCharacteristicCategory[category]))}))
}), {});
