import React, { useCallback } from "react";
import { InputNumber } from "antd";
import { TickerWithTooltip } from "PrimitiveComponents/index";
import { ConstituentDto } from "@firefly-trading-labs/customindex-api";
import {
    MemberTable,
    MemberTableRow,
    MemberTableHeader,
    MemberTableCell,
    LeftCell,
    CenterCell,
    RightCell,
    DisplayGrid,
    StyledNumber
} from "./common";
import { TickerSelecor } from "./TickerSelector";
import styled from "styled-components";

const Ticker = styled(TickerWithTooltip)`
    color: #ffb300;
`;
const TickerToRemove = ({ ticker }: { ticker: string }) => (
    <span style={{ color: "#4e4f50", textDecoration: "line-through" }}>
        {ticker.replace("Equity", "")}
    </span>
);

interface Props {
    constituents: ConstituentDto[];
    title: string;
    onChange: (data: ConstituentDto[]) => void;
}

export const ConstituentTable: React.FunctionComponent<Props> = ({
    constituents,
    title,
    onChange
}) => {
    const updateWeight = useCallback(
        (weight: number, index: number) => {
            const newConstituents = [...constituents];
            newConstituents[index].Weight = weight;
            onChange(newConstituents);
        },
        [JSON.stringify(constituents)]
    );

    const updatePrice = useCallback(
        (price: number, index: number) => {
            const newConstituents = [...constituents];
            newConstituents[index].BasePrice = price;
            onChange(newConstituents);
        },
        [JSON.stringify(constituents)]
    );

    const addTicker = useCallback(
        (ticker: string) => {
            const isAlreadyIncluded = constituents.some(c => c.BloombergTicker === ticker);
            if (!isAlreadyIncluded) {
                const newConstituents = [
                    ...constituents,
                    { BloombergTicker: ticker, Weight: 1, BasePrice: null }
                ];
                onChange(newConstituents);
            }
        },
        [JSON.stringify(constituents)]
    );
    return (
        <DisplayGrid style={{ gridTemplateRows: "20px minmax(150px, max-content)", gap: 5 }}>
            {title}
            <MemberTable style={{ tableLayout: "fixed", height: "95%" }}>
                <tbody>
                    <MemberTableHeader>
                        <MemberTableCell colSpan={3}>
                            <TickerSelecor onTickerSelected={addTicker} />
                        </MemberTableCell>
                    </MemberTableHeader>
                    <MemberTableHeader>
                        <LeftCell>Ticker</LeftCell>
                        <CenterCell style={{ textAlign: "center" }}>Weight</CenterCell>
                        <CenterCell>Price</CenterCell>
                    </MemberTableHeader>
                    {constituents.length < 1 ? (
                        <MemberTableRow />
                    ) : (
                        constituents.map((member, i) => (
                            <MemberTableRow
                                key={member.BloombergTicker}
                                style={{ verticalAlign: "top" }}
                            >
                                <LeftCell style={{ fontWeight: 500 }}>
                                    {!!member.Weight ? (
                                        <Ticker bbgTicker={member.BloombergTicker} />
                                    ) : (
                                        <TickerToRemove ticker={member.BloombergTicker} />
                                    )}
                                </LeftCell>
                                <CenterCell>
                                    {member.Weight !== undefined && (
                                        <InputNumber
                                            style={{
                                                width: 50,
                                                backgroundColor: "#131415",
                                                border: "1px solid #3b3c40",
                                                borderTop: "1.02px solid #3b3c40"
                                            }}
                                            min={0}
                                            size={"small"}
                                            defaultValue={member.Weight}
                                            onChange={val => updateWeight(Number(val), i)}
                                        />
                                    )}
                                </CenterCell>
                                <RightCell>
                                    <StyledNumber
                                        style={{ width: "100%", height: 25 }}
                                        type={"number"}
                                        onChange={({ target: { value } }) =>
                                            updatePrice(Number(value), i)
                                        }
                                        size={"small"}
                                        defaultValue={member.BasePrice}
                                    />
                                </RightCell>
                            </MemberTableRow>
                        ))
                    )}
                </tbody>
            </MemberTable>
        </DisplayGrid>
    );
};
