import React from "react";
import { DataLoader } from "@reddeer/raid-theme";
import { useAnalysisBlockData } from "../../api";
import { LineSeriesData, LineChart } from "../LineChart";
import { datePercentageLineChartDefinition } from "../ChartComponents";

type PortfolioWeightingComparisonData = {
    Date: string;
    EquallyWeighted: number;
    MktCapWeighted: number;
    Original: number;
};

const mapData = (data: PortfolioWeightingComparisonData[]) => {
    data = data || [];
    const xDataPoints = [];
    const ewpData: LineSeriesData[] = [];
    const mcwpData: LineSeriesData[] = [];
    const opData: LineSeriesData[] = [];

    for (let i = 0; i < data.length; i++) {
        const datum = data[i];
        xDataPoints.push({ Date: datum.Date });
        ewpData.push({ index: i, value: datum.EquallyWeighted });
        mcwpData.push({ index: i, value: datum.MktCapWeighted });
        opData.push({ index: i, value: datum.Original });
    }

    return {
        xDataPoints,
        ewpData,
        mcwpData,
        opData
    };
};

const requestType = "Firefly.Service.Calculator.TradingEnhancement.Contracts.TraderReview.PortfolioWeightingComparison.PortfolioWeightingComparisonRequest, Firefly.Service.Calculator.TradingEnhancement.Contracts";

const portfolioWeightingConstructionSeriesDefinition = [
    { label: "Equally Weighted Portfolio", color: "#0A98C3" },
    { label: "Market Cap Weighted Portfolio", color: "#C40AB9" },
    { label: "Original Portfolio", color: "#F5A623" }
];

export const PortfolioWeightingConstruction = (filterProps) => {
    const [data, requestState] = useAnalysisBlockData<any>({requestType, filterProps, responseMapper: data => data.Data});

    const { xDataPoints, ewpData, mcwpData, opData } = mapData(data);
    const series = [
        {...portfolioWeightingConstructionSeriesDefinition[0], data: ewpData },
        {...portfolioWeightingConstructionSeriesDefinition[1], data: mcwpData },
        {...portfolioWeightingConstructionSeriesDefinition[2], data: opData },
    ]

    return (
        <DataLoader {...requestState}>
            <LineChart definition={datePercentageLineChartDefinition} series={series} xDataPoints={xDataPoints} />
        </DataLoader>
    );
}