import { formatNumber, TableDefinition, numberFormats, formatDollarValue, ChangedValue } from "@reddeer/firefly-shared-controls"
import React from "react"

export type SectorRelativePLData = {
    sectorName: string,
    sectorRelativePL: number,

}

export const sectorRelativePLTableDefinition: TableDefinition<SectorRelativePLData> = {
    columns: [
        { header: "Sector Name", getCellValue: d => `${d.sectorName}`},
        { header: "Sector Relative PL", getCellValue: d => <ChangedValue value={d.sectorRelativePL}     formatType="dollar1dp"/> },
    ],
    getRowKey: (d, i) => `${i}`
}