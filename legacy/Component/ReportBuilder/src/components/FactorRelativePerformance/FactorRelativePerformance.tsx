// import React from "react";
// import { FixedRowTable, FixedRowTableDefinition, formatNumber, numberFormats, ChangedValue, withCommandStateDepreciated as withCommandState, TableDefinition, } from "@reddeer/firefly-shared-controls";
// import { TrafficLightContainer } from "../TrafficLightData/TrafficLightData";
// import { printableTable } from "./Styles.scss";
// import { createConfig, subscriptionCommandSpec, generateRequestBody } from "../../types";

// export type FactorRelativePerformanceData = {
//     positionHitRate:number;
//     averagePL:number;
//     maximumPL:number;
//     minimumPL:number;
//     averageReturnAllocation:number;
//     maximumReturnAllocation:number;
//     minimumReturnAllocation:number;
//     averageDaysHeld:number;
//     maximumDaysHeld:number;
//     averageNumberOfTrades:number;
//     maximumNumberOfTrades:number;
//     averageDaysBetweenTrades:number;
// }

// export const sectorRelativePLTableDefinition: TableDefinition<FactorRelativePerformanceData> = {
//     columns: [
//         { header: "Sector Name", getCellValue: d => `${d.sectorName}`},
//         { header: "Sector Relative PL", getCellValue: d => <ChangedValue value={d.sectorRelativePL}     formatType="dollar1dp"/> },
//     ],
//     getRowKey: (d, i) => `${i}`
// }









// export const FactorRelativePerformanceTable = ({ FactorRelativePerformanceData }: { FactorRelativePerformanceData: FactorRelativePerformanceData[] }) => {
//     return (
//         <div>
//             <FixedRowTable data={FactorRelativePerformanceData} definition={FactorRelativePerformanceTableDefinition} classNames={[printableTable]} />
//         </div>
//     )
// };

// export const BoundFactorRelativePerformanceTable = withCommandState(FactorRelativePerformanceTable,
//     createConfig({
//         FactorRelativePerformanceData: subscriptionCommandSpec("FactorRelativePerformanceRequest_FactorRelativePerformanceResponse_Subscription_Handler", ({ traderId, lookback,fundId, fundGroupID }) => generateRequestBody(traderId, lookback,fundId, fundGroupID))
//     }));

// export const FactorRelativePerformanceContainer = (entitySelectProps: any) => {
//     return (
//         <TrafficLightContainer title="Trade Level Summary">
//             <div>
//                 <BoundFactorRelativePerformanceTable {...entitySelectProps} />
//             </div>
//         </TrafficLightContainer>
//     )
// }