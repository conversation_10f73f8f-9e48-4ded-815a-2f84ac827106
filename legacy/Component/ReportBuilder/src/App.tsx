import React from "react";
import { TraderReviewReport } from "./TraderReviewReport";
import { SubscriptionClient, ServerConnection } from "@tradinglabs/viewserver-core";
import { configureStore } from "./configurestore";
import { Provider } from "react-redux";
import { ConnectionProviderFactoryDepreciated as ConnectionProviderFactory, InsightsFilters, InsightsFiltersProvider } from "@reddeer/firefly-shared-controls";
import styled from "styled-components";
import { ReportViewer } from "./ReportViewer";
import { AttributionReport } from "./AttributionReport";

const token = "";
export const EXISTENT_END_POINT = process.env.NODE_ENV === "production" ? "" :  "http://labsffbackbone2.mbam.local:5094"; 

const createStore = () => {
    const connection = new ServerConnection((window as any).RP_CONNECTION_NAME, `${EXISTENT_END_POINT}/viewserver`, {
        accessTokenFactory: () => Promise.resolve(token)
    });
    const client = new SubscriptionClient(connection, undefined);
    return configureStore(connection, client);
}; 

//@ts-ignore
const ConnectionProvider = ConnectionProviderFactory(window.RP_CONNECTION_NAME);

const store = createStore();
export const ReportBuilder = () => {
    return (
        <Provider store={store}>
            <ConnectionProvider>
                <InsightsFilters />
                <TraderReviewReport />
            </ConnectionProvider>
        </Provider>
    );
};

export const ReportBuilderApp = () => {
    return (
        <InsightsFiltersProvider>
            <ReportBuilder />
        </InsightsFiltersProvider>
    );
}

const AppContainer = styled.div`
    display: grid;
    width: 100%;
    height: 100%;
    padding: 20px;
    justify-content: center;
    grid-template-rows: min-content 1fr;
    grid-template-columns: 1fr;
    gap: 22px;

    @media print {
        visibility: hidden;
        padding: 0;
        justify-content: unset;
    }
`;

export const ReportViewerApp = () => {
    return (
        <InsightsFiltersProvider>
            <AppContainer>
                <InsightsFilters />
                <ReportViewer>
                    <AttributionReport />
                </ReportViewer>
            </AppContainer>
        </InsightsFiltersProvider>
    );
};