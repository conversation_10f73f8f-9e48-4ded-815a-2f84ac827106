import React from "react";
import { Tooltip } from "antd";

import { CatalystEventViewModel, CatalystEventType } from "@tradinglabs/orderform-api";
import { OrderFormCatalystSquare } from "Charting/index";

type CatalystEventProps = {
    catalystEvent: CatalystEventViewModel;
};
export const CatalystEvent = ({ catalystEvent }: CatalystEventProps) => {
    const letter = CatalystEventType[catalystEvent.Type]
        ? CatalystEventType[catalystEvent.Type].charAt(0)
        : "O";

    return (
        <Tooltip
            title={catalystEvent.Details}
            placement="bottom"
        >
            <div className="flex items-center gap-sm">
                <OrderFormCatalystSquare
                    colorKey={catalystEvent.Type.toString()}
                    isLetter
                >
                    {letter}
                </OrderFormCatalystSquare>
                <div>{catalystEvent.Title}</div>
            </div>
        </Tooltip>
    );
};
