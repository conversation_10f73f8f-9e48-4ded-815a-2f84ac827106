import React from "react";
import Flip<PERSON>ove from "react-flip-move";
import { LimitSelect, WorkingType, Compliance, AutoRouting, Send, Ems, ExpiryType } from ".";
import { useOrderForm } from "../../OrderFormProvider";
import { orderFormSection } from "../OrderForm.scss";

export const BottomSection = () => {
    const { orderForm } = useOrderForm();
    const show = !orderForm.Size.SizeDisabled && !!orderForm.Size.Quantity && !!orderForm.Strategy.StrategyName;


    return (
        <FlipMove enterAnimation="fade" leaveAnimation="fade">
            {
                !show ? null :
                <div className={orderFormSection}>
                    <LimitSelect />
                    <AutoRouting />
                    <WorkingType />
                    <Ems />
                    <ExpiryType />
                    <Compliance compliance={orderForm.Compliance} recommendations={orderForm.Recommendations} />
                    <Send />
                </div>
            }
        </FlipMove>
    );
};