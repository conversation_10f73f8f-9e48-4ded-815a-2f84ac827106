import React, { useCallback } from "react";
import { Select } from "antd";
import { css } from "@reddeer/firefly-shared-controls";
import { readOnlyInput } from "../OrderForm.scss";
import { PadLock } from "../../../icons";
import styled from "styled-components";
import { TraderViewModel } from "@tradinglabs/orderform-api";

const StyledSelect = styled(Select)`
    .ant-select-selection {
        background-color: var(--input__background-color) !important;
    }
`;
type Props = {
    onChange: (value: number) => void;
    readOnly: boolean;
    trader: TraderViewModel;
};

export const TraderSelect = ({ onChange, readOnly, trader }: Props) => {
    const { Options = [], ShowTraderOptions, TraderReadOnly } = trader;
    const isReadOnly = TraderReadOnly || readOnly;

    if (!ShowTraderOptions) {
        return null;
    }

    const selectedTrader = Options.find(t => t.Selected);

    const filterOption = useCallback((search: string, option: React.ReactElement) => {
        const traderName = option.props.children as string;
        return traderName.toLowerCase().startsWith(search.toLowerCase().trim());
    }, []);

    return (
        <div className={css({ [readOnlyInput]: isReadOnly })}>
            <StyledSelect
                placeholder="Select Trader"
                aria-label="TRADER"
                showSearch
                value={selectedTrader ? selectedTrader.Id : undefined}
                onChange={onChange}
                disabled={isReadOnly}
                filterOption={filterOption}
                id="trader-select"
                tabIndex={1}
                dropdownClassName="trader-select-list"
            >
                {Options.map(t => (
                    <Select.Option
                        key={t.Id}
                        value={t.Id}
                    >
                        {t.Name}
                    </Select.Option>
                ))}
            </StyledSelect>
            {isReadOnly && <PadLock />}
        </div>
    );
};
