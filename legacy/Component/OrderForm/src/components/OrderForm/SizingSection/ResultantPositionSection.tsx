import React, { useState, useCallback } from "react";
import { useOrderForm } from "../../OrderFormProvider";
import { css, DecimalInput } from "@reddeer/firefly-shared-controls";
import { getTooltipContainer } from "@reddeer/raid-theme";
import styled, { createGlobalStyle } from "styled-components";
import { PopoutIcon } from "../../../icons";
import { Tooltip } from "antd";
import { GridLayoutPanel } from "@reddeer/firefly-common-components";
import { twoColumnGrid } from "@reddeer/firefly-shared-controls/build/Common/Styles/Charts.scss";
import { alternateOrderFormSection } from "../OrderForm.scss";

enum LABELS {
    Position = "RES POSITION ($)",
    Swing = "RES SWING ($)",
    Quantity = "SHARES",
    DollarValue = "$ VALUE",
    AdjSwing = "ADJ SWING",
    XADV = "xADV",
    BPS = "BPS (Alloc)"
}


const GridTemplate = styled.div<{cols: string}>`
    display: grid;
    grid-gap: 8px;
    grid-template-columns: ${({cols}) => cols};
`;
const PopoutButton = styled.button<{isSelected: 1 | 0}>`
    height: 28px;
    width: 28px;
    padding: 4px 6px 0px 5px;
    color: #B1B1C4;
    border: 1px solid ${({isSelected}) => isSelected ? "#0d566f" : "#3b3c40"};
    border-radius: 2px;
    background-color: ${({isSelected}) => isSelected ? "#13455D" : "transparent"};
    transition: all 0.2s;
    cursor: pointer;
    &:active,
    &:focus {
        outline: none;
    }
`;

export const ResultantPositionSection = () => {
    const { orderForm } = useOrderForm();
    const { ResultantPosition } = orderForm;
    const [ isPopoutVisible, setIsPopoutVisible ] = useState<boolean>(false);
    const togglePopoutVisibilty = useCallback(() => {
        setIsPopoutVisible(!isPopoutVisible);
    }, [isPopoutVisible]);

    return <>
            <DecimalInput readOnly label={LABELS.Position} value={ResultantPosition.DollarValue} />
            <GridTemplate cols={"3fr 1fr"}>
                <DecimalInput readOnly label={LABELS.Swing} value={ResultantPosition.AdjustedSwing} />
                <Tooltip 
                    getPopupContainer={getTooltipContainer}
                    placement={"right"}
                    visible={isPopoutVisible}
                    title={
                        <GridLayoutPanel style={{width: 300}} title="Resultant Position" isDraggable={false} onClose={togglePopoutVisibilty} >
                            <div style={{padding: 10}} className={css(twoColumnGrid, alternateOrderFormSection)}>
                            
                            <DecimalInput readOnly label={LABELS.Quantity} value={ResultantPosition.Quantity} />
                            <DecimalInput readOnly label={LABELS.DollarValue} value={ResultantPosition.DollarValue} />
                            <DecimalInput readOnly label={LABELS.AdjSwing} value={ResultantPosition.AdjustedSwing} />
                            <GridTemplate cols={"1fr 1fr"}>
                                <DecimalInput readOnly label={LABELS.XADV} value={ResultantPosition.MultipleADV} />
                                <DecimalInput readOnly label={LABELS.BPS} value={ResultantPosition.BasisPoints} />
                            </GridTemplate>

                            </div>
                        </GridLayoutPanel>  
                    }>
                        <PopoutButton
                            isSelected={isPopoutVisible ? 1 : 0}
                            onClick={togglePopoutVisibilty}
                            ><PopoutIcon />
                        </PopoutButton>
                </Tooltip>
            </GridTemplate>
        </>
};