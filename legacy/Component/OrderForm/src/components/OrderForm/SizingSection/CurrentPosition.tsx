import React, { useCallback } from "react";
import { useOrderForm } from "../../OrderFormProvider";
import { DecimalInput, RadioInput, css } from "@reddeer/firefly-shared-controls";
import { twoColumnGrid } from "@reddeer/firefly-shared-controls/build/Common/Styles/Charts.scss";
import { UpdateSizeCommand, SizingType } from "@tradinglabs/orderform-api";
import { WorkingOrderIncludeType } from "./WorkingOrderIncludeType";
import { radioOptions, radioOptionsDisabled, sizingControl } from "../OrderForm.scss";

export const CurrentPosition = () => {
    const { orderForm, updateOrderForm } = useOrderForm();
    const { ShowPosition, PositionDivisorOptions, Position, IsReadOnly } = orderForm.Size;

    const onChange = useCallback(
        (id: number) => {
            const command = new UpdateSizeCommand();
            command.SizingType = SizingType.PositionDivisor;
            command.SizingParameter = id;
            updateOrderForm(command);
        },
        [orderForm]
    );

    if (!ShowPosition) {
        return null;
    }

    const selectedOption = PositionDivisorOptions.find(p => p.Selected);
    const items = PositionDivisorOptions.map(p => ({
        value: p.Id,
        label: p.Name,
        selected: p.Selected
    }));
    const readOnly = orderForm.ReadOnly || IsReadOnly;

    return (
        <>
            <div>
                <WorkingOrderIncludeType />
            </div>
            <div className={twoColumnGrid}>
                <DecimalInput
                    value={Position}
                    readOnly
                    label="CURRENT POSITION"
                />
                <RadioInput
                    classNames={[
                        css(radioOptions, readOnly && radioOptionsDisabled, sizingControl)
                    ]}
                    disabled={readOnly}
                    items={items}
                    value={selectedOption ? selectedOption.Id : undefined}
                    onChange={onChange}
                />
            </div>
        </>
    );
};
