import React from "react";
import Flip<PERSON>ove from "react-flip-move";
import { orderFormSection } from "../OrderForm.scss";
import { BookingOrderSize, BorrowOptions } from ".";
import { useBookingForm } from "../../BookingFormProvider";
import type { ClickParam } from "antd/lib/menu";
import {
    RequestBookingAutoBorrowCommand,
    RequestBookingManualBorrowCommand
} from "@tradinglabs/orderform-api";

export const BookingSizingSection = () => {
    const { bookingForm, updateBookingForm } = useBookingForm();
    const disabled = !bookingForm.BuySell.Options.some(o => o.Selected);

    const onManualClick = () => {
        const manualCommand = new RequestBookingManualBorrowCommand();
        updateBookingForm(manualCommand);
    };

    const handleSelectBroker = (param: ClickParam) => {
        const requestAutoBorrowCommand = new RequestBookingAutoBorrowCommand();
        requestAutoBorrowCommand.BorrowBrokerTypeId = Number(param.key);
        updateBookingForm(requestAutoBorrowCommand);
    };
    const handleAutomaticModeClick = () => {
        const requestAutoBorrowCommand = new RequestBookingAutoBorrowCommand();
        updateBookingForm(requestAutoBorrowCommand);
    };

    return (
        <FlipMove
            enterAnimation="fade"
            leaveAnimation="fade"
        >
            {disabled ? null : (
                <div className={orderFormSection}>
                    <BorrowOptions
                        onManualClick={onManualClick}
                        handleAutomaticModeClick={handleAutomaticModeClick}
                        handleSelectBroker={handleSelectBroker}
                        borrow={bookingForm.Borrow}
                    />
                </div>
            )}
        </FlipMove>
    );
};
