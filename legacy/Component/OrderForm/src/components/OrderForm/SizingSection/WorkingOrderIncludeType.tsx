import React, { useCallback } from "react";
import { useOrderForm } from "../../OrderFormProvider";
import { UpdateIncludeWorkingOrdersCommand } from "@tradinglabs/orderform-api";
import { RadioInput, css } from "@reddeer/firefly-shared-controls";
import { radioOptions, radioOptionsDisabled, workingOrderTypeControl } from "../OrderForm.scss";

export const WorkingOrderIncludeType = () => {
    const { orderForm, updateOrderForm } = useOrderForm();

    const { ShowPosition, WorkingOrderIncludeTypeOptions, IsReadOnly } = orderForm.Size;

    const onChange = useCallback(
        (id: number) => {
            const command = new UpdateIncludeWorkingOrdersCommand();
            command.IncludeType = id;
            updateOrderForm(command);
        },
        [orderForm]
    );

    const selectedOption = WorkingOrderIncludeTypeOptions.find(p => p.Selected);
    const items = WorkingOrderIncludeTypeOptions.map(p => ({
        value: p.Id,
        label: p.Name,
        selected: p.Selected
    }));

    const readOnly = orderForm.ReadOnly || IsReadOnly;

    return (
        <>
            {ShowPosition && (
                <RadioInput
                    classNames={[
                        css(radioOptions, readOnly && radioOptionsDisabled, workingOrderTypeControl)
                    ]}
                    disabled={readOnly}
                    items={items}
                    value={selectedOption ? selectedOption.Id : undefined}
                    onChange={onChange}
                />
            )}
        </>
    );
};
