import React from "react";
import { <PERSON><PERSON>, <PERSON>u, Dropdown, I<PERSON> } from "antd";
import { <PERSON>rrowLocates } from "./BorrowLocates";
import { BorrowStatus } from "./BorrowStatus";
import { Collapse } from "antd";
import type { ClickParam } from "antd/lib/menu";
const { Panel } = Collapse;
import * as styles from "./Styles.scss";
import { BorrowViewModel, StatusSeverity } from "@tradinglabs/orderform-api";

type Props = {
    onManualClick: () => void;
    handleSelectBroker: (param: ClickParam) => void;
    handleAutomaticModeClick: () => void;
    borrow: BorrowViewModel;
};

export const BorrowOptions = (props: Props) => {
    const { AutomaticBrokerOptions, Locates, ShowBorrow, Alert } = props.borrow;
    const { onManualClick, handleSelectBroker, handleAutomaticModeClick } = props;

    const showManualBorrow = Array.isArray(AutomaticBrokerOptions);

    const isBorrowRequired = () => {
        return Alert && Alert.Severity === StatusSeverity.Stop;
    };

    return (
        <>
            {ShowBorrow && isBorrowRequired() && (
                <div className={styles.borrowOptionsContainer}>
                    <div className={styles.tableGroup}>
                        <BorrowStatus alert={Alert} />
                    </div>
                    <div
                        className={styles.buttonGroup}
                        style={{ gridColumn: !showManualBorrow ? "1 / 3" : undefined }}
                    >
                        {!showManualBorrow ? (
                            <Button
                                id="auto-borrow-btn"
                                className={styles.manualButton}
                                onClick={handleAutomaticModeClick}
                            >
                                Locate
                            </Button>
                        ) : (
                            <Dropdown.Button
                                {...{ id: "auto-borrow-btn" }}
                                className={styles.automaticButton}
                                onClick={handleAutomaticModeClick}
                                icon={<Icon type="down" />}
                                overlay={
                                    <Menu
                                        onClick={handleSelectBroker}
                                        id="auto-borrow-broker-select-options"
                                    >
                                        {AutomaticBrokerOptions.map(t => (
                                            <Menu.Item key={t.Id}>{t.Name}</Menu.Item>
                                        ))}
                                    </Menu>
                                }
                            >
                                Automatic
                            </Dropdown.Button>
                        )}
                    </div>
                    {!showManualBorrow ? null : (
                        <div className={styles.buttonGroup}>
                            <Button
                                id="manual-borrow-btn"
                                className={styles.manualButton}
                                onClick={onManualClick}
                            >
                                Manual
                            </Button>
                        </div>
                    )}
                    {Locates.length > 0 && (
                        <div className={styles.tableGroup}>
                            <BorrowLocates locates={Locates} />
                        </div>
                    )}
                </div>
            )}
            {ShowBorrow && !isBorrowRequired() && (
                <div className={styles.borrowCollapse}>
                    <Collapse
                        bordered={false}
                        expandIconPosition="right"
                    >
                        <Panel
                            header={<BorrowStatus alert={Alert} />}
                            key="1"
                        >
                            <div className={styles.borrowOptionsContainer}>
                                {Locates.length > 0 && (
                                    <div className={styles.tableGroup}>
                                        <BorrowLocates locates={Locates} />
                                    </div>
                                )}
                            </div>
                        </Panel>
                    </Collapse>
                </div>
            )}
        </>
    );
};
