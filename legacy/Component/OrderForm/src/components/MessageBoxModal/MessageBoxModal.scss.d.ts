declare namespace MessageBoxModalScssNamespace {
  export interface IMessageBoxModalScss {
    autoCloseMessage: string;
    error: string;
    message: string;
    messageBoxBody: string;
    messageBoxButton: string;
    messageBoxFooter: string;
    messageBoxModal: string;
    messageBoxModalContent: string;
    messageBoxTitle: string;
    warning: string;
  }
}

declare const MessageBoxModalScssModule: MessageBoxModalScssNamespace.IMessageBoxModalScss & {
  /** WARNING: Only available when `css-loader` is used without `style-loader` or `mini-css-extract-plugin` */
  locals: MessageBoxModalScssNamespace.IMessageBoxModalScss;
};

export = MessageBoxModalScssModule;
