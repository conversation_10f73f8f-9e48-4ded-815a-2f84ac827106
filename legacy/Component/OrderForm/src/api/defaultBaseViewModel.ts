import { StatusSeverity, OrderFormCheckStatus } from "@tradinglabs/orderform-api";
import { BaseEntityViewModel } from "../components/OrderForm/types";

export const defaultBaseViewModel: BaseEntityViewModel = {
    $type: undefined,
    Alerts: { $type: "", Alerts: [], ShowAlertsPanel: false, StatusSeverity: StatusSeverity.None },
    Borrow: { $type: "", ShowBorrow: false, AutomaticBrokerOptions: [], Locates: [], Alert: null },
    BuySell: { $type: "", Options: [], BuySellReadOnly: false, IsSell: false },
    Compliance: {
        $type: "",
        CheckMessage: "",
        CheckResultSeverity: StatusSeverity.None,
        Status: OrderFormCheckStatus.NotRequested,
    },
    Instrument: {
        $type: "",
        Currency: "",
        InstrumentName: "",
        BBGTicker: "",
        InstrumentReadOnly: false,
        InstrumentOptions: [],
        InstrumentNotFound: false,
    },
    InstrumentDetail: {
        $type: "",
        AverageValueTraded: "",
        DailyNChange: "",
        ExpectedVolumeXAdv: "",
        MarketCap: "",
        PercentChangeToday: "",
        ShowInstrumentDetail: false,
        ValueTradedToday: "",
    },
    Strategy: {
        $type: "",
        StrategyName: "",
        SubStrategyName: "",
        StrategyIdentifier: "",
        LongStrategyOptions: [],
        ShortStrategyOptions: [],
        SubStrategyOptions: [],
        StrategyDisabled: false,
        StrategyReadOnly: false,
        UseNewStrategyStructure: false,
        ShowPositionTypeOptions: false,
        PositionTypeOptions: [],
    },
    Trader: {
        $type: "",
        Options: [],
        ShowTraderOptions: false,
        TraderName: "",
        TraderReadOnly: false,
    },
    Working: {
        $type: "",
        Options: [],
        Message: "",
        DealerNotes: "",
        ShowMessage: false,
        ShowOptions: false,
        ShowPanel: true,
        IsReadOnly: false,
    },
    Recommendations: {
        $type: "",
        CheckMessage: "",
        CheckResultSeverity: StatusSeverity.None,
        Status: OrderFormCheckStatus.NotRequested,
    },
    Breaches: { $type: "", Breaches: [] },
    Send: {
        $type: "",
        SendButtonDisabled: false,
        SendButtonText: "",
        SendButtonStatus: "",
        IdeaButtonText: "",
        ShowIdeaButton: false,
    },
};
