import React from "react";
import { withCommandStateDepreciated as withCommandState } from "@reddeer/firefly-shared-controls";
import { RequestMapperProps, createConfig, SubscriptionCommands, subscriptionCommandSpec, requestMapper, PLAndSizeApiModel } from "../../model";
import { ChartWithDonut } from "./ChartWithDonut";

type Props = RequestMapperProps & { data: PLAndSizeApiModel[]};

const PLAndSizeChart = (props: Props) => {
    return <ChartWithDonut<PLAndSizeApiModel>
        {...props}
        title="Size"
        chartTitle="Position PnL v Average Gross Exposure"
        donutTitle="Correlation Between Size and Performance"
        xAxisLabel={"Exposure ($)"}
        yAxisLabel={"PnL ($)"}
        xAxisField={"size"}
        yAxisField={"finalPL"}
    />
};

export const ScopedPLAndSizeChart = withCommandState(PLAndSizeChart, createConfig({data: subscriptionCommandSpec(SubscriptionCommands.PLAndSizeData, requestMapper)}));
