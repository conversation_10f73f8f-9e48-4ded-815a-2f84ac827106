import React from "react";
import { Widget, FlexRow, FlexItem } from "@reddeer/firefly-shared-controls/build/Common/Components";
import { RequestMapperProps } from "../../model";
import { HistoricTurnover } from "./HistoricTurnover";
import { TurnoverPerformance } from "./TurnoverPerformance";
import styled from "styled-components";

const Row = styled.div`display: flex; flex-flow: row;`;

type Props = RequestMapperProps;

export const ActivitySection = (props: Props) =>
    <Widget title={"Activity"}>
        <FlexRow>
            <FlexItem>
                <HistoricTurnover {...props} />
            </FlexItem>
            <FlexItem>
                <TurnoverPerformance {...props} />
            </FlexItem>
        </FlexRow>
    </Widget>;
