import React from "react";
import { withCommandStateDepreciated as withCommandState } from "@reddeer/firefly-shared-controls";
import { MultiBarChart, MultiBarChartDefinition } from "@reddeer/firefly-shared-controls/build/Common/Components";
import { formatNumber, numberFormats } from "@reddeer/firefly-shared-controls/build/Common/Utilities";
import { RequestMapperProps, createConfig, SubscriptionCommands, subscriptionCommandSpec, requestMapper } from "../../model";
import styled from "styled-components";
//need to move these to "theme"
const textColorDefault = "#FFFFFF";
const textColorPrint = "#000000";
const headingSizeDefault = "14px";

const Header = styled.div`
    color: ${textColorDefault};
    font-size: ${headingSizeDefault};
    font-weight: 400;
    margin-bottom: 10px;
    margin-top: 10px;

    @media print {
        color: ${textColorPrint};
    }
`;

type Props = RequestMapperProps;

const historicTurnoverChartDefinition: MultiBarChartDefinition = {
    showNthTicks: 4,
    series: {
        thisMonth: {
            label: "This Month",
            fillColor: "#C409B9"
        },
        lastMonth: {
            label: "Last Month",
            fillColor: "#0A98C3",
        }
    },
    tooltipFormatter: x => formatNumber(x, numberFormats.dollar0dp),
};

export const HistoricTurnover = (props: Props) =>
    <>
        <Header>Historical Daily Net Positioning</Header>
        <HistoricTurnoverMultiBarChart {...props} height={400} width={1000} definition={historicTurnoverChartDefinition} />;
    </>

const mapHistoricTurnoverResponse = (data: {date: string, turnover: number}[]) => {
    const chartData = (data || []).map(d => ({
        date: new Date(d.date.substring(0, 10)),
        value: d.turnover
    }));
    return { thisMonth: chartData };
}

const HistoricTurnoverMultiBarChart = withCommandState(MultiBarChart, createConfig({data: subscriptionCommandSpec(SubscriptionCommands.HistoricTurnover, requestMapper, mapHistoricTurnoverResponse)}));
