 ## Boilerplate to create a new Client app - Portfolio Monitor
- Portfolio Monitor will be used as a widget in RAID
- This new package has been added to `TradingEnhancement` component as a dependency 
- [See the design here](https://projects.invisionapp.com/d/main#/projects/prototypes/18485743).

#### You can run this in dev mode locally as stand-alone app
 - go to `Component\PortfolioMonitor` folder and run `yarn start` and then navigate to http://localhost:8080
