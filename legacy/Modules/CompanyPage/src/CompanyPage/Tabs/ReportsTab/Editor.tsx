import React, { ReactNode, useRef } from "react";
import styled from "styled-components";

type EditorProps = {
    title?: ReactNode;
    value?: string;
    placeholder?: string;
    disabled?: boolean;
    className?: string;
};

export const Editor = ({ title, value, disabled = false, placeholder, className }: EditorProps) => {
    const input = useRef<HTMLDivElement>();

    const handleClickLabel = () => input.current?.focus();

    return (
        <Container className={className}>
            <label onClick={handleClickLabel}>{title}</label>

            <StyledInput
                ref={input}
                role="textbox"
                placeholder={placeholder}
                contentEditable={!disabled}
                dangerouslySetInnerHTML={{ __html: value }}
            />
        </Container>
    );
};

const Container = styled.div`
    display: flex;
    flex-direction: column;
    flex-grow: 1;

    > label {
        color: #06c6dd;
        font-weight: bold;
        margin-bottom: 0.25rem;
        font-size: inherit;
    }
`;

const StyledInput = styled.div`
    outline: none;
    line-height: 1.25;
    overflow-x: hidden;
    overflow-y: auto;
    flex-grow: 1;
    word-break: break-word;

    > p,
    > div {
        padding: 0 0 0.25rem 0;
    }

    &:focus {
        background-color: rgba(255, 255, 255, 0.1);
    }

    &:empty:before {
        content: attr(placeholder);
        opacity: 0.2;
    }
`;
