import React from "react";
import styled from "styled-components";
import classnames from "classnames";

import { Editor } from "../Editor";

type HeaderProps = {
    inline?: boolean;
    className?: string;
};

export const Header = ({ inline = true, className }: HeaderProps) => {
    return (
        <Container>
            <TextInput style={{ marginBottom: "0.5rem" }}>
                <span>Idea Source</span>
                <input type="text" />
            </TextInput>

            <Editors className={classnames({ inline }, className)}>
                <Editor
                    title="Pros"
                    placeholder="n/a"
                    className="editor"
                />

                <Editor
                    title="Cons"
                    placeholder="n/a"
                    className="editor"
                />
            </Editors>
        </Container>
    );
};

const TextInput = styled.label`
    display: flex;
    align-items: baseline;
    font-size: inherit;

    > span {
        color: #06c6dd;
        font-weight: bold;
        white-space: nowrap;
        margin-right: 0.5rem;
    }

    input[type="text"] {
        background-color: inherit;
        font-size: inherit;
        color: inherit;
        border: 0;
        outline: none;
        flex-grow: 1;

        &:focus {
            background-color: rgba(255, 255, 255, 0.1);
        }
    }
`;

const Container = styled.header`
    && {
        display: flex;
        flex-grow: 1;
        flex-direction: column;
        overflow: hidden;
    }
`;

const Editors = styled.div`
    flex-grow: 1;

    && {
        overflow: hidden;

        &:not(.inline) {
            .editor {
                margin-bottom: 2rem;

                &:last-of-type {
                    margin-bottom: 0;
                }
            }
        }

        &.inline {
            display: flex;

            .editor {
                flex: 2;
                flex-shrink: 0;
                flex-grow: 1;
                padding-left: 0.5rem;
                margin-left: 0.5rem;
                border-left: 1px solid #3a414e;

                &:first-of-type {
                    margin-left: 0;
                    padding-left: 0;
                    border-left: 0;
                }

                &:last-of-type {
                    margin-right: 0;
                    padding-right: 0;
                }
            }
        }
    }
`;
