import React, { ComponentProps, useState } from "react";
import styled from "styled-components";

import { withLazyComponent, useStickyState } from "PrimitiveComponents/index";
import type { NewsConfig } from "ComponentLibrary/index";

const News = withLazyComponent(() => import("ComponentLibrary/index"), "News");

const StyledNews = styled(News)`
    height: 694px;
`;

type NewsTabProps = Omit<ComponentProps<typeof News>, "config" | "onUpdateConfig">;

const NewsForIdentifier = (props: NewsTabProps) => {
    const [onlyShowDaysWithNumbers, setOnlyShowDaysWithNumbers] = useState(false);
    const [config, setConfig] = useStickyState<NewsConfig>(
        {
            compactView: true,
            corrLimit: 0.7,
            dateFrame: 6,
            dateFrameBrokerNotes: 12,
            dateFrameEmail: 3,
            dateFrameNews: 6,
            forwardDateFrame: -2,
            fuzzEarningsDates: true,
            peersShown: true,
            relevance: 0.4,
            rowLayout: false,
            sections: ["broker", "news"],
            showCalendar: true,
            showHeader: false,
            showItemCounts: false,
            showNewsHover: false,
            canCollapse: true,
            canSort: true,
            canSearch: false
        },
        "newsConfig"
    );

    const activeConfig = {
        ...config,
        onlyShowDaysWithNumbers
    };

    const handleUpdateConfig = (
        updateConfig: NewsConfig | ((config: NewsConfig) => NewsConfig)
    ) => {
        const { onlyShowDaysWithNumbers, ...rest } =
            typeof updateConfig === "function" ? updateConfig(activeConfig) : updateConfig;
        setOnlyShowDaysWithNumbers(onlyShowDaysWithNumbers ?? false);
        setConfig(rest);
    };

    return (
        <StyledNews
            {...props}
            config={activeConfig}
            onUpdateConfig={handleUpdateConfig}
        />
    );
};

type Props = Omit<NewsTabProps, "identifier"> & {
    instrumentId: string;
};

export const NewsTab = ({ instrumentId, ...props }: Props) => (
    <NewsForIdentifier
        key={instrumentId}
        identifier={instrumentId}
        {...props}
    />
);
