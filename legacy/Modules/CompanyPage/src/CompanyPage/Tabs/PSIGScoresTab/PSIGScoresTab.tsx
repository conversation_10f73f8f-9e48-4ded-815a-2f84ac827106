import React, { useState } from "react";

import { FiltersProvider } from "Components/index";
import { Flex, LayoutGrid, Modal, withLazyComponent } from "PrimitiveComponents/index";
import { ConnectedComponentStatus } from "TransportGateway/index";
import type { ReportTemplateStandaloneBaseProps } from "ReportEngine/index";

import { SentimentAndPositioning } from "./SentimentAndPositioning";
import { ScoreTile, ConnectedScoreTile } from "./ScoreTile";
import { Checklist } from "./Checklist";

const ReportEngineAppStandalone = withLazyComponent(
    () => import("ReportEngine/index"),
    "ReportEngineAppStandalone"
);

type Props = {
    instrumentId?: string;
};

export const PSIGScoresTab = ({ instrumentId: bbgTicker }: Props) => {
    const [selectedReport, setSelectedReport] =
        useState<Partial<ReportTemplateStandaloneBaseProps<null>>>();

    return (
        <FiltersProvider defaultFilters={{ bbgTicker }}>
            <Flex
                flex="1"
                direction="column"
                gap="large"
            >
                <LayoutGrid
                    gap="large"
                    padding="xx-large"
                    gridTemplateColumns="1fr 1fr 1fr 1fr"
                >
                    <ConnectedScoreTile
                        reportKey="IAC_TechnicalScoring_SingleTicker"
                        title="Technical Score"
                        description="Signal:"
                        valueProperty="signal"
                        scoreDotProperty="hN_technical_score"
                        onSelect={setSelectedReport}
                    />
                    <ConnectedScoreTile
                        reportKey="IAC_SectorScoring_SingleTicker"
                        title="Sector Score"
                        description="AD Comments:"
                        valueProperty="sectorScreening_AD_Comments"
                        scoreDotProperty="andysector_scores"
                        onSelect={setSelectedReport}
                    />
                    <ConnectedScoreTile
                        reportKey="IAC_Sentiment"
                        title="Sentiment Score"
                        description="Performing As Expected?"
                        valueProperty="underperformingOrOutperformingSinceFigs"
                        scoreDotProperty="sentimentScore"
                        onSelect={setSelectedReport}
                    />
                    <ScoreTile
                        title="Earnings Score"
                        onSelect={setSelectedReport}
                    />
                </LayoutGrid>
                <SentimentAndPositioning />
                <Checklist onSelect={setSelectedReport} />

                <Modal
                    width={900}
                    visible={!!selectedReport}
                    onClose={() => setSelectedReport(null)}
                >
                    <Flex
                        minHeight={200}
                        flex="1"
                        padding="large"
                    >
                        <ConnectedComponentStatus progressBarOffset={-6}>
                            <ReportEngineAppStandalone
                                reportKey={selectedReport?.reportKey || ""}
                                templateName={selectedReport?.templateName || ""}
                                {...selectedReport}
                                config={{
                                    ParameterValues: {
                                        bbgTicker,
                                        ...(selectedReport?.config?.ParameterValues ?? {})
                                    }
                                }}
                            />
                        </ConnectedComponentStatus>
                    </Flex>
                </Modal>
            </Flex>
        </FiltersProvider>
    );
};
