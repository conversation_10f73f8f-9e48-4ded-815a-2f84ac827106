import React, { useState } from "react";
import moment from "moment";
import {
    useStickyState,
    LayoutContainer,
    LayoutHorizontal,
    DateFrameWithLabel,
    Radio
} from "PrimitiveComponents/index";
import { RegressionChart } from "ComponentLibrary/index";
import styled from "styled-components";
import { ConnectedTraderInput } from "../../Components/ExposureChart/ExposureChartContainer";

const RadioWrapper = styled.div`
    display: flex;

    align-items: center;

    &&&&& label.ant-radio-button-wrapper {
        min-width: inherit;
    }
`;

const dataMapper = (data = []) => {
    const unsorted = data.map(({ dateTime, absRsi: rsi, ...row }) => {
        const d = moment(dateTime, "YYYY-MM-DD").toDate();
        return { date: d, rsi, ...row };
    });
    return unsorted.sort((a, b) => a.date.valueOf() - b.date.valueOf());
};

const periodOptions = [1, 3, 6, 12, 24, 60];

const RadioButtonList = [
    {
        type: "priceView",
        options: [
            {
                label: "OHLC",
                value: "ohlc"
            },
            {
                label: "Line",
                value: "line"
            }
        ]
    }
];

export const OverviewChartContainer = ({
    instrumentId,
    traderId = undefined,
    width = 570,
    regressionChartHeight = 260
}) => {
    const [period, setPeriod] = useState(6);
    const [trader, setTrader] = useState(traderId ?? -1);
    const [priceView, setPriceView] = useStickyState("ohlc", "indexSummaryPriceView");

    return (
        <div style={{ width }}>
            <LayoutHorizontal>
                <div
                    style={{
                        color: "white",
                        minWidth: 80,
                        maxWidth: 120,
                        flexGrow: 1,
                        marginRight: 10
                    }}
                >
                    <ConnectedTraderInput
                        traderId={trader}
                        selectTrader={setTrader}
                        period={period}
                        reportKey="summaryTabindexChartHover"
                    />
                </div>
                <div>
                    {RadioButtonList.map(({ type, options }) => {
                        return (
                            <RadioWrapper key={type}>
                                <Radio
                                    options={options}
                                    value={priceView}
                                    onChange={setPriceView}
                                    size="small"
                                />
                            </RadioWrapper>
                        );
                    })}
                </div>
                <div style={{ marginLeft: "auto" }}>
                    <DateFrameWithLabel
                        value={period}
                        onChange={(val: number) => setPeriod(val)}
                        periods={periodOptions}
                    />
                </div>
            </LayoutHorizontal>
            <LayoutContainer height={regressionChartHeight}>
                <RegressionChart
                    drawRegression
                    headerFontSize={12}
                    isRelative
                    priceLabel=""
                    period={period}
                    chartHeight={regressionChartHeight}
                    identifier={instrumentId}
                    drawCandles
                    drawVolume={false}
                    reportName="RegressionChartRequestHandler_Stock"
                    reportKey="indexHover"
                    dataMapper={dataMapper}
                    drawExposure
                    drawRelativePrice
                    trader={trader}
                    priceView={priceView as "line" | "ohlc"}
                    rsiValueOnLabel
                    includeWorkingOrders
                />
            </LayoutContainer>
        </div>
    );
};
