import React, { ComponentType } from "react";
import { format } from "d3";
import styled from "styled-components";

import { LayoutVertical } from "PrimitiveComponents/index";
import { formatBigValue, isNumber } from "Utilities/index";

export const Container = styled.div`
     {
        display: flex;
        flex-direction: row;
        height: 100%;
        justify-content: flex-start;
    }
`;

export const Code = styled.div`
    display: flex;
    align-self: flex-end;
    margin-right: 10px;
    color: white;
    font-size: 18px;
    font-weight: bold;
`;

export const Name = styled.div`
    display: flex;
    align-self: flex-end;
    margin-right: 10px;
    color: white;
    font-size: 13px;
`;

export const Areas = styled.div`
    display: flex;
    align-self: flex-end;
    font-size: 12px;
    color: #73868f;
`;

export type InstrumentHeaderProps = {
    instrumentId: string;
    name: string;
    loading: boolean;
    hideInstrument: boolean;
    areas?: string[];
    sectors?: any[];
    description?: string;
    hideReddeer?: boolean;
    website?: string;
    sedol?: string;
    priceType?: string;
};

export type InstrumentPricingProps = {
    // eslint-disable-next-line react/no-unused-prop-types
    instrumentId: string;
    isEquity?: boolean;
    summaryPricingData?: {
        high: number;
        low: number;
        lastPrice: number;
        priceCcy: string;
        lastUpdated: string;
        mktCap: number;
        netDebt: number;
        specificChgToday: number;
        priceChg: number;
        dailyN: number;
        avgTurnover: number;
        turnoverUSD: number;
        expectedVolumeXadv30: number;
        daysToNextNumbers: number;
        daysSinceNumbers: number;
        dividendYield: number;
        daysToNextDividend: number;
        daysToEvent: number;
        eventType: string;
        eventDescription: string;
        marketGroup: string;
        absDailyNPercent: number;
        specificDailyNPercent: number;
        impliedEarningsMove: number;
        avgOnNumbersMove: number;
        indexWeightedAvgMarketCap?: number;
        indexNumberOfConstituents?: number;
        indexDailyLiquidity?: number;
    };
};

export const LineValue = styled.div`
    display: flex;
    color: var(--color);
    white-space: nowrap;
`;

export const ValueName = styled.div`
    color: var(--color--tertiary);
    font-weight: 500;
    white-space: nowrap;
`;

export const MarketMaker = styled.div`
    color: var(--color);
    font-weight: bold;
    white-space: nowrap;
`;

export const LastTick = styled.div`
    color: var(--color--tertiary, #141c26);
    white-space: nowrap;
`;
export const LastTickValue = styled.div`
    color: var(--color);
    white-space: nowrap;
    text-transform: lowercase;
    margin-left: var(--spacing--small, 4px);
`;

export const InfoLine = styled.div`
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
`;
/**
 * @deprecated use from PrimitiveComponents
 */
export const ColoredNumber = ({ value, f }) => (
    <div
        style={{
            color: value < 0 ? "var(--color__danger)" : "var(--color__success)"
        }}
    >
        {!isNumber(value) ? "-" : format(f)(value)}
    </div>
);
export const checkNan = (value, mapper = v => v) => (!isNumber(value) ? "-" : mapper(value));

export const NamedValue = ({ value, name, mapper = v => v }) => (
    <InfoLine>
        <ValueName>{name}</ValueName>
        <LineValue>{checkNan(value, mapper)}</LineValue>
    </InfoLine>
);

export const SimpleValue = ({ value, name, dashFor = -1, title = "" }) => (
    <InfoLine
        className="simpleValueLine"
        title={title}
    >
        <ValueName>{name}</ValueName>&nbsp;
        <LineValue>{value === dashFor ? "-" : value}</LineValue>
    </InfoLine>
);

export const formatMarketCap = value =>
    value >= 1000000000
        ? `$${format(".2f")(value / 1000000000)}bn`
        : `$${format(".2f")(value / 1000000)}m`;
// export const formatTraded = val => val === 0 ? "$0" : Math.abs(val) < 1000000 ? `$${format(".0f")(val / 1000)}k` : `$${format(".0f")(val / 1000000)}m`;
// export const formatTurnoverUsd = val => val === 0 ? "$0" : `$${format(".1f")(val)}m`;

export const formatNetDebt = mktCap => value => {
    const formattedValue =
        mktCap >= 1000000000
            ? `$${format(".2f")(Math.abs(value) / 1000000000)}bn`
            : `$${format(".2f")(Math.abs(value) / 1000000)}m`;

    if (value < 0) {
        return `(${formattedValue})`;
    }

    return formattedValue;
};

// export const formatMarketCap = value => `$${format(".2f")(value / 1000000000)}bn`;
export const formatTraded = val => `$${format(".2f")(val / 1000000)}m`;
export const formatTurnoverUsd = val => (!val ? "$0" : `$${formatBigValue(val, ".2f")}`);

const magicServerNullForDaysSinceLastFigures = -5000;

export const DaysTillControl = ({
    Container = LayoutVertical,
    summaryPricingData
}: InstrumentPricingProps & { Container?: ComponentType<any> }) => {
    const { daysToNextDividend, dividendYield, daysToNextNumbers, daysSinceNumbers } =
        summaryPricingData || {};

    const divDisplay =
        isNumber(daysToNextDividend) && isNumber(dividendYield)
            ? `${daysToNextDividend} (${format(".2")(dividendYield * 100)}%)`
            : daysToNextDividend;

    return (
        <Container spacing="none">
            <SimpleValue
                value={daysToNextNumbers}
                name="Days Until Next Figures"
            />
            <SimpleValue
                value={daysSinceNumbers}
                name="Days Since Last Figures"
                dashFor={magicServerNullForDaysSinceLastFigures}
            />
            <SimpleValue
                value={divDisplay}
                name="Days Until Next Dividend"
            />
        </Container>
    );
};

export const CorpEventControl = ({ summaryPricingData }: InstrumentPricingProps) => {
    const { daysToEvent, eventType, eventDescription } = summaryPricingData || {};

    return (
        <SimpleValue
            value={`${
                (daysToEvent &&
                    daysToEvent !== magicServerNullForDaysSinceLastFigures &&
                    `${daysToEvent}d`) ||
                ""
            } ${
                (eventType &&
                    parseInt(eventType, 10) !== magicServerNullForDaysSinceLastFigures &&
                    `(${eventType})`) ||
                ""
            }`}
            name="Days Until Next Corp Event"
            title={
                (eventDescription &&
                    parseInt(eventDescription, 10) !== magicServerNullForDaysSinceLastFigures &&
                    eventDescription) ||
                ""
            }
        />
    );
};
