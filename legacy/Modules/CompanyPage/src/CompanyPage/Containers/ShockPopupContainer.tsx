import {
    createConfig,
    reportSubscriptionCommandSpec,
    requestResponseCommandSpec,
    getCommandName,
    withCommandState
} from "TransportGateway/index";
import { ShockPopup, EpsValueChanges } from "../Components/ShokPopup";

import { arrangeByDate, checkNeg1, specialDayRequest, check0 } from "./common";

const earningChangesCommand = "InstrumentRequest_IBrokerEarningsSchema";
const shockCommand = getCommandName("RaidData")("GetInstrumentDataRequestDto", "ShockDto", true);

const earningChangesMap = data => {
    if (!data || !data.length) return [];

    const arranged = arrangeByDate(data);
    const changes = arranged.map((row: any) => {
        const {
            date,
            dateReal,
            brokerAlias: broker,
            fy1,
            fy1Chg: fy1chg,
            fy2,
            fy2Chg: fy2chg
        } = row;
        const mapped: EpsValueChanges = {
            date,
            dateReal,
            broker,
            fy1: check0(fy1),
            fy1chg: checkNeg1(fy1chg),
            fy2: check0(fy2),
            fy2chg: checkNeg1(fy2chg)
        };
        return mapped;
    });
    return changes;
};

const shockMap = data => {
    const {
        stockAvg: stock,
        mcapAvg: marketCap,
        sectorAvg: sector,
        shock: score,
        epsChange: epsChg
    } = data;

    return { stock, marketCap, sector, score, epsChg };
};

export const ShockPopupContainer = withCommandState(
    ShockPopup,
    createConfig({
        changes: reportSubscriptionCommandSpec(
            earningChangesCommand,
            ({ instrumentId: BBGTicker }) => ({ BBGTicker, dateFrame: 12 }),
            earningChangesMap
        ),
        shock: requestResponseCommandSpec(
            shockCommand,
            ({ instrumentId: BBGTicker }) => ({ BBGTicker, dateFrame: 12 }),
            shockMap
        ),
        specialDate: specialDayRequest()
    })
);
