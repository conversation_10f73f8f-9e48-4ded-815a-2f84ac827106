import React, { useState, useMemo, ComponentType } from "react";
import styled from "styled-components";

import { SelectInput } from "@reddeer/firefly-shared-controls";
import {
    getCommandName,
    requestResponseCommandSpec,
    reportSubscriptionCommandSpec,
    createConfig,
    withCommandState,
    apiGatewaySpec,
} from "TransportGateway/index";
import { ApiGatewayService, MarketToneType } from "Contracts/index";
import {
    useStickyState,
    DateFrameWithLabel,
    Radio,
    Checkbox,
    Flex,
    useDerivedStateFromProps,
    ModalDisplay,
} from "PrimitiveComponents/index";
import { CompanyPageSummaryChart, CompanyPageSummaryChartGateway } from "ComponentLibrary/index";
import { withRiskModel } from "ConnectedComponents/index";

import { RiskModelRadio } from "../Components";
import styles from "./styles.scss";
import { CHART_PADDING_LEFT } from "./common";
import { TechnicalChartsContainer } from "../Tabs/TechnicalTab/TechnicalChartsContainer";

const DEFAULT_ALL = -1;

type TraderInputParams = Partial<ApiGatewayService.CompanyPage.SummaryChartTradersDataResponse> & {
    traderId?: number;
    selectTrader?: any;
    showAll?: boolean;
};
const TraderInput = ({
    allTraders = [],
    tradersWithHistoricalExposure = [],
    traderId = DEFAULT_ALL,
    selectTrader = () => {},
    showAll = false,
}: TraderInputParams) => {
    const filteredTraders = useMemo(() => {
        if (!allTraders?.length) return [];

        return showAll || !tradersWithHistoricalExposure?.length
            ? allTraders
            : allTraders.filter(t => tradersWithHistoricalExposure.some(x => t.id === x.id));
    }, [allTraders, showAll, tradersWithHistoricalExposure]);
    const [currentTrader, setCurrentTrader] = useState(traderId);

    const updateTrader = t => {
        selectTrader(t);
        setCurrentTrader(t);
    };

    return (
        <SelectInput
            label="Trader"
            items={[
                { label: "All", value: DEFAULT_ALL },
                ...filteredTraders
                    .map(({ id: value, initials }) => ({
                        label: initials,
                        value,
                    }))
                    .sort((a, b) => a.label.localeCompare(b.label)),
                // {label: "None", value: -2 }
            ]}
            sortByLabel={false}
            labelClassName={styles.fancyLabel}
            classNames={[styles.labelContainer]}
            onChange={updateTrader}
            disabled={false}
            value={currentTrader}
        />
    );
};
const ConnectedTraderInput = withCommandState(
    TraderInput,
    createConfig({
        props: apiGatewaySpec<
            ApiGatewayService.CompanyPage.SummaryChartTradersRequest,
            ApiGatewayService.CompanyPage.SummaryChartTradersDataResponse
        >(
            "companypage.summary-chart-traders",
            ({ bbgTicker, dateFrame }) => bbgTicker && { bbgTicker, dateFrame },
            data => data?.length && data[0],
        ),
    }),
);

type FundGroupsProps = {
    fundGroups?: ApiGatewayService.Filters.FundGroupsResponse[];
    value: number;
    onChange: (fundGroup: number) => void;
};

const FundGroups = (props: FundGroupsProps) => {
    const { fundGroups, value, onChange } = props;
    const items = useMemo(
        () => [
            { label: "All", value: DEFAULT_ALL },
            ...(fundGroups?.map(({ id, name }) => ({
                label: name,
                value: id,
            })) || []),
        ],
        [fundGroups],
    );

    return (
        <SelectInput
            label="Fund Group"
            items={items}
            sortByLabel={false}
            labelClassName={styles.fancyLabel}
            classNames={[styles.labelContainer]}
            onChange={onChange}
            disabled={false}
            value={value}
        />
    );
};
const ConnectedFundGroups = withCommandState(
    FundGroups,
    createConfig({
        props: apiGatewaySpec<
            ApiGatewayService.Filters.FundGroupsRequest,
            ApiGatewayService.Filters.FundGroupsResponse
        >(
            "filters.fund-groups",
            ({ traderId }) => ({ traderId }),
            data => data?.length && { fundGroups: data },
        ),
    }),
);

const PERIODS = [1, 3, 6, 12, 24, 60];
const SPECIFIC_OPTIONS = [
    {
        value: false,
        label: "Market",
    },
    {
        value: true,
        label: "Specific",
    },
];

const gatewayFactorLookup = {
    market: undefined,
    value: "Value",
    leverage: "Leverage",
    growth: "Growth",
    profitability: "Profitability",
    earningsYield: "Earnings Yield",
    dividendYield: "Dividend Yield",
    size: "Size",
    liquidity: "Liquidity",
    marketSensitivity: "Market Sensitivity",
    volatility: "Volatility",
    momo: "Medium-Term Momentum",
    shortMomo: "Short-Term Momentum",
    exchangeRateSensitivity: "Exchange Rate Sensitivity",
};

type SummaryChartContainerProps = {
    instrumentId: string;
    chartHeight: number;
    chartWidth: number;
    riskModel: string;
    relativeCat?: any;
    reportAlias?: any;
    chartPeriod?: number;
    traderId?: number;
    showNInHover?: boolean;
    hideTooltip?: boolean;
    useGateway?: boolean;
    isEquity?: boolean;
    onDividendAdjusted: (val: boolean) => void;
    showTechnicals?: boolean;
    showSystematic: boolean;
};

const Header = styled.div`
    display: flex;
    flex: 1;
    flex-direction: column;

    label {
        font-weight: normal !important;
    }
`;

const SummaryChartRelativeLookupContainer = withCommandState(
    CompanyPageSummaryChart,
    createConfig({
        relativeTicker: requestResponseCommandSpec(
            "RaidData_RaidInstrumentRelationshipRequest_RaidInstrumentRelationshipResponse_RequestResponse_Handler",
            ({ instrumentId }: { instrumentId: string }) => ({
                bbgTicker: [instrumentId],
                relationshipTypes: ["ClosestSectorIndex"],
            }),
            data => data?.instrumentRelationships[0]?.toBBGTicker,
        ),
    }),
);

const SummaryChartContainerBase = ({
    reportAlias = "",
    chartPeriod,
    traderId,
    hideTooltip,
    useGateway = false,
    onDividendAdjusted,
    riskModel: riskModelProp,
    isEquity,
    chartHeight,
    showTechnicals = false,
    showSystematic = false,
    ...rest
}: SummaryChartContainerProps) => {
    const [riskModel, setRiskModel] = useDerivedStateFromProps(riskModelProp);
    const [period, setPeriod] = useState(6);
    const [trader, setTrader] = useState(traderId ?? -1);
    const [showSpecific, setShowSpecific] = useStickyState(false, "summarySpecific");
    const [fundGroup, setFundGroup] = useStickyState(-1, "cp.fundGroup");
    const [dividendAdjusted, setDividendAdjusted] = useState(true);
    const selectTrader = (value: number) => {
        setTrader(value);
    };

    const onDividendAdjustedChanged = (val: boolean) => {
        setDividendAdjusted(val);
        onDividendAdjusted(val);
    };

    const hasCompactLayout = useGateway && !isEquity;

    console.log(`[e-log] [SummaryChartContainerBase]`, {
        instrumentId: rest.instrumentId,
        periodInMonths: chartPeriod || period,
        chartWidth: 750,
    });

    return (
        <Flex
            direction="column"
            h="100%"
        >
            <Flex
                direction="column"
                gap="medium"
                style={{
                    width: rest.chartWidth,
                    marginBottom: hasCompactLayout ? -9 : 0,
                }}
            >
                <Flex
                    gap="small"
                    style={{ paddingLeft: CHART_PADDING_LEFT }}
                >
                    <Flex style={{ minWidth: 80, maxWidth: 120 }}>
                        <ConnectedTraderInput
                            traderId={trader}
                            selectTrader={selectTrader}
                            bbgTicker={rest.instrumentId}
                            dateFrame={chartPeriod || period}
                            {...rest}
                        />
                    </Flex>
                    <Flex style={{ minWidth: 150, maxWidth: 160 }}>
                        <ConnectedFundGroups
                            value={fundGroup}
                            onChange={setFundGroup}
                        />
                    </Flex>
                    <Flex items="center">
                        <Checkbox
                            onChange={onDividendAdjustedChanged}
                            value={dividendAdjusted}
                        >
                            Div Adjusted
                        </Checkbox>
                    </Flex>
                </Flex>
                <Flex
                    gap="medium"
                    style={{ paddingLeft: CHART_PADDING_LEFT }}
                >
                    <Flex items="baseline">
                        <Radio
                            options={SPECIFIC_OPTIONS}
                            value={showSpecific}
                            onChange={() => setShowSpecific(curr => !curr)}
                            size="small"
                            optionHorizontalPadding="medium"
                            compact
                        />
                    </Flex>

                    <Flex items="baseline">
                        <RiskModelRadio
                            value={riskModel}
                            onChange={setRiskModel}
                            disabled={!showSpecific}
                            optionHorizontalPadding="medium"
                        />
                    </Flex>

                    {!chartPeriod && (
                        <Flex>
                            <DateFrameWithLabel
                                periods={PERIODS}
                                value={period}
                                onChange={(val: number) => setPeriod(val)}
                                optionMinWidth="0"
                            />
                        </Flex>
                    )}

                    {showTechnicals && (
                        <Flex>
                            <ModalDisplay
                                btnLabel="Technicals"
                                modalProps={{
                                    title: `${rest.instrumentId} Technicals`,
                                }}
                            >
                                <TechnicalChartsContainer
                                    instrumentId={rest.instrumentId}
                                    periodInMonths={chartPeriod || period}
                                    chartWidth={750}
                                />
                            </ModalDisplay>
                        </Flex>
                    )}
                </Flex>
            </Flex>
            {!useGateway && (
                <SummaryChartRelativeLookupContainer
                    {...rest}
                    reportAlias={reportAlias}
                    period={chartPeriod || period}
                    trader={trader}
                    adjusted={dividendAdjusted}
                    relativeCat={rest.relativeCat}
                    showMarketTone
                    hideTooltip={hideTooltip}
                    instrumentId={rest.instrumentId}
                    displayRiskAttribution={showSpecific}
                    fundGroup={fundGroup}
                    riskModel={riskModel}
                    manageOverflow={false}
                    displayCurrentTraders
                    showSystematic={showSystematic}
                />
            )}
            {useGateway && (
                <CompanyPageSummaryChartGateway
                    {...rest}
                    reportAlias={reportAlias}
                    trader={trader}
                    relativeCat={rest.relativeCat}
                    showMarketTone
                    hideTooltip={hideTooltip}
                    displayRiskAttribution={showSpecific}
                    manageOverflow={false}
                    bbgTicker={rest.instrumentId}
                    dateFrame={chartPeriod || period}
                    traderId={trader}
                    fundGroupId={fundGroup}
                    isDivAdjusted={dividendAdjusted}
                    riskModel={riskModel}
                    toneType={MarketToneType[rest.relativeCat] as unknown as MarketToneType}
                    factor={gatewayFactorLookup[rest.relativeCat]}
                    displayEarnings={isEquity}
                    showSystematic={showSystematic}
                    chartHeight={chartHeight - (hasCompactLayout ? 21 : 0)}
                />
            )}
        </Flex>
    );
};

export const SummaryChartContainer = withRiskModel(SummaryChartContainerBase);
