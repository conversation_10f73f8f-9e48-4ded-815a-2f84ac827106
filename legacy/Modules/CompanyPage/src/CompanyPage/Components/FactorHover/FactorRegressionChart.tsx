import React from "react";

import { Regression<PERSON><PERSON>, RegressionLineProps } from "@reddeer/firefly-shared-controls";
import {
    reportSubscriptionCommandSpec,
    createConfig,
    withCommandStateFactory
} from "TransportGateway/index";
import moment from "moment";

const RegressionChartWithHeader = props => {
    return <RegressionChart {...props} />;
};

export const FactorRegressionChart = withCommandStateFactory(RegressionChartWithHeader, () =>
    createConfig({
        header: reportSubscriptionCommandSpec(
            "RegressionChartRequestHandler_Factor",
            ({ factor, riskModel, period: dateFrame }) => ({
                factor,
                riskModel,
                dateFrame
            }),
            data => {
                if (!data?.length) return null;
                return { ...data[data.length - 1] };
            },
            "Header"
        ),
        series: reportSubscriptionCommandSpec(
            "RegressionChartRequestHandler_Factor",
            ({ factor, riskModel, period: dateFrame }) => ({
                factor,
                riskModel,
                dateFrame
            }),
            (data = []) => {
                const unsorted = data.map(({ dateTime, absRsi: rsi, ...row }) => {
                    const d = moment(dateTime, "YYYY-MM-DD").toDate();
                    return { date: d, rsi, close, ...row };
                });
                return unsorted.sort((a, b) => a.date.valueOf() - b.date.valueOf());
            },
            "Series"
        ),
        regression: reportSubscriptionCommandSpec(
            "RegressionChartRequestHandler_Factor",
            ({ factor, riskModel, period: dateFrame }) => ({
                factor,
                riskModel,
                dateFrame
            }),
            (data = []): RegressionLineProps => {
                if (!data || !data.length) return null;
                const reg = data[data.length - 1];
                const result = {
                    current: reg.regressCurrent,
                    slope: reg.regressSlope,
                    length: reg.regressLength,
                    stdErr: reg.regressStdErr
                };
                return result;
            },
            "Lines"
        ),
        exposure: reportSubscriptionCommandSpec(
            "SummaryChartRequestHandler_Exposure_Factor",
            ({ factor, riskModel, period: dateFrame }) => ({
                factor,
                riskModel,
                dateFrame
            }),
            (data = []) => {
                const unsorted = data.map(({ dateTime, ...row }) => {
                    const d = moment(dateTime, "YYYY-MM-DD").toDate();
                    return { date: d, ...row };
                });
                return unsorted.sort((a, b) => a.date.valueOf() - b.date.valueOf());
            }
        )
    })
);
