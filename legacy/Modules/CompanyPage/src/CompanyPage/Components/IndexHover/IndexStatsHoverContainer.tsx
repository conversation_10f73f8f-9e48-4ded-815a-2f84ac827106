import React from "react";
import { format } from "d3";

import { formatDollarValue } from "Utilities/index";
import { ColoredNumber, LayoutVertical } from "PrimitiveComponents/index";
import {
    reportSubscriptionCommandSpec,
    createConfig,
    withCommandState
} from "TransportGateway/index";

import { ValueName, InfoLine, NamedValue, Name } from "../../Containers/InstrumentContainerCommon";

const IndexStatsHover = ({ data, constituents }) => {
    if (!data || !constituents) return null;
    const { chgTdy, name, dailyNMove, specificChgToday } = data;
    const { count, weightedAvgMktCap, dailyLiquidity } = constituents;

    return (
        <LayoutVertical spacing="small">
            <div>
                <Name>{name}</Name>
            </div>
            <LayoutVertical spacing="none">
                <InfoLine>
                    <ValueName>% Change Today</ValueName>
                    <ColoredNumber
                        value={chgTdy}
                        f=".2%"
                    />
                </InfoLine>
                <InfoLine>
                    <ValueName>% Specific Today</ValueName>
                    <ColoredNumber
                        value={specificChgToday}
                        f=".2%"
                    />
                </InfoLine>
                <InfoLine>
                    <ValueName>Daily N Move</ValueName>
                    <ColoredNumber
                        value={dailyNMove}
                        f=".2f"
                    />
                </InfoLine>
                <NamedValue
                    value={count}
                    mapper={value => format(".0f")(value)}
                    name="Number of Constituents"
                />
                <NamedValue
                    value={weightedAvgMktCap}
                    mapper={value => formatDollarValue(value)}
                    name="Weighted Avg Market Cap"
                />
                <NamedValue
                    value={dailyLiquidity}
                    mapper={value => formatDollarValue(value)}
                    name="Daily Liquidity"
                />
            </LayoutVertical>
        </LayoutVertical>
    );
};

const mapData = (data = []) => {
    return data.length && data[0];
};

export const IndexStatsHoverContainer = withCommandState(
    IndexStatsHover,
    createConfig({
        data: reportSubscriptionCommandSpec(
            "IndexSummary",
            ({ instrumentId: Index }: { instrumentId: string }) => ({ Index }),
            mapData
        ),
        constituents: reportSubscriptionCommandSpec(
            "IndexConstituents",
            ({ instrumentId: index }: { instrumentId: string }) => ({ index }),
            mapData,
            "WeightedAvgTurnover"
        )
    })
);
