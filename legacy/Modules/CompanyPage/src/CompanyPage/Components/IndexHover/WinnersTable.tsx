import React from "react";
import {
    TableDefinition,
    ChangedValue,
    TableColumnAlignType,
    Table
} from "@reddeer/firefly-shared-controls";
import { formatNumber, numberFormats, stripEquity } from "Utilities/index";
import {
    reportSubscriptionCommandSpec,
    createConfig,
    withCommandState
} from "TransportGateway/index";
import * as compStyles from "../../../Styles.scss";

type Props = {
    identifier: string;
    chgTdy: number;
    weight: number;
};

const tableDefinition: TableDefinition<Props> = {
    columns: [
        { flexWidth: "1 0 36%", getCellValue: x => stripEquity(x.identifier), header: "Ticker" },
        {
            flexWidth: "1 0 32%",
            getCellValue: x => (
                <ChangedValue
                    formatType="percent2dp"
                    hidePositiveSign={false}
                    value={x.chgTdy}
                />
            ),
            header: "Chg Tdy",
            align: TableColumnAlignType.Center
        },
        {
            flexWidth: "1 0 32%",
            getCellValue: x => formatNumber(x.weight, numberFormats.number1dp, ""),
            header: "Weight",
            align: TableColumnAlignType.Center
        }
    ],
    getRowKey: (x, i) => i + x.identifier,
    cellClassName: compStyles.denseTable
};

const WinnersTable = ({ table, top = true, max = 100 }) => {
    const data =
        table &&
        table
            .sort((a: Props, b: Props) => (top ? b.chgTdy - a.chgTdy : a.chgTdy - b.chgTdy))
            .slice(0, max);
    return (
        <div style={{ width: 220 }}>
            <Table
                definition={tableDefinition}
                data={data}
                classNames={[compStyles.hedgeTable]}
            />
        </div>
    );
};

const mapData = (data = []) => {
    if (!data.length) return;
    return data;
};

const report = "IndexConstituents";
const reportNode = "Constituents";

export const WinnersTableContainer = withCommandState(
    WinnersTable,
    createConfig({
        table: reportSubscriptionCommandSpec(
            report,
            ({ index }) => ({ index }),
            mapData,
            reportNode
        )
    })
);
