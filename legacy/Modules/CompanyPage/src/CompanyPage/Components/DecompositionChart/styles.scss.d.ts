declare namespace StylesScssNamespace {
    export interface IStylesScss {
        axis: string;
        dateAxis: string;
        decompositionChartContainer: string;
        gridLines: string;
        legend: string;
    }
}

declare const StylesScssModule: StylesScssNamespace.IStylesScss & {
    /** WARNING: Only available when `css-loader` is used without `style-loader` or `mini-css-extract-plugin` */
    locals: StylesScssNamespace.IStylesScss;
};

export = StylesScssModule;
