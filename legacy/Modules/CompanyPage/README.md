# Company Page

## Development

```
yarn start
```

Starts app at ```localhost:8080```

## Pointing to an environment

- Edit CompanyPage\CompanyPage.JS.Client\Client\src\App.tsx this file contains a few properties named "EXISTENT_END_POINT" comment out a property for the approriate environment. 


## Running

- Go to the root folder of the backbone solution ensure you have followed the steps in the README.md in that folder with regards to installing the dependencies like yarn
- From the root folder run the command yarn build-all this will take forever as it builds all of the JS projects in the solution
- Go back to the CompanyPage\CompanyPage.JS.Client\Client\ and run command "yarn start"
- Go to url http://localhost:8080/ to see company page in stand alone mode

## Running Server side locally. 

- If you're using rider simply launch the run configuration called "CompanyPageCompound" this will launch the ViewServerProxy and all the side car projects. If you're changing a backbone project too you can launch this seperately and service discovery should magically tell the sidecars to use this rather than the staging versions which will be used by default.
- If using vs at the point of writing the projects that you need to launch are 
    - StreetEvents.ViewServer
    - BloombergLicensedData.ViewServer 
    - PriceHistory.ViewServer
    - BloombergSnapshotHistory.ViewServer
    - DataRail.ViewServer
    - RiskModel.ViewServer
    - IBES2.ViewServer
    - Position.ViewServer
    - Exchange.ViewServer
    - RaidData
    - ViewServer.RaidProxy
    - CompanyPage
    - Universe.ViewServer
- To ensure that all of the sidecars
