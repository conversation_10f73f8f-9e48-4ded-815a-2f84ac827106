declare module "*.svg" {
    const url: string;
    export default url;

    // eslint-disable-next-line @typescript-eslint/no-empty-interface
    interface SvgrComponent extends React.FunctionComponent<React.SVGAttributes<SVGElement>> {}
    export const ReactComponent: SvgrComponent;
}

declare module "*.svg?react" {
    const url: string;
    export default url;

    // eslint-disable-next-line @typescript-eslint/no-empty-interface
    interface SvgrComponent extends React.FunctionComponent<React.SVGAttributes<SVGElement>> {}
    export const ReactComponent: SvgrComponent;
}
