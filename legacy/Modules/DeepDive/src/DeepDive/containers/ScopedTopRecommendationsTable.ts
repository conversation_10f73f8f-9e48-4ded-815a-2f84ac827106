// @TODO: use the import of withCommandState from "ViewServer" module - this also includes the diagnosis overlays...
import { createConfig, withCommandState } from "TransportGateway/index";
import { withComponentProps } from "PrimitiveComponents/index";
import { Grid } from "Grid/index";
import { requestMapper, SubscriptionCommands, scsFactory } from "../../model";

export const ScopedTopRecommendationsTable = withComponentProps(
    withCommandState(
        Grid,
        createConfig({
            data: scsFactory(SubscriptionCommands.TopDownRecommendation, requestMapper) as any // @TODO: resolve type
        })
    ),
    {
        gridOptions: {
            enableCellExpressions: true,
            domLayout: "autoHeight",
            animateRows: true,
            onGridReady: ({ api }) => api.sizeColumnsToFit(),
            onGridSizeChanged: ({ api }) => api.sizeColumnsToFit(),
            rowHeight: 30,
            defaultColDef: { editable: false, resizable: true, sortable: true, filter: true }
        }
    }
);
