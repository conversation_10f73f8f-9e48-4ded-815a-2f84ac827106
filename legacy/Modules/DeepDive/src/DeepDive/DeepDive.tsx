import React, { useCallback, useMemo, useState } from "react";
import {
    ChangedValue,
    Widget,
    useInsightFilters,
    useTradeSnapshotModal
} from "@reddeer/firefly-shared-controls";
import { Modal } from "antd";
import styled from "styled-components";
import type { GridBaseProps } from "Grid/index";
import {
    ScopedBottomPositionTable,
    ScopedBottomRecommendationsTable,
    ScopedImagePortfolio,
    ScopedTopPositionTable,
    ScopedTopRecommendationsTable
} from "./containers";
import { SnapShotLink } from "../SnapShotLink";

const DeepDivePage = styled.div`
    display: grid;
    grid-gap: 12px;
`;

export const DeepDive = () => {
    const { traderId, fundId, fundGroupId, lookback, toDate, fromDate } = useInsightFilters();
    const { showSnapshotModal } = useTradeSnapshotModal();
    const props = {
        traderId,
        fundId,
        fundGroupId,
        lookback,
        toDate,
        fromDate
    };

    const [selectedPolicyId, setSelectedPolicyId] = useState<number>(undefined);
    const onClickTicker = useCallback(
        (instrumentIdentifier: string) => {
            showSnapshotModal({
                instrumentIdentifier
            });
        },
        [showSnapshotModal]
    );

    const snapShotTickers = useCallback(
        ({ value }) => (
            <SnapShotLink
                tickers={[value]}
                clickHandler={onClickTicker}
            />
        ),
        [onClickTicker]
    );

    const changeValue0Dp = useCallback(
        ({ value }) => (
            <ChangedValue
                formatType="dollar0dp"
                value={value}
            />
        ),
        []
    );

    const topBottomPositionTableDefinition: GridBaseProps = useMemo(
        () => ({
            columnDefs: [
                {
                    field: "ticker",
                    cellRenderer: snapShotTickers,
                    headerName: "Ticker"
                },
                {
                    field: "pL",
                    cellRenderer: changeValue0Dp,
                    headerName: "PnL $",
                    cellStyle: { textAlign: "right", paddingRight: "30px" }
                },
                { field: "strategy", headerName: "Strategy" },
                { field: "subStrategy", headerName: "Substrategy" },
                { field: "characteristic", headerName: "Characteristic" },
                { field: "driver", headerName: "Driver" }
            ],
            getRowId: params => `${params.data.ticker}-${params.data.pL}`,
            enableCellChangeFlash: true
        }),
        [changeValue0Dp, snapShotTickers]
    );

    const snapShotTickerSplit = useCallback(
        ({ value }) => (
            <SnapShotLink
                tickers={value.split(", ")}
                clickHandler={onClickTicker}
            />
        ),
        [onClickTicker]
    ) as unknown as ({ value }: { value: string[] }) => HTMLElement;

    const changeValue2Dp = useCallback(
        ({ value }) => (
            <ChangedValue
                formatType="percent2dp"
                value={value}
            />
        ),
        []
    );

    const recommendationsTableDefinition: GridBaseProps = useMemo(
        () => ({
            columnDefs: [
                {
                    field: "tradingPolicy",
                    headerName: "Policy",
                    onCellClicked: ({ data }) => setSelectedPolicyId(data.tradingPolicyID)
                },
                { field: "category", headerName: "Characteristic" },
                {
                    field: "tickers",
                    headerName: "Tickers",
                    cellRenderer: snapShotTickerSplit
                },
                {
                    field: "ann3MReturnChange",
                    headerName: "3M Ann Return",
                    cellRenderer: changeValue2Dp,
                    cellStyle: { textAlign: "right", paddingRight: "30px" }
                },
                {
                    field: "ann12MReturnChange",
                    headerName: "12M Ann Return",
                    cellRenderer: changeValue2Dp,
                    cellStyle: { textAlign: "right", paddingRight: "30px" }
                },
                { field: "conviction", headerName: "Conviction" }
            ],
            getRowId: params => `${params.data.tradingPolicy}-${params.data.ann3MReturnChange}`,
            enableCellChangeFlash: true
        }),
        [changeValue2Dp, snapShotTickerSplit]
    );
    const closeModal = () => setSelectedPolicyId(undefined);
    const filtersValid = (traderId || fundId || fundGroupId) && ((fromDate && toDate) || lookback);

    return (
        <DeepDivePage>
            {!filtersValid ? null : (
                <>
                    <Widget title="Top Positions">
                        <ScopedTopPositionTable
                            {...topBottomPositionTableDefinition}
                            {...props}
                        />
                    </Widget>
                    <Widget title="Bottom Positions">
                        <ScopedBottomPositionTable
                            {...topBottomPositionTableDefinition}
                            {...props}
                        />
                    </Widget>
                    <Widget title="Top Down Recommendation">
                        <ScopedTopRecommendationsTable
                            {...recommendationsTableDefinition}
                            {...props}
                        />
                    </Widget>
                    <Widget title="Bottom Up Recommendation">
                        <ScopedBottomRecommendationsTable
                            {...recommendationsTableDefinition}
                            {...props}
                        />
                    </Widget>
                    <Modal
                        visible={!!selectedPolicyId}
                        onOk={closeModal}
                        onCancel={closeModal}
                        width={860}
                    >
                        <ScopedImagePortfolio
                            {...props}
                            policyId={selectedPolicyId}
                        />
                    </Modal>
                </>
            )}
        </DeepDivePage>
    );
};
