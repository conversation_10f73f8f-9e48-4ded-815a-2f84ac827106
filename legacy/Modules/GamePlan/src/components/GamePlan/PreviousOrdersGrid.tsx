import React, { useState, useCallback, useEffect, useRef } from "react";
import { orderBy } from "lodash";
import styled from "styled-components";
import { Tooltip, Icon, Typography } from "antd";
import { FireflyAgGrid, GridLayoutPanel } from "@reddeer/firefly-common-components";
import { PreviousOrderViewModel } from "@firefly-trading-labs/gameplan-api";

import { DriverOptionsModel } from "./model";
import { GamePlanWidget, GradientContainer } from "./common";
import { TradeRationaleSummary } from "./TradeRationaleSummary";
import { getTooltipContainer } from "@reddeer/raid-theme";
import { EditPreviousOrderFn } from "../../model";
import moment from "moment";

const { Paragraph } = Typography;
const StyledGamePlanWidget = styled(GamePlanWidget)`
    padding-bottom: 0px;
`;
const Ticker = styled.span`
    color: #f5a623;
`;
const Buy = styled.span`
    color: #09a90d;
`;
const Sell = styled.span`
    color: #f53232;
`;
const Panel = styled(GridLayoutPanel)`
    width: 260px;
    border: initial;
    box-shadow: none;
`;
const StyledAgGrid = styled(FireflyAgGrid)`
    &&&& {
        .ag-row .ag-cell {
            border-right: none;
            padding-left: 4px;
            padding-right: 4px;
            font-weight: 200;
            font-size: 12px;
            line-height: 23px;
        }
        .ag-row-selected {
            background-color: rgb(4, 174, 245, 0.19);
        }
        .ag-header {
            border: 0px;
        }
        .ag-root-wrapper,
        .ag-status-bar {
            border-width: 0px;
        }
    }
`;
const ConvictionFlag = styled.div<{ score }>`
    color: #ffffff;
    text-align: center;
    border-radius: 3px;
    height: 18px;
    line-height: 1.4;
    margin-top: 3px;
    background: transparent;
    border-style: solid;
    border-width: 1px;
    width: 18px;
    ${({ score }) => {
        const color =
            score === 1
                ? "#f6631b"
                : score === 2
                ? "#f8c035"
                : score === 3
                ? "#76e219"
                : score === 4
                ? "#049f01"
                : "#f50200";
        return `color: ${color}; border-color: ${color};`;
    }}
`;

interface PreviousOrdersGridProps {
    previousOrders: PreviousOrderViewModel[];
    driverOptions: DriverOptionsModel;
    gamePlanId: number;
    onEditPreviousOrder?: EditPreviousOrderFn;
    onSelectPreviousOrder?: EditPreviousOrderFn;
    showPreviousOrderAsTooltip?: boolean;
}

export const PreviousOrdersGrid: React.FunctionComponent<PreviousOrdersGridProps> = ({
    previousOrders,
    driverOptions,
    gamePlanId,
    onEditPreviousOrder,
    showPreviousOrderAsTooltip = false,
    onSelectPreviousOrder = () => undefined
}) => {
    const [selectedRowData, setSelectedRowData] = useState<PreviousOrderViewModel>();
    const isRowEditable = !!onEditPreviousOrder;

    useEffect(() => {
        setSelectedRowData(undefined);
    }, [gamePlanId]);

    const formatDate = (date: Date) => {
        const REFERENCE = moment();
        const TODAY = REFERENCE.clone().startOf("day");
        const A_YEAR_OLD = REFERENCE.clone().subtract(365, "days").startOf("day");

        let formattedDate = moment(date).format("DD/MM");

        if (moment(date).isSame(TODAY, "d")) {
            formattedDate = moment(date).format("HH:mm");
        }
        if (moment(date).isBefore(A_YEAR_OLD)) {
            formattedDate = moment(date).format("YYYY");
        }
        return formattedDate;
    };

    const rowData = orderBy(previousOrders, "Date", "asc").map(o => ({
        ...o,
        Date: formatDate(o.Date),
        BBGTicker: (o.BBGTicker || "").replace(/ Equity/g, "")
    }));

    const tradeHolder = useRef<HTMLDivElement>(null);
    useEffect(() => {
        const div = tradeHolder.current;
        if (div) div.scrollTo(0, div.scrollHeight); // NB: we just want to scroll to the bottom, overshootting has no effect
    }, []); // i.e. run on initial load only

    const buySellCellRenderer = ({ value = "" }) => {
        const val = value.toUpperCase();
        return val === "BUY" ? (
            <Buy style={{ fontWeight: 400 }}>{val}</Buy>
        ) : (
            <Sell style={{ fontWeight: 400 }}>{val}</Sell>
        );
    };
    const onRowSelected = useCallback(
        ({ data }: { data: PreviousOrderViewModel }) => {
            if (data) {
                onSelectPreviousOrder(data.OmsOrderId, data.OrderFormId);
                setSelectedRowData(data);
            }
        },
        [
            previousOrders,
            driverOptions,
            gamePlanId,
            onEditPreviousOrder,
            showPreviousOrderAsTooltip,
            onSelectPreviousOrder
        ]
    );
    return (
        <>
            <Tooltip
                visible={showPreviousOrderAsTooltip && !!selectedRowData}
                getPopupContainer={getTooltipContainer}
                placement={"rightBottom"}
                title={
                    <Panel
                        title={"Trade Rationale"}
                        isDraggable={false}
                        onClose={() => setSelectedRowData(undefined)}
                        headerAction={
                            isRowEditable && (
                                <Icon
                                    type="edit"
                                    onClick={() =>
                                        onEditPreviousOrder(
                                            selectedRowData.OmsOrderId,
                                            selectedRowData.OrderFormId
                                        )
                                    }
                                />
                            )
                        }
                    >
                        {!!selectedRowData && (
                            <TradeRationaleSummary
                                {...{
                                    drivers: selectedRowData.Drivers,
                                    investmentNotes: selectedRowData.InvestmentNotes,
                                    conviction: selectedRowData.ConvictionScore,
                                    driverOptions
                                }}
                            />
                        )}
                    </Panel>
                }
            >
                <StyledGamePlanWidget label="Trades">
                    <GradientContainer>
                        <div
                            style={{ maxHeight: 140, overflow: "auto" }}
                            ref={tradeHolder}
                        >
                            <StyledAgGrid
                                rowData={rowData}
                                rowHeight={25}
                                rowSelection={"single"}
                                headerHeight={0}
                                onRowClicked={onRowSelected}
                                columnDefs={[
                                    { width: 40, field: "Date", suppressSizeToFit: true },
                                    {
                                        width: 70,
                                        field: "BBGTicker",
                                        suppressSizeToFit: true,
                                        cellRendererFramework: ({ value }) => (
                                            <Ticker>{value}</Ticker>
                                        )
                                    },
                                    {
                                        width: 45,
                                        field: "BuySell",
                                        suppressSizeToFit: true,
                                        cellRendererFramework: buySellCellRenderer
                                    },
                                    {
                                        width: 80,
                                        field: "TradeActionType",
                                        suppressSizeToFit: true
                                    },
                                    {
                                        width: 500,
                                        field: "Drivers",
                                        cellRendererFramework: ({ value = [] }) => (
                                            <Paragraph
                                                style={{ color: "#ffffff" }}
                                                ellipsis
                                            >
                                                {value?.join(", ")}
                                            </Paragraph>
                                        )
                                    },
                                    {
                                        width: 500,
                                        field: "InvestmentNotes",
                                        cellRendererFramework: ({ value = "" }) => (
                                            <Paragraph
                                                style={{ color: "#ffffff" }}
                                                ellipsis
                                            >
                                                <span title={value}>{value}</span>
                                            </Paragraph>
                                        )
                                    },
                                    {
                                        width: 30,
                                        field: "ConvictionScore",
                                        suppressSizeToFit: true,
                                        cellRendererFramework: ({ value }) =>
                                            value > 0 ? (
                                                <ConvictionFlag score={value}>
                                                    {value}
                                                </ConvictionFlag>
                                            ) : (
                                                <></>
                                            )
                                    },
                                    {
                                        width: 30,
                                        field: "Locked",
                                        suppressSizeToFit: true,
                                        cellRendererFramework: ({ value, data }) =>
                                            isRowEditable &&
                                            !value && (
                                                <Icon
                                                    onClick={() =>
                                                        onEditPreviousOrder(
                                                            data.OmsOrderId,
                                                            data.OrderFormId
                                                        )
                                                    }
                                                    type={"edit"}
                                                />
                                            )
                                    }
                                ]}
                            />
                        </div>
                    </GradientContainer>
                </StyledGamePlanWidget>
            </Tooltip>
        </>
    );
};
