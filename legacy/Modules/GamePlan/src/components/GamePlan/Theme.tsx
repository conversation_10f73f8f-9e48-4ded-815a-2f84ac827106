import React, { useState, useCallback } from "react";
import { Icon, AutoComplete } from "antd";
import { GamePlanWidget } from "./common";
import { useDebounce } from "../../api/useDebounce";
import { TraderGamePlansViewModel } from "@firefly-trading-labs/gameplan-api";
import styled from "styled-components";
import { getContainer } from "@reddeer/raid-theme";

export type ThemeProps = {
    theme: string;
    updateTheme: (val: string) => void;
    readOnly: boolean;
    gamePlanId: number;
    existingGamePlanOptions: TraderGamePlansViewModel | undefined;
    isExistingGamesLoading: boolean;
};
const HighlightedText = styled.span`
    background-color: rgba(255, 255, 0, 0.3);
`;

export const Theme = (props: ThemeProps) => {
    const {
        theme,
        updateTheme,
        gamePlanId,
        readOnly,
        existingGamePlanOptions,
        isExistingGamesLoading
    } = props;
    const [searchText, setSearchText] = useState<string>(theme || "");
    const disabled = !gamePlanId;
    const options =
        existingGamePlanOptions &&
        existingGamePlanOptions.Active &&
        existingGamePlanOptions.RecentlyClosed
            ? [
                  ...new Set(
                      [
                          ...existingGamePlanOptions.Active,
                          ...existingGamePlanOptions.RecentlyClosed
                      ].map(({ Theme }) => Theme)
                  )
              ]
                  .filter(theme => !!theme)
                  .sort()
            : [];
    const onValueChange = value => {
        const txt = value.toString();
        setSearchText(txt);
        debounceTheme(txt.trim());
    };
    const debounceTheme = useDebounce(updateTheme);

    const filterOption = useCallback((search: string, option: React.ReactElement) => {
        const t = option.props.value as string;
        return t.toLowerCase().includes(search.toLowerCase().trim());
    }, []);

    return (
        <GamePlanWidget label="Theme">
            {readOnly ? (
                <span style={{ flex: 1 }}>{theme}</span>
            ) : (
                <AutoComplete
                    style={{ flex: 1 }}
                    disabled={disabled}
                    defaultValue={theme}
                    onChange={onValueChange}
                    getPopupContainer={getContainer}
                    showSearch
                    filterOption={filterOption}
                    optionLabelProp="value"
                    notFoundContent={
                        isExistingGamesLoading ? (
                            <Icon
                                type="spin"
                                style={{ fontSize: 12 }}
                            />
                        ) : null
                    }
                >
                    {options.map(theme => {
                        const searchStart = !theme
                            ? 0
                            : theme.toLowerCase().indexOf(searchText.toLowerCase());
                        const themeLabel = !(options.length > 1) ? (
                            theme
                        ) : (
                            <span>
                                {theme.substring(0, searchStart)}
                                <HighlightedText className={"search-text"}>
                                    {theme.substring(searchStart, searchStart + searchText.length)}
                                </HighlightedText>
                                {theme.substring(searchStart + searchText.length, theme.length)}
                            </span>
                        );
                        return (
                            <AutoComplete.Option
                                key={theme}
                                value={theme}
                            >
                                {themeLabel}
                            </AutoComplete.Option>
                        );
                    })}
                </AutoComplete>
            )}
        </GamePlanWidget>
    );
};
