import React, { createContext, useContext, useState, useEffect, useCallback } from "react";
import { GatewayProvider } from "TransportGateway/index";
import { isEqual } from "lodash";
import { createMessageBusRequest, IBusApiWrapper } from "@reddeer/firefly-shared-controls";
import {
    GamePlanResponse,
    GetGamePlanCommand,
    GamePlanViewModel,
    GetOrCreateGamePlanCommand,
    GetOrCreateTradeRationaleCommand,
    GetTradeRationaleCommand,
    UpdateNameCommand,
    UpdateThesisCommand,
    UpdateSubStrategyCommand,
    LinkToGamePlanCommand,
    TradeRationaleViewModel,
    UpdateInvestmentNotesCommand,
    UpdateDriversCommand,
    UpdateConvictionScoreCommand,
    UpdateThemeCommand,
    GetTraderGamePlansCommand,
    TraderGamePlansViewModel,
    TraderHoldingPeriodDaysViewModel,
    AppendThesisCommand,
    AddNewConvictionScoreCommand,
    UpdateSourceOfIdeaCommand,
    UpdateInvestmentCategoryCommand,
    UpdateHoldingPeriodCommand,
    UpdateHoldingPeriodDaysCommand,
    UpdateTargetSizeCommand,
    UpdateTargetTypeCommand,
    UpdateUpsideDownsideTypeCommand,
    UpdateUpsideCommand,
    UpdateDownsideCommand
} from "@firefly-trading-labs/gameplan-api";
import {
    sendGamePlanCommand,
    UpdateGamePlanCommand,
    TradeRationaleCommand,
    sendTradeRationaleCommand
} from "../api";
import {
    ConnectionState,
    GamePlanError,
    EditPreviousOrderFn,
    UpdateSubStrategyHandler
} from "../model";
import { GamePlanHub, startGamePlanHub } from "./GamePlanHub";
import { getNameValidation } from "./validation";
import { useFeature, withFeatureProvider, type OrderFormSummary } from "ConnectedComponents/index";

type GamePlanContextProps = {
    connectionState: ConnectionState;
    error: GamePlanError | undefined;
    gamePlan: GamePlanViewModel;
    gamePlanValidationComments: { [fieldName: string]: string };
    gamePlanUpdateInProgress: boolean;
    gamePlanCommands: {
        updateName: (name: string) => void;
        updateThesis: (name: string) => void;
        updateTheme: (name: string) => void;
        linkToGamePlan: (id: number) => void;
        updateSubStrategy: UpdateSubStrategyHandler;
        appendThesis: (name: string) => void;
        addAttachment: (file: File) => void;
        addConvictionScore: (convictionScore: number) => void;
        updateSourceOfIdea: (name: string) => void;
        updateInvestmentCategory: (id: number) => void;
        updateHoldingPeriod: (id: number) => void;
        updateHoldingPeriodDays: (value: number) => void;
        updateTargetSize: (value: number) => void;
        updateTargetType: (id: number) => void;
        updateUpsideDownsideType: (id: number) => void;
        updateUpside: (value: number) => void;
        updateDownside: (value: number) => void;
    };
    tradeRationale: TradeRationaleViewModel;
    tradeRationaleDisabled: boolean;
    tradeRationaleCommands: {
        updateDrivers: (drivers: number[]) => void;
        updateInvestmentNotes: (notes: string) => void;
        updateConviction: (n: number) => void;
    };
    selectGamePlan: boolean;
    isGamePlanLoading: boolean;
    isTradeRationaleLoading: boolean;
    resetGamePlanAndTradeRationale: () => void;
    readOnly: boolean;
    onEditPreviousOrder?: EditPreviousOrderFn | undefined;
    onSelectPreviousOrder?: EditPreviousOrderFn | undefined;
    showPreviousOrderAsTooltip?: boolean;
    existingGamePlanOptions: TraderGamePlansViewModel;
    isExistingGamesLoading: boolean;
    positionId?: number;
    strategyIdentifier?: string;
    ticker?: string;
    traderId?: number;
    orderFormId?: number;
    omsOrderId?: string;
    checklistId?: number;
    setChecklistId?: (id?: number) => void;
    allowChecklist?: boolean;
    orderSummary: OrderFormSummary;
    setOrderSummary: (orderSummary: OrderFormSummary) => void;
};

export const GamePlanContext = createContext<GamePlanContextProps>({} as any);
export interface GamePlanProviderProps {
    positionId?: number;
    strategyIdentifier?: string;
    ticker?: string;
    traderId?: number;
    selectGamePlan?: boolean;
    orderFormId?: number;
    omsOrderId?: string;
    draftOrderSummary?: OrderFormSummary;
    readOnly?: boolean;
    onEditPreviousOrder?: EditPreviousOrderFn | undefined;
    onSelectPreviousOrder?: EditPreviousOrderFn | undefined;
    showPreviousOrderAsTooltip?: boolean;
    gamePlanId?: number;
}
const GamePlanProviderInternal = ({
    children,
    positionId,
    traderId,
    strategyIdentifier,
    ticker,
    selectGamePlan = true,
    orderFormId,
    omsOrderId,
    readOnly = false,
    draftOrderSummary,
    gamePlanId,
    ...rest
}: GamePlanProviderProps & { children: any }) => {
    let mounted = true;
    const [error, setError] = useState<GamePlanError | undefined>(undefined);
    const [connectionState, setConnectionState] = useState<ConnectionState>(
        ConnectionState.Connecting
    );
    const isConnected = connectionState === ConnectionState.Connected;

    const defaultTradeRationale = {
        ...new TradeRationaleViewModel(),
        Id: 0,
        TraderDriverOptions: [],
        CommonDriverOptions: [],
        RankedDrivers: [],
        InvestmentNotes: "",
        Conviction: 0,
        Locked: true
    };
    const defaultGamePlan = {
        ...new GamePlanViewModel(),
        Name: "",
        Thesis: "",
        Theme: "",
        GamePlanId: 0,
        PreviousOrders: [],
        Locked: true,
        SubStrategy: "",
        SubStrategyOptions: [],
        CurrentConvictionScore: 0,
        Convictions: [],
        InvestmentCategoryOptions: [],
        SourceOfIdea: "",

        TargetTypeOptions: [],
        TargetSize: null,
        InitialSizeDollarValue: null,
        InitialSizePercentageAllocation: null,
        UpsideDownSideOptions: [],
        Upside: null,
        Downside: null,
        IRR: null,
        RiskReward: null,
        HoldingPeriodOptions: [],
        HoldingPeriodDays: null,
        CurrentPositions: { $type: "", CurrentPositions: [], Net: 0, TotalInceptionToDate: 0 },
        CreatedOn: new Date(),
        UpdatedOn: new Date()
    };
    const defaultTraderGamePlans = {
        $type: "",
        Active: [],
        RecentlyClosed: [],
        HoldingPeriodDefaultDays: []
    };

    //   GAME PLAN
    const [initialGamePlanId, setInitialGamePlanId] = useState<number | undefined>();
    const [gamePlan, setGamePlan] = useState<GamePlanViewModel | undefined>(defaultGamePlan);
    const [isGamePlanLoading, setGamePlanLoading] = useState<boolean>(false);
    const [gamePlanValidationComments, setGamePlanValidationComments] = useState<{
        [fieldName: string]: string;
    }>({});

    //   MONITOR UPDATES IN PROGRESS
    const [gamePlanCommandsSent, setGamePlanCommandsSent] = useState<any>({});
    const monitoredFields = ["Name", "Thesis"];
    const gamePlanUpdateInProgress = monitoredFields.some(
        field => field in gamePlanCommandsSent && gamePlanCommandsSent[field] !== gamePlan[field]
    );

    //  TRADE RATIONALE
    const [tradeRationale, setTradeRationale] = useState<TradeRationaleViewModel | undefined>(
        defaultTradeRationale
    );
    const [tradeRationaleDisabled, setTradeRationaleDisabled] = useState<boolean>(true);
    const [isTradeRationaleLoading, setTradeRationaleLoading] = useState<boolean>(false);
    const [orderSummary, setOrderSummary] = useState<OrderFormSummary | undefined>();

    // CHECKLIST
    const { hasFeature } = useFeature();
    const allowChecklist = hasFeature("gameplan.checklist") && gamePlan?.ChecklistEnabled;
    const [checklistId, setChecklistId] = useState<number | undefined>();
    useEffect(() => {
        const positions = gamePlan?.CurrentPositions?.CurrentPositions ?? [];
        // if there's only one position, then just use that
        // otherwise, don't set it
        if (positions.length === 1) {
            setChecklistId(positions[0].GamePlanPositionId);
        } else {
            const position = positions.find(pos => pos.BBGTicker === ticker);
            setChecklistId(position?.GamePlanPositionId);
        }
    }, [JSON.stringify(gamePlan?.CurrentPositions?.CurrentPositions), ticker]);

    //   OTHER
    const [existingGamePlanOptions, setExistingGamePlanOptions] =
        useState<TraderGamePlansViewModel>(defaultTraderGamePlans);
    const [isExistingGamesLoading, setExistingGamesLoading] = useState<boolean>(false);

    const resetGamePlanAndTradeRationale = () => {
        setGamePlan(defaultGamePlan);
        setTradeRationale(defaultTradeRationale);
    };

    useEffect(() => {
        const hasPositionRef = !!positionId || (!!strategyIdentifier && !!ticker && !!traderId);
        setGamePlan(defaultGamePlan);
        if (isConnected && hasPositionRef) {
            // before requesting new GP, first reset all fields to default
            setGamePlanLoading(true);
            sendGamePlanCommand({
                ...new GetOrCreateGamePlanCommand(),
                PositionId: positionId,
                TraderId: traderId,
                BBGTicker: ticker,
                Strategy: strategyIdentifier
            });
        }
    }, [positionId, strategyIdentifier, ticker, traderId, isConnected]);

    //   VALIDATION
    useEffect(() => {
        const { CurrentPositions = [] } = gamePlan.CurrentPositions;

        if (gamePlan.Locked || readOnly) {
            setGamePlanValidationComments({});
        } else {
            const currentName =
                "Name" in gamePlanCommandsSent ? gamePlanCommandsSent.Name : gamePlan.Name;
            const newComments = {
                ...gamePlanValidationComments,
                Name: getNameValidation(currentName, CurrentPositions, existingGamePlanOptions)
            };
            // remove any undefined
            setGamePlanValidationComments(
                Object.keys(newComments).reduce((acc, field) => {
                    if (newComments[field]) acc[field] = newComments[field];
                    return acc;
                }, {})
            );
        }
    }, [
        JSON.stringify(gamePlanValidationComments),
        gamePlan?.Locked,
        readOnly,
        gamePlan?.CurrentPositions,
        gamePlan?.Name,
        gamePlanCommandsSent,
        existingGamePlanOptions
    ]);

    const updateTradeRationale = useCallback(
        async (command: TradeRationaleCommand) => {
            if (!tradeRationale.Id) {
                return;
            }
            const commandType = command.$type;
            try {
                await sendTradeRationaleCommand(command);

                if (mounted) {
                    setError(undefined);
                }
            } catch (e) {
                if (mounted) {
                    setError({ commandType, message: e.toString() });
                }
            }
        },
        [tradeRationale]
    );
    const getTradeRationale = useCallback(
        async (command: TradeRationaleCommand) => {
            const commandType = command.$type;
            try {
                await sendTradeRationaleCommand(command);

                if (mounted) {
                    setError(undefined);
                }
            } catch (e) {
                if (mounted) {
                    setError({ commandType, message: e.toString() });
                }
            }
        },
        [tradeRationale]
    );

    useEffect(() => {
        /* 
        @TODO - refactor this when we replace the legacy GamePlan logic
        Need a better solution for DRAFT order details - should really be in backend
        Draft orders (in trade ticket) are not available on gamePlan.PreviousOrders
        So, we need to pass the draft order details in from the ticket       
        */
        const order = gamePlan?.PreviousOrders?.find(
            o => o.OrderFormId === orderFormId || o.OmsOrderId === omsOrderId
        );
        if (order) {
            setOrderSummary({
                ticker: order.BBGTicker,
                orderFormId: order.OrderFormId,
                tradeActionType: order.TradeActionType as OrderFormSummary["tradeActionType"],
                buySell: order.BuySell,
                date: order.Date
            });
        } else {
            setOrderSummary(draftOrderSummary);
        }
    }, [gamePlan, draftOrderSummary, orderFormId, omsOrderId]);

    useEffect(() => {
        if (!initialGamePlanId && gamePlan.GamePlanId !== 0) {
            setInitialGamePlanId(gamePlan.GamePlanId);
        }
    }, [gamePlan.GamePlanId, initialGamePlanId]);

    //     GAME PLAN COMMANDS
    const updateGamePlan = useCallback(
        async (command: UpdateGamePlanCommand) => {
            setGamePlanCommandsSent({ ...command });
            const commandType = command.$type;
            try {
                await sendGamePlanCommand({
                    ...command,
                    Id: gamePlan.GamePlanId,
                    Version: gamePlan.Version
                });

                if (mounted) {
                    setError(undefined);
                }
            } catch (e) {
                if (mounted) {
                    setError({ commandType, message: e.toString() });
                }
            }
        },
        [gamePlan.GamePlanId, gamePlan.Version, initialGamePlanId, gamePlanCommandsSent]
    );

    const updateName = useCallback(
        (name: string) => {
            updateGamePlan({ ...new UpdateNameCommand(), Name: name });
        },
        [updateGamePlan]
    );

    const updateThesis = useCallback(
        (thesis: string) => {
            updateGamePlan({ ...new UpdateThesisCommand(), Thesis: thesis });
        },
        [updateGamePlan]
    );

    const updateTheme = useCallback(
        (theme: string) => {
            updateGamePlan({ ...new UpdateThemeCommand(), Theme: theme });
        },
        [updateGamePlan]
    );

    const updateSubStrategy = useCallback(
        (subStrat: string, gamePlanPositionId: number) => {
            const command = { ...new UpdateSubStrategyCommand(), SubStrategy: subStrat };

            command.GamePlanPositionId = gamePlanPositionId;

            updateGamePlan(command);
        },
        [updateGamePlan]
    );
    /* const submitGamePlan = useCallback(() => {
        updateGamePlan({ ...new SubmitGamePlanCommand() });
    }, [updateGamePlan]); */

    const appendThesis = useCallback(
        (thesis: string) => {
            updateGamePlan({ ...new AppendThesisCommand(), Thesis: thesis });
        },
        [updateGamePlan]
    );

    const addConvictionScore = useCallback(
        (convictionScore: number) => {
            updateGamePlan({
                ...new AddNewConvictionScoreCommand(),
                ConvictionScore: convictionScore
            });
        },
        [updateGamePlan]
    );

    const updateSourceOfIdea = useCallback(
        (name: string) => {
            updateGamePlan({
                ...new UpdateSourceOfIdeaCommand(),
                SourceOfIdea: name
            });
        },
        [updateGamePlan]
    );

    const updateInvestmentCategory = useCallback(
        (id: number) => {
            updateGamePlan({
                ...new UpdateInvestmentCategoryCommand(),
                InvestmentCategory: id
            });
        },
        [updateGamePlan]
    );

    const updateHoldingPeriod = useCallback(
        (id: number) => {
            updateGamePlan({
                ...new UpdateHoldingPeriodCommand(),
                HoldingPeriodId: id
            });
        },
        [updateGamePlan]
    );

    const updateHoldingPeriodDays = useCallback(
        (value: number) => {
            updateGamePlan({
                ...new UpdateHoldingPeriodDaysCommand(),
                HoldingPeriodDays: value
            });
        },
        [updateGamePlan]
    );

    const updateTargetSize = useCallback(
        (value: number) => {
            updateGamePlan({
                ...new UpdateTargetSizeCommand(),
                TargetSize: value
            });
        },
        [updateGamePlan]
    );

    const updateTargetType = useCallback(
        (id: number) => {
            updateGamePlan({
                ...new UpdateTargetTypeCommand(),
                TargetType: id
            });
        },
        [updateGamePlan]
    );

    const updateUpsideDownsideType = useCallback(
        (id: number) => {
            updateGamePlan({
                ...new UpdateUpsideDownsideTypeCommand(),
                UpsideDownsideType: id
            });
        },
        [updateGamePlan]
    );

    const updateUpside = useCallback(
        (value: number) => {
            updateGamePlan({
                ...new UpdateUpsideCommand(),
                Upside: value
            });
        },
        [updateGamePlan]
    );

    const updateDownside = useCallback(
        (value: number) => {
            updateGamePlan({
                ...new UpdateDownsideCommand(),
                Downside: value
            });
        },
        [updateGamePlan]
    );

    const addAttachment = useCallback(
        async (file: File) => {
            try {
                const formData = new FormData();
                formData.append("Attachment", file);
                formData.append("GamePlanId", gamePlan.GamePlanId.toString());

                const response = await fetch("/backbone/gameplan/attachments/add-attachment", {
                    method: "POST",
                    body: formData
                });

                const responseObj = await response.json();
                if (responseObj?.GamePlan)
                    onGamePlanUpdate(responseObj?.GamePlan as GamePlanViewModel);
            } catch (e) {
                if (mounted) {
                    setError({
                        commandType:
                            "Firefly.Service.Calculator.GamePlan.Contracts.Commands.AddAttachmentCommand, Firefly.Service.Calculator.GamePlan.Contracts",
                        message: e.toString()
                    });
                }
            }
        },
        [gamePlan]
    );

    useEffect(() => {
        if (!!traderId && !!gamePlan.GamePlanId) {
            // in case where the request fails (or is very slow)
            // make sure to reset the list to default
            setExistingGamePlanOptions(defaultTraderGamePlans);
            setExistingGamesLoading(true);
            updateGamePlan({
                ...new GetTraderGamePlansCommand(),
                TraderId: traderId,
                GamePlanId: gamePlan.GamePlanId
            });
        }
    }, [traderId, gamePlan.GamePlanId]);

    //         TRADE RATIONALE COMMANDS
    const updateInvestmentNotes = useCallback(
        (notes: string) => {
            updateTradeRationale({
                ...new UpdateInvestmentNotesCommand(),
                Id: tradeRationale.Id,
                InvestmentNotes: notes
            });
        },
        [tradeRationale]
    );
    const updateDrivers = useCallback(
        (drivers: number[]) => {
            const existingDrivers = tradeRationale.RankedDrivers.map(d => d.Id);
            if (!isEqual(drivers, existingDrivers)) {
                updateTradeRationale({
                    ...new UpdateDriversCommand(),
                    Id: tradeRationale.Id,
                    Drivers: drivers
                });
            }
        },
        [tradeRationale]
    );
    const updateConviction = useCallback(
        (score: number) => {
            updateTradeRationale({
                ...new UpdateConvictionScoreCommand(),
                Id: tradeRationale.Id,
                ConvictionScore: score
            });
        },
        [tradeRationale]
    );

    const linkToGamePlan = async (newGamePlanId: number | undefined) => {
        try {
            await sendGamePlanCommand({
                ...new LinkToGamePlanCommand(),
                Id: gamePlan.GamePlanId,
                GamePlanIdToLinkTo: newGamePlanId || initialGamePlanId,
                Version: gamePlan.Version,
                BBGTicker: ticker,
                Strategy: strategyIdentifier
            });
        } catch (e) {}
    };

    const onGamePlanUpdate = (updatedGamePlan: GamePlanViewModel) => {
        if (mounted) {
            setGamePlan(updatedGamePlan);
            setGamePlanLoading(false);
        }
    };

    const onTradeRationaleUploaded = (updatedTradeRationale: TradeRationaleViewModel) => {
        if (mounted) {
            setTradeRationale(updatedTradeRationale);
            setTradeRationaleLoading(false);
            setTradeRationaleDisabled(false);
        }
    };

    const onTraderGamePlansLoaded = (traderGamePlans: TraderGamePlansViewModel) => {
        if (mounted) {
            setExistingGamePlanOptions(traderGamePlans);
            setExistingGamesLoading(false);
        }
    };
    const onDisconnect = useCallback((error?: any) => {
        setConnectionState(ConnectionState.Error);
        setError({
            commandType: "GamePlan",
            message: error ? error.toString() : "Connection lost to GamePlan service."
        });
    }, []);

    useEffect(() => {
        (async () => {
            try {
                if (isConnected && gamePlanId) {
                    setGamePlanLoading(true);

                    const handleRequest = <TRequest, TResponse, TReturnType>(
                        request: TRequest,
                        apiWrapper: IBusApiWrapper<TResponse, TRequest>,
                        fallbackData: TReturnType,
                        selectData?: (data: TResponse) => TReturnType
                    ): Promise<TReturnType> => {
                        apiWrapper.cancel();
                        return apiWrapper
                            .invoke(request)
                            .then(d => {
                                const data = selectData
                                    ? selectData(d)
                                    : (d as unknown as TReturnType);
                                return data ?? fallbackData;
                            })
                            .catch(e => {
                                console.error(e);
                                return fallbackData;
                            });
                    };
                    const getGamePlan = createMessageBusRequest<
                        GetGamePlanCommand,
                        GamePlanResponse
                    >(null, "gameplan_by_id");

                    const gameplanAtDateRequest = new GetGamePlanCommand();
                    gameplanAtDateRequest.GamePlanId = gamePlanId;

                    const response = await handleRequest<
                        GetGamePlanCommand,
                        GamePlanResponse,
                        GamePlanViewModel
                    >(gameplanAtDateRequest, getGamePlan, defaultGamePlan, d => d?.GamePlan);
                    setGamePlan(response);
                }
            } catch (ex) {
                console.error(ex);
                setError(ex);
            } finally {
                setGamePlanLoading(false);
            }
        })();
    }, [gamePlanId, isConnected]);

    useEffect(() => {
        GamePlanHub.on("GamePlanUpdated", onGamePlanUpdate);
        GamePlanHub.on("TradeRationaleUpdated", onTradeRationaleUploaded);
        GamePlanHub.on("TraderGamePlansLoaded", onTraderGamePlansLoaded);
        GamePlanHub.onclose(onDisconnect);
        GamePlanHub.onclose(console.log);

        startGamePlanHub()
            .then(() => {
                setConnectionState(ConnectionState.Connected);
            })
            .catch(e => {
                setError({
                    commandType: "startGamePlanHub",
                    message: e.toString()
                });
                setConnectionState(ConnectionState.Error);
            });

        return () => {
            mounted = false;
            setConnectionState(ConnectionState.Error);
        };
    }, []);

    return (
        <GamePlanContext.Provider
            value={{
                error,
                connectionState,
                gamePlan,
                gamePlanValidationComments,
                gamePlanUpdateInProgress,
                gamePlanCommands: {
                    updateName,
                    updateThesis,
                    updateTheme,
                    linkToGamePlan,
                    updateSubStrategy,
                    appendThesis,
                    addAttachment,
                    addConvictionScore,
                    updateSourceOfIdea,
                    updateInvestmentCategory,
                    updateHoldingPeriod,
                    updateHoldingPeriodDays,
                    updateTargetSize,
                    updateTargetType,
                    updateUpsideDownsideType,
                    updateUpside,
                    updateDownside
                },
                // tradeRationale: { TradeRationaleId, Drivers, DriverOptions, InvestmentNotes, ConvictionScore: Conviction },
                tradeRationale,
                tradeRationaleCommands: {
                    updateDrivers,
                    updateInvestmentNotes,
                    updateConviction
                },
                tradeRationaleDisabled,
                existingGamePlanOptions,
                isExistingGamesLoading,
                selectGamePlan,
                isGamePlanLoading,
                isTradeRationaleLoading,
                resetGamePlanAndTradeRationale,
                readOnly,
                positionId,
                strategyIdentifier,
                ticker,
                traderId,
                orderFormId,
                omsOrderId,
                checklistId,
                setChecklistId,
                allowChecklist,
                orderSummary,
                setOrderSummary,
                ...rest
            }}
        >
            {children}
        </GamePlanContext.Provider>
    );
};
/** @deprecated this is the OLD gameplan - soon to be nuked */
export const useGamePlan = () => useContext(GamePlanContext);

const GamePlanProviderWithFeature = withFeatureProvider(GamePlanProviderInternal);
/**
 * @deprecated
 */
export const GamePlanProvider = (props: GamePlanProviderProps & { children: any }) => (
    <GatewayProvider>
        <GamePlanProviderWithFeature {...props} />
    </GatewayProvider>
);
