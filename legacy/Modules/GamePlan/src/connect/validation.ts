import { CurrentPositionViewModel, TraderGamePlansViewModel } from "@firefly-trading-labs/gameplan-api";

export const getNameValidation = (
    name: string,
    positions: CurrentPositionViewModel[],
    existingGamePlanOptions: TraderGamePlansViewModel
): string | undefined => {
    const isLinked = positions.length > 1;
    const existingNames =
        existingGamePlanOptions &&
        existingGamePlanOptions.Active &&
        existingGamePlanOptions.RecentlyClosed
            ? [
                  ...new Set(
                      [
                          ...existingGamePlanOptions.Active,
                          ...existingGamePlanOptions.RecentlyClosed
                      ].map(({ Name }) => Name)
                  )
              ]
                  .filter(name => !!name)
                  .sort()
            : [];
    // Name required ONLY when GP is linked (i.e. CurrentPositions > 1)
    return isLinked && !name
        ? "You MUST enter a name for Game Plans with more than one position."
        : // enforce unique name on ANY game plan (even unlinked GPs)
        !!name && existingNames.includes(name)
        ? "Name must be unique. This name has already been used."
        : undefined;
};
