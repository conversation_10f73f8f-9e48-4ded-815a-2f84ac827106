import { onSideOffSideColour } from "../onSideOffSideColour";
import { buyColor, sellColor } from "../../../../models/colors";

const cases = [
    ["Onside", "Onside", buyColor],
    ["Offside", "Offside", sellColor],
    ["Other string", "string", ""],
    ["Greater than 0", 1, buyColor],
    ["Less than 0", -1, sellColor],
    ["Equals 0", 0, ""]
];

describe("onSideOffSideColour", () => {
    it.each(cases)("should return the correct colour for %s", (_, value, result) => {
        expect(onSideOffSideColour(value)).toEqual(result);
    });
});
