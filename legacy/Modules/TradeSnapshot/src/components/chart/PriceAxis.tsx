import React, { useEffect } from "react";
import { select, NumberValue } from "d3";

import { useTradeSnapshotChart, useTradeSnapshotData } from "../../stores";
import { chartMargin } from "../../models";
import { relativeIndexColor } from "../../models/colors";
import { Axis } from "./Axis";

const priceAxisElementId = "trade-snapshot-price-axis";
const relativeIndexAxisElementId = "trade-snapshot-relative-index-axis";

export const PriceAxis = () => {
    const { width, height, scalesAndAxes } = useTradeSnapshotChart();
    const { data } = useTradeSnapshotData();

    useEffect(() => {
        if (data.length === 0) {
            return;
        }

        const priceAxisX = width - chartMargin.right;
        const priceAxisY = 0;

        select<SVGGElement, NumberValue>(`#${priceAxisElementId}`)
            .attr("transform", `translate(${priceAxisX}, ${priceAxisY})`)
            .transition()
            .attr("transform", `translate(${priceAxisX}, ${priceAxisY})`)
            .call(scalesAndAxes.current.price.priceAxis);

        const relativeIndexAxisX = chartMargin.left;
        const relativeIndexAxisY = 0;

        select<SVGGElement, NumberValue>(`#${relativeIndexAxisElementId}`)
            .attr("transform", `translate(${relativeIndexAxisX}, ${relativeIndexAxisY})`)
            .transition()
            .attr("transform", `translate(${relativeIndexAxisX}, ${relativeIndexAxisY})`)
            .call(scalesAndAxes.current.price.relativeIndexAxis);
    }, [data, width, height, scalesAndAxes]);

    return (
        <>
            <Axis id={priceAxisElementId} />
            <Axis
                id={relativeIndexAxisElementId}
                textColor={relativeIndexColor}
            />
        </>
    );
};
