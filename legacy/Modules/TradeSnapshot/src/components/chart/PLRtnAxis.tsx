import React, { useEffect } from "react";
import { select, NumberValue } from "d3";
import { useTradeSnapshotData, useTradeSnapshotChart, useTradeSnapshotFilters } from "../../stores";
import { chartMargin } from "../../models";
import { Axis } from "./Axis";

const plRtnAxisElementId = "trade-snapshot-pl-rtn-axis";

export const PLRtnAxis = () => {
    const { width, height, scalesAndAxes } = useTradeSnapshotChart();
    const { chartMode } = useTradeSnapshotFilters();
    const { data } = useTradeSnapshotData();

    useEffect(() => {
        if (data.length === 0) {
            return;
        }

        const axisX = chartMargin.left;
        const axisY = 0;
        select<SVGGElement, NumberValue>(`#${plRtnAxisElementId}`)
            .transition()
            .attr("transform", `translate(${axisX}, ${axisY})`)
            .transition()
            .attr("transform", `translate(${axisX}, ${axisY})`)
            .call(scalesAndAxes.current.plRtn.axis);
    }, [width, height, data, chartMode, scalesAndAxes]);

    return (
        <Axis
            id={plRtnAxisElementId}
            textColor="white"
        />
    );
};
