import React, { useEffect } from "react";
import { select, line } from "d3";
import styled from "styled-components";

import { useTradeSnapshotChart, useTradeSnapshotData } from "../../stores";
import { TradeSnapshotDayData } from "../../models";
import { relativeIndexColor } from "../../models/colors";
import { shadowId } from "../../models/elementIds";

const relativeIndexSeriesElementId = "trade-snapshot-relative-index-series";

const StyledRelativeIndexSeriesGroup = styled.g`
    path {
        stroke: ${relativeIndexColor};
        fill: none;
        stroke-width: 1px;
    }
`;

export const RelativeIndexSeries = () => {
    const { width, height, scalesAndAxes } = useTradeSnapshotChart();
    const { data } = useTradeSnapshotData();
    const dateScale = scalesAndAxes.current.date.scale;
    const priceScale = scalesAndAxes.current.price.scale;

    useEffect(() => {
        if (data.length === 0) {
            return;
        }

        const filteredData = data.filter(d => d.relativeIndexAdjustedClose !== 0);

        const relativeLineFn = line<TradeSnapshotDayData>()
            .x((_, i) => dateScale(i))
            .y(d => priceScale(d.relativeIndexAdjustedClose));

        select(`#${relativeIndexSeriesElementId}`)
            .select("path")
            .transition()
            .attr("d", relativeLineFn(filteredData));
    }, [width, height, data, dateScale, priceScale]);

    return (
        <StyledRelativeIndexSeriesGroup id={relativeIndexSeriesElementId}>
            <path filter={`url(#${shadowId})`} />
        </StyledRelativeIndexSeriesGroup>
    );
};
