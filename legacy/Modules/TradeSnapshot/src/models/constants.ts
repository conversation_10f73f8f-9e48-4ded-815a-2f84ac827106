import { DropdownOption } from "@reddeer/firefly-shared-controls";
import { TradeSnapshotEventType } from "@firefly-trading-labs/raiddata-api";

export const panelPadding = 10;
export const chartFiltersHeight = 28;
export const chartGridGap = 16;
export const chartPaddingTop = 10;
export const eventOptionItems: DropdownOption[] = [
    {
        label: "Catalysts",
        value: TradeSnapshotEventType.Catalysts,
        preSelected: true
    },
    {
        label: "Profit Target / Stop Loss",
        value: TradeSnapshotEventType.LevelBreaches
    },
    {
        label: "Exposure Alerts",
        value: TradeSnapshotEventType.RiskAlerts
    },
    {
        label: "Calls",
        value: TradeSnapshotEventType.Calls
    },
    {
        label: "Corporate Actions",
        value: TradeSnapshotEventType.CorporateActions
    },
    {
        label: "Meetings",
        value: TradeSnapshotEventType.Meetings
    }
];
