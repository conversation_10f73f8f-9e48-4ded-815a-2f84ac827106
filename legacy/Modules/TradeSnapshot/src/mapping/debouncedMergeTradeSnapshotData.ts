import { debounce } from "lodash";
import {
    MergedTradeSnapshotData,
    mergeTradeSnapshotData,
    MergeTradeSnapshotDataParameters
} from "./mergeTradeSnapshotData";

export const debouncedMergeTradeSnapshotData = debounce(
    (
        parameters: MergeTradeSnapshotDataParameters,
        onMerged: (data: MergedTradeSnapshotData) => void
    ) => {
        const result = mergeTradeSnapshotData(parameters);
        onMerged(result);
    },
    300
);
