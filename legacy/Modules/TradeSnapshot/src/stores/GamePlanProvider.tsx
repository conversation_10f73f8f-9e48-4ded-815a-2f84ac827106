import React, {
    useEffect,
    useRef,
    useState,
    useCallback,
    createContext,
    useContext,
    useMemo
} from "react";
import moment from "moment";
import {
    GamePlanAndSubstrategyDto,
    GamePlanByPositionIdRequest,
    GamePlanByPositionIdResponse,
    GamePlanResponse,
    GamePlanViewModel,
    GetGamePlanCommand
} from "@firefly-trading-labs/gameplan-api";

import { toLocalDateTime, useLoading } from "@reddeer/firefly-shared-controls";
import { getStartOfDayTimestamp } from "../mapping";
import { getGamePlan, getGamePlansByPositionId, handleRequest } from "../api";
import type {
    GamePlanAndSubstrategyWithPosition,
    GamePlanData,
    GamePlanViewModelAtDate,
    GamePlanViewModelWithPositionId,
    ProviderProps
} from "../models/stores";
import { useTradeSnapshotFilters } from "./TradeSnapshotFiltersProvider";
import { useTradeSnapshotData } from "./TradeSnapshotDataProvider";

export const GamePlanDataContext = createContext<GamePlanData>({} as GamePlanData);

export const GamePlanProvider = ({ children }: ProviderProps) => {
    const { positions } = useTradeSnapshotData();
    const { updateHighlightedDateRange } = useTradeSnapshotFilters();
    const [loading, updateLoading] = useLoading();
    const cachedGamePlans = useRef<Record<number, GamePlanViewModelWithPositionId>>({});
    const [gamePlanList, setGamePlanList] = useState<GamePlanAndSubstrategyWithPosition[]>([]);
    const [selectedGamePlan, setSelectedGamePlan] =
        useState<GamePlanViewModelWithPositionId>(undefined);

    const [selectedGamePlanAtDate, setSelectedGamePlanAtDate] =
        useState<GamePlanViewModelAtDate>(undefined);

    useEffect(() => {
        if (positions.length === 0) {
            setGamePlanList([]);
            return;
        }

        const gamePlansRequest: GamePlanByPositionIdRequest = {
            ...new GamePlanByPositionIdRequest(),
            PositionIds: positions.map(p => p.positionId)
        };

        updateLoading(
            handleRequest<
                GamePlanByPositionIdRequest,
                GamePlanByPositionIdResponse,
                GamePlanAndSubstrategyDto[]
            >(gamePlansRequest, getGamePlansByPositionId, [], d => d.GamePlanDetails).then(d => {
                const mapped: GamePlanAndSubstrategyWithPosition[] = d.map(g => {
                    const position = positions.find(p => p.positionId === g.PositionId);
                    return {
                        ...g,
                        asAtDateCache: {},
                        startDateOnChart: position.chartStartDate,
                        endDateOnChart: position.chartEndDate,
                        opened: position.opened,
                        closed: position.closed
                    };
                });

                setGamePlanList(mapped);
            })
        );
    }, [positions, updateLoading]);

    useEffect(() => {
        if (selectedGamePlan) {
            const matchingGamePlan = positions.find(
                p => p.positionId === selectedGamePlan.positionId
            );
            if (!matchingGamePlan) {
                setSelectedGamePlan(undefined);
            }
        }

        if (selectedGamePlanAtDate) {
            const matchingGamePlan = gamePlanList.find(
                g => g.GamePlanId === selectedGamePlanAtDate.GamePlanId
            );

            if (!matchingGamePlan) {
                setSelectedGamePlanAtDate(undefined);
            }
        }
    }, [positions, selectedGamePlan, selectedGamePlanAtDate, gamePlanList]);

    const selectGamePlanAtDate = useCallback(
        async (date: Date) => {
            if (loading || gamePlanList.length === 0) {
                return;
            }

            const dateAsMoment = moment(date);
            const matchingGamePlan = gamePlanList.find(gp => {
                const startMoment = moment(gp.startDateOnChart);
                const endMoment = moment(gp.endDateOnChart);

                return (
                    dateAsMoment.isSameOrAfter(startMoment) &&
                    dateAsMoment.isSameOrBefore(endMoment)
                );
            });

            if (!matchingGamePlan) {
                return;
            }

            const dateTimeStamp = getStartOfDayTimestamp(date);
            if (matchingGamePlan.asAtDateCache[dateTimeStamp]) {
                setSelectedGamePlanAtDate({
                    ...matchingGamePlan.asAtDateCache[dateTimeStamp],
                    date
                });
                return;
            }

            const gameplanAtDateRequest = new GetGamePlanCommand();
            gameplanAtDateRequest.AsAtDate = toLocalDateTime(date);
            gameplanAtDateRequest.GamePlanId = matchingGamePlan.GamePlanId;

            const loadPromise = handleRequest<
                GetGamePlanCommand,
                GamePlanResponse,
                GamePlanViewModel
            >(gameplanAtDateRequest, getGamePlan, null, d => d.GamePlan).then(d => {
                setSelectedGamePlanAtDate({
                    ...d,
                    date
                });
                matchingGamePlan.asAtDateCache[dateTimeStamp] = d;
            });

            updateLoading(loadPromise);
        },
        [gamePlanList, updateLoading, loading]
    );

    const selectGamePlan = useCallback(
        (gp: GamePlanAndSubstrategyWithPosition) => {
            const { GamePlanId, startDateOnChart, endDateOnChart } = gp;
            if (cachedGamePlans.current[GamePlanId]) {
                setSelectedGamePlan(cachedGamePlans.current[GamePlanId]);
                return;
            }

            const getGamePlanRequest: GetGamePlanCommand = {
                ...new GetGamePlanCommand(),
                GamePlanId
            };

            updateLoading(
                handleRequest<GetGamePlanCommand, GamePlanResponse, GamePlanViewModel>(
                    getGamePlanRequest,
                    getGamePlan,
                    null,
                    d => d.GamePlan
                ).then(d => {
                    if (d) {
                        updateHighlightedDateRange(startDateOnChart, endDateOnChart);
                        const gamePlanViewPositionId: GamePlanViewModelWithPositionId = {
                            ...d,
                            positionId: gp.PositionId
                        };

                        setSelectedGamePlan(gamePlanViewPositionId);
                        cachedGamePlans.current[GamePlanId] = gamePlanViewPositionId;
                    }
                })
            );
        },
        [updateLoading, updateHighlightedDateRange]
    );

    const clearGamePlan = useCallback(() => {
        setSelectedGamePlan(undefined);
    }, []);

    const clearGamePlanAtDate = useCallback(() => {
        setSelectedGamePlanAtDate(undefined);
    }, []);

    const values = useMemo(
        () => ({
            gamePlans: gamePlanList,
            loading,
            selectGamePlan,
            selectGamePlanAtDate,
            clearGamePlan,
            clearGamePlanAtDate,
            selectedGamePlan,
            selectedGamePlanAtDate
        }),
        [
            clearGamePlan,
            clearGamePlanAtDate,
            gamePlanList,
            loading,
            selectGamePlan,
            selectGamePlanAtDate,
            selectedGamePlan,
            selectedGamePlanAtDate
        ]
    );

    return <GamePlanDataContext.Provider value={values}>{children}</GamePlanDataContext.Provider>;
};

export const useGamePlanData = (): GamePlanData => useContext(GamePlanDataContext);
