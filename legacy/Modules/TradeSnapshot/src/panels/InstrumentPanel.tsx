import React from "react";
import { TickerSelect } from "@reddeer/firefly-shared-controls";
import { Panel } from "PrimitiveComponents/index";
import { InstrumentDetails } from "../components/instrument";
import { useTradeSnapshotInstruments } from "../stores";

export type InstrumentPanelProps = {
    disableTickerSelect?: boolean;
};
export const InstrumentPanel = ({ disableTickerSelect }: InstrumentPanelProps) => {
    const { instrument, updateInstrument } = useTradeSnapshotInstruments();

    return (
        <Panel
            title="Stock Info"
            padding="large"
        >
            {!disableTickerSelect && (
                <TickerSelect
                    instrument={instrument}
                    updateInstrument={updateInstrument}
                    showSelect
                />
            )}
            <InstrumentDetails />
        </Panel>
    );
};
