import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import { ThemeIterator, LayoutHorizontal, ThemeContainer } from "PrimitiveComponents/index";

import { InvokeApiGatewayUploadButton } from "./InvokeApiGatewayUploadButton";

<Meta title="ComponentLibrary/Components/ApiGateway/InvokeApiGatewayUploadButton" component={InvokeApiGatewayUploadButton} />

export const Template = args => (
    <LayoutHorizontal spacing="large">
        <ThemeIterator>
            {() => (
                <ThemeContainer>
                    <InvokeApiGatewayUploadButton {...args}>
                        Upload a file
                    </InvokeApiGatewayUploadButton>
                </ThemeContainer>
            )}
        </ThemeIterator>
    </LayoutHorizontal>
);

# InvokeApiGatewayUploadButton

<Description of={InvokeApiGatewayUploadButton} />

See also [useInvokeApiGatewayCommand](?path=/docs/transportgateway_hooks-useinvokeapigatewaycommand--page).

<Canvas>
    <Story
        name="InvokeApiGatewayUploadButton"
        parameters={{
            controls: {
                include: ["multiple", "size", "disabled", "type"]
            }
        }}
        args={{
            size: "default",
            disabled: false,
            multiple: false
        }}
        argTypes={{
            progressPercent: {
                control: {
                    type: "range",
                    min: 0,
                    max: 100,
                    step: 1
                }
            },
        }}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes of={InvokeApiGatewayUploadButton} sort="requiredFirst" />

### Example

In this example, files would be uploaded to an end-point `/gateway/api/test/upload`.

```tsx
import React from "react";

import { LayoutHorizontal } from "PrimitiveComponents/index";
import { InvokeApiGatewayUploadButton } from "ComponentLibrary/index";

const MyComponent = () => (
    <LayoutHorizontal>
        <InvokeApiGatewayUploadButton
            commandName="test.upload"
            multiple
            onError={error => console.error("Completed with error:", error)}
            onComplete={response => console.log("Success:", response)}
        >
            Upload files
        </InvokeApiGatewayUploadButton>

        <div>Other options</div>
    </LayoutHorizontal>
);
```

