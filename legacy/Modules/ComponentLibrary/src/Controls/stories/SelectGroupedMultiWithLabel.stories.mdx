import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import {
    ThemeIterator,
    LayoutGrid,
    ThemeContainer,
    LayoutVertical
} from "PrimitiveComponents/index";
import { withFixtures } from "TransportGateway/index";
import { SelectGroupedMultiWithLabel } from "../SelectGroupedMultiWithLabel";

<Meta title="ComponentLibrary/Controls/SelectGroupedMultiWithLabel" component={SelectGroupedMultiWithLabel} />

export const SelectGroupedMultiWithLabelMock = withFixtures(SelectGroupedMultiWithLabel, {});

export const Template = args => (
    <LayoutGrid columns={2} gap="xx-large">
        <ThemeIterator>
            {theme => (
                <LayoutVertical spacing="xx-large">
                    <ThemeContainer>
                        <SelectGroupedMultiWithLabelMock
                            value={theme}
                            {...args}
                            onChange={() => {}}
                        />
                    </ThemeContainer>
                    <ThemeContainer>
                        <SelectGroupedMultiWithLabelMock
                            label="Control Label"
                            value={theme}
                            {...args}
                            onChange={() => {}}
                        />
                    </ThemeContainer>
                    <ThemeContainer>
                        <SelectGroupedMultiWithLabelMock
                            required
                            label="Control Label"
                            value={theme}
                            {...args}
                            onChange={() => {}}
                        />
                    </ThemeContainer>
                    <ThemeContainer>
                        <SelectGroupedMultiWithLabelMock
                            error="Some error"
                            label="Control Label"
                            value={theme}
                            {...args}
                            onChange={() => {}}
                        />
                    </ThemeContainer>
                    <ThemeContainer>
                        <SelectGroupedMultiWithLabelMock
                            disabled
                            value={theme}
                            {...args}
                            onChange={() => {}}
                        />
                    </ThemeContainer>
                    <ThemeContainer>
                        <SelectGroupedMultiWithLabelMock
                            disabled
                            label="Control Label (disabled)"
                            value={theme}
                            {...args}
                            onChange={() => {}}
                        />
                    </ThemeContainer>
                </LayoutVertical>
            )}
        </ThemeIterator>
    </LayoutGrid>
);

# SelectGroupedMultiWithLabel

<Description of={SelectGroupedMultiWithLabel} />

<Canvas>
    <Story
        name="SelectGroupedMultiWithLabel"
        parameters={{
            controls: {
                disable: false,
                include: ["value"]
            }
        }}
        args={{
            value: ""
        }}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes of={SelectGroupedMultiWithLabel} sort="requiredFirst" />
