import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import {
    ThemeIterator,
    LayoutGrid,
    ThemeContainer,
    LayoutVertical
} from "PrimitiveComponents/index";
import { withFixtures } from "TransportGateway/index";
import { SelectBasketTypeWithLabel } from "../SelectBasketTypeWithLabel";

<Meta title="ComponentLibrary/Controls/SelectBasketTypeWithLabel" component={SelectBasketTypeWithLabel} />

export const SelectBasketTypeWithLabelMock = withFixtures(SelectBasketTypeWithLabel, {});

export const Template = args => (
    <LayoutGrid columns={2} gap="xx-large">
        <ThemeIterator>
            {theme => (
                <LayoutVertical spacing="xx-large">
                    <ThemeContainer>
                        <SelectBasketTypeWithLabelMock
                            value={theme}
                            {...args}
                            onChange={() => {}}
                        />
                    </ThemeContainer>
                    <ThemeContainer>
                        <SelectBasketTypeWithLabelMock
                            label="Control Label"
                            value={theme}
                            {...args}
                            onChange={() => {}}
                        />
                    </ThemeContainer>
                    <ThemeContainer>
                        <SelectBasketTypeWithLabelMock
                            required
                            label="Control Label"
                            value={theme}
                            {...args}
                            onChange={() => {}}
                        />
                    </ThemeContainer>
                    <ThemeContainer>
                        <SelectBasketTypeWithLabelMock
                            error="Some error"
                            label="Control Label"
                            value={theme}
                            {...args}
                            onChange={() => {}}
                        />
                    </ThemeContainer>
                    <ThemeContainer>
                        <SelectBasketTypeWithLabelMock
                            disabled
                            value={theme}
                            {...args}
                            onChange={() => {}}
                        />
                    </ThemeContainer>
                    <ThemeContainer>
                        <SelectBasketTypeWithLabelMock
                            disabled
                            label="Control Label (disabled)"
                            value={theme}
                            {...args}
                            onChange={() => {}}
                        />
                    </ThemeContainer>
                </LayoutVertical>
            )}
        </ThemeIterator>
    </LayoutGrid>
);

# SelectBasketTypeWithLabel

<Description of={SelectBasketTypeWithLabel} />

<Canvas>
    <Story
        name="SelectBasketTypeWithLabel"
        parameters={{
            controls: {
                disable: false,
                include: ["value"]
            }
        }}
        args={{
            value: ""
        }}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes of={SelectBasketTypeWithLabel} sort="requiredFirst" />
