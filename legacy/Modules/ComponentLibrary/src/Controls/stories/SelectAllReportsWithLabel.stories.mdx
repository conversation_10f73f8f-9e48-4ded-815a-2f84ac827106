import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import {
    ThemeIterator,
    LayoutGrid,
    ThemeContainer,
    LayoutVertical
} from "PrimitiveComponents/index";
import { withFixtures } from "TransportGateway/index";
import { SelectAllReportsWithLabel } from "../SelectAllReportsWithLabel";

<Meta title="ComponentLibrary/Controls/SelectAllReportsWithLabel" component={SelectAllReportsWithLabel} />

export const SelectAllReportsWithLabelMock = withFixtures(SelectAllReportsWithLabel, {});

export const Template = args => (
    <LayoutGrid columns={2} gap="xx-large">
        <ThemeIterator>
            {theme => (
                <LayoutVertical spacing="xx-large">
                    <ThemeContainer>
                        <SelectAllReportsWithLabelMock
                            value={theme}
                            {...args}
                            onChange={() => {}}
                        />
                    </ThemeContainer>
                    <ThemeContainer>
                        <SelectAllReportsWithLabelMock
                            label="Control Label"
                            value={theme}
                            {...args}
                            onChange={() => {}}
                        />
                    </ThemeContainer>
                    <ThemeContainer>
                        <SelectAllReportsWithLabelMock
                            required
                            label="Control Label"
                            value={theme}
                            {...args}
                            onChange={() => {}}
                        />
                    </ThemeContainer>
                    <ThemeContainer>
                        <SelectAllReportsWithLabelMock
                            error="Some error"
                            label="Control Label"
                            value={theme}
                            {...args}
                            onChange={() => {}}
                        />
                    </ThemeContainer>
                    <ThemeContainer>
                        <SelectAllReportsWithLabelMock
                            disabled
                            value={theme}
                            {...args}
                            onChange={() => {}}
                        />
                    </ThemeContainer>
                    <ThemeContainer>
                        <SelectAllReportsWithLabelMock
                            disabled
                            label="Control Label (disabled)"
                            value={theme}
                            {...args}
                            onChange={() => {}}
                        />
                    </ThemeContainer>
                </LayoutVertical>
            )}
        </ThemeIterator>
    </LayoutGrid>
);

# SelectAllReportsWithLabel

<Description of={SelectAllReportsWithLabel} />

<Canvas>
    <Story
        name="SelectAllReportsWithLabel"
        parameters={{
            controls: {
                disable: false,
                include: ["value"]
            }
        }}
        args={{
            value: "Report"
        }}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes of={SelectAllReportsWithLabel} sort="requiredFirst" />
