import React, {
    ComponentProps,
    ComponentType,
    forwardRef,
    ReactNode,
    useMemo,
    useRef
} from "react";
import styled from "styled-components";
import { Dropdown, message } from "antd";
import {
    useClickOutside,
    ButtonBadge,
    getContainer,
    useStateIfMounted,
    SimpleLoader
} from "PrimitiveComponents/index";
import { useViewserverApi, ViewserverApi, ViewserverApiMethod } from "TransportGateway/index";
import type { Schema } from "Contracts/index";
import { createColumnDefFromSchema, getKeyColumn } from "../Components/Grid";

message.config({
    getContainer
});

export type InvokeViewserverCommandButtonFormProps<Model extends {}> = {
    onUpdate: (data: Model) => Promise<boolean> | undefined | void;
    data?: Model;
};

export type InvokeViewserverCommandButtonProps<
    Data,
    Model,
    D,
    ApiMethod extends ViewserverApiMethod<D>,
    P extends InvokeViewserverCommandButtonFormProps<Model>
> = {
    data?: Data;
    schema?: Schema;
    FormComponent?: ComponentType<P>;
    formComponentProps?:
        | Omit<P, "onUpdate">
        | ((params: {
              data: any[];
              schema: Schema;
              identifierName: string;
          }) => Omit<P, "onUpdate">);
    editableMetadataColumnName: string;
    label?: ReactNode;
    successMessage?: ReactNode;
    overlayProps?: ComponentProps<typeof Dropdown>;
    buttonProps?: ComponentProps<typeof ButtonBadge>;
    getApi: (api: ViewserverApi) => ApiMethod;
    getPayload: (rows: any[], identifierName: string, data: Model) => D;
    onComplete?: (model: Model) => void;
};

type OverlayProps = {
    className?: string;
    children: ReactNode;
};

const Container = styled.div`
    && {
        min-width: 300px;
        border: 1px solid #343a49;
        border-radius: 3px;
        padding: 0.5rem;
        font: inherit;
        font-size: 12px;
        line-height: inherit;
    }
`;
const OverlayMenu = forwardRef<HTMLDivElement, OverlayProps>(({ className, children }, ref) => (
    <Container
        className={`ant-menu ${className}`}
        ref={ref}
    >
        {children}
    </Container>
));

export const InvokeViewserverCommandButton = <
    Data,
    Model,
    D,
    ApiMethod extends ViewserverApiMethod<D>,
    P extends InvokeViewserverCommandButtonFormProps<Model>
>({
    data,
    schema,
    FormComponent,
    editableMetadataColumnName,
    label,
    successMessage = "Sent",
    formComponentProps,
    overlayProps,
    buttonProps,
    getApi,
    getPayload,
    onComplete
}: InvokeViewserverCommandButtonProps<Data, Model, D, ApiMethod, P>) => {
    const overlayContainer = useRef();
    const [visible, setVisible] = useStateIfMounted(false);
    const handleClickOutside = useClickOutside(overlayContainer);
    const [processing, setProcessing] = useStateIfMounted(false);
    const [error, setError] = useStateIfMounted<string>(undefined);

    // @TODO: this should directly use the schema instead of convertng to a grid ColDef
    const columns = useMemo(() => schema?.map(createColumnDefFromSchema), [schema]);
    const identifierName = useMemo(() => getKeyColumn(columns)?.field, [columns]);

    const viewserverApi = useViewserverApi();

    const clearError = () => {
        if (!processing && error) {
            setError(undefined);
        }
    };

    handleClickOutside(() => {
        clearError();
        setVisible(false);
    });

    const handleToggleOverlay = e => {
        e.nativeEvent.stopImmediatePropagation();
        setVisible(prevState => !prevState);
        clearError();
    };

    const handleCommit = async (model?: Model) => {
        setError(undefined);
        setProcessing(true);
        try {
            const metadataColumn = columns?.find(
                ({ field }) => field === editableMetadataColumnName
            );

            await getApi(viewserverApi)(
                getPayload(Array.isArray(data) ? data : [data], identifierName, model),
                metadataColumn?.editableMetadata
            );

            if (successMessage) {
                message.info(successMessage);
            }

            setVisible(false);
            if (onComplete) onComplete(model);
            return true;
        } catch (ex) {
            setError(ex.message);
            throw ex;
        } finally {
            setProcessing(false);
        }
    };

    const button = (
        <SimpleLoader
            title={error || ""}
            isLoading={processing && !error}
            hasError={!!error}
        >
            <ButtonBadge
                {...buttonProps}
                badge={Array.isArray(data) ? data?.length : 0}
                disabled={!data || !!(Array.isArray(data) && data?.length === 0) || processing}
                onClick={FormComponent ? handleToggleOverlay : () => handleCommit()}
            >
                {label || "Send"}
            </ButtonBadge>
        </SimpleLoader>
    );

    if (!FormComponent) {
        return button;
    }

    const formComponentResolvedProps =
        (visible &&
            (typeof formComponentProps === "function"
                ? formComponentProps({
                      data: (data && Array.isArray(data) ? data : [data]) || [],
                      schema: schema || [],
                      identifierName
                  })
                : formComponentProps)) ||
        undefined;

    return (
        <Dropdown
            overlayStyle={{ zIndex: 1000 }}
            placement="bottomRight"
            {...overlayProps}
            getPopupContainer={getContainer}
            overlay={
                <OverlayMenu ref={overlayContainer}>
                    <FormComponent
                        {...(formComponentResolvedProps as P)}
                        onUpdate={handleCommit}
                    />
                </OverlayMenu>
            }
            visible={visible}
            {...{ destroyPopupOnHide: true }} // antd does not forward this prop in it's types... :(
        >
            <div>{button}</div>
        </Dropdown>
    );
};
