import {
    apiGatewaySpec,
    withCommandStateFactory,
    getCommandName,
    createConfig,
    reportSubscriptionCommandSpec,
    requestResponseCommandSpec,
    subscriptionCommandSpec,
} from "TransportGateway/index";

import type { ApiGatewayService } from "Contracts/index";
import { getDuration } from "Utilities/index";
import { MarketToneType } from "@tradinglabs/contracts";

import { ComponentDataBaseProps, withComponentData } from "../../ComponentData";
import { SummaryChart as SummaryChartBase, SummaryChartParams } from "../SummaryChart";

const DEFAULT_DATE_FRAME = 6;
const CURRENT_TRADERS_DATE_FRAME = 0;

const traderWithExposureCommandName = getCommandName("Exposure")(
    "GetTradersWithExposureRequest",
    "GetTradersWithExposureResponse",
);
const traderCommandName = getCommandName("RaidData")("GetTradersRequest", "GetTradersResponse");

type Params = {
    identifier: ComponentDataBaseProps["identifier"];
    params?: ComponentDataBaseProps["params"];
    parameterValues?: ComponentDataBaseProps["parameterValues"];
    seriesAll?: Record<string, unknown>[];
    marketTone?: Record<string, unknown>[];
};

type RiskAttributionParams = Params & {
    displayRiskAttribution?: boolean;
};

type SummaryChartProps = SummaryChartParams & {
    displayCurrentTraders?: boolean;
    seriesAll?: Record<string, unknown>[];
    marketTone?: Record<string, unknown>[];
};

export const SummaryChart = withComponentData(
    withCommandStateFactory(
        SummaryChartBase,
        ({
            reportKeySummaryChart = "SummaryChartRequestHandler",
            reportKeySummaryChartMarket = "SummaryChartRequestHandler_Market",
            reportKeySummaryChartRelative = "SummaryChartRequestHandler_Relative",
            reportKeyGeneralTone = "GeneralTone",
            reportKeyRiskAttribution = "RiskAttribution_card",
            displayCurrentTraders,
        }: SummaryChartProps) =>
            createConfig({
                seriesAll: reportSubscriptionCommandSpec(
                    reportKeySummaryChart,
                    ({ identifier, params, parameterValues, seriesAll }: Params) => {
                        if (seriesAll) return undefined;
                        return (
                            identifier && {
                                bbgTicker: identifier,
                                dateFrame: parameterValues?.DateFrame || DEFAULT_DATE_FRAME,
                                traderId: parameterValues?.TraderExposure || -1,
                                isDivAdjusted: true,
                                ...params,
                            }
                        );
                    },
                    data => data,
                ),
                market: reportSubscriptionCommandSpec(
                    reportKeySummaryChartMarket,
                    ({ identifier, params, parameterValues }: Params) =>
                        identifier && {
                            bbgTicker: identifier,
                            dateFrame: parameterValues?.DateFrame || DEFAULT_DATE_FRAME,
                            traderId: parameterValues?.TraderExposure || -1,
                            isDivAdjusted: true,
                            ...params,
                        },
                    data => data,
                ),
                relative: reportSubscriptionCommandSpec(
                    reportKeySummaryChartRelative,
                    ({ identifier, params, parameterValues }: Params) =>
                        identifier && {
                            bbgTicker: identifier,
                            dateFrame: parameterValues?.DateFrame || DEFAULT_DATE_FRAME,
                            traderId: parameterValues?.TraderExposure || -1,
                            isDivAdjusted: true,
                            ...params,
                        },
                    data => data,
                ),
                rsiData: reportSubscriptionCommandSpec(
                    "SummaryChartRequestHandler_Rsi",
                    ({ identifier, parameterValues }: Params) => ({
                        bbgTicker: identifier,
                        dateFrame: 0,
                        isDivAdjusted: true,
                    }),
                    data => data,
                ),
                specialDates: requestResponseCommandSpec(
                    "RaidData_GetEventRequestDto_IEnumerable[CompleteEventDataDto]_RequestResponse_Handler",
                    ({ identifier, params, parameterValues }: Params) =>
                        identifier && {
                            bbgTicker: identifier,
                            dateFrame: parameterValues?.DateFrame || DEFAULT_DATE_FRAME,
                            eventTypes: [1, 2, 3, 13, 114],
                            ...params,
                        },
                    data => data,
                ),
                traders: subscriptionCommandSpec(
                    traderWithExposureCommandName,
                    ({ identifier, params, parameterValues }: Params) =>
                        identifier && {
                            bbgTickers: [identifier],
                            dateFrame: parameterValues?.DateFrame || DEFAULT_DATE_FRAME,
                            ...params,
                        },
                ),
                currentTraders: displayCurrentTraders
                    ? subscriptionCommandSpec(
                          traderWithExposureCommandName,
                          ({ identifier, params }: Params) =>
                              identifier && {
                                  bbgTickers: [identifier],
                                  dateFrame: CURRENT_TRADERS_DATE_FRAME,
                                  ...params,
                              },
                      )
                    : undefined,
                allTraders: subscriptionCommandSpec(traderCommandName),
                marketTone: reportSubscriptionCommandSpec(
                    reportKeyGeneralTone,
                    ({ identifier, params, parameterValues, marketTone }: Params) => {
                        if (marketTone) return undefined;
                        return (
                            identifier && {
                                dateFrame: parameterValues?.DateFrame || DEFAULT_DATE_FRAME,
                                ...params,
                            }
                        );
                    },
                    data => data,
                ),
                riskAttribution: reportSubscriptionCommandSpec(
                    reportKeyRiskAttribution,
                    ({
                        identifier,
                        params,
                        parameterValues,
                        displayRiskAttribution,
                    }: RiskAttributionParams) =>
                        displayRiskAttribution &&
                        identifier && {
                            BbgTicker: identifier,
                            dateFrame: parameterValues?.DateFrame || DEFAULT_DATE_FRAME,
                            ...params,
                        },
                    data => data,
                ),
            }),
    ),
);

export const SummaryChartGateway = withComponentData(
    withCommandStateFactory(SummaryChartBase, () =>
        createConfig({
            seriesAll: apiGatewaySpec<
                ApiGatewayService.CompanyPage.SummaryChartSeriesRequest,
                ApiGatewayService.CompanyPage.SummaryChartSeriesDataResponse
            >(
                "companypage.summary-chart-series",
                ({ bbgTicker }) =>
                    bbgTicker && {
                        bbgTicker,
                        dateFrame: DEFAULT_DATE_FRAME,
                        traderId: -1,
                        isDivAdjusted: true,
                    },
                data => data,
            ),
            market: apiGatewaySpec<
                ApiGatewayService.CompanyPage.SummaryChartMarketDataRequest,
                ApiGatewayService.CompanyPage.SummaryChartMarketDataResponse
            >(
                "companypage.summary-chart-market",
                ({ bbgTicker }) =>
                    bbgTicker && {
                        bbgTicker,
                        dateFrame: DEFAULT_DATE_FRAME,
                    },
                data => data,
            ),
            relative: apiGatewaySpec<
                ApiGatewayService.CompanyPage.SummaryChartRelativeDataRequest,
                ApiGatewayService.CompanyPage.SummaryChartRelativeDataResponse
            >(
                "companypage.summary-chart-relative",
                ({ bbgTicker }) =>
                    bbgTicker && {
                        bbgTicker,
                        dateFrame: DEFAULT_DATE_FRAME,
                    },
                data => data,
            ),
            rsiData: apiGatewaySpec<
                ApiGatewayService.CompanyPage.SummaryChartRsiRequest,
                ApiGatewayService.CompanyPage.SummaryChartRsiDataResponse
            >(
                "companypage.summary-chart-rsi",
                ({ bbgTicker }) =>
                    bbgTicker && {
                        bbgTicker,
                        isDivAdjusted: true,
                    },
                data => data,
            ),
            specialDates: apiGatewaySpec<
                ApiGatewayService.CompanyPage.SummaryChartEventsRequest,
                ApiGatewayService.CompanyPage.SummaryChartEventsDataResponse
            >(
                "companypage.summary-chart-events",
                ({ bbgTicker }) =>
                    bbgTicker && {
                        bbgTicker,
                        dateFrame: DEFAULT_DATE_FRAME,
                    },
                data => data,
            ),
            allTraders: apiGatewaySpec<
                ApiGatewayService.CompanyPage.SummaryChartTradersRequest,
                ApiGatewayService.CompanyPage.SummaryChartTradersDataResponse
            >(
                "companypage.summary-chart-traders",
                ({ bbgTicker }) =>
                    bbgTicker && {
                        bbgTicker,
                        dateFrame: DEFAULT_DATE_FRAME,
                    },
                data => data?.length && data[0].allTraders,
            ),
            traders: apiGatewaySpec<
                ApiGatewayService.CompanyPage.SummaryChartTradersRequest,
                ApiGatewayService.CompanyPage.SummaryChartTradersDataResponse
            >(
                "companypage.summary-chart-traders",
                ({ bbgTicker, dateFrame = DEFAULT_DATE_FRAME, fundGroupId }) =>
                    bbgTicker && {
                        bbgTicker,
                        dateFrame,
                        fundGroupId,
                    },
                data => data?.length && data[0].tradersWithHistoricalExposure,
            ),
            marketTone: apiGatewaySpec<
                Omit<ApiGatewayService.CompanyPage.MarketToneRequest, "toneType">,
                ApiGatewayService.CompanyPage.ToneResponse
            >(
                "companypage.market-tone",
                ({ bbgTicker }: { bbgTicker: string }) =>
                    bbgTicker && {
                        bbgTicker,
                        dateFrame: DEFAULT_DATE_FRAME,
                        toneType: MarketToneType.market,
                    },
                data => data,
                {
                    longPolling: true,
                    longPollingDelayMS: getDuration("MINUTE"),
                },
            ),
        }),
    ),
);
