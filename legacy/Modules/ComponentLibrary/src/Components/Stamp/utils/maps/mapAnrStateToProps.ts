import { anrDefaultProps } from "./consts";

export const mapAnrStateToProps = data => {
    if (!data || data.length < 4) return anrDefaultProps;

    const today = data.find(d => d.header === "Today") ?? {};
    const ago1w = data.find(d => d.header === "1 Week Ago") ?? {};
    const monthAgo = data.find(d => d.header === "1 Month Ago") ?? {};
    const props = {
        score: today.meanRec,
        anrBars: [
            { title: "Today", buys: today.buys, holds: today.holds, sells: today.sells },
            { title: "1w ago", buys: ago1w.buys, holds: ago1w.holds, sells: ago1w.sells },
            { title: "1m ago", buys: monthAgo.buys, holds: monthAgo.holds, sells: monthAgo.sells }
        ]
    };
    return props;
};
