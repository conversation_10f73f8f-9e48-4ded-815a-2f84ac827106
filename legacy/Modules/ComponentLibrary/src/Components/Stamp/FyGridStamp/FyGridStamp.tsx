import React, { useMemo } from "react";

import { isNumber } from "Utilities/index";
import { LayoutContainer } from "PrimitiveComponents/index";

import { Grid } from "../../Grid";
import { colDefs } from "./consts";
import { InfoStampProps, InfoStamp } from "../InfoStamp";

type EpsTableStampProps = {
    headerName: string;
    fY1: number;
    fY1Change: number;
    fY2: number;
    fY2Change: number;
};

type FyGridProps = InfoStampProps & {
    infoLines?: EpsTableStampProps[];
    flavour?: string;
};

export const FyGridStamp = ({
    infoLines,
    flavour,
    width = 340,
    height = 75,
    ...props
}: FyGridProps) => {
    const colDef = useMemo(() => colDefs[flavour], [flavour]);

    if (!infoLines?.length || !colDef) {
        return (
            <InfoStamp
                width={width}
                height={height}
                headStyle={{
                    display: "none"
                }}
                bodyStyle={{
                    padding: 8
                }}
                {...props}
            />
        );
    }

    return (
        <InfoStamp
            width={width}
            height={height}
            headStyle={{
                display: "none"
            }}
            bodyStyle={{
                padding: 8
            }}
            {...props}
        >
            {infoLines && (
                <LayoutContainer height={height}>
                    <Grid
                        data={infoLines}
                        onGridReady={({ api }) => api.sizeColumnsToFit()}
                        getRowId={params => params?.data?.header}
                        defaultColDef={{
                            editable: false,
                            sortable: false,
                            cellStyle: ({ value }) => {
                                if (isNumber(value)) {
                                    return {
                                        textAlign: "right"
                                    };
                                }
                                return {};
                            }
                        }}
                        columnDefs={colDef}
                    />
                </LayoutContainer>
            )}
        </InfoStamp>
    );
};
