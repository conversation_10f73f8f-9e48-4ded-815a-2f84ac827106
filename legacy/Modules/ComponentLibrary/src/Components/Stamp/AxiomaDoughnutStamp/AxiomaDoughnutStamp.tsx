import React, { FunctionComponent } from "react";

import { <PERSON><PERSON><PERSON><PERSON>hart, DougnutChartProps } from "@reddeer/firefly-common-components";

import { InfoStamp, InfoStampProps } from "../InfoStamp";

export type AxiomaDoughnutStampProps = InfoStampProps & {
    doughnutData?: DougnutChartProps;
};

const doughnut: DougnutChartProps = {
    width: 200,
    height: 120,
    showLegend: true,
    data: [
        { name: "Specific", value: 40 },
        { name: "Country", value: 5 }
        // { name: "Industry", value: 3 },
        // { name: "Currency", value: 11 },
        // { name: "Style", value: 7 },
        // { name: "Market", value: 17 },
    ]
};

export const AxiomaDoughnutStamp: FunctionComponent<AxiomaDoughnutStampProps> = ({
    doughnutData,
    width = 240,
    height = 260,
    ...props
}) => {
    if (!doughnutData?.data?.length)
        return (
            <InfoStamp
                title="Revenue Split"
                width={width}
                height={height}
                {...props}
            />
        );
    return (
        <InfoStamp
            title="Title"
            width={width}
            height={height}
            {...props}
        >
            <DoughnutChart {...doughnutData} />
        </InfoStamp>
    );
};
