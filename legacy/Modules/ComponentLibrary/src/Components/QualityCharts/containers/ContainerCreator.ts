import { ComponentType } from "react";
import {
    createConfig,
    reportSubscriptionCommandSpec,
    withCommandState
} from "TransportGateway/index";

import { getMultiLineCurvesData, mapMultiLineStateToProps } from "../../../Utils";

const commandQuality = "QualityChartsData";

export const ContainerCreator = <P>(wrapper: ComponentType<P>, columns, node) =>
    withCommandState(
        wrapper,
        createConfig({
            curves: reportSubscriptionCommandSpec(
                commandQuality,
                ({ instrumentId: BBGTicker }: { instrumentId: string }) => ({
                    BBGTicker,
                    dateFrame: 180
                }),
                mapMultiLineStateToProps(
                    getMultiLineCurvesData({ columns, dateColumn: "fiscalYearEnd" })
                ),
                node
            )
        })
    );
