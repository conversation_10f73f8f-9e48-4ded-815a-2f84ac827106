import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import { ThemeIterator, LayoutGrid, ThemeContainer, LayoutContainer } from "PrimitiveComponents/index";
import { withFixtures } from "TransportGateway/index";
import { TradeButton } from "../TradeButton";

<Meta title="ComponentLibrary/Components/TradeButton" component={TradeButton} />

export const TradeButtonMock = withFixtures(TradeButton, {
    indexPairs: () => import("./fixtures/indexPairs.json")
});

export const Template = args => (
    <LayoutGrid columns={2} gap="xx-large">
        <ThemeIterator>
            {() => (
                <ThemeContainer>
                    <LayoutContainer style={{ maxWidth: 80 }}>
                        <TradeButtonMock {...args} />
                    </LayoutContainer>
                </ThemeContainer>
            )}
        </ThemeIterator>
    </LayoutGrid>
);

# TradeButton

<Description of={TradeButton} />

<Canvas>
    <Story
        name="TradeButton"
        parameters={{
            controls: {
                disable: false,
                include: [
                    "instrumentId",
                    "relativeTicker",
                    "indexPairs",
                    "buyTicker",
                    "sellTicker",
                    "buy",
                    "sell",
                    "traderId",
                    "openInNewWindow"
                ]
            }
        }}
        args={{
            instrumentId: "VOD",
            openInNewWindow: false
        }}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes of={TradeButton} sort="requiredFirst" />
