import { Option } from "PrimitiveComponents/index";
import { TradeImpactBuySell, TradeImpactParams } from "../types";

const parseTradeImpactBuySellChanges = (buySellChanges: TradeImpactBuySell): TradeImpactBuySell => {
    if (!buySellChanges?.ticker) {
        return buySellChanges;
    }

    // @Note: This is to handle a change of value type when either the primary/secondary
    // ticker is set to an Option<string> from the BBGTickerSelect component instead of a string.
    // Not sure if/how this has worked before, ideally this should be handled in the onChange of the
    // BBGTickerSelect component.

    const parsedTicker =
        typeof buySellChanges.ticker === "string"
            ? buySellChanges.ticker
            : (buySellChanges?.ticker as unknown as Option<string>)?.value ?? "";

    return {
        ...buySellChanges,
        ticker: parsedTicker
    };
};

export const parseTradeImpactChanges = (
    changeSet: Partial<TradeImpactParams>
): Partial<TradeImpactParams> => {
    return {
        ...changeSet,
        primary: changeSet.primary ? parseTradeImpactBuySellChanges(changeSet.primary) : undefined,
        secondary: changeSet.secondary
            ? parseTradeImpactBuySellChanges(changeSet.secondary)
            : undefined
    };
};
