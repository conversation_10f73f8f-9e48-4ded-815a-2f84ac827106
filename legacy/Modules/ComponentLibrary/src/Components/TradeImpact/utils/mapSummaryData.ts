import { isNumber } from "Utilities/index";

import { SummaryDataItem, SummaryRow } from "../types";

const summaryTitle = (key: string) => {
    switch (key) {
        case "referenceValue":
            return "grossExposure";
        case "percentFactor":
            return "totalRiskFactor";
        case "percentSpecific":
            return "totalRiskSpecific";
        default:
            return key;
    }
};

const summaryValue = (key: string, value: number) => {
    switch (key) {
        case "valueAtRisk95":
            return isNumber(value) ? Math.abs(value) : undefined;
        default:
            return value;
    }
};

const summaryTableRowKeys: Array<keyof SummaryDataItem> = [
    "referenceValue",
    "netExposure",
    "totalRisk",
    "totalFactorRisk",
    "percentFactor",
    "totalSpecificRisk",
    "percentSpecific",
    "swing",
    "valueAtRisk95"
];

export const mapSummaryData = (data: SummaryDataItem[] = []): SummaryRow[] => {
    if (!data?.length) return null;

    const summaryDataIndexed: { [key: string]: Omit<SummaryDataItem, "source"> } = data.reduce(
        (acc, current) => {
            const { source, ...values } = current;
            acc[source] = { ...values };
            return acc;
        },
        {}
    );

    return summaryTableRowKeys.map(key => ({
        rowId: key,
        risk: summaryTitle(key),
        value: summaryValue(key, (summaryDataIndexed.Before || {})[key]),
        after: summaryValue(key, (summaryDataIndexed.After || {})[key]),
        longLeg: summaryValue(key, (summaryDataIndexed.Buy || {})[key]),
        shortLeg: summaryValue(key, (summaryDataIndexed.Sell || {})[key]),
        trade: summaryValue(key, (summaryDataIndexed.Pair || {})[key]),
        change: (summaryDataIndexed.Change || {})[key],
        delta: summaryValue(key, (summaryDataIndexed.Delta || {})[key])
    }));
};
