import React from "react";
import { isUndefined } from "Utilities/index";
import { ButtonGroup, Button } from "PrimitiveComponents/index";
import {
    withCommandState,
    createConfig,
    reportSubscriptionCommandSpec
} from "TransportGateway/index";

import { ComponentDataBaseProps, withComponentData } from "../../ComponentData";
import { OrderInputOptions, TradeImpactParams } from "../types";

type AdjustmentValues = {
    beta?: number;
    swingRatio?: number;
};

type AdjustmentData = {
    buy: number;
    sell: number;
};

type AdjustmentProps = {
    relativeTicker: string;
    buyTicker: string;
    adjustmentValues: AdjustmentValues;
    onChange: (data: AdjustmentData) => void;
    className?: string;
    handleUpdate?: (params: Partial<TradeImpactParams>) => void;
} & ComponentDataBaseProps &
    AdjustmentData;

const Adjustment = ({
    relativeTicker,
    buyTicker,
    sell = 0,
    buy = 0,
    adjustmentValues,
    onChange,
    className,
    handleUpdate
}: AdjustmentProps) => {
    const { swingRatio, beta } = adjustmentValues || {};

    const update = (value: number) => {
        if (value === 1) {
            if (handleUpdate) {
                handleUpdate({
                    updateInput: {
                        secondary: OrderInputOptions["secondary.value"]
                    }
                });
            }
            return onChange({ buy, sell: buy });
        }

        if (buyTicker === relativeTicker) {
            if (handleUpdate) {
                handleUpdate({
                    updateInput: {
                        primary: OrderInputOptions["primary.value"]
                    }
                });
            }
            return onChange({ buy: Math.round(sell * value), sell });
        }

        if (handleUpdate) {
            handleUpdate({
                updateInput: {
                    secondary: OrderInputOptions["secondary.value"]
                }
            });
        }
        return onChange({ buy, sell: Math.round(buy * value) });
    };

    return (
        <ButtonGroup className={className}>
            <Button
                size="x-small"
                onClick={() => update(1)}
            >
                Delta: 1
            </Button>
            <Button
                size="x-small"
                onClick={() => update(beta)}
                disabled={isUndefined(beta)}
            >
                Beta {(beta || 0)?.toFixed(1)}
            </Button>
            <Button
                size="x-small"
                onClick={() => update(swingRatio)}
                disabled={isUndefined(swingRatio)}
            >
                Swing {(swingRatio || 0).toFixed(1)}
            </Button>
        </ButtonGroup>
    );
};

export const TradeAdjustment = withComponentData(
    withCommandState(
        Adjustment,
        createConfig({
            adjustmentValues: reportSubscriptionCommandSpec(
                "RelationshipCalc",
                ({
                    identifier,
                    relativeTicker
                }: Pick<AdjustmentProps, "identifier" | "relativeTicker">) =>
                    identifier &&
                    relativeTicker && {
                        BBGHedge: relativeTicker,
                        BBGPrimary: identifier
                    },
                (data): AdjustmentValues => ({
                    beta: data?.[0]?.beta,
                    swingRatio: data?.[0]?.swingRatio
                }),
                "fetchData"
            )
        })
    )
);
