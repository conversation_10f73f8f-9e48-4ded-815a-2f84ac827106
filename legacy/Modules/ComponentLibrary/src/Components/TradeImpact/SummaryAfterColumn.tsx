import React from "react";
import styled from "styled-components";

import { isNumberType, isStringType, getFormattedValue } from "Utilities/index";

import { getChangeValueIconColor, formatSummaryCellNumber, getChangeValueContent } from "./utils";
import { SummaryRow } from "./types";

const ChangeValueIcon = styled.div<{ color?: string }>`
    display: flex;
    width: var(--font__size--large);
    margin-left: var(--spacing--small);

    justify-content: center;
    align-items: center;

    color: ${props => props.color};
    font-size: var(--font__size--large);
    text-align: center;
`;

const Container = styled.div`
    display: flex;

    justify-content: flex-end;
    align-items: center;
`;

type Props = {
    value: number;
    rowData: SummaryRow;
    displayChangeValue?: boolean;
};

export const SummaryAfterColumn = ({ value, rowData, displayChangeValue }: Props) => {
    const iconColor = getChangeValueIconColor(rowData?.change);
    const displayValue = formatSummaryCellNumber(value, rowData);
    const title =
        isStringType(displayValue) && displayValue.includes("$")
            ? getFormattedValue(value, {
                  showPositiveIndicator: false,
                  formatterType: "dollar",
              })
            : displayValue;

    return (
        <Container>
            <div title={String(title)}>{displayValue}</div>
            {displayChangeValue && isNumberType(rowData.change) && rowData.change !== 0 && (
                <ChangeValueIcon color={iconColor}>
                    {getChangeValueContent(rowData.change)}
                </ChangeValueIcon>
            )}
        </Container>
    );
};
