import {
    createConfig,
    reportSubscriptionCommandSpec,
    requestResponseCommandSpec,
    withCommandState
} from "TransportGateway/index";
import { ChartAxisType } from "@reddeer/firefly-shared-controls";
import {
    getMultiLineChartDef,
    mapSpecialDates,
    getMultiLineChart,
    mapMultiLineStateToProps,
    getMultiLineCurvesData
} from "../../../Utils";
import { putCallRatioColumn } from "../consts";
import type { Props } from "./types";

const putCallRatioChartWrapper = (props = {}) =>
    getMultiLineChart({
        lineProps: props,
        definition: getMultiLineChartDef({
            columns: putCallRatioColumn,
            xAxisType: ChartAxisType.Number,
            yAxisLabel: "Put/Call Ratio",
            yAxisRightLabel: "Put/Call Ratio"
        })
    });

export const PutCallRatioChartContainer = withCommandState(
    putCallRatioChartWrapper(),
    createConfig({
        curves: reportSubscriptionCommandSpec(
            "SkewTicker",
            ({ instrumentId: BBGTicker, period }: Props) => ({
                BBGTicker,
                dateFrame: period
            }),
            mapMultiLineStateToProps(getMultiLineCurvesData({ columns: putCallRatioColumn })),
            "ProjectionNode"
        ),
        specialDates: requestResponseCommandSpec(
            "RaidData_GetEventRequestDto_IEnumerable[CompleteEventDataDto]_RequestResponse_Handler",
            ({ instrumentId: BBGTicker, period }: Props) => ({
                BBGTicker,
                dateFrame: period,
                eventTypes: [1, 2, 3]
            }),
            mapSpecialDates
        )
    })
);
