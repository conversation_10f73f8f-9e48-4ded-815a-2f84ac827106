import React, { useCallback, useState } from "react";
import { DateFrameWithLabel, SelectWithLabel, Option } from "PrimitiveComponents/index";
import styled from "styled-components";
import { RegressionPairPriceHistory } from "./containers/RegressionPairPriceHistory";
import { DefaultValues } from "./consts";

type Props = {
    instrumentId: string;
    ticker: string;
    height?: number;
    width?: number;
    tickerOptions: Option[];
};

const PERIODS = [1, 3, 6, 12, 24, 48];

const Filters = styled.div`
    display: flex;

    justify-content: space-between;
    align-items: center;
`;

const Heading = styled.div`
    display: flex;
    margin-bottom: 0.5rem;

    justify-content: center;
    align-items: center;
`;

export const FactorPairsChart = ({
    instrumentId,
    ticker,
    height = 350,
    width = 750,
    tickerOptions
}: Props) => {
    const [dateFrame, setDateFrame] = useState<number>(DefaultValues.dateFrame);
    const [factor, setFactor] = useState<string>(ticker);
    const [zoom, setZoom] = useState(false);
    const handleDateFrameChange = useCallback(val => setDateFrame(val), []);
    const handleFactorChange = useCallback(val => setFactor(val), []);

    return (
        <>
            <Heading>{instrumentId}</Heading>
            <Filters>
                <SelectWithLabel
                    label="Factor name"
                    value={factor}
                    options={tickerOptions}
                    onChange={handleFactorChange}
                />
                <DateFrameWithLabel
                    label="Date Period"
                    periods={PERIODS}
                    value={dateFrame}
                    onChange={handleDateFrameChange}
                />
            </Filters>
            <RegressionPairPriceHistory
                instrumentId={instrumentId}
                chooseFactor={factor}
                dateFrame={dateFrame}
                height={height}
                width={width}
                zoom={zoom}
                onZoom={setZoom}
            />
        </>
    );
};
