import React, { useState, useMemo } from "react";
import { BasicCard } from "PrimitiveComponents/index";
import { isNumberType } from "Utilities/index";
import { MacroTodayCorrelation } from "./containers";
import * as Title from "./styled";
import { DefaultValues, headCardStyle } from "./consts";
import { DriverCardProps } from "./types";

export const CorrelationDriversCard = ({
    instrumentId,
    hideCols = [],
    allowEditCols = false,
    height = 150,
    limitOptions = [5, 10, 20],
    title = "Correlation Drivers",
    displayBorder = false,
    defaultLimit = DefaultValues.limit,
    sellTicker,
    externalLimit,
    transparent,
    bodyStyle
}: DriverCardProps) => {
    const [limit, setLimit] = useState<number>(defaultLimit);
    const handleChangeLimit = val => setLimit(val);

    const options = useMemo(() => {
        if (!limitOptions?.length) return null;
        return limitOptions.map(value => ({
            value,
            label: `Top ${value}`
        }));
    }, [limitOptions]);

    return (
        <BasicCard
            title={
                <Title.Bar>
                    {title}
                    {!!options?.length && !isNumberType(externalLimit) && (
                        <Title.RadioBtns
                            onChange={handleChangeLimit}
                            options={options}
                            value={limit}
                            size="x-small"
                        />
                    )}
                </Title.Bar>
            }
            bodyStyle={bodyStyle}
            headStyle={headCardStyle}
            displayBorder={displayBorder}
            transparent={transparent}
        >
            <MacroTodayCorrelation
                instrumentId={instrumentId}
                height={height}
                hideCols={hideCols}
                allowEditCols={allowEditCols}
                limit={isNumberType(externalLimit) ? externalLimit : limit}
                pair={!!sellTicker}
                sellTicker={sellTicker}
            />
        </BasicCard>
    );
};
