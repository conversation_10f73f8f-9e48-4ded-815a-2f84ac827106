import React from "react";
import styled from "styled-components";

import {
    FlipBetween,
    FlipBox,
    LayoutGrid,
    LayoutVertical,
    LayoutContainer,
} from "PrimitiveComponents/index";
import { PriceChartWrapper } from "../PriceChart";
import { MomoChart } from "../MomoChart";
import { EarningsChangesGrid } from "../EarningsChangesGrid";
import { Stamp } from "../Stamp";

const Title = styled.div`
    display: flex;

    align-self: center;

    color: var(--color);
    font-size: 14px;
    font-weight: bold;
`;

type Props = {
    instrumentId: string;
};

export const EpsCharts = ({ instrumentId }: Props) => {
    return (
        <LayoutGrid
            columns={2}
            gap="medium"
        >
            <LayoutVertical>
                <Title>Revenue</Title>
                <PriceChartWrapper
                    instrumentId={instrumentId}
                    group="rev"
                    height={225}
                />
                <Title>Uly 3m chg</Title>
                <LayoutContainer height={140}>
                    <MomoChart
                        report="revCommand"
                        period={24}
                        instrumentId={instrumentId}
                    />
                </LayoutContainer>
                <Stamp
                    flavour="revTable"
                    instrumentId={instrumentId}
                />
            </LayoutVertical>

            <FlipBetween>
                <FlipBox title={<Title>EPS</Title>}>
                    <PriceChartWrapper
                        instrumentId={instrumentId}
                        group="eps"
                    />
                    <Title>Uly 3m chg 2</Title>
                    <LayoutContainer height={140}>
                        <MomoChart
                            report="momoCommand"
                            period={24}
                            instrumentId={instrumentId}
                        />
                    </LayoutContainer>
                    <Stamp
                        flavour="epsTable"
                        instrumentId={instrumentId}
                        popup={<EarningsChangesGrid instrumentId={instrumentId} />}
                    />
                </FlipBox>
                <FlipBox
                    title={<Title>EBITDA</Title>}
                    verticalAlign="baseline"
                >
                    <PriceChartWrapper
                        instrumentId={instrumentId}
                        group="ebitda"
                    />
                    <Title>Uly 3m chg</Title>
                    <LayoutContainer height={140}>
                        <MomoChart
                            report="ebitdaCommand"
                            period={24}
                            instrumentId={instrumentId}
                        />
                    </LayoutContainer>
                    <Stamp
                        flavour="ebitdaTable"
                        instrumentId={instrumentId}
                    />
                </FlipBox>
            </FlipBetween>
        </LayoutGrid>
    );
};
