import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import { ThemeIterator, LayoutGrid, ThemeContainer } from "PrimitiveComponents/index";
import { OptionPopover } from "../OptionPopover";

<Meta title="ComponentLibrary/Components/OptionPopover" component={OptionPopover} />

export const Template = args => (
    <LayoutGrid columns={2} gap="xx-large">
        <ThemeIterator>
            {() => (
                <ThemeContainer>
                    <OptionPopover {...args}>1</OptionPopover>
                </ThemeContainer>
            )}
        </ThemeIterator>
    </LayoutGrid>
);

# OptionPopover

<Description of={OptionPopover} />

<Canvas>
    <Story
        name="OptionPopover"
        parameters={{
            controls: {
                disable: false,
                include: [
                    "instrumentId",
                    "relativeTicker",
                    "indexPairs",
                    "buyTicker",
                    "sellTicker",
                    "buy",
                    "sell",
                    "traderId",
                    "openInNewWindow"
                ]
            }
        }}
        args={{
            instrumentId: "GP"
        }}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes of={OptionPopover} sort="requiredFirst" />
