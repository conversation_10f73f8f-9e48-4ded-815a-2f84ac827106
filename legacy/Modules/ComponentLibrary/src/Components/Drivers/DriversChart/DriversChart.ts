import moment from "moment";
import {
    apiGatewaySpec,
    createConfig,
    reportSubscriptionCommandSpec,
    requestResponseCommandSpec,
    withCommandStateFactory
} from "TransportGateway/index";
import { mapSpecialDates } from "../../../Utils";
import { ComponentDataBaseProps, withComponentData } from "../../ComponentData";

import { DEFAULT_DATE_FRAME, DEFAULT_DAYS_LAG } from "./consts";
import { InnerDriversChart } from "./InnerDriversChart";
import { mapRegressionLines } from "./utils";
import { ApiGatewayService } from "Contracts/index";

type DriversChartRequestProps = {
    hedgeTicker?: string;
    daysLag?: number;
    displayRegression?: boolean;
    identifier: ComponentDataBaseProps["identifier"];
    params?: ComponentDataBaseProps["params"];
    parameterValues?: ComponentDataBaseProps["parameterValues"];
};

export const DriversChartWithCommandState = withCommandStateFactory(
    InnerDriversChart,
    ({ hedgeTicker: pairTicker }: DriversChartRequestProps) =>
        createConfig({
            chartData: reportSubscriptionCommandSpec(
                pairTicker ? "RiskAttributionHedged" : "RiskAttribution",
                ({ identifier, params, parameterValues }: DriversChartRequestProps) =>
                    identifier && {
                        BbgTicker: identifier,
                        dateFrame: parameterValues?.DateFrame || DEFAULT_DATE_FRAME,
                        hedgeTicker: pairTicker,
                        ...params
                    },
                (data = []) =>
                    data.length
                        ? data.sort(
                              (a, b) =>
                                  moment(a.date, "YYYY-MM-DD").valueOf() -
                                  moment(b.date, "YYYY-MM-DD").valueOf()
                          )
                        : []
            ),
            specialDates: requestResponseCommandSpec(
                "RaidData_GetEventRequestDto_IEnumerable[CompleteEventDataDto]_RequestResponse_Handler",
                ({ identifier, params, parameterValues }: DriversChartRequestProps) =>
                    identifier && {
                        bbgTicker: identifier,
                        dateFrame: parameterValues?.DateFrame || DEFAULT_DATE_FRAME,
                        eventTypes: [1, 2, 3, 13, 114],
                        ...params
                    },
                mapSpecialDates
            ),
            regression: reportSubscriptionCommandSpec(
                pairTicker ? "specificRegressionLinesSpread" : "specificRegressionLines",
                ({
                    identifier,
                    params,
                    parameterValues,
                    daysLag = DEFAULT_DAYS_LAG,
                    displayRegression = true
                }: DriversChartRequestProps) =>
                    identifier &&
                    displayRegression && {
                        BBGTicker: identifier,
                        dateFrame: parameterValues?.DateFrame || DEFAULT_DATE_FRAME,
                        pairTicker,
                        daysLag,
                        ...params
                    },
                mapRegressionLines,
                "Lines"
            )
        })
);

export const DriversChartDataGateway = withCommandStateFactory(
    InnerDriversChart,
    ({ pairTicker }) =>
        createConfig({
            chartData: apiGatewaySpec<
                Partial<ApiGatewayService.DecisionSupportPage.DriversChartRequest>,
                ApiGatewayService.DecisionSupportPage.DriversChartResponse,
                { identifier?: string }
            >(
                pairTicker
                    ? `decision-support-page.drivers-chart-pair`
                    : `decision-support-page.drivers-chart`,
                ({ identifier }) =>
                    identifier && {
                        BbgTicker: identifier,
                        dateFrame: DEFAULT_DATE_FRAME,
                        pairTicker
                    },
                (data = []) => data
            ),
            specialDates: apiGatewaySpec<
                Partial<ApiGatewayService.DecisionSupportPage.RegressionChartSpecialDatesRequest>,
                ApiGatewayService.DecisionSupportPage.RegressionChartSpecialDatesResponse,
                { identifier?: string }
            >(
                "decision-support-page.regression-chart-special-dates",
                ({ identifier }) =>
                    identifier
                        ? {
                              bbgTicker: identifier,
                              dateFrame: DEFAULT_DATE_FRAME
                          }
                        : undefined,
                mapSpecialDates
            ),
            regression: apiGatewaySpec<
                Partial<ApiGatewayService.DecisionSupportPage.DriversChartRegressionLinesRequest>,
                ApiGatewayService.DecisionSupportPage.DriversChartRegressionLinesResponse,
                { identifier?: string },
                { slope: number; stdErr: number; length: number; interceptVsLast: number },
                ApiGatewayService.DecisionSupportPage.DriversChartRegressionLinesResponse
            >(
                pairTicker
                    ? "decision-support-page.drivers-chart-pair-regression-lines"
                    : "decision-support-page.drivers-chart-regression-lines",
                ({ identifier }: { identifier: string }) =>
                    identifier && {
                        bbgTicker: identifier,
                        pairTicker
                    },
                data => ({
                    slope: data.regressSlope,
                    stdErr: data.regressStdErr,
                    length: data.regressLength,
                    interceptVsLast: data.interceptVsLast
                })
            )
        })
);

export const DriversChart = withComponentData(DriversChartWithCommandState);
