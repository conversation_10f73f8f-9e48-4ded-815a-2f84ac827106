{"status": {"results": {}, "isCommandLoading": false, "stateKey": "SubscribeToReport@fyInfo-62", "commandId": 45, "command": {"isSubscription": true, "commandId": 45, "Headers": [], "name": "SubscribeToReport", "connectionName": "viewserver", "payload": {"ReportKey": "ConsensusFyInfoRequest", "OutputOperatorName": "", "ParameterValues": {"BBGTicker": "VOD LN Equity", "MeasureCode": "9"}}}, "connectionName": "viewserver"}, "state": {"sync": "completed", "operatorStatus": {"Status": 7, "StatusMessage": null}, "command": {"isSubscription": true, "commandId": 45, "Headers": [], "name": "SubscribeToReport", "connectionName": "viewserver", "payload": {"ReportKey": "ConsensusFyInfoRequest", "OutputOperatorName": "", "ParameterValues": {"BBGTicker": "VOD LN Equity", "MeasureCode": "9"}}}, "status": "Finished: Running execution plan command: 45 with result True. Message Succeeded in creating 4 nodes 3 connections 1 contexts", "metaData": {"executionplanlocal": "[{\"FromNodeType\":\"Deserializer\",\"FromPath\":\"/<PERSON>_<PERSON>_fVO42LNuz_ZptrFZ9cvVKw/ConsensusFyInfoRequest/ConsensusFyInfoRequest~BbgTickerVODLNEquityDateFrameTodayMeasureCode9:45\",\"ToNodeType\":\"Filter\",\"ToPath\":\"/<PERSON>_<PERSON>_fVO42LNuz_ZptrFZ9cvVKw/ConsensusFyInfoRequest/userFilter:45.in\"},{\"FromNodeType\":\"Filter\",\"FromPath\":\"/William_<PERSON>rry_fVO42LNuz_ZptrFZ9cvVKw/ConsensusFyInfoRequest/userFilter:45\",\"ToNodeType\":\"Sort\",\"ToPath\":\"/<PERSON>_<PERSON>_fVO42LNuz_ZptrFZ9cvVKw/ConsensusFyInfoRequest/sort:45.in\"},{\"FromNodeType\":\"Sort\",\"FromPath\":\"/<PERSON>_<PERSON>_fVO42LNuz_ZptrFZ9cvVKw/ConsensusFyInfoRequest/sort:45\",\"ToNodeType\":\"Serializer\",\"ToPath\":\"/<PERSON>_Corry_fVO42LNuz_ZptrFZ9cvVKw/ConsensusFyInfoRequest/serializer:45.in\"}]", "consensusfyinforequestxxstatus": "Finished:Committing \"userFilter:45\" for 00:00:00.0000456", "consensusfyinforequestbbgtickervodlnequitydateframetodaymeasurecode945xxstatus": "Finished: Running execution plan command: 3447 with result True. Message Succeeded in creating 2 nodes 1 connections 2 contexts", "executionplandatarail0": "[{\"FromNodeType\":\"DataRailLiveRequest_DataRailResponse_ConsumerResponse\",\"FromPath\":\"/ReportEngine>DataRail/<PERSON>_<PERSON>_fVO42LNuz_ZptrFZ9cvVKw/ConsensusFyInfoRequest/ConsensusFyInfoRequest~BbgTickerVODLNEquityDateFrameTodayMeasureCode9:3447\",\"ToNodeType\":\"Serializer\",\"ToPath\":\"/ReportEngine>DataRail/ConsensusFyInfoRequest/serializer:3447.in\"}]", "consensusfyinforequestbbgtickervodlnequitydateframetodaymeasurecode93447xxstatus": "Finished:Running Loading 1 rows into \"DataRail\" table  for 0 milliseconds|", "rowcount": 1}, "data": [{"rank": 0, "dateTimeAndIdentifier": "2022-11-30VOD LN Equity", "dateTime": "2022-11-30", "identifierType": 1, "identifier": "VOD LN Equity", "fy1Date": "2023-03-31-00.00.00", "fy1Brokers": 16, "fy2Date": "2024-03-31-00.00.00", "fy2Brokers": 16, "fy3Date": "2025-03-31-00.00.00", "fy3Brokers": 13, "fy4Date": "2026-03-31-00.00.00", "fy4Brokers": 7, "rowId": 0}], "lastUpdated": "2022-11-30T10:46:56.120Z", "snapshot": true, "schema": [{"columnId": 3, "contentType": 5, "traits": "hidden", "name": "rank", "metaData": {"width": null, "sortable": false, "searchable": false, "hidden": false, "flags": {}, "dimensionName": null, "nullValue": {"boolValue": null, "nullableBoolValue": null, "byteValue": null, "intValue": null, "shortValue": null, "longValue": null, "floatValue": null, "doubleValue": null, "decimalValue": null, "stringValue": null, "nullValue": false, "dateValue": null, "dateTimeValue": null}, "contentTypeForDisplay": null, "displayName": "Rank"}}, {"columnId": 4, "contentType": 5, "traits": "hidden,previous", "name": "previous", "metaData": {"width": null, "sortable": false, "searchable": false, "hidden": false, "flags": {}, "dimensionName": null, "nullValue": {"boolValue": null, "nullableBoolValue": null, "byteValue": null, "intValue": null, "shortValue": null, "longValue": null, "floatValue": null, "doubleValue": null, "decimalValue": null, "stringValue": null, "nullValue": false, "dateValue": null, "dateTimeValue": null}, "contentTypeForDisplay": null, "displayName": "Previous"}}, {"columnId": 5, "contentType": 9, "traits": null, "name": "dateTimeAndIdentifier", "metaData": {"width": null, "sortable": false, "searchable": false, "hidden": false, "flags": {}, "dimensionName": null, "nullValue": {"boolValue": null, "nullableBoolValue": null, "byteValue": null, "intValue": null, "shortValue": null, "longValue": null, "floatValue": null, "doubleValue": null, "decimalValue": null, "stringValue": null, "nullValue": false, "dateValue": null, "dateTimeValue": null}, "contentTypeForDisplay": null, "displayName": "Date Time And Identifier"}}, {"columnId": 6, "contentType": 10, "traits": null, "name": "dateTime", "metaData": {"width": null, "sortable": false, "searchable": false, "hidden": false, "flags": {}, "dimensionName": null, "nullValue": {"boolValue": null, "nullableBoolValue": null, "byteValue": null, "intValue": null, "shortValue": null, "longValue": null, "floatValue": null, "doubleValue": null, "decimalValue": null, "stringValue": null, "nullValue": false, "dateValue": null, "dateTimeValue": null}, "contentTypeForDisplay": null, "displayName": "Date Time"}}, {"columnId": 7, "contentType": 5, "traits": null, "name": "identifierType", "metaData": {"width": null, "sortable": false, "searchable": false, "hidden": false, "flags": {}, "dimensionName": null, "nullValue": {"boolValue": null, "nullableBoolValue": null, "byteValue": null, "intValue": null, "shortValue": null, "longValue": null, "floatValue": null, "doubleValue": null, "decimalValue": null, "stringValue": null, "nullValue": false, "dateValue": null, "dateTimeValue": null}, "contentTypeForDisplay": null, "displayName": "Identifier Type"}}, {"columnId": 8, "contentType": 14, "traits": null, "name": "identifier", "metaData": {"width": null, "sortable": false, "searchable": false, "hidden": false, "flags": {}, "dimensionName": null, "nullValue": {"boolValue": null, "nullableBoolValue": null, "byteValue": null, "intValue": null, "shortValue": null, "longValue": null, "floatValue": null, "doubleValue": null, "decimalValue": null, "stringValue": null, "nullValue": false, "dateValue": null, "dateTimeValue": null}, "contentTypeForDisplay": null, "displayName": "Identifier"}}, {"columnId": 9, "contentType": 11, "traits": "0", "name": "fy1Date", "metaData": {"width": null, "sortable": false, "searchable": false, "hidden": false, "flags": {}, "dimensionName": null, "nullValue": {"boolValue": null, "nullableBoolValue": null, "byteValue": null, "intValue": null, "shortValue": null, "longValue": null, "floatValue": null, "doubleValue": null, "decimalValue": null, "stringValue": null, "nullValue": false, "dateValue": null, "dateTimeValue": null}, "contentTypeForDisplay": null, "displayName": "Fy 1Date"}}, {"columnId": 10, "contentType": 5, "traits": "1", "name": "fy1Brokers", "metaData": {"width": null, "sortable": false, "searchable": false, "hidden": false, "flags": {}, "dimensionName": null, "nullValue": {"boolValue": null, "nullableBoolValue": null, "byteValue": null, "intValue": null, "shortValue": null, "longValue": null, "floatValue": null, "doubleValue": null, "decimalValue": null, "stringValue": null, "nullValue": false, "dateValue": null, "dateTimeValue": null}, "contentTypeForDisplay": null, "displayName": "Fy 1Brokers"}}, {"columnId": 11, "contentType": 11, "traits": "2", "name": "fy2Date", "metaData": {"width": null, "sortable": false, "searchable": false, "hidden": false, "flags": {}, "dimensionName": null, "nullValue": {"boolValue": null, "nullableBoolValue": null, "byteValue": null, "intValue": null, "shortValue": null, "longValue": null, "floatValue": null, "doubleValue": null, "decimalValue": null, "stringValue": null, "nullValue": false, "dateValue": null, "dateTimeValue": null}, "contentTypeForDisplay": null, "displayName": "Fy 2Date"}}, {"columnId": 12, "contentType": 5, "traits": "3", "name": "fy2Brokers", "metaData": {"width": null, "sortable": false, "searchable": false, "hidden": false, "flags": {}, "dimensionName": null, "nullValue": {"boolValue": null, "nullableBoolValue": null, "byteValue": null, "intValue": null, "shortValue": null, "longValue": null, "floatValue": null, "doubleValue": null, "decimalValue": null, "stringValue": null, "nullValue": false, "dateValue": null, "dateTimeValue": null}, "contentTypeForDisplay": null, "displayName": "Fy 2Brokers"}}, {"columnId": 13, "contentType": 11, "traits": "4", "name": "fy3Date", "metaData": {"width": null, "sortable": false, "searchable": false, "hidden": false, "flags": {}, "dimensionName": null, "nullValue": {"boolValue": null, "nullableBoolValue": null, "byteValue": null, "intValue": null, "shortValue": null, "longValue": null, "floatValue": null, "doubleValue": null, "decimalValue": null, "stringValue": null, "nullValue": false, "dateValue": null, "dateTimeValue": null}, "contentTypeForDisplay": null, "displayName": "Fy 3Date"}}, {"columnId": 14, "contentType": 5, "traits": "5", "name": "fy3Brokers", "metaData": {"width": null, "sortable": false, "searchable": false, "hidden": false, "flags": {}, "dimensionName": null, "nullValue": {"boolValue": null, "nullableBoolValue": null, "byteValue": null, "intValue": null, "shortValue": null, "longValue": null, "floatValue": null, "doubleValue": null, "decimalValue": null, "stringValue": null, "nullValue": false, "dateValue": null, "dateTimeValue": null}, "contentTypeForDisplay": null, "displayName": "Fy 3Brokers"}}, {"columnId": 15, "contentType": 11, "traits": "6", "name": "fy4Date", "metaData": {"width": null, "sortable": false, "searchable": false, "hidden": false, "flags": {}, "dimensionName": null, "nullValue": {"boolValue": null, "nullableBoolValue": null, "byteValue": null, "intValue": null, "shortValue": null, "longValue": null, "floatValue": null, "doubleValue": null, "decimalValue": null, "stringValue": null, "nullValue": false, "dateValue": null, "dateTimeValue": null}, "contentTypeForDisplay": null, "displayName": "Fy 4Date"}}, {"columnId": 16, "contentType": 5, "traits": "7", "name": "fy4Brokers", "metaData": {"width": null, "sortable": false, "searchable": false, "hidden": false, "flags": {}, "dimensionName": null, "nullValue": {"boolValue": null, "nullableBoolValue": null, "byteValue": null, "intValue": null, "shortValue": null, "longValue": null, "floatValue": null, "doubleValue": null, "decimalValue": null, "stringValue": null, "nullValue": false, "dateValue": null, "dateTimeValue": null}, "contentTypeForDisplay": null, "displayName": "Fy 4Brokers"}}], "stateKey": "SubscribeToReport@fyInfo-62"}}