import React, { useMemo } from "react";
import { Chart } from "Charting/index";
import { LayoutContainer, NoData, SpecialDate } from "PrimitiveComponents/index";
import type { Schema } from "Contracts/index";

import { legendName } from "./utils";
import { FyColors } from "../../consts";

export type PriceData = {
    dateTime: string;
    price?: number;
    fy1?: number;
    fy2?: number;
};

type Curves = {
    schema: Schema;
    data: PriceData[];
};

type Props = {
    specialDates: SpecialDate[];
    curves: Curves;
    fyInfo: [string, number, number][];
    height?: number | string;
};

export const PriceLineChart = ({ height, curves, fyInfo, specialDates }: Props) => {
    const schema = useMemo(() => {
        if (!curves?.schema?.length || !curves?.data?.length) return [];
        if (!fyInfo?.length) return curves.schema;

        return curves.schema.map(({ name, ...rest }) => {
            const [legend, fy] = legendName(name, fyInfo);
            return {
                name,
                ...rest,
                metaData: {
                    ...rest.metaData,
                    displayName: legend,
                    properties: {
                        ...rest.metaData?.properties,
                        chartSeriesPropsStroke: fy ? FyColors[fy] : undefined
                    }
                }
            };
        }) as Schema;
    }, [curves?.data, curves?.schema, fyInfo]);

    return (
        <LayoutContainer height={height}>
            {!curves?.data?.length ? (
                <NoData />
            ) : (
                <Chart
                    data={curves?.data}
                    specialDates={specialDates}
                    schema={schema}
                    domainAxis={["YAxisRight"]}
                    minMaxProps={{
                        YAxisRight: ["fy1", "fy2"]
                    }}
                />
            )}
        </LayoutContainer>
    );
};
