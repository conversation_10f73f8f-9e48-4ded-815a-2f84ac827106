import moment from "moment";
import {
    BROKER_DOWNGRADE_EVENT_TYPE,
    BROKER_NO_CHANGE_EVENT_TYPE,
    BROKER_UPGRADE_EVENT_TYPE,
    EventSpecialDateItem
} from "../types";

const getEventType = eventType => {
    switch (eventType) {
        case "up":
            return BROKER_UPGRADE_EVENT_TYPE;

        case "down":
            return BROKER_DOWNGRADE_EVENT_TYPE;

        default:
            return BROKER_NO_CHANGE_EVENT_TYPE;
    }
};

export const mapBrokerUpDownGrades = (data = []) =>
    data.reduce(
        (acc, { brokerAndDate, stance, broker, date, stance_Trend: stanceTrend, prevStance }) => {
            return [
                ...acc,
                {
                    id: brokerAndDate,
                    name: `${broker ?? "Broker"} - ${stance}||${prevStance}`,
                    daysDelta: 0,
                    eventType: getEventType(stanceTrend),
                    date: moment(date.substring(0, 10)).toDate(),
                    bloombergTickers: [],
                    topics: []
                }
            ];
        },
        [] as EventSpecialDateItem[]
    );
