import React, { useEffect, useRef } from "react";
import { geoCircle, geoOrthographic, geoPath, scaleLinear, select } from "d3";
import styled from "styled-components";

type Props = {
    percent: number;
    size?: number;
};

const StyledSvg = styled("svg")`
    width: 100%;
    height: 100%;
    pointer-events: all;
    flex: 1;
    user-select: none;
`;

export const MoonIndicator = ({ percent, size = 16 }: Props) => {
    const svgRef = useRef(null);

    useEffect(() => {
        const percentScale = scaleLinear()
            .domain([0, 100]) //
            .range([-180, 0])
            .clamp(true);

        const svg = select(svgRef.current)
            .attr("width", size) //
            .attr("height", size);

        const circle = geoCircle();
        const projection = geoOrthographic()
            .scale(40) //
            .translate([50, 50]);
        const path = geoPath().projection(projection);

        svg.append("path") //
            .attr("fill", "#2b281b")
            .attr("d", path(circle()));

        svg.append("path")
            .attr("fill", "#f7f6f2")
            .attr("d", path(circle.center([percentScale(percent), 0])()));
    }, [svgRef, percent, size]);

    return (
        <StyledSvg
            ref={svgRef}
            viewBox="0 0 100 100"
        />
    );
};
