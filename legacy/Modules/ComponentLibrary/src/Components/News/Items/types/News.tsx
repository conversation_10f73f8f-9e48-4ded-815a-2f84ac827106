import React from "react";
import { Popover } from "antd";
import styled, { createGlobalStyle } from "styled-components";

import { getContainer } from "PrimitiveComponents/index";
import {
    createConfig,
    reportSubscriptionCommandSpec,
    withCommandState
} from "TransportGateway/index";

import { NewsItem } from "../../types";
import { HTMLContainer } from "../../../HTMLContainer";
import { Title } from "../Title";
import { Body } from "../Body";
import { MoonIndicator } from "../MoonIndicator";

type NewsProp = {
    identifier: string;
    item: NewsItem;
    showHover: boolean;
};

const ConnectedNewsContent = withCommandState(
    HTMLContainer,
    createConfig({
        text: reportSubscriptionCommandSpec(
            "CompanyPage_DataItem_News",
            ({ identifier, dataItemId }: { identifier: string; dataItemId?: string }) =>
                identifier && {
                    bbgTicker: identifier,
                    dataItemId
                },
            data => {
                return data[0]?.dataItem_Text ?? "Loading";
            },
            "GetDataItemById"
        )
    })
);

const StyledMoonIndicator = styled("div")`
    margin: 4px 0 0 0;
    height: 16px;
    align-self: flex-end;
`;

const StyledPopover = styled(Popover)`
    cursor: pointer;
`;

const NewsPopover = styled.div`
    max-width: 750px;
    max-height: 500px;
    overflow-y: auto;
    padding: 8px;
`;

const GlobalStyles = createGlobalStyle`
    .news-popover {
        .ant-popover-content {
            > .ant-popover-arrow {
            border-color: var(--border__color--outline) !important;
            z-index: -1;
            }

            > .ant-popover-inner {
            border: 1px solid var(--border__color--outline);

            .ant-popover-inner-content {
                padding: 0;
                font-size: 12px;
            
                .ant-popover-message {
                font-size: inherit;
                }
            }
            }
        }
    }
`;

const NewsWrapper = ({ item, identifier, showHover, children }) => {
    const onInteraction = () =>
        window
            .open(
                `https://mbam.reddeer.com/Timeline/Accordion/Content/${item.itemType ?? "278"}/${
                    item.originalId
                }`,
                `_blank`
            )
            .focus();
    return showHover ? (
        <>
            <GlobalStyles />
            <StyledPopover
                title=""
                overlayClassName="news-popover"
                content={
                    <NewsPopover>
                        <ConnectedNewsContent
                            identifier={identifier}
                            dataItemId={item.id}
                            text="Loading"
                        />
                    </NewsPopover>
                }
                destroyTooltipOnHide
                getPopupContainer={getContainer}
            >
                {children}
            </StyledPopover>
        </>
    ) : (
        <div
            style={{ cursor: "pointer", flexGrow: 1 }}
            onClick={onInteraction}
            onKeyDown={onInteraction}
            role="button"
            tabIndex={0}
        >
            {children}
        </div>
    );
};

export const News = ({ item, identifier, showHover }: NewsProp) => {
    return (
        <NewsWrapper
            showHover={showHover}
            item={item}
            identifier={identifier}
        >
            <Title
                date={item.date}
                metadata={
                    <StyledMoonIndicator title={`Relevance: ${Math.round(item.relevance * 100)}%`}>
                        <MoonIndicator percent={item.relevance * 100} />
                    </StyledMoonIndicator>
                }
            >
                {item.headline}
            </Title>
            <Body>
                {item.source}{" "}
                {!item.topics || item.topics.length === 0 ? "" : ` - ${item.topics.join(", ")}`}
            </Body>
        </NewsWrapper>
    );
};
