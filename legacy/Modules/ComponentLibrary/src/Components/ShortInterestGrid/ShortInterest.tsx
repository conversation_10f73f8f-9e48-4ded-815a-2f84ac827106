import React from "react";
import styled from "styled-components";

import { isNumber } from "Utilities/index";

import { ShortInterestData } from "./types";
import { Grid } from "../Grid";
import { colDef } from "./consts";

type Props = {
    data: ShortInterestData[];
};

const TableContainer = styled("div")`
    grid-area: table;
    height: 150px;
`;

export const ShortInterest = ({ data }: Props) => {
    return (
        <TableContainer>
            <Grid
                data={data}
                defaultColDef={{
                    editable: false,
                    sortable: false,
                    cellStyle: ({ value }) => {
                        if (isNumber(value)) {
                            return {
                                textAlign: "right"
                            };
                        }
                        return {};
                    }
                }}
                onGridReady={({ api }) => api.sizeColumnsToFit()}
                getRowId={params => params.data.holderName}
                hasTransparentBackground
                columnDefs={colDef}
            />
        </TableContainer>
    );
};
