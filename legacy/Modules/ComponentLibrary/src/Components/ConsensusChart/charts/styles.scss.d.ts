declare namespace StylesScssNamespace {
    export interface IStylesScss {
        axis: string;
        consensusChartContainer: string;
        date: string;
        dateAxis: string;
        gridLines: string;
        itemColor: string;
        mean: string;
        specialAxis: string;
        statsLine: string;
        stdDev1: string;
        stdDev2: string;
        tooltip: string;
        tooltipContainer: string;
        trackingAxis: string;
    }
}

declare const StylesScssModule: StylesScssNamespace.IStylesScss & {
    /** WARNING: Only available when `css-loader` is used without `style-loader` or `mini-css-extract-plugin` */
    locals: StylesScssNamespace.IStylesScss;
};

export = StylesScssModule;
