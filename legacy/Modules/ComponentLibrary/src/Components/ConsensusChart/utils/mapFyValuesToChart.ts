import moment from "moment";

export const mapFyValuesToChart = (data = []) => {
    if (!data.length) return [];
    const d = data[0];
    return [
        {
            name: "FY1",
            color: "blue",
            date: d.dateTime && moment(d.dateTime, "YYYY-MM-DD").toDate(),
            value: d.fY1
        },
        {
            name: "FY2",
            color: "yellow",
            date: d.dateTime && moment(d.dateTime, "YYYY-MM-DD").toDate(),
            value: d.fY2
        }
    ];
};
