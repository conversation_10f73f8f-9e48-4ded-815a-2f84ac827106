import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";
import { ThemeIterator, LayoutVertical, ThemeContainer } from "PrimitiveComponents/index";
import { withFixtures } from "TransportGateway/index";
import { DaysToCoverChart } from "../DaysToCoverChart";

<Meta
    title="ComponentLibrary/Charts/DaysToCoverChart"
    component={DaysToCoverChart}
/>

export const DaysToCoverMock = withFixtures(DaysToCoverChart, {
    curves: () => import("./fixtures/daysToCover.json"),
    specialDates: () => import("./fixtures/specialDates.json")
});
export const Template = () => (
    <LayoutVertical gap="xx-large">
        <ThemeIterator>
            {() => (
                <ThemeContainer
                    flex
                    style={{ height: 275 }}
                >
                    <DaysToCoverMock />
                </ThemeContainer>
            )}
        </ThemeIterator>
    </LayoutVertical>
);

# DaysToCoverChart

<Description of={DaysToCoverChart} />

<Canvas>
    <Story
        name="Line"
        parameters={{
            controls: {
                disable: false,
                include: []
            }
        }}
        args={{}}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes
    of={DaysToCoverChart}
    sort="requiredFirst"
/>
