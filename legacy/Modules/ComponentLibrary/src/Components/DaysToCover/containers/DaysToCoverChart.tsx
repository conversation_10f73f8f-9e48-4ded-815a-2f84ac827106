import React, { useMemo } from "react";

import { withSchemaOverrides } from "@mocks/api-gateway";
import {
    createConfig,
    reportSubscriptionCommandSpec,
    withCommandStateFactory,
} from "TransportGateway/index";
import { Chart } from "Charting/index";

import { specialDates } from "../../../Utils";

type Props = {
    reportKey?: string;
    reportName?: string;
    specialDates?: null;
};

type DaysToCoverData = {
    dateTime: string;
    daysToCover?: number;
    shortUtil?: number;
    percentOfMarketCap?: number;
    borrowCost?: number;
    shortMomentum?: number;
    squeezeRisk?: number;
    crowdedScore?: number;
    dailyNetMtmPl?: number;
    current?: number;
};

type Params = {
    instrumentId: string;
    period: number;
};

const DaysToCoverChartBase = ({
    data,
    schema: schemaProp,
    hideLegend,
    ...rest
}: ChartProps<DaysToCoverData>) => {
    const schema = useMemo(
        () =>
            withSchemaOverrides(schemaProp, {
                overrides: {
                    dateTime: {
                        properties: {
                            chartPropsMargin: { top: 0, right: 0, bottom: 0, left: 0 },
                            chartLegendPropsShow: !hideLegend,
                        },
                    },
                },
            }),
        [schemaProp, hideLegend],
    );

    return (
        <Chart
            data={data}
            schema={schema}
            {...rest}
        />
    );
};

export const DaysToCoverChart = withCommandStateFactory(
    DaysToCoverChartBase,
    ({
        reportKey,
        reportName = "DaysToCoverRequestHandler",
        specialDates: specialDatesProp,
    }: Props) =>
        createConfig({
            props: reportSubscriptionCommandSpec(
                reportName,
                ({ instrumentId, period }: Params) => ({
                    BBGTicker: instrumentId,
                    dateFrame: period,
                }),
                (data: DaysToCoverData[], _, schema) => ({ data, schema }),
                reportKey,
            ),
            ...(specialDatesProp !== null ? { specialDates: specialDates() } : {}),
        }),
);
