import {
    createConfig,
    reportSubscriptionCommandSpec,
    withCommandStateFactory
} from "TransportGateway/index";
import { Chart } from "Charting/index";
import { specialDates } from "../../../Utils";

type Props = {
    reportKey?: string;
    reportName?: string;
    specialDates?: null;
};

type DaysToCoverData = {
    dateTime: string;
    daysToCover?: number;
    shortUtil?: number;
    percentOfMarketCap?: number;
    borrowCost?: number;
    shortMomentum?: number;
    squeezeRisk?: number;
    crowdedScore?: number;
    dailyNetMtmPl?: number;
    current?: number;
};

type Params = {
    instrumentId: string;
    period: number;
};

export const DaysToCoverChart = withCommandStateFactory(
    Chart,
    ({ reportKey, reportName = "DaysToCoverRequestHandler", specialDates: specialDatesProp, }: Props) =>
        createConfig({
            props: reportSubscriptionCommandSpec(
                reportName,
                ({ instrumentId, period }: Params) => ({
                    BBGTicker: instrumentId,
                    dateFrame: period
                }),
                (data: DaysToCoverData[], _, schema) => ({ data, schema }),
                reportKey
            ),
            ...(
                specialDatesProp !== null ?  { specialDates: specialDates() } : {} 
            )
        })
);
