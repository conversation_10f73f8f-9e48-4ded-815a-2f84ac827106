import React from "react";
import { format } from "d3";
import styled from "styled-components";
import type { BoxPlotProps } from "PrimitiveComponents/index";
import { BoxPlotWidget } from "./BoxPlotWidget";
import { InfoStampProps } from "../Stamp";

export type BoxPlotSingleWidgetProps = InfoStampProps & {
    data?: {
        score: number;
        boxPlots: BoxPlotProps;
        change1w: number;
        change1m: number;
    };
};

const SideContainer = styled.div`
    display: flex;
    justify-content: space-between;
    width: 100%;
`;
export const BoxPlotSingleWidget = ({ title, data, ...props }: BoxPlotSingleWidgetProps) => {
    const { change1w, change1m } = data || {};
    return (
        <BoxPlotWidget
            title={title}
            data={data}
            {...props}
        >
            <SideContainer>
                <div>{format(".2f")(change1w)}</div>
                <div>{format(".2f")(change1m)}</div>
            </SideContainer>
            <SideContainer>
                <div>1 wk ago</div>
                <div>1 m ago</div>
            </SideContainer>
        </BoxPlotWidget>
    );
};
