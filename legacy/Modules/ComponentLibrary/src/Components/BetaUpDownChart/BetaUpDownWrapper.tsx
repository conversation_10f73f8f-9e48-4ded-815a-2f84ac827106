import React, { useMemo, useState } from "react";
import {
    SelectWithLabel,
    DateFrameWithLabel,
    DecimalInputWithLabel,
    LayoutVertical,
    LayoutHorizontal
} from "PrimitiveComponents/index";
import styled from "styled-components";
import { BetaUpDownChart } from "./containers/BetaUpDownChart";

export type BetaUpDownChartsProps = {
    instrumentId: string;
    relativeTicker: string;
    benchmarkTicker: string;
    chartHeight?: number;
};

const PERIODS = [6, 12, 24, 60];

const Heading = styled.h3`
    font-size: var(--font__size--header);
    font-weight: var(--font__weight--header);
`;

export const BetaUpDownWrapper = ({
    instrumentId,
    relativeTicker,
    benchmarkTicker,
    chartHeight = 350
}: BetaUpDownChartsProps) => {
    const [period, setPeriod] = useState(24);
    const [daysLookback, setDaysLookback] = useState<number>(20);
    const indexLookup = useMemo(
        () => ({
            Momentum: "GSPEMFMO Index",
            Growth: "GSPEMFGR Index",
            Value: "GSPEMFVA Index",
            Sector: relativeTicker
        }),
        [relativeTicker]
    );
    const OPTIONS = useMemo(
        () =>
            Object.keys(indexLookup).map(key => ({
                label: key,
                value: key
            })),
        [indexLookup]
    );
    const [comparison, setComparison] = useState<keyof typeof indexLookup>("Sector");
    const handlePeriodChange = val => setPeriod(val);
    const handleSelect = val => setComparison(val);

    return (
        <LayoutVertical spacing="large">
            <LayoutHorizontal style={{ justifyContent: "space-between" }}>
                <DecimalInputWithLabel
                    label="Days Lookback"
                    value={daysLookback}
                    onChange={setDaysLookback}
                />
                <DateFrameWithLabel
                    periods={PERIODS}
                    value={period}
                    onChange={handlePeriodChange}
                />
            </LayoutHorizontal>
            <Heading>vs {benchmarkTicker}</Heading>
            <BetaUpDownChart
                height={chartHeight}
                instrumentId={instrumentId}
                benchmarkTicker={benchmarkTicker}
                dateFrame={period}
                daysLookback={daysLookback}
            />
            <SelectWithLabel
                label="Comparison"
                options={OPTIONS}
                onChange={handleSelect}
                value={comparison}
            />
            <Heading>
                vs {comparison} ({indexLookup[comparison]})
            </Heading>
            <BetaUpDownChart
                height={chartHeight}
                instrumentId={instrumentId}
                benchmarkTicker={indexLookup[comparison]}
                dateFrame={period}
                daysLookback={daysLookback}
            />
        </LayoutVertical>
    );
};
