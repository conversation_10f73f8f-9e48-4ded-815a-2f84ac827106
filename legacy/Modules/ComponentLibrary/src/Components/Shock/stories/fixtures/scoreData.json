{"status": {"results": {}, "isCommandLoading": false, "stateKey": "SubscribeToReport@scoreData-321", "commandId": 1176, "command": {"isSubscription": true, "commandId": 1176, "Headers": [], "name": "SubscribeToReport", "payload": {"ReportKey": "epsShockScore_Ticker", "OutputOperatorName": "", "ParameterValues": {"BBGTicker": "TOP DC Equity", "manualInput": "false"}}}}, "state": {"sync": "completed", "operatorStatus": {"Status": 7, "StatusMessage": null}, "command": {"isSubscription": true, "commandId": 1176, "Headers": [], "name": "SubscribeToReport", "payload": {"ReportKey": "epsShockScore_Ticker", "OutputOperatorName": "", "ParameterValues": {"BBGTicker": "TOP DC Equity", "manualInput": "false"}}}, "status": "Finished: Running execution plan command: 1176 with result True. Message Succeeded in creating 4 nodes 3 connections 1 contexts", "metaData": {"executionplanlocal": "[{\"FromNodeType\":\"Deserializer\",\"FromPath\":\"/<PERSON>_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/ProjectionNode~BBGTickerTOPDCEquitymanualEstimate02manualInputfalse:1176\",\"ToNodeType\":\"Filter\",\"ToPath\":\"/<PERSON>_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/userFilter:1176.in\"},{\"FromNodeType\":\"Filter\",\"FromPath\":\"/<PERSON>_<PERSON>rry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/userFilter:1176\",\"ToNodeType\":\"Sort\",\"ToPath\":\"/<PERSON>_<PERSON>_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/sort:1176.in\"},{\"FromNodeType\":\"Sort\",\"FromPath\":\"/<PERSON>_<PERSON>_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/sort:1176\",\"ToNodeType\":\"Serializer\",\"ToPath\":\"/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/serializer:1176.in\"}]", "epsshockscoretickerxxstatus": "Finished:Committing \"history~BBGTickerTOPDCEquity:1525\" for 00:00:00.0000137", "projectionnodebbgtickertopdcequitymanualestimate02manualinputfalse1176xxstatus": "Succeeded in creating 53 nodes 52 connections 12 contexts", "executionplandatarail0": "[{\"FromNodeType\":\"DataRailLiveRequest_DataRailResponse_ConsumerResponse\",\"FromPath\":\"/ReportEngine>DataRail/<PERSON>_<PERSON>_Sj88xMUQGedY0j9zfOLKoA/EPSShockData_Ticker/fetchData~BBGTickerTOPDCEquityLookback0:1525\",\"ToNodeType\":\"CrossThread\",\"ToPath\":\"/ReportEngine>DataRail/<PERSON>_<PERSON>_Sj88xMUQGedY0j9zfOLKoA/epsShockCurrent_Ticker/CrossThread_[ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAEPSShockDataTickerfetchDataBBGTickerTOPDCEquityLookback01525_ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAepsShockCurrentTickerfetchDataBBGTickerTOPDCEquity1525in]~BBGTickerTOPDCEquity:1525\"},{\"FromNodeType\":\"CrossThread\",\"FromPath\":\"/ReportEngine>DataRail/William_<PERSON>rry_Sj88xMUQGedY0j9zfOLKoA/epsShockCurrent_Ticker/CrossThread_[ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAEPSShockDataTickerfetchDataBBGTickerTOPDCEquityLookback01525_ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAepsShockCurrentTickerfetchDataBBGTickerTOPDCEquity1525in]~BBGTickerTOPDCEquity:1525\",\"ToNodeType\":\"Projection\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockCurrent_Ticker/fetchData~BBGTickerTOPDCEquity:1525.in\"},{\"FromNodeType\":\"Projection\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockCurrent_Ticker/fetchData~BBGTickerTOPDCEquity:1525.out\",\"ToNodeType\":\"Calc\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockCurrent_Ticker/CalcNode~BBGTickerTOPDCEquity:1525.in\"},{\"FromNodeType\":\"Calc\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockCurrent_Ticker/CalcNode~BBGTickerTOPDCEquity:1525\",\"ToNodeType\":\"CrossThread\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/CrossThread_[ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAepsShockCurrentTickerCalcNodeBBGTickerTOPDCEquity1525_ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAepsShockScoreTickercurrentBBGTickerTOPDCEquity1525in]~BBGTickerTOPDCEquity:1525\"},{\"FromNodeType\":\"CrossThread\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/CrossThread_[ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAepsShockCurrentTickerCalcNodeBBGTickerTOPDCEquity1525_ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAepsShockScoreTickercurrentBBGTickerTOPDCEquity1525in]~BBGTickerTOPDCEquity:1525\",\"ToNodeType\":\"Projection\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/current~BBGTickerTOPDCEquity:1525.in\"},{\"FromNodeType\":\"DataRailLiveRequest_DataRailResponse_ConsumerResponse\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/EPSShockData_Ticker/fetchData~BBGTickerTOPDCEquityLookback13:1525\",\"ToNodeType\":\"CrossThread\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockHistory_Ticker/CrossThread_[ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAEPSShockDataTickerfetchDataBBGTickerTOPDCEquityLookback131525_ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAepsShockHistoryTickerfetchDataBBGTickerTOPDCEquityLookback131525in]~BBGTickerTOPDCEquityLookback13:1525\"},{\"FromNodeType\":\"CrossThread\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockHistory_Ticker/CrossThread_[ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAEPSShockDataTickerfetchDataBBGTickerTOPDCEquityLookback131525_ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAepsShockHistoryTickerfetchDataBBGTickerTOPDCEquityLookback131525in]~BBGTickerTOPDCEquityLookback13:1525\",\"ToNodeType\":\"Projection\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockHistory_Ticker/fetchData~BBGTickerTOPDCEquityLookback13:1525.in\"},{\"FromNodeType\":\"Projection\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockHistory_Ticker/fetchData~BBGTickerTOPDCEquityLookback13:1525.out\",\"ToNodeType\":\"GroupBy\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockHistory_Ticker/GroupByNodeMostRecentNumbers~BBGTickerTOPDCEquityLookback13:1525.in\"},{\"FromNodeType\":\"GroupBy\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockHistory_Ticker/GroupByNodeMostRecentNumbers~BBGTickerTOPDCEquityLookback13:1525.detail\",\"ToNodeType\":\"Filter\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockHistory_Ticker/FilterNode~BBGTickerTOPDCEquityLookback13:1525.in\"},{\"FromNodeType\":\"Filter\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockHistory_Ticker/FilterNode~BBGTickerTOPDCEquityLookback13:1525.out\",\"ToNodeType\":\"Sort\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockHistory_Ticker/SortNode~BBGTickerTOPDCEquityLookback13:1525.in\"},{\"FromNodeType\":\"Sort\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockHistory_Ticker/SortNode~BBGTickerTOPDCEquityLookback13:1525.out\",\"ToNodeType\":\"Calc\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockHistory_Ticker/CalcNode~BBGTickerTOPDCEquityLookback13:1525.in\"},{\"FromNodeType\":\"Calc\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockHistory_Ticker/CalcNode~BBGTickerTOPDCEquityLookback13:1525.out\",\"ToNodeType\":\"GroupBy\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockHistory_Ticker/GroupByNode~BBGTickerTOPDCEquityLookback13:1525.in\"},{\"FromNodeType\":\"GroupBy\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockHistory_Ticker/GroupByNode~BBGTickerTOPDCEquityLookback13:1525.detail\",\"ToNodeType\":\"Filter\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockHistory_Ticker/FilterNodeUniqueEvents~BBGTickerTOPDCEquityLookback13:1525.in\"},{\"FromNodeType\":\"Filter\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockHistory_Ticker/FilterNodeUniqueEvents~BBGTickerTOPDCEquityLookback13:1525.out\",\"ToNodeType\":\"GroupBy\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockHistory_Ticker/GroupByNodeMinMax~BBGTickerTOPDCEquityLookback13:1525.in\"},{\"FromNodeType\":\"GroupBy\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockHistory_Ticker/GroupByNodeMinMax~BBGTickerTOPDCEquityLookback13:1525\",\"ToNodeType\":\"CrossThread\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/CrossThread_[ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAepsShockHistoryTickerGroupByNodeMinMaxBBGTickerTOPDCEquityLookback131525_ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAepsShockScoreTickerhistoryBBGTickerTOPDCEquity1525in]~BBGTickerTOPDCEquity:1525\"},{\"FromNodeType\":\"CrossThread\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/CrossThread_[ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAepsShockHistoryTickerGroupByNodeMinMaxBBGTickerTOPDCEquityLookback131525_ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAepsShockScoreTickerhistoryBBGTickerTOPDCEquity1525in]~BBGTickerTOPDCEquity:1525\",\"ToNodeType\":\"Projection\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/history~BBGTickerTOPDCEquity:1525.in\"},{\"FromNodeType\":\"Projection\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/current~BBGTickerTOPDCEquity:1525.out\",\"ToNodeType\":\"Join\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/JoinNode~BBGTickerTOPDCEquity:1525.left\"},{\"FromNodeType\":\"Projection\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/history~BBGTickerTOPDCEquity:1525.out\",\"ToNodeType\":\"Join\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/JoinNode~BBGTickerTOPDCEquity:1525.right\"},{\"FromNodeType\":\"Join\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/JoinNode~BBGTickerTOPDCEquity:1525.out\",\"ToNodeType\":\"DataRailLiveLookupRequest_DataRailResponse_ConsumerResponse\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/DataRailLiveLookupRequestNode~BBGTickerTOPDCEquity:1525.in\"},{\"FromNodeType\":\"DataRailLiveRequest_DataRailResponse_ConsumerResponse\",\"FromPath\":\"/Reports/EPSShockCurrentData/fetchData~Lookback0UniverseRegionCompanyPage\",\"ToNodeType\":\"CrossThread\",\"ToPath\":\"/Reports/epsShockCurrent/CrossThread_[ReportsEPSShockCurrentDatafetchDataLookback0UniverseRegionCompanyPage_ReportsepsShockCurrentfetchDataUniverseRegionCompanyPagein]~UniverseRegionCompanyPage\"},{\"FromNodeType\":\"CrossThread\",\"FromPath\":\"/Reports/epsShockCurrent/CrossThread_[ReportsEPSShockCurrentDatafetchDataLookback0UniverseRegionCompanyPage_ReportsepsShockCurrentfetchDataUniverseRegionCompanyPagein]~UniverseRegionCompanyPage\",\"ToNodeType\":\"Projection\",\"ToPath\":\"/Reports/epsShockCurrent/fetchData~UniverseRegionCompanyPage.in\"},{\"FromNodeType\":\"Projection\",\"FromPath\":\"/Reports/epsShockCurrent/fetchData~UniverseRegionCompanyPage.out\",\"ToNodeType\":\"Calc\",\"ToPath\":\"/Reports/epsShockCurrent/CalcNode~UniverseRegionCompanyPage.in\"},{\"FromNodeType\":\"Calc\",\"FromPath\":\"/Reports/epsShockCurrent/CalcNode~UniverseRegionCompanyPage\",\"ToNodeType\":\"CrossThread\",\"ToPath\":\"/Reports/epsShockScore/CrossThread_[ReportsepsShockCurrentCalcNodeUniverseRegionCompanyPage_ReportsepsShockScorecurrentin]\"},{\"FromNodeType\":\"CrossThread\",\"FromPath\":\"/Reports/epsShockScore/CrossThread_[ReportsepsShockCurrentCalcNodeUniverseRegionCompanyPage_ReportsepsShockScorecurrentin]\",\"ToNodeType\":\"Projection\",\"ToPath\":\"/Reports/epsShockScore/current.in\"},{\"FromNodeType\":\"DataRailLiveRequest_DataRailResponse_ConsumerResponse\",\"FromPath\":\"/Reports/EPSShockData/fetchData~Lookback13UniverseRegionCompanyPage\",\"ToNodeType\":\"CrossThread\",\"ToPath\":\"/Reports/epsShockHistory/CrossThread_[ReportsEPSShockDatafetchDataLookback13UniverseRegionCompanyPage_ReportsepsShockHistoryfetchDataLookback13UniverseRegionCompanyPagein]~Lookback13UniverseRegionCompanyPage\"},{\"FromNodeType\":\"CrossThread\",\"FromPath\":\"/Reports/epsShockHistory/CrossThread_[ReportsEPSShockDatafetchDataLookback13UniverseRegionCompanyPage_ReportsepsShockHistoryfetchDataLookback13UniverseRegionCompanyPagein]~Lookback13UniverseRegionCompanyPage\",\"ToNodeType\":\"Projection\",\"ToPath\":\"/Reports/epsShockHistory/fetchData~Lookback13UniverseRegionCompanyPage.in\"},{\"FromNodeType\":\"Projection\",\"FromPath\":\"/Reports/epsShockHistory/fetchData~Lookback13UniverseRegionCompanyPage.out\",\"ToNodeType\":\"GroupBy\",\"ToPath\":\"/Reports/epsShockHistory/GroupByNodeMostRecentNumbers~Lookback13UniverseRegionCompanyPage.in\"},{\"FromNodeType\":\"GroupBy\",\"FromPath\":\"/Reports/epsShockHistory/GroupByNodeMostRecentNumbers~Lookback13UniverseRegionCompanyPage.detail\",\"ToNodeType\":\"Filter\",\"ToPath\":\"/Reports/epsShockHistory/FilterNode~Lookback13UniverseRegionCompanyPage.in\"},{\"FromNodeType\":\"Filter\",\"FromPath\":\"/Reports/epsShockHistory/FilterNode~Lookback13UniverseRegionCompanyPage.out\",\"ToNodeType\":\"Sort\",\"ToPath\":\"/Reports/epsShockHistory/SortNode~Lookback13UniverseRegionCompanyPage.in\"},{\"FromNodeType\":\"Sort\",\"FromPath\":\"/Reports/epsShockHistory/SortNode~Lookback13UniverseRegionCompanyPage.out\",\"ToNodeType\":\"Calc\",\"ToPath\":\"/Reports/epsShockHistory/CalcNode~Lookback13UniverseRegionCompanyPage.in\"},{\"FromNodeType\":\"Calc\",\"FromPath\":\"/Reports/epsShockHistory/CalcNode~Lookback13UniverseRegionCompanyPage.out\",\"ToNodeType\":\"GroupBy\",\"ToPath\":\"/Reports/epsShockHistory/GroupByNode~Lookback13UniverseRegionCompanyPage.in\"},{\"FromNodeType\":\"GroupBy\",\"FromPath\":\"/Reports/epsShockHistory/GroupByNode~Lookback13UniverseRegionCompanyPage.detail\",\"ToNodeType\":\"Filter\",\"ToPath\":\"/Reports/epsShockHistory/FilterNodeUniqueEvents~Lookback13UniverseRegionCompanyPage.in\"},{\"FromNodeType\":\"Filter\",\"FromPath\":\"/Reports/epsShockHistory/FilterNodeUniqueEvents~Lookback13UniverseRegionCompanyPage.out\",\"ToNodeType\":\"GroupBy\",\"ToPath\":\"/Reports/epsShockHistory/GroupByNodeMinMax~Lookback13UniverseRegionCompanyPage.in\"},{\"FromNodeType\":\"GroupBy\",\"FromPath\":\"/Reports/epsShockHistory/GroupByNodeMinMax~Lookback13UniverseRegionCompanyPage\",\"ToNodeType\":\"CrossThread\",\"ToPath\":\"/Reports/epsShockScore/CrossThread_[ReportsepsShockHistoryGroupByNodeMinMaxLookback13UniverseRegionCompanyPage_ReportsepsShockScorehistoryin]\"},{\"FromNodeType\":\"CrossThread\",\"FromPath\":\"/Reports/epsShockScore/CrossThread_[ReportsepsShockHistoryGroupByNodeMinMaxLookback13UniverseRegionCompanyPage_ReportsepsShockScorehistoryin]\",\"ToNodeType\":\"Projection\",\"ToPath\":\"/Reports/epsShockScore/history.in\"},{\"FromNodeType\":\"Projection\",\"FromPath\":\"/Reports/epsShockScore/current.out\",\"ToNodeType\":\"Join\",\"ToPath\":\"/Reports/epsShockScore/JoinNode.left\"},{\"FromNodeType\":\"Projection\",\"FromPath\":\"/Reports/epsShockScore/history.out\",\"ToNodeType\":\"Join\",\"ToPath\":\"/Reports/epsShockScore/JoinNode.right\"},{\"FromNodeType\":\"Join\",\"FromPath\":\"/Reports/epsShockScore/JoinNode.out\",\"ToNodeType\":\"Calc\",\"ToPath\":\"/Reports/epsShockScore/CalcNode.in\"},{\"FromNodeType\":\"Calc\",\"FromPath\":\"/Reports/epsShockScore/CalcNode\",\"ToNodeType\":\"CrossThread\",\"ToPath\":\"/Reports/epsShockScore_sector/CrossThread_[ReportsepsShockScoreCalcNode_ReportsepsShockScoresectorepsShockScorein]\"},{\"FromNodeType\":\"CrossThread\",\"FromPath\":\"/Reports/epsShockScore_sector/CrossThread_[ReportsepsShockScoreCalcNode_ReportsepsShockScoresectorepsShockScorein]\",\"ToNodeType\":\"Projection\",\"ToPath\":\"/Reports/epsShockScore_sector/epsShockScore.in\"},{\"FromNodeType\":\"Projection\",\"FromPath\":\"/Reports/epsShockScore_sector/epsShockScore.out\",\"ToNodeType\":\"DataRailLiveLookupRequest_DataRailResponse_ConsumerResponse\",\"ToPath\":\"/Reports/epsShockScore_sector/DataRailLiveLookupRequestNode.in\"},{\"FromNodeType\":\"DataRailLiveLookupRequest_DataRailResponse_ConsumerResponse\",\"FromPath\":\"/Reports/epsShockScore_sector/DataRailLiveLookupRequestNode.out\",\"ToNodeType\":\"Filter\",\"ToPath\":\"/Reports/epsShockScore_sector/FilterNode.in\"},{\"FromNodeType\":\"Filter\",\"FromPath\":\"/Reports/epsShockScore_sector/FilterNode.out\",\"ToNodeType\":\"GroupBy\",\"ToPath\":\"/Reports/epsShockScore_sector/GroupByNode.in\"},{\"FromNodeType\":\"GroupBy\",\"FromPath\":\"/Reports/epsShockScore_sector/GroupByNode.out\",\"ToNodeType\":\"Calc\",\"ToPath\":\"/Reports/epsShockScore_sector/CalcNode.in\"},{\"FromNodeType\":\"Calc\",\"FromPath\":\"/Reports/epsShockScore_sector/CalcNode\",\"ToNodeType\":\"CrossThread\",\"ToPath\":\"/Reports/epsShockScore_Ticker/CrossThread_[ReportsepsShockScoresectorCalcNode_ReportsepsShockScoreTickerepsShockScoresectorin]\"},{\"FromNodeType\":\"CrossThread\",\"FromPath\":\"/Reports/epsShockScore_Ticker/CrossThread_[ReportsepsShockScoresectorCalcNode_ReportsepsShockScoreTickerepsShockScoresectorin]\",\"ToNodeType\":\"Projection\",\"ToPath\":\"/Reports/epsShockScore_Ticker/epsShockScore_sector.in\"},{\"FromNodeType\":\"DataRailLiveLookupRequest_DataRailResponse_ConsumerResponse\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/DataRailLiveLookupRequestNode~BBGTickerTOPDCEquity:1525.out\",\"ToNodeType\":\"Join\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/JoinNodesector~BBGTickerTOPDCEquity:1525.left\"},{\"FromNodeType\":\"Projection\",\"FromPath\":\"/Reports/epsShockScore_Ticker/epsShockScore_sector.out\",\"ToNodeType\":\"CrossThread\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/CrossThread_[ReportsepsShockScoreTickerepsShockScoresectorout_ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAepsShockScoreTickerJoinNodesectorBBGTickerTOPDCEquity1525right]~BBGTickerTOPDCEquity:1525\"},{\"FromNodeType\":\"CrossThread\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/CrossThread_[ReportsepsShockScoreTickerepsShockScoresectorout_ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAepsShockScoreTickerJoinNodesectorBBGTickerTOPDCEquity1525right]~BBGTickerTOPDCEquity:1525\",\"ToNodeType\":\"Join\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/JoinNodesector~BBGTickerTOPDCEquity:1525.right\"},{\"FromNodeType\":\"Join\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/JoinNodesector~BBGTickerTOPDCEquity:1525.out\",\"ToNodeType\":\"Calc\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/CalcNode~BBGTickerTOPDCEquitymanualEstimate02manualInputfalse:1525.in\"},{\"FromNodeType\":\"Calc\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/CalcNode~BBGTickerTOPDCEquitymanualEstimate02manualInputfalse:1525.out\",\"ToNodeType\":\"Projection\",\"ToPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/ProjectionNode~BBGTickerTOPDCEquitymanualEstimate02manualInputfalse:1525.in\"},{\"FromNodeType\":\"Projection\",\"FromPath\":\"/ReportEngine>DataRail/William_Corry_Sj88xMUQGedY0j9zfOLKoA/epsShockScore_Ticker/ProjectionNode~BBGTickerTOPDCEquitymanualEstimate02manualInputfalse:1525\",\"ToNodeType\":\"Serializer\",\"ToPath\":\"/ReportEngine>DataRail/epsShockScore_Ticker/serializer:1525.in\"}]", "epsshockscoresectorxxstatus": "Finished:Committing \"CrossThread_[ReportsepsShockScoreCalcNode_ReportsepsShockScoresectorepsShockScorein]\" for 00:00:00.0000176", "dataraillivelookuprequestnodexxstatus": "Finished:Running  0 Loading 2501 rows into \"DataRail\" table (Store Responses (DataStore: GicsSector, Responses: 2501).) for 221 milliseconds|", "epsshockscorexxstatus": "Finished:Committing \"CrossThread_[ReportsepsShockHistoryGroupByNodeMinMaxLookback13UniverseRegionCompanyPage_ReportsepsShockScorehistoryin]\" for 00:00:00.0000367", "epsshockhistoryxxstatus": "Finished:Committing \"CrossThread_[ReportsEPSShockDatafetchDataLookback13UniverseRegionCompanyPage_ReportsepsShockHistoryfetchDataLookback13UniverseRegionCompanyPagein]~Lookback13UniverseRegionCompanyPage\" for 00:00:00.0000654", "fetchdatalookback13universeregioncompanypagexxstatus": "Finished:Running Getting data from \"DataRail\"  for 9 minutes, 20 seconds|", "epsshockcurrentxxstatus": "Finished:Committing \"CrossThread_[ReportsEPSShockCurrentDatafetchDataLookback0UniverseRegionCompanyPage_ReportsepsShockCurrentfetchDataUniverseRegionCompanyPagein]~UniverseRegionCompanyPage\" for 00:00:00.0000789", "fetchdatalookback0universeregioncompanypagexxstatus": "Finished:Running Live Reload - Loading 2543 rows into \"DataRail\" table  for 9 milliseconds|", "rowcount": 273, "epsshockcurrenttickerxxstatus": "Finished:Committing \"CrossThread_[ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAEPSShockDataTickerfetchDataBBGTickerTOPDCEquityLookback01525_ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAepsShockCurrentTickerfetchDataBBGTickerTOPDCEquity1525in]~BBGTickerTOPDCEquity:1525\" for 00:00:00.0000624", "epsshockhistorytickerxxstatus": "Finished:Committing \"CrossThread_[ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAEPSShockDataTickerfetchDataBBGTickerTOPDCEquityLookback131525_ReportEngineDataRailWilliamCorrySj88xMUQGedY0j9zfOLKoAepsShockHistoryTickerfetchDataBBGTickerTOPDCEquityLookback131525in]~BBGTickerTOPDCEquityLookback13:1525\" for 00:00:00.0025796", "fetchdatabbgtickertopdcequitylookback131525xxstatus": "Finished:Running Loading 273 rows into \"DataRail\" table  for 3 milliseconds|", "dataraillivelookuprequestnodebbgtickertopdcequity1525xxstatus": "Finished:Running  0 Loading 1 rows into \"DataRail\" table  for 0 milliseconds|"}, "data": [{"rank": 0, "sector": "Insurance", "sector_numberOfStocks": 9, "sector_avgShock": -0.4670069418544608, "ticker": "TOP DC Equity", "numbersDate": "2022-04-27-00.00.00", "minAdjDegrees": 0.16376175011160396, "maxAdjDegrees": 1.3737121261155842, "avgDegrees": 1.3921252592860034, "currentChg": -0.00711295, "ePSFy1Chg": -0.010005, "ePSFy2Chg": -0.0042209, "degreesChg": -0.40753937797631745, "upperBandDegrees": 2.740940127051534, "lowerBandDegrees": -1.259059872948466, "epsShock": -1.1484795050278511, "rowId": 0}], "lastUpdated": "2022-04-28T13:16:45.939Z", "snapshot": true, "schema": [{"ColumnId": 3, "ContentType": 5, "Name": "Rank", "Traits": "hidden", "MetaData": {"DisplayName": null, "Width": null, "Sortable": false, "Searchable": false, "Hidden": false, "Flags": {}, "DimensionName": null, "NullValue": {"BoolValue": null, "NullableBoolValue": null, "ByteValue": null, "IntValue": null, "ShortValue": null, "LongValue": null, "FloatValue": null, "DoubleValue": null, "DecimalValue": null, "StringValue": null, "NullValue": false, "DateValue": null, "DateTimeValue": null}, "ContentTypeForDisplay": null}}, {"ColumnId": 4, "ContentType": 5, "Name": "Previous", "Traits": "hidden,previous", "MetaData": {"DisplayName": null, "Width": null, "Sortable": false, "Searchable": false, "Hidden": false, "Flags": {}, "DimensionName": null, "NullValue": {"BoolValue": null, "NullableBoolValue": null, "ByteValue": null, "IntValue": null, "ShortValue": null, "LongValue": null, "FloatValue": null, "DoubleValue": null, "DecimalValue": null, "StringValue": null, "NullValue": false, "DateValue": null, "DateTimeValue": null}, "ContentTypeForDisplay": null}}, {"ColumnId": 5, "ContentType": 9, "Name": "sector", "Traits": null, "MetaData": {"DisplayName": null, "Width": null, "Sortable": false, "Searchable": false, "Hidden": false, "Flags": {}, "DimensionName": null, "NullValue": {"BoolValue": null, "NullableBoolValue": null, "ByteValue": null, "IntValue": null, "ShortValue": null, "LongValue": null, "FloatValue": null, "DoubleValue": null, "DecimalValue": null, "StringValue": null, "NullValue": false, "DateValue": null, "DateTimeValue": null}, "ContentTypeForDisplay": null}}, {"ColumnId": 6, "ContentType": 5, "Name": "sector_numberOfStocks", "Traits": null, "MetaData": {"DisplayName": null, "Width": null, "Sortable": false, "Searchable": false, "Hidden": false, "Flags": {}, "DimensionName": null, "NullValue": {"BoolValue": null, "NullableBoolValue": null, "ByteValue": null, "IntValue": null, "ShortValue": null, "LongValue": null, "FloatValue": null, "DoubleValue": null, "DecimalValue": null, "StringValue": null, "NullValue": false, "DateValue": null, "DateTimeValue": null}, "ContentTypeForDisplay": null}}, {"ColumnId": 7, "ContentType": 8, "Name": "sector_avgShock", "Traits": null, "MetaData": {"DisplayName": null, "Width": null, "Sortable": false, "Searchable": false, "Hidden": false, "Flags": {}, "DimensionName": null, "NullValue": {"BoolValue": null, "NullableBoolValue": null, "ByteValue": null, "IntValue": null, "ShortValue": null, "LongValue": null, "FloatValue": null, "DoubleValue": null, "DecimalValue": null, "StringValue": null, "NullValue": false, "DateValue": null, "DateTimeValue": null}, "ContentTypeForDisplay": null}}, {"ColumnId": 8, "ContentType": 9, "Name": "Ticker", "Traits": null, "MetaData": {"DisplayName": null, "Width": null, "Sortable": false, "Searchable": false, "Hidden": false, "Flags": {}, "DimensionName": null, "NullValue": {"BoolValue": null, "NullableBoolValue": null, "ByteValue": null, "IntValue": null, "ShortValue": null, "LongValue": null, "FloatValue": null, "DoubleValue": null, "DecimalValue": null, "StringValue": null, "NullValue": false, "DateValue": null, "DateTimeValue": null}, "ContentTypeForDisplay": null}}, {"ColumnId": 9, "ContentType": 11, "Name": "numbersDate", "Traits": null, "MetaData": {"DisplayName": null, "Width": null, "Sortable": false, "Searchable": false, "Hidden": false, "Flags": {}, "DimensionName": null, "NullValue": {"BoolValue": null, "NullableBoolValue": null, "ByteValue": null, "IntValue": null, "ShortValue": null, "LongValue": null, "FloatValue": null, "DoubleValue": null, "DecimalValue": null, "StringValue": null, "NullValue": false, "DateValue": null, "DateTimeValue": null}, "ContentTypeForDisplay": null}}, {"ColumnId": 10, "ContentType": 8, "Name": "minAdjDegrees", "Traits": null, "MetaData": {"DisplayName": null, "Width": null, "Sortable": false, "Searchable": false, "Hidden": false, "Flags": {}, "DimensionName": null, "NullValue": {"BoolValue": null, "NullableBoolValue": null, "ByteValue": null, "IntValue": null, "ShortValue": null, "LongValue": null, "FloatValue": null, "DoubleValue": null, "DecimalValue": null, "StringValue": null, "NullValue": false, "DateValue": null, "DateTimeValue": null}, "ContentTypeForDisplay": null}}, {"ColumnId": 11, "ContentType": 8, "Name": "maxAdjDegrees", "Traits": null, "MetaData": {"DisplayName": null, "Width": null, "Sortable": false, "Searchable": false, "Hidden": false, "Flags": {}, "DimensionName": null, "NullValue": {"BoolValue": null, "NullableBoolValue": null, "ByteValue": null, "IntValue": null, "ShortValue": null, "LongValue": null, "FloatValue": null, "DoubleValue": null, "DecimalValue": null, "StringValue": null, "NullValue": false, "DateValue": null, "DateTimeValue": null}, "ContentTypeForDisplay": null}}, {"ColumnId": 12, "ContentType": 8, "Name": "avgDegrees", "Traits": null, "MetaData": {"DisplayName": null, "Width": null, "Sortable": false, "Searchable": false, "Hidden": false, "Flags": {}, "DimensionName": null, "NullValue": {"BoolValue": null, "NullableBoolValue": null, "ByteValue": null, "IntValue": null, "ShortValue": null, "LongValue": null, "FloatValue": null, "DoubleValue": null, "DecimalValue": null, "StringValue": null, "NullValue": false, "DateValue": null, "DateTimeValue": null}, "ContentTypeForDisplay": null}}, {"ColumnId": 13, "ContentType": 8, "Name": "currentChg", "Traits": null, "MetaData": {"DisplayName": null, "Width": null, "Sortable": false, "Searchable": false, "Hidden": false, "Flags": {}, "DimensionName": null, "NullValue": {"BoolValue": null, "NullableBoolValue": null, "ByteValue": null, "IntValue": null, "ShortValue": null, "LongValue": null, "FloatValue": null, "DoubleValue": null, "DecimalValue": null, "StringValue": null, "NullValue": false, "DateValue": null, "DateTimeValue": null}, "ContentTypeForDisplay": null}}, {"ColumnId": 14, "ContentType": 8, "Name": "EPSFy1Chg", "Traits": null, "MetaData": {"DisplayName": null, "Width": null, "Sortable": false, "Searchable": false, "Hidden": false, "Flags": {}, "DimensionName": null, "NullValue": {"BoolValue": null, "NullableBoolValue": null, "ByteValue": null, "IntValue": null, "ShortValue": null, "LongValue": null, "FloatValue": null, "DoubleValue": null, "DecimalValue": null, "StringValue": null, "NullValue": false, "DateValue": null, "DateTimeValue": null}, "ContentTypeForDisplay": null}}, {"ColumnId": 15, "ContentType": 8, "Name": "EPSFy2Chg", "Traits": null, "MetaData": {"DisplayName": null, "Width": null, "Sortable": false, "Searchable": false, "Hidden": false, "Flags": {}, "DimensionName": null, "NullValue": {"BoolValue": null, "NullableBoolValue": null, "ByteValue": null, "IntValue": null, "ShortValue": null, "LongValue": null, "FloatValue": null, "DoubleValue": null, "DecimalValue": null, "StringValue": null, "NullValue": false, "DateValue": null, "DateTimeValue": null}, "ContentTypeForDisplay": null}}, {"ColumnId": 16, "ContentType": 8, "Name": "degreesChg", "Traits": null, "MetaData": {"DisplayName": null, "Width": null, "Sortable": false, "Searchable": false, "Hidden": false, "Flags": {}, "DimensionName": null, "NullValue": {"BoolValue": null, "NullableBoolValue": null, "ByteValue": null, "IntValue": null, "ShortValue": null, "LongValue": null, "FloatValue": null, "DoubleValue": null, "DecimalValue": null, "StringValue": null, "NullValue": false, "DateValue": null, "DateTimeValue": null}, "ContentTypeForDisplay": null}}, {"ColumnId": 17, "ContentType": 8, "Name": "upperBandDegrees", "Traits": null, "MetaData": {"DisplayName": null, "Width": null, "Sortable": false, "Searchable": false, "Hidden": false, "Flags": {}, "DimensionName": null, "NullValue": {"BoolValue": null, "NullableBoolValue": null, "ByteValue": null, "IntValue": null, "ShortValue": null, "LongValue": null, "FloatValue": null, "DoubleValue": null, "DecimalValue": null, "StringValue": null, "NullValue": false, "DateValue": null, "DateTimeValue": null}, "ContentTypeForDisplay": null}}, {"ColumnId": 18, "ContentType": 8, "Name": "lowerBandDegrees", "Traits": null, "MetaData": {"DisplayName": null, "Width": null, "Sortable": false, "Searchable": false, "Hidden": false, "Flags": {}, "DimensionName": null, "NullValue": {"BoolValue": null, "NullableBoolValue": null, "ByteValue": null, "IntValue": null, "ShortValue": null, "LongValue": null, "FloatValue": null, "DoubleValue": null, "DecimalValue": null, "StringValue": null, "NullValue": false, "DateValue": null, "DateTimeValue": null}, "ContentTypeForDisplay": null}}, {"ColumnId": 19, "ContentType": 8, "Name": "epsShock", "Traits": null, "MetaData": {"DisplayName": null, "Width": null, "Sortable": false, "Searchable": false, "Hidden": false, "Flags": {}, "DimensionName": null, "NullValue": {"BoolValue": null, "NullableBoolValue": null, "ByteValue": null, "IntValue": null, "ShortValue": null, "LongValue": null, "FloatValue": null, "DoubleValue": null, "DecimalValue": null, "StringValue": null, "NullValue": false, "DateValue": null, "DateTimeValue": null}, "ContentTypeForDisplay": null}}], "stateKey": "SubscribeToReport@scoreData-321"}}