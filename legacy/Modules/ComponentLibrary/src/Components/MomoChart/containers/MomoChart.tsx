import React, { useMemo } from "react";

import { withSchemaOverrides } from "@mocks/api-gateway";
import {
    createConfig,
    reportSubscriptionCommandSpec,
    requestResponseCommandSpec,
    withCommandStateFactory,
} from "TransportGateway/index";
import { Chart } from "Charting/index";

import { mapSpecialDates } from "../../../Utils";

export enum MomoChartCommands {
    momoCommand = "peMomoChartRequestHandler",
    revCommand = "revCycleChg",
    ebitdaCommand = "ebitdaCycleChg",
}

enum MomoChartNodes {
    momoCommand = "peMomoChartRequestHandlerProjection",
    revCommand = "revCycleChgProjection",
    ebitdaCommand = "ebitdaCycleChgProjection",
}

type MomoData = {
    dateTime: string;
    price?: number;
    momo?: number;
};

const eventsCommandName =
    "RaidData_GetEventRequestDto_IEnumerable[CompleteEventDataDto]_RequestResponse_Handler";

type Props = {
    report: keyof typeof MomoChartCommands;
};

type Params = {
    instrumentId: string;
    period: number;
};

const MomoChartBase = ({ schema: schemaProp, schemaOverride, ...rest }) => {
    const schema = useMemo(
        () => withSchemaOverrides(schemaProp, schemaOverride ?? {}),
        [schemaProp],
    );

    return (
        <Chart
            schema={schema}
            {...rest}
        />
    );
};

export const MomoChart = withCommandStateFactory(MomoChartBase, ({ report }: Props) =>
    createConfig({
        props: reportSubscriptionCommandSpec(
            MomoChartCommands[report],
            ({ instrumentId, period }: Params) => ({ bbgTicker: instrumentId, dateFrame: period }),
            (data: MomoData, props, schema) => ({ data, schema }),
            MomoChartNodes[report],
        ),
        specialDates: requestResponseCommandSpec(
            eventsCommandName,
            ({ instrumentId: BBGTicker, period }: Params) => ({
                BBGTicker,
                dateFrame: period,
                eventTypes: [1, 2, 3],
            }),
            mapSpecialDates,
        ),
    }),
);
