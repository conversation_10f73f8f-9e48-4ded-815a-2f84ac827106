import React, { ReactNode } from "react";
import type { ButtonProps } from "antd/lib/button";
import { Button, Popover } from "antd";
import type { PopoverProps } from "antd/lib/popover";
import classnames from "classnames";
import styled, { createGlobalStyle } from "styled-components";

import { GamePlanPreviewById } from "../GamePlanPreviewById";

import GamePlanIcon from "./icons/GamePlan.svg";

let tooltipContainer: HTMLDivElement;

const getPopupContainer = () => {
    if (!tooltipContainer) {
        tooltipContainer = document.createElement("div");
        tooltipContainer.className = "with-antd raid-tooltip-container-outline";
        document.body.appendChild(tooltipContainer);
    }

    return tooltipContainer;
};

type GamePlanButtonProps = {
    gamePlanId: number;
    useIcon?: boolean;
    showOutline?: boolean;
    label?: ReactNode;
    popoverWidth?: number;
    popoverMinHeight?: number;
    popoverProps?: Omit<PopoverProps, "content">;
} & Omit<ButtonProps, "children">;

const Icon = styled("img")`
    &&& {
        display: block;
        height: 1em;
        width: auto;
        cursor: pointer;
        &.disabled {
            cursor: inherit;
            opacity: 0.5;
        }
    }
`;

const GlobalStyles = createGlobalStyle`
    .raid-tooltip-container-outline {
        &.with-antd .ant-popover-content,
        .ant-popover-content {
            box-shadow: 8px 0 20px 5px #00000063;
            > .ant-popover-inner {
                border: 1px solid var(--border__color--outline);
                .ant-popover-inner-content {
                    padding: 5px 10px;
                    font-size: 12px;
                
                    .ant-popover-message {
                        font-size: inherit;
                    }
                }
            }
        }
    }
`;

export const GamePlanButton = ({
    gamePlanId,
    useIcon = true,
    showOutline = false,
    disabled = false,
    label = "GP",
    className,
    popoverWidth = 592,
    popoverMinHeight = 550,
    popoverProps,
    ...rest
}: GamePlanButtonProps) => {
    if (!gamePlanId) {
        return null;
    }

    const contentInner = useIcon ? (
        <Icon
            className={classnames({ disabled }, className)}
            src={GamePlanIcon}
        />
    ) : (
        label
    );

    const content = showOutline ? (
        <Button
            disabled={disabled}
            className={className}
            {...rest}
            type="default"
            size="small"
        >
            {contentInner}
        </Button>
    ) : (
        contentInner
    );

    if (disabled) {
        return <>{content}</>;
    }

    return (
        <>
            <GlobalStyles />
            <Popover
                getPopupContainer={getPopupContainer}
                destroyTooltipOnHide
                autoAdjustOverflow
                placement="rightTop"
                title="Game Plan"
                trigger="click"
                {...popoverProps}
                content={
                    <div style={{ width: popoverWidth, minHeight: popoverMinHeight }}>
                        <GamePlanPreviewById gamePlanId={gamePlanId} />
                    </div>
                }
            >
                {content}
            </Popover>
        </>
    );
};
