import React from "react";
import styled from "styled-components";
import { Switch } from "antd";
import { HedgeParams } from "./types";

interface ToggleInputProps {
    title: string;
    value: boolean;
    accessor: string;
    onChange: (changed: Partial<HedgeParams>) => void;
}
interface InnerWrapperProps {
    isDisabled: boolean;
    hasHighLimitInput?: boolean;
}

export const InnerWrapper = styled("div")<InnerWrapperProps>`
    max-width: ${({ hasHighLimitInput }) => (hasHighLimitInput ? "310px" : "215px")};
    display: flex;
    flex: 1;
    align-items: center;
    position: relative;
    opacity: ${({ isDisabled }) => (isDisabled ? "0.4" : "1")};
`;

export const Title = styled("span")`
    width: 157px;
`;

export const InputContainer = styled("div")`
    width: 530px;
    display: flex;
    align-items: center;
    margin-top: 15px;
    margin-left: 20px;
`;

export const DisableToggle = styled("div")`
    margin-right: 10px;
`;

export const ToggleInput: React.FC<ToggleInputProps> = ({ title, accessor, value, onChange }) => {
    const toggle = () => {
        const updated = {};
        updated[accessor] = !value;
        onChange(updated);
    };

    return (
        <InputContainer>
            <DisableToggle>
                <Switch
                    onChange={toggle}
                    checked={value}
                />
            </DisableToggle>
            <Title>{title} </Title>
            <InnerWrapper isDisabled={value} />
        </InputContainer>
    );
};
