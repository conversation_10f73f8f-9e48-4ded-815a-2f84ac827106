export type HedgeParamValue = {
    apply: boolean;
    limit: number;
    highLimit?: number;
};

export enum HedgeParamLong {
    Buy = 1,
    Sell = 0,
    BuySell = -1
}

export type HedgeParams = {
    long: HedgeParamLong;
    correlation: HedgeParamValue;
    regressionSlope: HedgeParamValue;
    regressionLevel: HedgeParamValue;
    distfrom50d: HedgeParamValue;
    weightFilter: HedgeParamValue;
    minMembers: HedgeParamValue;
    maxFactorRisk: HedgeParamValue;
    excludePeers: boolean;
};
