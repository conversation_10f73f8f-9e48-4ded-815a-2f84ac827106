import React, { ComponentType } from "react";

import { useHedgeParams } from "./context";
import type { HedgeTableWrapperProps } from "./HedgeTableWrapper";
import { HedgeParams } from "./types";

// @TODO: fix the types here - the outer props are not correct so currently removing and adding onChange back on :(
export const withHedgeParams = <P extends Omit<HedgeTableWrapperProps, "onChange">>(
    Component: ComponentType<P>
) => {
    const WrappedComponent = ({
        hedgeParams: hedgeParamsProp,
        ...rest
    }: Omit<P, "hedgeParams" | "onChange"> & { hedgeParams?: HedgeParams } & {
        onChange?: HedgeTableWrapperProps["onChange"];
    }) => {
        const [hedgeParams] = useHedgeParams();

        return (
            <Component
                {...(rest as P & {
                    onChange?: HedgeTableWrapperProps["onChange"];
                })}
                hedgeParams={{
                    ...hedgeParams,
                    ...hedgeParamsProp
                }}
            />
        );
    };

    WrappedComponent.displayName = `withHedgeParams(${Component.displayName || Component.name})`;

    return WrappedComponent;
};
