export const fcfYieldMapToBoxPlot = (data: any[] = []) => {
    if (!data.length) return undefined;

    const { fCFSum, fCFZScore, fCFAvg, fCFMax, fCFMin } = data[data.length - 1];

    const boxPlots = [
        {
            period: "2 YR",
            low: fCFMin,
            high: fCFMax,
            avg: fCFAvg,
            sd: fCFZScore,
            curr: fCFSum
        }
    ];
    return {
        score: fCFSum,
        boxPlots
    };
};
