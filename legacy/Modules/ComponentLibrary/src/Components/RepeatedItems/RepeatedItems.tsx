import React, { ComponentType, ReactNode } from "react";
import styled from "styled-components";

import { Viewport, ViewportProps } from "PrimitiveComponents/index";

type RepeatedItemsProps<I, P extends {}> = {
    items: I[];
    children: (item: I, index: number) => ReactNode;
    Container?: ComponentType<P>;
    containerProps?: P;
    overscan?: number;
} & Omit<ViewportProps, "children">;

const ViewportContainer = styled(Viewport)<{ overscan?: number }>`
    &&&&&&&& {
        flex-grow: 1;
        ${({ overscan }) => `
        margin-top: -${overscan}px;
        padding-top: ${overscan}px;
        margin-bottom: -${overscan}px;
        padding-bottom: ${overscan}px;
    `}
    }
`;

export const RepeatedItems = <I, P extends {}>({
    items,
    children,
    Container,
    containerProps,
    overscan,
    ...viewportProps
}: RepeatedItemsProps<I, P>) => {
    return (
        items?.map((item, index) => {
            const key = index; // @TODO: Do not use Array index in keys react/no-array-index-key
            return Container ? (
                <Container
                    key={key}
                    {...containerProps}
                >
                    <ViewportContainer
                        {...viewportProps}
                        overscan={overscan}
                    >
                        {children(item, index)}
                    </ViewportContainer>
                </Container>
            ) : (
                <ViewportContainer
                    key={key}
                    {...viewportProps}
                    overscan={overscan}
                >
                    {children(item, index)}
                </ViewportContainer>
            );
        }) || null
    );
};
