import {
    createConfig,
    reportSubscriptionCommandSpec,
    withCommandStateFactory
} from "TransportGateway/index";
import { Grid } from "../Grid";

type RecentFiredSignalsProps = {
    instrumentId: string;
};

type FactoryProps = {
    report?: string;
};

export const RecentFiredSignals = withCommandStateFactory(
    Grid,
    ({ report = "recentFiredSignals" }: FactoryProps) =>
        createConfig({
            props: reportSubscriptionCommandSpec(
                report,
                ({ instrumentId }: RecentFiredSignalsProps) =>
                    instrumentId && {
                        bbgTicker: instrumentId
                    },
                (data, props, schema) => ({
                    data,
                    schema
                }),
                "ProjectionNode"
            )
        }),
    { forceOwnLoadingState: true }
);
