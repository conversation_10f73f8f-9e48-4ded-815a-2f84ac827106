import {
    createConfig,
    reportSubscriptionCommandSpec,
    withCommandState
} from "TransportGateway/index";
import { fullPeersColDef } from "../consts";
import { PeersFullGrid } from "../PeersFullGrid";
import { mapFullPeersTable } from "../utils";

export const PeersFullTableInternal = withCommandState(
    PeersFullGrid(fullPeersColDef),
    createConfig({
        data: reportSubscriptionCommandSpec(
            "PeerData",
            ({ instrumentId, peers = [] }) => ({ bbgTicker: instrumentId, tickers: peers }),
            mapFullPeersTable
        )
    })
);
