import React from "react";
import { LayoutContainer } from "PrimitiveComponents/index";
import { DriverPerformanceGrid } from "./containers";
import { InfoStampProps, InfoStamp } from "../Stamp";

type Props = InfoStampProps & {
    instrumentId: string;
    period: number;
    hedgeTicker?: string;
    model?: string;
    hedged: boolean;
    height?: number;
    factorSelector?: (factor: string) => void;
    data?: Record<string, unknown>[];
};

export const DriverPerformanceStamp = ({
    title,
    width = 222,
    height = 320,
    instrumentId,
    period,
    hedgeTicker,
    model,
    factorSelector,
    ...props
}: Props) => (
    <InfoStamp
        title={title}
        {...props}
        width={width}
    >
        <LayoutContainer height={height}>
            <DriverPerformanceGrid
                instrumentId={instrumentId}
                period={period}
                hedgeTicker={hedgeTicker}
                model={model}
                factorSelector={factorSelector}
                height={height}
                {...props}
            />
        </LayoutContainer>
    </InfoStamp>
);
