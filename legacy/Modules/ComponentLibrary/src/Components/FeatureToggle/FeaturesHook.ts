import React, { useState, useEffect } from "react";
import { useViewServerCommand, RequestState } from "TransportGateway/index";
import { FeatureToggleState } from "./Models";

type SetStateAction<S> = S | ((prevState: S) => S);
type Dispatch<A> = (value: A) => void;
type RaidSettings = { [key: string]: any };

const EVENT_KEY = "useLocalStorage";
const VIEWSERVER_CONNECTION = window.TE_CONNECTION_NAME || "global";

export const useRaidSettings = (): [RaidSettings] => {
    const command = `RaidData_GetDashboardUserConfigRequest_GetDashboardUserConfigResponse_RequestResponse_Handler`;
    const payload = {};
    const raidSettingsRequest = useViewServerCommand<{ userConfig: string }>(
        VIEWSERVER_CONNECTION,
        command,
        payload
    );
    const [raidSettings, setRaidSettings] = useState<RaidSettings>({});
    useEffect(() => {
        if (raidSettingsRequest.status === RequestState.Done) {
            setRaidSettings(JSON.parse(raidSettingsRequest.data.userConfig));
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [raidSettingsRequest.status]); // get ts error if you add raidSettingsRequest.data.userConfig to dependency array
    return [raidSettings];
};

const useLocalStorage = <S>(key: string): [S, Dispatch<SetStateAction<S>>] => {
    const [raidSettings] = useRaidSettings();
    const readLocalStorage = React.useCallback(() => localStorage.getItem(key) || "{}", [key]);

    // Store string representation to make skipping updates easier
    const [storageJson, setStorageJson] = React.useState(readLocalStorage);

    useEffect(() => {
        setStorageJson(readLocalStorage()); // In case it updated whilst mounting or key changed

        const handler = () => setStorageJson(readLocalStorage());

        window.addEventListener(EVENT_KEY, handler);
        return () => window.removeEventListener(EVENT_KEY, handler);
    }, [readLocalStorage, setStorageJson]);

    const state = React.useMemo(() => JSON.parse(storageJson), [storageJson]) as S;
    const setState: Dispatch<SetStateAction<S>> = React.useCallback(
        (action: any) => {
            const existing = JSON.parse(readLocalStorage()) as S;
            const next = typeof action === "function" ? action(existing) : action;
            localStorage.setItem(key, JSON.stringify(next));
            window.dispatchEvent(new CustomEvent(EVENT_KEY));
        },
        [key, readLocalStorage]
    );

    return [{ ...state, ...raidSettings }, setState];
};

const STORAGE_KEY = "features";

export const useFeatures = () => useLocalStorage<FeatureToggleState>(STORAGE_KEY);
