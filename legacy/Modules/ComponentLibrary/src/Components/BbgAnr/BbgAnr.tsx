import React from "react";
import { LayoutContainer } from "PrimitiveComponents/index";
import styled from "styled-components";
import { BbgAnrBarChart, BbgAnrInfo, BbgAnrStance } from "./containers";

type Display = {
    displayInfoBox?: boolean;
    displayChart?: boolean;
    displayTable?: boolean;
};

type BbgAnrProps = Display & {
    instrumentId: string;
    tableHeight?: number | string;
    chartHeight?: number | string;
};

const Container = styled("div")<{ displayTopRow: boolean }>`
    display: grid;
    grid-template-columns: 1fr 4.7fr;
    grid-template-rows: ${({ displayTopRow }) => (displayTopRow ? "auto" : undefined)};
    column-gap: var(--spacing--x-large);
    row-gap: ${({ displayTopRow }) => (displayTopRow ? "var(--spacing--large)" : undefined)};
`;

const TableContainer = styled(LayoutContainer)`
    grid-area: table;
    grid-column: 1 / span 2;
`;

const Wrapper = styled("div")<Display>`
    display: ${({ displayInfoBox, displayChart }) => {
        if (displayInfoBox && displayChart) {
            return "contents";
        }
        return "block";
    }};

    grid-column: ${({ displayInfoBox, displayChart }) => {
        if (displayInfoBox && displayChart) {
            return undefined;
        }
        return "1 / span 2";
    }};
`;

export const BbgAnr = ({
    instrumentId,
    tableHeight = 300,
    chartHeight = 180,
    displayInfoBox = true,
    displayChart = true,
    displayTable = true
}: BbgAnrProps) => {
    const displayTopRow = displayChart || displayInfoBox;
    return (
        <Container displayTopRow={displayTopRow}>
            {displayTopRow && (
                <Wrapper
                    displayChart={displayChart}
                    displayInfoBox={displayInfoBox}
                >
                    {displayInfoBox && <BbgAnrInfo instrumentId={instrumentId} />}
                    {displayChart && (
                        <BbgAnrBarChart
                            instrumentId={instrumentId}
                            height={chartHeight}
                        />
                    )}
                </Wrapper>
            )}
            {displayTable && (
                <TableContainer height={tableHeight}>
                    <BbgAnrStance instrumentId={instrumentId} />
                </TableContainer>
            )}
        </Container>
    );
};
