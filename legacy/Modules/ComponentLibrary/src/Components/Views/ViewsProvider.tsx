import React, { ReactNode, useMemo, useState } from "react";

import { useComponentData } from "../ComponentData";
import { ViewsContext } from "./contexts";
import { View } from "./types";

type ViewsProviderProps = {
    children: ReactNode;
};

export const ViewsProvider = ({ children }: ViewsProviderProps) => {
    const [views, setViews] = useState<{
        current: View[];
        selectedViewName?: string;
    }>({ current: [] });

    const { componentOptions, setComponentOptions } = useComponentData();

    const api = useMemo(
        () => ({
            setAvailableViews(currentViews: View[], defaultViewName?: string) {
                setViews(prevViews => {
                    const updatedViews = currentViews
                        .filter(({ visible }) => visible !== false)
                        .map((view, index) => ({
                            ...view,
                            active:
                                !!prevViews.current[index]?.active ||
                                view.name === defaultViewName ||
                                !!(!defaultViewName && index === 0)
                        }));

                    const selectedViewName = prevViews.selectedViewName || defaultViewName;

                    return {
                        current: updatedViews,
                        selectedViewName:
                            (selectedViewName &&
                                updatedViews.find(view => view.name === selectedViewName)?.name) ||
                            updatedViews?.filter(({ visible }) => visible !== false)?.[0]?.name
                    };
                });
            },
            selectView(name: string) {
                setViews(prevViews => {
                    if (name !== componentOptions?.viewName && setComponentOptions) {
                        setComponentOptions({
                            viewName: name
                        });
                    }

                    const selectedViewName =
                        (name && prevViews.current.find(view => view.name === name)?.name) ||
                        prevViews.current?.filter(({ visible }) => visible !== false)?.[0]?.name;

                    return {
                        current: prevViews.current.map(view => ({
                            ...view,
                            active: view.active || view.name === selectedViewName
                        })),
                        selectedViewName
                    };
                });
            }
        }),
        [setViews, setComponentOptions, componentOptions?.viewName]
    );

    const values = useMemo(
        () => ({
            api,
            views: views.current,
            selectedViewName: views.selectedViewName
        }),
        [api, views]
    );

    return <ViewsContext.Provider value={values}>{children}</ViewsContext.Provider>;
};
