import { MultiLineChartDefinitionEx } from "@reddeer/firefly-shared-controls";
import { numberFormats, formatNumber } from "Utilities/index";
import { defaultChartDefinition } from "../consts";

export const get30DaysVolatilityChartDefinition = (
    bbgTicker: string,
    relativeTicker: string
): MultiLineChartDefinitionEx => ({
    ...defaultChartDefinition,
    yAxisFormatter: value => formatNumber(value, numberFormats.percent0dp, ""),
    lines: {
        bBGTickerVolatillity: {
            name: bbgTicker || "BBG ticker",
            stroke: "#00adf8",
            strokeWidth: "2"
        },
        reltiveTickerVolatillity: {
            name: relativeTicker || "Relative ticker",
            stroke: "white",
            strokeWidth: "2"
        }
    }
});
