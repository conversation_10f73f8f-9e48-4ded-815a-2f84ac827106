import { ChartAxisType } from "@reddeer/firefly-shared-controls";
import {
    withCommandState,
    createConfig,
    requestResponseCommandSpec,
    reportSubscriptionCommandSpec
} from "TransportGateway/index";
import {
    mapSpecialDates,
    getMultiLineCurvesData,
    mapMultiLineStateToProps,
    MultiLineChartProps,
    getMultiLineChart,
    getMultiLineChartDef
} from "../../../Utils";

const basketChartColumns = ["stock", "index"];

const command = "SummaryChartRequestHandler_Price";
const eventsCommandName =
    "RaidData_GetEventRequestDto_IEnumerable[CompleteEventDataDto]_RequestResponse_Handler";

const BasketChartWrapper = (props: MultiLineChartProps = {}) =>
    getMultiLineChart({
        lineProps: props,
        definition: getMultiLineChartDef({
            columns: basketChartColumns,
            xAxisType: ChartAxisType.Number,
            overides: {
                growth: {
                    name: "Exp 12m fwd Rev Growth",
                    strokeWidth: "2"
                },
                ePSgrowth: {
                    name: "EPS growth",
                    strokeWidth: "2"
                }
            }
        }),
        dataProps: basketChartColumns
    });

export const BasketChart = withCommandState(
    BasketChartWrapper({ rightLines: ["index"] }),
    createConfig({
        stock: reportSubscriptionCommandSpec(
            command,
            ({ instrumentId: BBGTicker, period }) => ({ BBGTicker, dateFrame: period || 12 }),
            mapMultiLineStateToProps(
                getMultiLineCurvesData({ columns: basketChartColumns[0], overide: "close" })
            )
        ),
        index: reportSubscriptionCommandSpec(
            command,
            ({ indexTicker: BBGTicker, period }) => ({ BBGTicker, dateFrame: period || 12 }),
            mapMultiLineStateToProps(
                getMultiLineCurvesData({ columns: basketChartColumns[1], overide: "close" })
            )
        ),
        specialDates: requestResponseCommandSpec(
            eventsCommandName,
            ({ instrumentId: BBGTicker }) => ({ BBGTicker, dateFrame: 24, eventTypes: [1, 2, 3] }),
            mapSpecialDates
        )
    })
);
