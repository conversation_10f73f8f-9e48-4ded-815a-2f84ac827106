import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import { ThemeIterator, LayoutGrid, ThemeContainer } from "PrimitiveComponents/index";
import { withFixtures } from "TransportGateway/index";
import { HedgeRiskTable } from "../containers";

<Meta
    title="ComponentLibrary/Components/HedgeRiskTable"
    component={HedgeRiskTable}
/>

export const HedgeRiskTableMock = withFixtures(HedgeRiskTable, {
    tableData: () => import("./fixtures/tableData.json")
});

export const Template = args => (
    <LayoutGrid
        columns={2}
        gap="xx-large"
        rowHeight={440}
    >
        <ThemeIterator>
            {() => (
                <ThemeContainer flex>
                    <HedgeRiskTableMock {...args} />
                </ThemeContainer>
            )}
        </ThemeIterator>
    </LayoutGrid>
);

# HedgeRiskTable

<Description of={HedgeRiskTable} />

<Canvas>
    <Story
        name="HedgeRiskTable"
        parameters={{
            controls: {
                disable: false,
                include: ["identifier", "buyAmount", "sellAmount", "identifierSell"]
            }
        }}
        args={{
            identifier: "",
            buyAmount: 1,
            sellAmount: 0,
            identifierSell: "dummy"
        }}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes
    of={HedgeRiskTable}
    sort="requiredFirst"
/>
