import {
    createConfig,
    reportSubscriptionCommandSpec,
    withCommandState
} from "TransportGateway/index";
import { Suprise } from "../Suprise";

type Props = {
    instrumentId: string;
};

export const SupriseGrid = withCommandState(
    Suprise,
    createConfig({
        tableData: reportSubscriptionCommandSpec(
            "BBG_Surprise",
            ({ instrumentId: BBGTicker }: Props) => ({ BBGTicker }),
            (data, _, schema) => ({
                data,
                schema
            })
        )
    })
);
