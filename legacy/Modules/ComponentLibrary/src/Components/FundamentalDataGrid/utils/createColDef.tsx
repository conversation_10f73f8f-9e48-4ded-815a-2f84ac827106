import React from "react";
import { formatBigValue, isUndefined, formatNumber, numberFormats } from "Utilities/index";
import { ChangedValue } from "PrimitiveComponents/index";
import { formatYearHeading } from "./formatYearHeading";
import { bigValues, RowType } from "../consts";
import { getRowType } from "./getRowType";

export const createColDef = (colName, header, postfix = "") => ({
    cellRenderer: ({ data }) => {
        const value = data?.[colName];
        if (!data || isUndefined(value)) return null;
        if (
            ["days", "leverage", "liquidity"].some(key => key === data?.category) ||
            data?.rowType === RowType.Section
        ) {
            if (data.category === "days") {
                return `${formatNumber(value, numberFormats[data.format || "number0dp"])}d`;
            }

            if (data.category === "leverage" || data.category === "liquidity") {
                if (data.name === "NetDebt") {
                    return formatBigValue(value);
                }

                return `${formatNumber(value, numberFormats[data.format || "number1dpFixed"])}${
                    data.format.includes("percent") ? "" : "x"
                }`;
            }

            if (data?.rowType === RowType.Section) {
                const isBig = bigValues.some(v => v === data.name.toLowerCase());
                return isBig
                    ? formatBigValue(value)
                    : formatNumber(value, numberFormats[data.format || "number2dp"]);
            }

            return value;
        }

        const rowType = getRowType(data.name);
        const inPercent = RowType.Growth || rowType === RowType.Margin;
        const formatType = data.format || inPercent ? "percent1dp" : "number2dp";
        const isColored = rowType === RowType.Growth && (colName === "col4" || colName === "col5");
        const styleFn = () =>
            isColored
                ? {
                      fontStyle: "italic"
                  }
                : {
                      fontStyle: "italic",
                      color: "var(--color--tertiary)"
                  };

        return (
            <ChangedValue
                formatType={formatType}
                value={value}
                disableColor={!isColored}
                styleFn={styleFn}
            />
        );
    },
    field: "colName",
    headerName: formatYearHeading(header[colName], postfix),
    cellStyle: {
        justifyContent: "center",
        textAlign: "center"
    }
});
