import { formatNumber, numberFormats, isUndefined } from "Utilities/index";
import { formatValuationYearHeading } from "./formatValutaionYearHeading";

export const createValuationColDef = (colName, header) => ({
    valueFormatter: ({ value, data }) => {
        if (!data || isUndefined(value)) return null;

        const isYield =
            data.metric &&
            (data.metric.includes("Yield") ||
                data.metric.includes("Avg Cpn") ||
                data.metric.includes("Avg Mtty"));
        let val = Number(value);
        if (data.metric === "Avg Cpn") val /= 100;
        const suffix = !isYield ? "x" : "";

        if (data.metric === "Avg Mtty") {
            return `${formatNumber(val, numberFormats.number1dp)}${suffix}`;
        }

        return `${
            !isYield
                ? formatNumber(val, numberFormats[data.format || "number1dpFixed"])
                : formatNumber(val, numberFormats.percent1dp)
        }${suffix}`;
    },
    field: colName,
    headerName: formatValuationYearHeading(header[colName === "valuation1" ? "col4" : "col5"])
});
