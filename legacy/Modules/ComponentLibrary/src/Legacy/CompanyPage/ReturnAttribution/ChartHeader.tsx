import React from "react";
import { Label, Select, Option, LayoutHorizontal, ModalDisplay } from "PrimitiveComponents/index";
import {
    ReturnAttributionModelComparison,
    returnAttributionTerms,
    ReturnAttributionRollingPerfChart
} from "../../../Components/ReturnAttribution";
import { BBGTickerSelect } from "../../../Controls";

type Props = {
    title: string;
    ticker: string;
    onTicker: (value: string) => void;
    model: string;
    onModel: (value: string) => void;
    instrumentId: string;
    daysLag: number;
};

export const ChartHeader = ({
    title,
    ticker,
    onTicker,
    model,
    onModel,
    instrumentId,
    daysLag
}: Props) => {
    const handleChangeModel = (value: Option) => {
        if (onModel) onModel(value.value?.toString());
    };

    const handleChangeTicker = (value: Option) => {
        if (onTicker) onTicker(value.value?.toString());
    };

    return (
        <LayoutHorizontal style={{ justifyContent: "space-between" }}>
            <LayoutHorizontal>
                <Label label="Model">
                    <Select
                        options={returnAttributionTerms}
                        value={model}
                        width="150px"
                        isSearchable={false}
                        onChange={handleChangeModel}
                    />
                </Label>
                <ModalDisplay btnLabel="Compare">
                    <ReturnAttributionModelComparison
                        instrumentId={instrumentId}
                        daysLag={daysLag}
                    />
                </ModalDisplay>

                <ModalDisplay
                    btnLabel="📈"
                    borderLess
                    modalProps={{
                        title: "Rolling performance",
                        width: 800
                    }}
                >
                    <ReturnAttributionRollingPerfChart
                        identifier={instrumentId}
                        height={200}
                    />
                </ModalDisplay>
            </LayoutHorizontal>
            <div>{title}</div>
            <Label>
                <BBGTickerSelect
                    value={ticker}
                    onChange={handleChangeTicker}
                    width="150px"
                />
            </Label>
        </LayoutHorizontal>
    );
};
