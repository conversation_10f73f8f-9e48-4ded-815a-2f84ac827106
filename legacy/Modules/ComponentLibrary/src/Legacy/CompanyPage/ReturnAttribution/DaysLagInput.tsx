import React, { useState } from "react";
import styled from "styled-components";
import { InputNumberWithLabel } from "PrimitiveComponents/index";

const NumberInput = styled(InputNumberWithLabel)<{ positionLeft: boolean }>`
    &&&&&& {
        margin-left: ${({ positionLeft }) => (positionLeft ? 0 : "auto")};

        font-size: var(--font__size--small);
    }
`;

export const DaysLagInput = ({
    defaultValue,
    onChange,
    max,
    positionLeft
}: {
    defaultValue: number;
    onChange: (value: number) => void;
    max: number;
    positionLeft?: boolean;
}) => {
    const [value, setValue] = useState(defaultValue);
    const handleSubmitValue = () => {
        if (value < 0) {
            onChange(0);
            return;
        }
        onChange(value <= max ? value : max);
    };
    const handleKeyDown = e => {
        if (e.key === "Enter") {
            handleSubmitValue();
        }
    };
    return (
        <NumberInput
            min={0}
            max={max}
            label="Regression Days Lag"
            onBlur={handleSubmitValue}
            onKeyDown={handleKeyDown}
            value={value}
            onChange={setValue}
            positionLeft={positionLeft}
            size="small"
        />
    );
};
