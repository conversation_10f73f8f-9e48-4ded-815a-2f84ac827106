import {
    createConfig,
    reportSubscriptionCommandSpec,
    withCommandStateFactory
} from "TransportGateway/index";
import { Jpm } from "../Jpm";
import { mapHalfYear, mapRank } from "../utils";

type Props = {
    instrumentId: string;
};

type CommadProps = {
    EuRelative?: boolean;
};

export const JpmWidget = withCommandStateFactory(Jpm, ({ EuRelative = false }: CommadProps) =>
    createConfig({
        rank: reportSubscriptionCommandSpec(
            EuRelative ? "CompanyMatrixEURebase" : "CompanyMatrixRequestHandler",
            ({ instrumentId }: Props) => ({
                bbgTicker: instrumentId,
                dateFrame: 0
            }),
            mapRank
        ),
        halfYear: reportSubscriptionCommandSpec(
            EuRelative ? "CompanyMatrixEURebase" : "CompanyMatrixRequestHandler",
            ({ instrumentId }: Props) => ({
                bbgTicker: instrumentId,
                dateFrame: 1006
            }),
            mapHalfYear
        )
    })
);
