import React, { useState, useEffect, useMemo } from "react";
import { format, ScaleTime } from "d3";
import moment from "moment";
import styled from "styled-components";

import { isNumber } from "Utilities/index";

import { DataPoint } from "../../../types";

type Props = {
    date: number | Date;
    dateScale: ScaleTime<number, number>;
    data: DataPoint[];
    showBillions: boolean;
};

const getValue = (value: number, showBillions: boolean) => {
    if (!isNumber(value)) return "-";
    if (showBillions) return `${format(".3f")(value / 1000)}b`;
    return format(".2f")(value);
};

const Tooltip = styled.div`
    position: absolute;

    display: flex;

    flex-flow: column;

    background-color: transparent;
    transition: opacity 0.5s;

    pointer-events: none;

    .tooltip {
        display: flex;
        width: 7rem;
        min-height: 1.75rem;
        padding: 0.5rem 0.75rem;

        flex-flow: column;
        justify-content: center;

        border-radius: 4px;
        font-size: 0.75rem;
        text-align: right;
        color: var(--color--tertiary);
        white-space: pre;
        background-color: var(--color__background);

        .tooltip__value {
            color: var(--color);
        }
    }
`;

export const GrowthTooltip = ({ date = 0, dateScale, data = [], showBillions = false }: Props) => {
    const [tooltipContainerStyles, setTooltipContainerStyles] = useState<React.CSSProperties>();
    const value = useMemo(() => {
        const val = data.filter(d => d.date.valueOf() === date.valueOf());

        return getValue(val[0]?.value, showBillions);
    }, [data, date, showBillions]);

    useEffect(() => {
        if ((date as number) < 0 || !dateScale || !data.length) return;

        const left = dateScale(date) - 100;
        const top = 10;

        setTooltipContainerStyles({ left, top });
    }, [date, data, dateScale]);

    return date === -1 || !data || !data.length ? null : (
        <Tooltip style={tooltipContainerStyles}>
            <div className="tooltip">
                <div>{moment(date).format("ddd, DD MMM YY")}</div>
                <div className="tooltip__value">{value}</div>
            </div>
        </Tooltip>
    );
};
