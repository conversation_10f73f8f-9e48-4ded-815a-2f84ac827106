import moment from "moment";
import { fieldToDate } from "./fieldToDate";

type Data<P> = P & {
    date: string;
};

export const arrangeByDate = <P>(data: Data<P>[] = []) => {
    const sorted = data.sort((a, b) => (a.date < b.date ? 0 : -1));
    const dateString = d => {
        const date = fieldToDate(d);
        if (!date) return "";
        const today = new Date().toDateString() === date.toDateString();
        return today
            ? d.substring(11, 16).replace(".", ":")
            : moment(d.substring(0, 10)).format("DD[/]MM");
    };
    return sorted
        .filter(a => a.date)
        .map(a => ({
            ...a,
            date: dateString(a.date),
            dateReal: moment(a.date.substring(0, 10)).toDate()
        }))
        .slice(0, 50);
};
