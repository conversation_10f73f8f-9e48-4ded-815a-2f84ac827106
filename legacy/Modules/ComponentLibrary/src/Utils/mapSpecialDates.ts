import moment from "moment";
import type { SpecialDate } from "PrimitiveComponents/index";

export const specialEventMap = {
    1: "R",
    2: "S",
    3: "TS",
    13: "D",
    114: "SD"
};

export const mapSpecialDates = (data): SpecialDate[] => {
    if (!data) return [];
    const array = Object.values(data) as any[];

    const result = array.map(d => {
        const { dateTime, eventType } = d;
        return { name: specialEventMap[eventType], date: moment(dateTime, "YYYY-MM-DD").toDate() };
    });
    return result;
};
