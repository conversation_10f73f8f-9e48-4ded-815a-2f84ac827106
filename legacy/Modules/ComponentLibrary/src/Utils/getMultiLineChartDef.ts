import type {
    MultiLineChartDefinitionEx,
    MultiLineChartLineDefinitionEx
} from "@reddeer/firefly-shared-controls";
import { isStringType } from "Utilities/index";
import { startCase } from "lodash";
import { chartLinecolors } from "../consts";

export type MultiLineChartDefProps = Omit<MultiLineChartDefinitionEx, "lines"> & {
    columns: string | string[];
    overides?: Record<string, Record<string, unknown>>;
};

const getLines = (
    columns: string | string[],
    overides: Record<string, Record<string, unknown>> = {}
): Record<string, MultiLineChartLineDefinitionEx> => {
    if (isStringType(columns)) {
        return {
            [columns]: {
                name: startCase(columns),
                stroke: chartLinecolors[0],
                strokeWidth: "1.5px",
                ...overides[columns]
            }
        };
    }

    return columns
        .map((col, index) => ({
            name: startCase(col),
            stroke: chartLinecolors[index % chartLinecolors.length],
            strokeWidth: index === 0 ? "1.5px" : "1px",
            ...overides[col],
            col
        }))
        .reduce((acc, { col, ...item }) => {
            const line = acc;
            line[col] = item;

            return line;
        }, {});
};

export const getMultiLineChartDef = ({
    columns,
    overides = {},
    ...rest
}: MultiLineChartDefProps): MultiLineChartDefinitionEx => {
    const lines = getLines(columns, overides);

    return {
        ...rest,
        lines
    };
};
