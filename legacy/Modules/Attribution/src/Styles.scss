@import "@reddeer/firefly-shared-controls/build/Common/Styles/Common.scss";
@import "@reddeer/firefly-shared-controls/build/Common/Styles/Colors.scss";
@import "@reddeer/firefly-shared-controls/build/Common/Styles/Sizes.scss";
@import "@reddeer/firefly-shared-controls/build/Common/Styles/Variables.scss";
$axisColor: #656465;
$textColor: white;
$chartColor: #14648a;
$chartText: #0ac3ff;
$disabledOpacity: 0.7;

.portfolioVsMarketReturnSection {
  grid-row: 1/3;
  grid-column: 2;
  display: grid;
  grid-gap: $widgetMargin;
}

.twoColumnGridSection {
  display: grid;
  padding: 10px;
  grid-gap: $widgetMargin;
  grid-template-columns: 1fr 1fr;
}

.strategySection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  padding: 10px;

  .strategyCharts {
    grid-row: 1 / 3;
    grid-column: 2;
  }
}

.riskAndPortfolioSummarySection {
  @extend .twoColumnGridSection;
  grid-column: 1 / 3;
}

.pnlExposureSection {
  @extend .twoColumnGridSection;

  .chartContainer {
    display: flex;
    flex-flow: column;
  }

  .pnlToggleContainer {
    width: 200px;
    align-self: center;
  }
}

.attributionPage {
  display: grid;
  grid-gap: $widgetMargin;
  grid-template-columns: 1fr 1fr;
  font-size: $textSizeDefault;

  .sectionContainer {
    display: flex;
    height: 100%;
  }

  .widget {
    margin: 10px 20px;
  }

  .subSection {
    margin-bottom: 40px;
    padding-top: 0;

    @media print {
      margin-bottom: 20px;
    }
  }

  .tableRowLabel {
    background: none;
    font-weight: normal;
  }
}

.pageBreakSection {
  page-break-before: always;
}

.information {
  color: #ffffff;
}

.supportedInformation {
  color: #71838c;
}

.widgetHeading {
  color: $textColorDefault;
  font-size: $headingSizeDefault;
  font-weight: 400;
  margin-bottom: 10px;

  @media print {
    color: $textColorPrint;
  }
}

.sectionHeading {
  color: $headingColorDefault;
  font-size: 22px;
  font-weight: 300;
  margin: 0 10px;
}

.section {
  background: $backgroundColorLight;
  border-radius: 3px;
  margin-bottom: 10px;
  padding: 10px 0;
  width: 100%;

  @media print {
    background-color: transparent;
  }

  &:nth-child(2) {
    border-top-right-radius: 0;
  }

  &:last-of-type {
    margin-bottom: 0;
  }
}

svg {
  text .legend {
    fill: $axisColor;
    font-size: 12px;
    font-family: "Roboto Regular, sans-serif";
  }

  .gridLines,
  .axis {
    path,
    line {
      stroke: $axisColor;
    }
    text {
      fill: $textColor;
      font-size: 11px;
    }
  }
  .dateAxis {
    path,
    line {
      stroke: $axisColor;
      shape-rendering: auto;
      stroke-width: 1px;
      // opacity: 42%;
    }

    .seriesLine {
      shape-rendering: auto;
      fill: none;
    }

    text {
      fill: $textColor;
      font-size: 11px;
      font-weight: lighter;
    }
  }
  .hiddenAxis {
    path {
      stroke: none;
      fill: none;
      stroke-width: 0px;
    }
    line {
      stroke: $axisColor;
      shape-rendering: auto;
      stroke-width: 1px;
      opacity: 42%;
    }
  }
  .netSeriesLine {
    stroke: #f5a623;
    fill: none;
  }
  .grossSeriesLine {
    stroke: #ffffff;
    fill: none;
  }
}

.strategyWidget {
  display: flex;
  flex-direction: column;
  padding-top: 10px;
}

.strategyContainer {
  display: flex;
  justify-content: space-between;
}

.toggleContainer {
  display: flex;
  width: 400px;
  margin-bottom: 6px;
  margin-left: 6px;
  margin-right: 6px;
}

.strategyChartContainer {
  margin: 60px 100px 60px -30px;
  overflow: "hidden"
}

.chartLabel {
  display: block;
  text-align: center;
  fill: $textColor;
  font-size: 14px;
  font-weight: normal;
  margin-top: 50px;
  margin-left: 120px;
}

.radioOptions {
  transition: opacity 0.2s;

  &.radioOptionsDisabled {
    opacity: $disabledOpacity;

    label {
      cursor: not-allowed;
      color: #777;
    }
  }

  div {
    height: 26px;
  }

  div label {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 !important;
  }

  .positiveVsNegativeTooltip {
    display: flex;
    flex-flow: column;
    background-color: black;
    padding: 8px;

    .tooltipTicker {
        font-weight: bold;
        margin-bottom: 6px;
    }
}

.positiveVsNegativeChartContainer {
    flex: 1;
    position: relative;

    .bars {
        rect {
            stroke: black;
            stroke-width: 0.5;
        }

        .positive {
            rect {
                fill: #053D06;
            }
        }
    
        .negative {
            transform: scaleX(-1);

            rect {
                fill: #7B1818;
            }
        }
    }

    .gridLines {
        &.xAxisGridLines {
            line {
                stroke-dasharray: 1px 3px;
            }
        }
    }

    .gridLines line,
    .axis path {
        stroke: #656465;
    }

    .axis {
        line {
            display: none;
        }

        text {
            color: #8396A1;
        }

        &.hideTicks {
            text {
                display: none;
            }
        }
    }

    .categoryLabels {
        text {
            fill: $labelColorDefault;
            font-size: 14px;
        }
    }

    .baseLine {
        stroke: #75777B;
        stroke-dasharray: 2px 3px;
    }

    .tooltip {
        position: absolute;
        transition: all 0.2s;
    }
}
}
.pvnChart {
position: absolute;
    transition: all 0.2s;
    pointer-events: none;

    .tooltipInner {
        display: flex;
        flex-flow: column;
        padding: 8px;
        background-color: rgba(0, 0, 0, 0.8);
        pointer-events: none;
        font-size: 12px;
        border-radius: 3px;
        align-items: flex-start;
    }

    .tooltipInner div:not(:last-of-type) {
        margin-bottom: 4px;
    }
    
    .ticker {
        font-weight: bold;
    }

    .valueAndLabel {
        display: flex;
        justify-content: space-between;
        width: 100%;
    }

    .valueAndLabel span:first-of-type {
        margin-right: 4px;
    }

    .valueAndLabel span:last-of-type {
        font-weight: lighter;
    }
  }

  .tooltipContainer {
    position: absolute;
    transition: opacity 0.5s;
    background-color: transparent;
    display: flex;
    flex-flow: column;
    pointer-events: none;

    .tooltip {
        position: absolute;
        display: flex;
        flex-flow: column;
        background-color: rgba(0, 0, 0, 0.6);
        white-space: pre;
        min-height: 26px;
        border-radius: 4px;
        font-size: 11px;
        color: #bbb;
        padding: 4px 8px;
        left: 8px;
        top: 0px;

        .date {
            font-weight: 600;
        }

        .itemColor {
            width: 6px;
            height: 6px;
            border-radius: 3px;
            margin-right: 4px;
            margin-bottom: 2px;
            display: inline-block;
        }
    }
}

.trackingAxis {
  path{
      stroke: white;
      stroke-width: 0.5px;
  }
}

.chartTooltip {
  position: absolute;
  transition: opacity 0.5s;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-flow: column;
  pointer-events: none;
}