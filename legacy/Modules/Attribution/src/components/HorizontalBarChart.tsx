import React, { useRef, useEffect, useCallback } from "react";
import styled from "styled-components";

import { ScaleBand, scaleBand, ScaleLinear, scaleLinear, select, axisBottom } from "d3";
import { useElementSize } from "@reddeer/firefly-shared-controls";
import classNames from "classnames";
import * as styles from "../Styles.scss";

export interface HorizontalBarChartProps {
    loading?: boolean;
    data: HorizontalBarChartData;
}

export interface HorizontalBarChartData {
    values: DataPoint[];
    maxRange: number;
    optimalValue: number;
    color: string;
}

const StyledContainer = styled.div`
    svg {
        width: 100%;
        height: 22px;
    }
`;

type DataPoint = { name: string; value: number; color: string };

export const HorizontalBarChart = ({ data }) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const [width, height] = useElementSize(containerRef);
    const yscaleRef = useRef<ScaleBand<string>>(scaleBand());
    const xscaleRef = useRef<ScaleLinear<number, number>>(scaleLinear());

    const draw = useCallback(() => {
        if (!data || !data.values.length) return;

        const xscale = xscaleRef.current;
        const yscale = yscaleRef.current;
        const sidePadding = 6;
        const topPadding = 2;
        const yOffset = 10;
        const chartWidth = width - sidePadding * 2;
        const chartHeight = height - topPadding;

        const svg = select(containerRef.current).select("svg");

        xscale
            .domain([0, data.maxRange]) //
            .range([sidePadding, chartWidth])
            .nice();

        const names = data.values.map(x => x.name);
        yscale.domain(names).range([0, chartHeight - yOffset]);

        svg.selectAll("g").remove();

        const axes = svg
            .append("g")
            .attr("id", "axes")
            .attr("class", classNames(styles.hiddenAxis));

        const xAxis = axisBottom(xscale)
            .ticks(3) //
            .tickSize(-3)
            .tickSizeOuter(0)
            .tickPadding(3);

        axes.append("g")
            .attr("transform", `translate(0, ${chartHeight - yOffset})`)
            .call(xAxis)
            .selectAll("text")
            .style("text-anchor", "centre")
            .style("font-size", "8px")
            .style("fill", "#8396A1");

        axes.selectAll()
            .data(data.values)
            .enter()
            .append("rect")
            .attr("fill", (d: DataPoint) => d.color)
            .attr("height", () => yscale.bandwidth() - 1)
            .attr("width", (d: DataPoint) => Math.abs(xscale(d.value) - xscale(0)))
            .attr("x", () => sidePadding)
            .attr("y", (d: DataPoint) => yscale(d.name) + 1);

        axes.append("rect")
            .attr("height", () => yscale.bandwidth() * 2 + 3)
            .attr("width", "1.5px")
            .attr("fill", data.color)
            .attr("x", xscale(data.optimalValue))
            .attr("y", chartHeight - yOffset * 2 - 1);
    }, [data, height, width]);

    useEffect(() => {
        if (containerRef.current) {
            draw();
        }
    }, [data, draw, height, width]);

    return (
        <StyledContainer ref={containerRef}>
            <svg />
        </StyledContainer>
    );
};
