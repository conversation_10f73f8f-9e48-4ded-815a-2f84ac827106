import { UnstackedAreaChartDefinition, ChartAxisType } from "@reddeer/firefly-shared-controls";
import { formatNumber, numberFormats } from "Utilities/index";

export const performanceChartDefinition: UnstackedAreaChartDefinition = {
    lines: {
        portfolio: {
            name: "Portfolio PnL",
            stroke: "#0A98C3",
            strokeWidth: "1.5px",
            areaFill: "url(#pnlGradient)"
        },
        market: {
            name: "SXXP",
            stroke: "#C40AB9",
            strokeWidth: "1.5px",
            areaFill: "url(#marketGradient)"
        }
    },
    xAxisLabel: undefined,
    xAxisType: ChartAxisType.Date,
    yAxisLabel: "PnL/Market %",
    yAxisFormatter: x => formatNumber(x, numberFormats.percent0dp),
    gradients: [
        {
            id: "pnlGradient",
            stops: [
                { offset: 0, color: "rgba(10,152,195,0.5)" },
                { offset: 1, color: "rgba(2,253,254,0)" }
            ],
            options: {
                x1: 0,
                y1: 0,
                x2: 0,
                y2: 1
            }
        },
        {
            id: "marketGradient",
            stops: [
                { offset: 0, color: "rgba(195,10,184,0.5)" },
                { offset: 1, color: "rgba(168,63,164,0)" }
            ],
            options: {
                x1: 0,
                y1: 0,
                x2: 0,
                y2: 1
            }
        }
    ]
};
