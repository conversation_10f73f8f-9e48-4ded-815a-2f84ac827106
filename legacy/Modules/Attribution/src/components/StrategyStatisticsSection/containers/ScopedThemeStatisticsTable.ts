import { withCommandState, createConfig } from "TransportGateway/index";
import { blockDataGetter } from "../../../types";
import { PnLExpandingTable } from "../PnLExpandingTable";
import { dataMapper } from "../utils/dataMapper";

export const ScopedThemeStatisticsTable = withCommandState(
    PnLExpandingTable,
    createConfig({
        data: blockDataGetter("ThemeStatistics", d => dataMapper(d, "theme"), "StrategyStatistics")
    })
);
