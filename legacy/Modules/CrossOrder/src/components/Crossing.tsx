import React, { useState } from "react";
import styled from "styled-components";

import {
    LayoutGrid,
    LayoutVertical,
    LayoutHorizontal,
    NumberInputWithLabel,
    Checkbox    
} from "PrimitiveComponents/index";
import { getPositiveNegativeColour } from "Utilities/index";

import { useCrossOrder } from "../CrossOrderProvider";
import { TraderSelect } from "./ConnectedSelects";
import { CrossOrderAllocations } from "./CrossOrderAllocations";
import {
    getPercantageFromQuantity,
    getQuantityFromPercentage,
    getTotalPositionFromAllocations
} from "../utils";
import { CrossOrderStrategySelect } from "./CrossOrderStrategySelect";

const StyledNumberInput = styled(NumberInputWithLabel)`
    text-align: right;
    width: 100px;
`;

const CrossingGrid = styled(LayoutGrid)`
    &&&& {
        padding: var(--spacing--large);
        border-style: solid;
        border-color: var(--border__color);
        border-width: 1px 0;

        &:nth-of-type(1) {
            border-right-width: 1px;
            margin-left: calc(-1 * var(--spacing--large));
        }

        &:nth-of-type(2) {
            margin-right: calc(-1 * var(--spacing--large));
        }
    }

    > :nth-child(even) {
        color: var(--color--secondary);
    }

    > :nth-child(odd) {
        color: var(--color--primary);
        justify-self: end;
    }

    > :first-child {
        grid-column: 1 / 3;
        justify-self: start;
    }
`;

export const Crossing = () => {
    const { crossOrder, totalOriginalPosition, updaterFactory } = useCrossOrder();

    const resetDisabled = crossOrder && crossOrder.percentageToCross < 100;
   
    return (
        <LayoutGrid columns={2}>
            <CrossingGrid
                padding="medium"
                gap="large"
                columns={2}
            >
                <div>Cross From:</div>
                <div>Trader</div>
                <div>{crossOrder.fromTrader}</div>
                <div>Strategy</div>
                <div>
                    {crossOrder.fromStrategyDescription} ({crossOrder.fromStrategy})
                </div>
                <div>Direction</div>
                <div style={{ color: getPositiveNegativeColour(totalOriginalPosition * -1) }}>
                    {totalOriginalPosition > 0 ? "Sell" : "Buy"}
                </div>

                {crossOrder.showAllocations && (
                    <>
                        <div>Prime Broker</div>
                        <LayoutVertical style={{ width: "100%" }}>
                            {crossOrder.fromAllocations.map(fa => (
                                <LayoutHorizontal
                                    key={fa.custodianId}
                                    justifyContent="space-between"
                                >
                                    <div>{fa.custodian}</div>
                                    <div>{fa.position}</div>
                                </LayoutHorizontal>
                            ))}
                            <LayoutHorizontal justifyContent="space-between">
                                <div>Total</div>
                                <div>{totalOriginalPosition}</div>
                            </LayoutHorizontal>
                        </LayoutVertical>
                    </>
                )}
            </CrossingGrid>
            <CrossingGrid
                padding="medium"
                gap="large"
                columns={2}
            >
                <div>Cross To:</div>
                <div>Trader</div>
                <TraderSelect value={crossOrder.toTraderId} />
                <div>Strategy</div>
                <div>
                    {crossOrder.toTraderId && (
                        <CrossOrderStrategySelect traderId={crossOrder.toTraderId} />
                    )}
                </div>
                <div>Direction</div>
                <div style={{ color: getPositiveNegativeColour(totalOriginalPosition) }}>
                    {totalOriginalPosition < 0 ? "Sell" : "Buy"}
                </div>

                <div>Reset Position</div>
                <div style={{ width: "1em" }}>
                    <Checkbox
                        onChange={updaterFactory("resetPositionId")}
                        value={crossOrder?.resetPositionId}                        
                        disabled={resetDisabled}
                    >                     
                    </Checkbox>
                 </div>                
               
                <CrossOrderAllocations />
            </CrossingGrid>
        </LayoutGrid>
    );
};
