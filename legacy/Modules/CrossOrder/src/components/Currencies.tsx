import { useCrossOrder } from "CrossOrderProvider";
import React from "react";
import styled from "styled-components";

const LabelTitle = styled.div`
    color: var(--color--secondary);
`;

export const Currencies = () => {

    const { crossOrder } = useCrossOrder();

    return (
        <div style={
            {
                display: 'flex',
                width: '30%',
                justifyContent: 'space-between'
            }}>
            <LabelTitle>Instrument Currency</LabelTitle>
            <div>{crossOrder.currency}</div>
            <LabelTitle>PL Currency</LabelTitle>
            <div>{crossOrder.plCurrency}</div>
        </div>
    );
}