

import { BorrowBrokerType } from './BorrowBrokerType';

export class UpdateBorrowBrokerCommand {
    public readonly $type: string = "Firefly.Service.Calculator.OrderForm.Contracts.Commands.UpdateBorrowBrokerCommand, Firefly.Service.Calculator.OrderForm.Contracts";
    public OrderId: number;
    public Version: number;
    public LocateId: string;
    public BorrowBrokerTypeId: BorrowBrokerType;
}


