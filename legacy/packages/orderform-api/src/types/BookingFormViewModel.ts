import { BookingSizeViewModel } from "./BookingSizeViewModel";
import { BookingAllocationViewModel } from "./BookingAllocationViewModel";
import { SettlementInstructionViewModel } from "./SettlementInstructionViewModel";
import { RemainingBookedViewModel } from "./RemainingBookedViewModel";
import { BaseEntityViewModel } from "./BaseEntityViewModel";
import { AllocationProfileViewModel } from "./AllocationProfileViewModel";

export class BookingFormViewModel extends BaseEntityViewModel {
    public readonly $type: string =
        "Firefly.Service.Calculator.OrderForm.Contracts.ViewModels.BookingFormViewModel, Firefly.Service.Calculator.OrderForm.Contracts";
    public AllocationProfile: AllocationProfileViewModel;
    public BookingAllocations: BookingAllocationViewModel[];
    public BookingFormId: number;
    public IsManual: boolean;
    public ReadOnly: boolean;
    public RemainingBooked: RemainingBookedViewModel;
    public SettlementInstructions: SettlementInstructionViewModel[];
    public ShowStatusMessage: boolean;
    public Size: BookingSizeViewModel;
    public StatusMessage: string;
    public Version: number;
}
