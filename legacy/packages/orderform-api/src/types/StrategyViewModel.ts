

import { OptionViewModel } from './OptionViewModel';

export class StrategyViewModel {
    public readonly $type: string = "Firefly.Service.Calculator.OrderForm.Contracts.ViewModels.StrategyViewModel, Firefly.Service.Calculator.OrderForm.Contracts";
    public LongStrategyOptions: OptionViewModel[];
    public ShortStrategyOptions: OptionViewModel[];
    public StrategyName: string;
    public StrategyIdentifier: string;
    public SubStrategyOptions: OptionViewModel[];
    public SubStrategyName: string;
    public StrategyDisabled: boolean;
    public StrategyReadOnly: boolean;
}


