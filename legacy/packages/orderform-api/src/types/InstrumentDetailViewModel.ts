



export class InstrumentDetailViewModel {
    public readonly $type: string = "Firefly.Service.Calculator.OrderForm.Contracts.ViewModels.InstrumentDetailViewModel, Firefly.Service.Calculator.OrderForm.Contracts";
    public ShowInstrumentDetail: boolean;
    public MarketCap: string;
    public PercentChangeToday: string;
    public DailyNChange: string;
    public AverageValueTraded: string;
    public ValueTradedToday: string;
    public ExpectedVolumeXAdv: string;
}


