

import { BorrowBrokerType } from './BorrowBrokerType';

export class UpdateBookingBorrowBrokerCommand {
    public readonly $type: string = "Firefly.Service.Calculator.OrderForm.Contracts.Commands.UpdateBookingBorrowBrokerCommand, Firefly.Service.Calculator.OrderForm.Contracts";
    public BookingFormId: number;
    public Version: number;
    public LocateId: string;
    public BorrowBrokerTypeId: BorrowBrokerType;
}


