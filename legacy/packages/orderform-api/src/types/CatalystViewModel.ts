

import { CatalystItemViewModel } from './CatalystItemViewModel';
import { OptionViewModel } from './OptionViewModel';

export class CatalystViewModel {
    public readonly $type: string = "Firefly.Service.Calculator.OrderForm.Contracts.ViewModels.CatalystViewModel, Firefly.Service.Calculator.OrderForm.Contracts";
    public Cards: CatalystItemViewModel[];
    public ShowNewsFlowPanel: boolean;
    public ImpactStocksDefaultValue: boolean;
    public EventOptions: OptionViewModel[];
    public FromWeekOptions: OptionViewModel[];
    public ToWeekOptions: OptionViewModel[];
    public ShowSpinner: boolean;
}


