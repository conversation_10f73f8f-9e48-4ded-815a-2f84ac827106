import { createStore, applyMiddleware, combineReducers, Store } from "redux";
import {
  SubscriptionClient,
  ServerConnection,
  ISubscriptionClient,
} from "@tradinglabs/viewserver-core";
import {
  subscriptionMiddleware,
  connectionMiddleware,
  connectionReducer,
  subscriptionReducer,
} from "@tradinglabs/viewserver-redux";
import { getEmptyAuthHeaders } from "./getAuthHeaders";
import {Logger, LogLevel} from "@tradinglabs/viewserver-core";

if(localStorage.getItem("vsLogging") == "true"){
  Logger.level = LogLevel.TRACE;
  Logger.blockLogging = false;
}else{
  Logger.blockLogging = true;
}

const createViewServerMiddleware = (
  name: string,
  url: string,
  onClientCreated: (client: ISubscriptionClient) => void
) => {
  const connection = new ServerConnection(name, url);
  const client = new SubscriptionClient(connection, getEmptyAuthHeaders);

  let doStart = async () => {
    await client.start();
  }
  doStart();
  onClientCreated(client);

  return [
    connectionMiddleware(connection),
    subscriptionMiddleware(client, getEmptyAuthHeaders),
  ];
};

const rootReducer = combineReducers({
  connection: connectionReducer,
  subscriptions: subscriptionReducer,
});

let sharedStore: Store | null = null;

// @TODO: temp code: remove
const devToolsEnabled = localStorage.getItem("debug:devToolsEnabled") === "true" ?? false;


export const createConditionalMiddleware = (
  onClientCreated: (client: ISubscriptionClient) => void,
  viewServerUrl: string = `/backbone/viewserver`
) => {
  // @ts-ignore
  return applyMiddleware(
    ...createViewServerMiddleware("global", viewServerUrl, onClientCreated)
  );
};

export const useServerStore = (
  viewServerUrl: string = `/backbone/viewserver`
) => {
  if (sharedStore === null) {
    let client;
    sharedStore = createStore(
      rootReducer,
      undefined,
      createConditionalMiddleware((cl) => (client = cl), viewServerUrl)
    );
    (sharedStore as any).client = client;
  }
  return sharedStore;
};
