import { BaseSelection, topRoundedRect, bottomRoundedRect } from "../Utilities";
import { extent, Selection, BaseType } from "d3";

export type SingleSeriesBarOptions<T> = {
    mapX: (data: T) => number;
    mapY: (data: T) => number;
    baseY: number;
    padding?: number;
    svgWidth?: number;
    shiftX?: boolean;
    tickWidth?: number;
    negativeBarClass?: string;
    positiveBarClass?: string;
};

export const singleSeriesRoundedBars = <T>(selection: BaseSelection, data: T[], options: SingleSeriesBarOptions<T>) => {
    const { mapX, mapY, baseY, padding, svgWidth, shiftX, tickWidth } = options;
    const mappedXValues = data.map(mapX);
    const xDomain = extent(mappedXValues) as [number, number];
    const domainWidth = Math.abs(xDomain[0] - xDomain[1]);
    const tick = tickWidth === undefined ? domainWidth / data.length : tickWidth;
    const barPadding = padding !== undefined ? padding : tick * 0.1;
    const barWidth = tick - (barPadding * 2);

    const roundedBars = selection
        .selectAll("path")
        .data(data);

    const paths = roundedBars.enter()
        .append("path")
        .attr("d", _ => {
            const startX = svgWidth ? svgWidth / 2 : 0;
            return `M${startX},${0}`;
        });

    paths
        .merge(roundedBars as Selection<SVGPathElement, T, BaseType, unknown>)
        .transition()
        .attr("d", d => {
            const startY = mapY(d);
            if (startY === baseY) {
                return "";
            }

            const dateX = mapX(d);
            const startX = shiftX ? dateX - (barWidth / 2) : dateX + barPadding;
            const isNegative = startY > baseY;

            if (isNegative) {
                const height = startY - baseY;
                return bottomRoundedRect(startX, baseY, barWidth, height, 5);
            }

            const height = baseY - startY;
            return topRoundedRect(startX, startY, barWidth, height, 5);
        })
        .attr("class", d => {
            const startY = mapY(d);
            const isNegative = startY > baseY;
            return isNegative ? (options.negativeBarClass || "negativeBar") :
                (options.positiveBarClass || "positiveBar");
        });

    roundedBars.exit().remove();
};

export const singleSeriesBars = <T>(selection: BaseSelection, data: T[], options: SingleSeriesBarOptions<T>) => {
    const { mapX, mapY, baseY, padding, svgWidth, shiftX, tickWidth } = options;
    const mappedXValues = data.map(mapX);
    const xDomain = extent(mappedXValues) as [number, number];
    const domainWidth = Math.abs(xDomain[0] - xDomain[1]);
    const tick = tickWidth === undefined ? domainWidth / data.length : tickWidth;
    const barPadding = padding !== undefined ? padding : tick * 0.1;
    const barWidth = tick - (barPadding * 2);

    const bars = selection
        .selectAll("rect")
        .data(data);

    const rects = bars.enter()
        .append("rect")
        .attr("x", svgWidth ? svgWidth / 2 : 0);

    rects
        .merge(bars as Selection<SVGRectElement, T, BaseType, unknown>)
        .transition()
        .attr("x", d => {
            const tickStart = mapX(d);
            return shiftX ? tickStart - (barWidth / 2) : tickStart + barPadding;
        })
        .attr("y", d => {
            const startY = mapY(d);
            const isNegative = startY > baseY;
            return isNegative ? baseY : startY;
        })
        .attr("width", barWidth)
        .attr("height", d => {
            const startY = mapY(d);
            const isNegative = startY > baseY;
            return isNegative ? startY - baseY : baseY - startY;
        })
        .attr("class", d => {
            const startY = mapY(d);
            const isNegative = startY > baseY;
            return isNegative ? (options.negativeBarClass || "negativeBar") :
                (options.positiveBarClass || "positiveBar");
        });

    bars.exit().remove();
};
