import sanitizeHtmlBase, { IOptions } from 'sanitize-html';

export const sanitizeHtml = (html: string, options?: IOptions) => sanitizeHtmlBase(html, {
    allowedTags: [
        'img',
        ...sanitizeHtmlBase.defaults.allowedTags
    ],
    allowedAttributes: {
        'img': ['alt', 'src'],
        '*': ['id'],
        ...sanitizeHtmlBase.defaults.allowedAttributes
    },
    transformTags: {
        'a': sanitizeHtmlBase.simpleTransform('a', { target: '_blank' }),
        ...options?.transformTags
    },
    ...options
});
