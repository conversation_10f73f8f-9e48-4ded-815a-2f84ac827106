import {
    Axis,
    AxisDomain,
    axisBottom,
    axisLeft,
    extent,
    line,
    ScaleLinear,
    scaleLinear,
    select,
    axisRight,
    area,
    Area,
    brushX,
    CurveFactory,
    curveLinear,
} from "d3";
import classnames from "classnames";
import { flatMap, uniqWith } from "lodash";
import React, { createRef, RefObject } from "react";

import {
    ChartData,
    ChartPoint,
    BaseChart,
    BaseChartConfig,
    BaseChartProps,
    MultiLineProps,
    ChartGradient,
    RightAxisLines,
    SpecialDates,
} from "..";
import { ChartAxisType } from "../../Models";
import {
    addGradient,
    BaseSelection,
    extentWithPadding,
    getMoment,
    isNumber,
} from "../../Utilities";

import { TrackingLine, ChartTooltip } from "./ChartTooltip";
import * as styles from "./MultiLineChartStyles.scss";
import { findDOMNode } from "react-dom";

type LineArea = {
    signFlip?: boolean;
    posGradientColor?: string;
    negGradientColor?: string;
};

type LegendState = {
    enabled: boolean;
    position?: "top" | "bottom";
    align?: "right" | "left" | "center";
};

type DateAxisState = {
    dateFormat: string;
    labelTransform?: string;
};

const defaultLegendState: LegendState = {
    enabled: true,
    position: "top",
    align: "center",
};

export type MultiLineChartDefinitionEx = {
    xAxisLabel?: string;
    yAxisLeftInvert?: boolean;
    xAxisType: ChartAxisType;
    yAxisLabel?: string;
    yAxisRightLabel?: string;
    yAxisRightInvert?: boolean;
    yAxisFormatter?: (x: number) => string;
    yAxisFormatterRight?: (x: number) => string;
    yAxisDataFormatter?: (x: number) => string;
    yAxisDataFormatterRight?: (x: number) => string;
    lines: { [key: string]: MultiLineChartLineDefinitionEx };
    gradients?: ChartGradient[];
    legend?: LegendState;
    dateAxisState?: DateAxisState;
    tickSize?: number;
    yAxisDomain?: [number, number];
};

export type MultiLineChartLineDefinitionEx = {
    name: string;
    stroke?: string;
    strokeWidth?: string;
    strokeDashArray?: string;
    legendGradient?: ChartGradient;
    hideLegend?: boolean;
    hideTooltip?: boolean;
    lineArea?: LineArea;
    hasDots?: boolean;
    isBar?: boolean;
    curveFunction?: CurveFactory;
};

type Config = BaseChartConfig & {
    padding: {
        top: number;
        bottom: number;
    };
};

const config: Config = {
    className: styles.multilineChartEx,
    margin: {
        top: 10,
        right: 60,
        bottom: 40,
        left: 60,
    },
    padding: {
        top: 10,
        bottom: 10,
    },
};

export type MultiLineChartExConfig = Config;

type yAxisBag = {
    lines: string[];
    scale: ScaleLinear<number, number>;
    scaleFormat: (t: number) => string;
    axis: Axis<AxisDomain>;
    selection: BaseSelection;
    labels: BaseSelection;
    lineContainer: BaseSelection;
    areaContainer: BaseSelection;
    chartLines: BaseSelection[];
};

/**
 * @deprecated use from ComponentLibrary
 */
export type ZoomState = [Date, Date] | false;

type PropsEx<T> = BaseChartProps & {
    data: ChartData<T>;
    definition: MultiLineChartDefinitionEx;
    legendOnly?: boolean;
    onZoom?: (zoom: ZoomState) => any;
    zoom?: ZoomState;
    refDates?: Date[];
    disabled?: boolean;
    config?: Config;
    hideTooltip?: boolean;
    className?: string;
    tooltipTop?: number;
};

export type MultiLinePropsEx<T> = PropsEx<T> & RightAxisLines & SpecialDates;

type MultiLineChartExState<T> = {
    currDate: Date | number;
    activeData: ChartData<T>;
    refDates?: Date[];
    activeDates: [Date, Date] | "all";
};

/**
 * @deprecated create a new version in module ticket TL-6508
 */
export class MultiLineChartEx<T> extends BaseChart<MultiLinePropsEx<T>, MultiLineChartExState<T>> {
    private lines: string[] = [];
    private dates: Date[] = [];
    private onZoom = (z: ZoomState) => {};

    private leftYbag: yAxisBag;
    private rightYbag: yAxisBag;

    private xScale: ScaleLinear<number, number>;
    private xAxis: Axis<AxisDomain>;
    private xAxisSelection: BaseSelection;
    private yScaleFormat: (t: number) => string;
    private yAxisGridlines: BaseSelection;

    private labels: BaseSelection;
    private gradients: BaseSelection;
    private brush: BaseSelection;

    constructor(props: MultiLinePropsEx<T>) {
        super(props, props.config || config);
        this.state = {
            currDate: -1,
            activeData: props.data,
            activeDates: "all",
            refDates: props.refDates,
        };
        this.dates = this.calcRefDates(props);
        this.xScale = scaleLinear();
        this.onHover = this.onHover.bind(this);
        props.onZoom && (this.onZoom = props.onZoom);
    }

    componentDidMount() {
        super.componentDidMount();
    }

    calcRefDates(props: MultiLinePropsEx<T>): Date[] {
        if (!props || !props.data) return [];

        const refDates = props.refDates && props.refDates.length && props.refDates;
        const allDates =
            refDates ||
            (Object.entries(props.data).flatMap(([_, point]) =>
                point.map(p => p.xValue),
            ) as unknown as Date[]);
        const dates = uniqWith(allDates, (d1, d2) => d1 === d2).sort(
            (a, b) => a.valueOf() - b.valueOf(),
        );
        return dates;
    }

    updateLines(props) {
        if (!props.data) {
            return;
        }
        this.lines = Object.keys(props.data);
        let leftLines = this.lines;
        let rightLines = this.props.rightLines;
        if (!rightLines) {
            this.leftYbag = this.getAxisBag(leftLines);
            return;
        }
        rightLines = this.lines.filter(l => rightLines.includes(l));
        leftLines = this.lines.filter(l => !rightLines.includes(l));
        this.leftYbag = this.getAxisBag(leftLines);
        this.rightYbag = this.getAxisBag(rightLines, false);
    }

    shouldComponentUpdate(nextProps: MultiLinePropsEx<T>, prevState: MultiLineChartExState<T>) {
        // if (!nextProps.data) {
        //     return false;
        // }
        return true;
    }

    updateData() {
        this.setState((state, props) => ({
            ...state,
            activeData: props.data,
            currDate: -1,
            refDates: props.refDates,
        }));
    }

    componentDidUpdate(prevProps: MultiLinePropsEx<T>, prevState: MultiLineChartExState<T>) {
        if (this.props.disabled || !this.props.data) {
            this.disposeChart();
            //this.setState(state => ({ ...state, activeData: null }));
            return;
        }
        if (
            prevProps.data !== this.props.data ||
            prevProps.definition !== this.props.definition ||
            prevProps.refDates !== this.props.refDates
        ) {
            this.updateData();
        } else if (this.state.activeData && this.state.activeData !== prevState.activeData) {
            this.initChart();
            this.updateChart();
        }
        if (prevProps.zoom !== this.props.zoom) this.zoom(this.props.zoom);
        super.componentDidUpdate(prevProps, prevState);
    }

    private getAxisBag(lines: string[], left = true): yAxisBag {
        const svgCurr = select(this.svg.current);
        const id = left ? "leftBag" : "rightBag";
        svgCurr.selectAll(`#${id}`).remove();
        const svg = svgCurr.append("g").attr("id", id);
        const axisFunc = left ? axisLeft : axisRight;
        const scale = scaleLinear().nice();
        const moveX = left ? this.config.margin.left : this.chartWidth + this.config.margin.right;
        const formatter = left
            ? this.props.definition.yAxisFormatter
            : this.props.definition.yAxisFormatterRight;
        const scaleFormat = formatter || (x => x.toString());
        return {
            lines: lines,
            scale: scale,
            scaleFormat: scaleFormat,
            axis: axisFunc(scale)
                .ticks(this.chartHeight / 30)
                .tickFormat((x: number) => scaleFormat(x)),
            selection: svg
                .append("g")
                .attr("class", `${styles.axis} + ${styles.yAxis}`)
                .attr("transform", `translate(${moveX}, 0)`),
            labels: svg.append("g"),
            lineContainer: svg.append("g").attr("class", `${styles.lines}`),
            areaContainer: svg.append("g").attr("id", `${id}Areas`),
            chartLines: [],
        };
    }

    protected initChart(): void {
        if (!this.props || !this.props.definition) return;

        this.updateLines(this.props);
        const svg = select(this.svg.current);

        const dateAxisState = this.props.definition.dateAxisState;
        const dateFormat = dateAxisState ? dateAxisState.dateFormat : "MMM YY";

        this.xAxis = axisBottom(this.xScale)
            //.tickSize(0)
            //.tickSizeOuter(5)
            //.tickSizeInner(0)
            //.tickPadding(8)
            //.ticks(this.chartWidth / 60)
            .tickFormat((i: number) => {
                if (!this.dates.length) return "";
                const date = this.dates[i];
                if (!date) return "";
                const asMoment = getMoment(date as any);
                return asMoment.format(dateFormat);
            });

        const tickSize = this.props.definition.tickSize;
        if (tickSize === 0 || !!tickSize) this.xAxis.tickSize(tickSize);

        if (!dateAxisState) this.xAxis.ticks(this.chartWidth / 60);

        svg.selectAll("g").remove();

        this.xAxisSelection = svg
            .append("g")
            .attr("class", `${styles.axis} + ${styles.xAxis}`)
            .attr("transform", `translate(0, ${this.config.margin.top + this.chartHeight})`);

        // Y axis
        this.yScaleFormat = this.props.definition.yAxisFormatter || (x => x.toString());

        // Grid lines
        this.yAxisGridlines = svg.append("g").attr("class", `${styles.gridLines}`);

        // Labels
        this.labels = svg.append("g");

        // Gradients
        this.gradients = svg.append("g");
    }

    protected updateChart(): void {
        if (!this.props.data) return;
        this.updateLines(this.props);
        this.updateGradients();
        this.updateScalesAndAxes();
        this.updateChartLines(this.leftYbag, true);
        this.updateChartLines(this.rightYbag, false);
        this.drawSpecialDates();
        this.updateZoom();
    }

    private zoom(zoomState: ZoomState) {
        if (!zoomState) {
            this.updateData();
            return;
        }
        const [d1, d2] = zoomState;
        const entries = Object.entries(this.props.data).map(([key, points]) => [
            key,
            points.filter(
                p =>
                    (p.xValue.valueOf() as number) >= d1.valueOf() &&
                    (p.xValue.valueOf() as number) <= d2.valueOf(),
            ),
        ]);
        const refDates =
            this.props.refDates &&
            this.props.refDates
                .filter(d => d >= d1 && d <= d2)
                .sort((d1, d2) => d1.valueOf() - d2.valueOf());
        const activeData = Object.fromEntries(entries);
        this.setState({ activeData, currDate: -1, refDates });
    }

    private updateZoom() {
        const updateDates = event => {
            if (!event.selection || !this.props.data) return;
            const [x1, x2] = event.selection;
            const index1 = Math.round(this.xScale.invert(x1) as number);
            const d1 = this.dates[index1];

            const index2 = Math.round(this.xScale.invert(x2) as number);
            const d2 = this.dates[index2];
            this.zoom([d1, d2]);
            this.onZoom([d1, d2]);
        };

        const svg = select(this.svg.current);
        svg.select("#zoombrush").remove();
        const brushFn = brushX()
            .extent([
                [this.config.margin.left, this.config.margin.top],
                [
                    this.config.margin.left + this.chartWidth,
                    this.config.margin.top + this.chartHeight,
                ],
            ])
            .on("end", updateDates);

        this.brush = svg
            .append("g")
            .attr("id", "zoombrush")
            .attr("class", "brush")
            .call(brushFn)
            .raise();

        this.brush.on("dblclick", () => {
            this.zoom(false);
            this.setState({
                activeData: this.props.data,
                currDate: -1,
                refDates: this.props.refDates,
            });
            this.onZoom(false);
        });
    }

    private updateGradients(): void {
        if (!this.gradients) return;

        this.gradients.selectAll("linearGradient").remove();

        if (this.props.definition.gradients && this.svg) {
            for (const gradient of this.props.definition.gradients) {
                addGradient(
                    select(this.svg.current),
                    gradient.id,
                    gradient.stops,
                    gradient.options,
                );
            }
        }
    }

    private drawSpecialDates() {
        this.drawSpecialDatesHelper(true);
        this.drawSpecialDatesHelper(false);
    }

    private drawSpecialDatesHelper(primary: boolean): void {
        const propName = primary ? "specialDates" : "specialDates2";
        const specialDates = this.props[propName];
        if (!specialDates || !specialDates.length || !this.xScale) return;
        const svgCurr = select(this.svg.current);
        const id = propName;
        svgCurr.select(`#${id}`).remove();
        const group = svgCurr
            .append("g")
            .attr("id", id)
            .attr("class", primary ? styles.specialAxis : styles.specialAxis2);

        specialDates.forEach(d => {
            const index = this.dateIndex(d.date);
            if (index < 0) return;
            const x = this.xScale(index);
            const y1 = this.config.margin.top;
            const y2 = this.config.margin.top + this.chartHeight;
            const textY = y1 + 5;
            const textX = x - 5;
            group.append("path").attr("stroke-dasharray", "1,2").attr("d", `M${x} ${y1} V${y2}`);
            group
                .append("text")
                .attr("y", `${textY}`)
                .attr("x", `${textX}`)
                .attr("alignment-baseline", "hanging")
                .text(d.name);
        });
    }

    getChartOffset(): number {
        // if (!svgRef.current)
        //     return 0;
        const offset = (findDOMNode(this.svg.current) as Element).getBoundingClientRect().left;
        return offset;
    }

    private onHover(ev: React.MouseEvent<SVGSVGElement, MouseEvent>): void {
        if (!this.props.data || this.props.hideTooltip) return;
        const x = ev.clientX;
        const lastIndex = this.dates.length - 1;
        const chartOffset = this.getChartOffset();
        const index = Math.round(this.xScale.invert(x - chartOffset).valueOf());
        const i = index < 0 ? 0 : index > lastIndex ? lastIndex : index;
        this.setState(state => ({ currDate: i }));
    }

    private dateIndex(date) {
        return this.dates.indexOf(date.valueOf());
    }

    calcStateActiveDates() {
        const props = {
            ...this.props,
            data: this.state.activeData,
            refDates: this.state.refDates,
        };
        return this.calcRefDates(props);
    }

    private updateScalesAndAxes(): void {
        if (!this.labels || !this.yAxisGridlines) return;

        this.labels.selectAll("text").remove();

        this.yAxisGridlines.selectAll("line").remove();

        this.dates = this.calcStateActiveDates();

        // X scale
        switch (this.props.definition.xAxisType) {
            case ChartAxisType.Date: {
                (this.xScale as ScaleLinear<number, number>)
                    .domain([0, this.dates.length - 1])
                    //.nice()
                    .range([this.config.margin.left, this.config.margin.left + this.chartWidth]);

                this.xAxis.tickFormat((x: Date) => {
                    if (x === undefined) return "";

                    return x.toLocaleDateString("en-GB", {
                        day: "2-digit",
                        month: "short",
                    });
                });

                break;
            }
            case ChartAxisType.Number: {
                // const allNumbers = this.lines.reduce((acc, x) => acc.concat(this.state.activeData[x].map(x1 => x1.xValue as any)), [] as number[]);
                // const xExtent = extent(allNumbers);

                (this.xScale as ScaleLinear<number, number>)
                    //.domain([xExtent[0] || 0, xExtent[1] || 0])
                    .domain([0, this.dates.length - 1])
                    //.nice()
                    .range([this.config.margin.left, this.config.margin.left + this.chartWidth]);

                break;
            }
            default:
                throw new Error("ChartAxisType Not Implemented");
        }

        const comonAxis = this.xAxisSelection
            .transition()
            .call(this.xAxis as any)
            .selectAll("text");

        const transform =
            this.props.definition.dateAxisState &&
            this.props.definition.dateAxisState.labelTransform;
        !!transform && comonAxis.attr("transform", transform);

        if (this.props.definition.xAxisLabel) {
            this.labels
                .append("text")
                .attr("class", `${styles.yAxisLabel}`)
                .attr(
                    "transform",
                    `translate(${this.containerWidth / 2}, ${
                        this.config.margin.top + this.chartHeight + 30
                    })`,
                )
                .text(this.props.definition.xAxisLabel);
        }
    }

    private updateBagScales(bag: yAxisBag, isLeftAxis: boolean) {
        if (!bag) {
            return;
        }
        const allValues: number[] = bag.lines
            ? bag.lines.flatMap((l: string) => this.state.activeData[l].map(d => d.yValue))
            : [];
        let minMax =
            this.props.definition.yAxisDomain ??
            extentWithPadding(
                allValues,
                (this.config as Config).padding.top,
                (this.config as Config).padding.bottom,
            );

        const invertAxis =
            (isLeftAxis && this.props.definition.yAxisLeftInvert) ||
            (!isLeftAxis && this.props.definition.yAxisRightInvert);

        if (invertAxis) {
            minMax = [minMax[1], minMax[0]];
        }

        bag.scale
            .domain(minMax)
            .range([this.config.margin.top + this.chartHeight, this.config.margin.top]);

        bag.selection.transition().call(bag.axis as any);

        const defaultOffset = 20;
        const left = bag === this.leftYbag;
        const axisLabel = left
            ? this.props.definition.yAxisLabel
            : this.props.definition.yAxisRightLabel;
        const xpos = left
            ? defaultOffset
            : this.chartWidth +
              this.config.margin.left +
              this.config.margin.right -
              defaultOffset +
              5;
        if (axisLabel) {
            bag.labels
                .append("text")
                .attr("class", `${styles.yAxisLabel}`)
                .attr(
                    "transform",
                    `translate(${xpos}, ${
                        this.config.margin.top + this.chartHeight / 2 + defaultOffset * 2
                    }) rotate(-90)`,
                )
                .text(`${axisLabel}${(invertAxis && " (Inverted)") || ""}`);
        }

        bag.scale.ticks(this.chartHeight / 30).forEach(t => {
            const yPosition = bag.scale(t);
            if (bag === this.leftYbag || !this.leftYbag.lines.length)
                this.yAxisGridlines
                    .append("line")
                    .attr("x1", this.config.margin.left)
                    .attr("y1", yPosition)
                    .attr("x2", this.config.margin.left + this.chartWidth)
                    .attr("y2", yPosition);
        });
    }

    private makeGradient(group, color) {
        if (!group) return;
        const gradientId = `gradient${color}`;
        group.select("linearGradient").remove();

        const gradient = group
            .append("linearGradient")
            .attr("id", gradientId)
            .attr("x1", 0)
            .attr("x2", 0)
            .attr("y1", 0)
            .attr("y2", "100%");

        gradient
            .append("svg:stop")
            .attr("offset", "0%")
            .attr("stop-color", color)
            .attr("stop-opacity", 0.2);

        gradient.append("svg:stop").attr("offset", "100%").attr("stop-opacity", 0.2);
    }

    private drawLines(
        bag: yAxisBag,
        lineDefData: MultiLineChartLineDefinitionEx,
        lineData: ChartPoint<T>[],
    ) {
        const lineFn = line<ChartPoint<T>>()
            .defined(l => this.dateIndex(l.xValue) >= 0)
            .x(d => this.xScale(this.dateIndex(d.xValue)))
            .y(y => bag.scale(y.yValue))
            .curve(lineDefData.curveFunction ?? curveLinear);

        const lineSelection = bag.lineContainer.append("g").append("path");

        if (lineDefData.stroke) lineSelection.attr("stroke", lineDefData.stroke);
        if (lineDefData.strokeWidth) lineSelection.attr("stroke-width", lineDefData.strokeWidth);
        if (lineDefData.strokeDashArray)
            lineSelection.attr("stroke-dasharray", lineDefData.strokeDashArray);

        lineSelection.datum(lineData).transition().attr("d", lineFn);
        bag.chartLines.push(lineSelection);

        if (lineDefData.hasDots) {
            const circles = bag.lineContainer
                .append("g")
                .style("fill", lineDefData.stroke)
                .selectAll("circle")
                .data(lineData)
                .enter()
                .append("circle")
                //.attr("stroke", lineDefData.stroke)
                //.attr("fill", lineDefData.stroke)
                .attr("cx", d => this.xScale(this.dateIndex(d.xValue)))
                .attr("cy", y => bag.scale(y.yValue))
                .attr("r", 3);
            bag.chartLines.push(circles);
        }
    }

    private drawBars(
        bag: yAxisBag,
        lineDefData: MultiLineChartLineDefinitionEx,
        lineData: ChartPoint<T>[],
    ) {
        const bars = bag.lineContainer.append("g").style("fill", lineDefData.stroke);

        const scale = val => bag.scale(val);
        const [a, b] = this.xScale.range();
        const barWidth = ((b - a) / lineData.length) * 0.5;
        const [minData, maxData] = bag.scale.domain();
        const barStart = minData < 0 ? Math.min(0, maxData) : Math.max(0, minData);
        bars.selectAll("rect")
            .data(lineData)
            .enter()
            .append("rect")
            .attr("x", d => this.xScale(this.dateIndex(d.xValue)))
            //.attr("y", (d) => scale(d.yValue))
            .attr("y", d => (d.yValue < 0 ? scale(0) : scale(d.yValue)))
            .attr("height", d => Math.abs(scale(d.yValue) - scale(barStart)))
            .attr("opacity", 0.5)
            //.attr("width", d => d.xValue.valueOf() === lastDate ? 3 : barWidth)
            .attr("width", barWidth)
            //.attr("class", d => styles.lightBar)
            .attr("transform", `translate(${-barWidth / 2}, ${0})`)
            .lower();

        bag.chartLines.push(bars);
    }

    private drawAreas(
        bag: yAxisBag,
        lineDefData: MultiLineChartLineDefinitionEx,
        lineData: ChartPoint<T>[],
    ) {
        const makeAreaChart = (areaFn: Area<ChartPoint<T>>, color: string) => {
            //bag.areaContainer.selectAll("g").remove();
            this.makeGradient(bag.areaContainer, color);

            const areaSelection = bag.areaContainer.append("g").lower().append("path");

            areaSelection
                .datum(lineData)
                // .style("fill", `url(#gradientgreen`)
                .attr("fill", color)
                .attr("fill-opacity", 0.5)
                .attr("stroke", color)
                .attr("d", areaFn);
            bag.chartLines.push(areaSelection);
        };

        const r = extent(lineData.map(d => d.yValue));
        const [minData, maxData] = bag.scale.domain();
        const barStart = minData < 0 ? Math.min(0, maxData) : Math.max(0, minData);
        const areaFn = (compare: (x: ChartPoint<T>) => boolean) =>
            area<ChartPoint<T>>()
                .defined(l => this.dateIndex(l.xValue) >= 0 && compare(l))
                .x(d => this.xScale(this.dateIndex(d.xValue)))
                .y0(bag.scale(barStart))
                .y1(d => bag.scale(d.yValue));

        makeAreaChart(
            areaFn(x => x.yValue >= 0),
            lineDefData.lineArea!.posGradientColor || "green",
        );
        makeAreaChart(
            areaFn(x => x.yValue < 0),
            lineDefData.lineArea!.negGradientColor || "red",
        );
    }

    private updateChartLines(bag: yAxisBag, isLeftAxis: boolean) {
        if (!bag || !this.xScale) {
            return;
        }

        this.updateBagScales(bag, isLeftAxis);

        bag.chartLines = [];
        const [min, max] = extent(this.calcStateActiveDates());
        for (const lineName of bag.lines) {
            const lineDefData = this.props.definition.lines[lineName];
            const lineData = this.state.activeData[lineName].filter(
                d => (d.xValue as any) >= min && (d.xValue as any) <= max,
            );

            if (!lineDefData || !lineData) continue;
            const hasArea = !!lineDefData.lineArea;
            const isBar = !!lineDefData.isBar;
            if (hasArea) this.drawAreas(bag, lineDefData, lineData);
            else if (isBar) this.drawBars(bag, lineDefData, lineData);
            else this.drawLines(bag, lineDefData, lineData);
        }
    }

    protected disposeChart(): void {
        select(this.svg.current).selectAll("g").remove();
    }

    Legend = ({ ...props }) => (
        <div {...props}>
            {Object.keys(this.props.data).map(lineName => {
                const line = this.props.definition.lines[lineName];
                return (
                    !!line &&
                    !line.hideLegend && (
                        <div
                            key={line.name}
                            className={styles.legendItemEx}
                        >
                            <span style={{ marginRight: 4 }}>{line.name}</span>
                            <svg
                                width={20}
                                height={4}
                                shapeRendering="crispEdges"
                            >
                                {line.legendGradient && (
                                    <linearGradient
                                        id={line.legendGradient.id}
                                        gradientUnits="objectBoundingBox"
                                        x1={line.legendGradient.options.x1}
                                        y1={line.legendGradient.options.y1}
                                        x2={line.legendGradient.options.x2}
                                        y2={line.legendGradient.options.y2}
                                    >
                                        {line.legendGradient.stops.map(stop => (
                                            <stop
                                                key={stop.offset + stop.color}
                                                offset={stop.offset}
                                                stopColor={stop.color}
                                            ></stop>
                                        ))}
                                    </linearGradient>
                                )}
                                <line
                                    x1="0%"
                                    y1="40%"
                                    x2="100%"
                                    y2="40.00001%" // Chrome is unable to draw a horizontal line with a gradient
                                    stroke={
                                        line.legendGradient
                                            ? `url(#${line.legendGradient.id})`
                                            : line.stroke
                                    }
                                    strokeWidth={line.strokeWidth}
                                    strokeDasharray={line.strokeDashArray}
                                />
                            </svg>
                        </div>
                    )
                );
            })}
        </div>
    );

    render() {
        if (!this.props.data) {
            return <div className={styles.multiLineChartContainer}>{super.render()}</div>;
        }
        const legend = this.props.definition.legend || defaultLegendState;
        const legendClass =
            !legend.align || legend.align === "center"
                ? styles.legendEx
                : legend.align === "left"
                ? styles.legendExLeft
                : styles.legendExRight;
        const Legend = this.Legend;
        // Workaround to make this chart correctly responsive - the chart height does not take the legend height into account
        // which means setting a height will actually render a chart of height + legend height. This allows the chart to render
        // without a legend so it uses the correct height and the parent component can render the legend separately in a
        // responsive manner...
        if (this.props.legendOnly) {
            return <Legend className={legendClass} />;
        }
        const { width, height, hideTooltip, className } = this.props;
        const legendBottom = this.config.margin.top + this.chartHeight + this.config.margin.bottom;
        const leftFormatter =
            this.props.definition.yAxisDataFormatter || this.props.definition.yAxisFormatter;
        const rightFormatter =
            this.props.definition.yAxisDataFormatterRight ||
            this.props.definition.yAxisFormatterRight;

        return (
            <div className={classnames(className, styles.multiLineChartContainerEx)}>
                {legend.enabled && legend.position === "top" && <Legend className={legendClass} />}
                {!hideTooltip && (
                    <ChartTooltip
                        date={this.dates.length && this.dates[this.state.currDate.valueOf()]}
                        dateIndex={this.state.currDate && this.state.currDate.valueOf()}
                        dateScale={this.xScale}
                        series={this.state.activeData}
                        rightLines={this.props.rightLines}
                        leftFormatter={leftFormatter}
                        rightFormatter={rightFormatter}
                        lines={this.props.definition.lines}
                        x={this.xScale(this.state.currDate && this.state.currDate.valueOf())}
                        top={this.props.tooltipTop}
                    />
                )}
                <svg
                    id="mysvg"
                    onMouseMove={this.onHover}
                    onMouseLeave={() => {
                        this.setState(state => ({ ...this.state, currDate: -1 }));
                    }}
                    className={this.config.className}
                    width={this.props.fixedSize ? width : undefined}
                    height={this.props.fixedSize ? height : undefined}
                    viewBox={`0 0 ${width} ${height}`}
                    ref={this.svg}
                >
                    {!hideTooltip && (
                        <TrackingLine
                            date={this.state.currDate}
                            height={this.chartHeight}
                            dateScale={this.xScale}
                            topBottom={this.config.margin.top}
                        />
                    )}
                </svg>
                {legend.enabled && legend.position === "bottom" && (
                    <Legend
                        className={legendClass}
                        style={{ top: legendBottom }}
                    />
                )}
            </div>
        );
    }
}
