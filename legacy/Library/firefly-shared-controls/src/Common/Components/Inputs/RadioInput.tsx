import React, { Component, ChangeEvent } from "react";

import { css } from "../../Utilities";

import * as styles from "./Styles.scss";

export type RadioItem<T> = Readonly<{
    label: string;
    selected?: boolean;
    value: T;
}>;

type Props<T> = {
    items: Array<RadioItem<T>>;
    label?: string;
    onChange?: (value?: T) => void;
    required?: boolean;
    disabled: boolean;
    value?: T;
    classNames?: string[];
};

/**
 * Radio Input
 * Radio buttons displayed as a single horizontal row.
 */
export class RadioInput<T extends any> extends Component<Props<T>> {
    private onChange = (event: ChangeEvent<HTMLInputElement>) => {
        if (this.props.onChange) {
            const item = this.props.items.find(x => x.value.toString() === event.target.value);
            this.props.onChange(item ? item.value : undefined);
        }
    }

    render() {
        return (
            <div className={css(styles.radioContainer, this.props.classNames || [])}>
                {
                    this.props.label &&
                    <div className={styles.radioLabel}>{this.props.label}</div>
                }
                <div className={styles.radioList}>
                    {
                        this.props.items.map((x, i) => (
                            <label
                                className={css(
                                    styles.radioItem,
                                    x.selected ? styles.active : "",
                                    this.props.required && !(this.props.value && this.props.items.find(x => x.value === this.props.value) !== undefined) ? styles.required : "")}
                                key={`${i}_${x.value.toString()}`}
                            >
                                <input
                                    hidden
                                    checked={this.props.value === x.value}
                                    onChange={this.onChange}
                                    type="radio"
                                    value={x.value.toString()}
                                    disabled={this.props.disabled}
                                />
                                <span>{x.label}</span>
                            </label>
                        ))
                    }
                </div>
            </div>
        );
    }
}
