import { isEqual, sortBy } from "lodash";
import React, { Component, ChangeEvent } from "react";

import { css } from "../../Utilities";

import * as styles from "./Styles.scss";

export type SelectItem<T> = Readonly<{
    label: string;
    value: T;
}>;

type Props<T> = {
    disabled?: boolean;
    items: Array<SelectItem<T>>;
    label?: string;
    onChange?: (value?: T) => void;
    required?: boolean;
    sortByLabel?: boolean;
    value?: T;
    classNames?: string[];
    labelClassName?: string;
};

type State = {
    active: boolean;
    focus: boolean;
    internalValue: string;
};

/**
 * Select Input
 * A dropdown select input with a floating label.
 */
export class SelectInput<T extends any> extends Component<Props<T>, State> {
    state: State = this.calculateState();

    componentDidUpdate(prevProps: Props<T>) {
        if (!isEqual(prevProps.value, this.props.value) || !isEqual(prevProps.items, this.props.items)) {
            this.updateState();
        }
    }

    private updateState(): void {
        this.setState(this.calculateState());
    }

    private calculateState(): State {
        const valid = this.props.value !== undefined && this.props.items.find(x => x.value === this.props.value) !== undefined;
        return {
            active: valid,
            focus: false,
            internalValue: valid ? this.props.value!.toString() : "",
        };
    }

    private onBlur = () => {
        this.updateState();
    }

    private onChange = (event: ChangeEvent<HTMLSelectElement>) => {
        if (this.props.onChange) {
            const item = this.props.items.find(x => x.value.toString() === event.target.value);
            this.props.onChange(item ? item.value : undefined);
        }
    }

    private onFocus = () => {
        this.setState({
            active: true,
            focus: true,
        });
    }

    render() {
        const containerClasses = css(
            styles.container,
            {
                [styles.active] : this.state.active,
                [styles.focus]: this.state.focus,
                [styles.required]: this.props.required && this.state.internalValue === "",
                [styles.disabled]: this.props.disabled
            },
            [...this.props.classNames || []]
        );

        return (
            <div className={containerClasses}>
                <div className={styles.labelContainer}>
                    <div className={styles.labelMask}>{this.props.label}</div>
                    <div className={css(styles.label, this.props.labelClassName)}>{this.props.label}</div>
                </div>
                <select
                    className={styles.input}
                    onBlur={this.onBlur}
                    onChange={this.onChange}
                    onFocus={this.onFocus}
                    value={this.state.internalValue}
                    disabled={this.props.disabled}
                >
                    <option disabled style={{ display: "none" }} value=""></option>
                    {
                        this.props.sortByLabel
                            ? sortBy(this.props.items, x => x.label).map(x => (<option key={x.value.toString()} value={x.value.toString()}>{x.label}</option>))
                            : this.props.items.map(x => (<option key={x.value.toString()} value={x.value.toString()}>{x.label}</option>))
                    }
                </select>
            </div>
        );
    }
}
