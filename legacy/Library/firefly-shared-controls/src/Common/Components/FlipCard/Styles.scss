@import "../../Styles/Colors.scss";
@import "../../Styles/Sizes.scss";

.flip-card-container {
    width: 100%;
    perspective: 1000px;
}

.flip-card {
    position: relative;
    transition: 0.4s;
    transform-style: preserve-3d;
    width: 94%;

    &.toggle {
        cursor: pointer;
    }

    &.flipped {
        transform: rotateY(180deg);
    }

    .flip-card-back,
    .flip-card-front {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
    }

    .flip-card-back {
        transform: rotateY(180deg);
        backface-visibility: hidden;
    }
}

.toggle-buttons {
    display: flex;

    .toggle-button {
        color: $textColorHighlight;
        border: 1px solid #333;
        font-size: $textSizeDefaultOld;
        font-weight: 500;
        padding: 6px 10px;
        min-width: 150px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        user-select: none;

        @media print {
            color: $textColorHighlightPrint;
        }

        &.toggled {
            color: white;
            background-color: black;
            cursor: default;
        }

        &:hover {
            background-color: black;
        }
    }
}
