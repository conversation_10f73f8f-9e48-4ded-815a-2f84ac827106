import React, { Component } from "react";
import { css } from "../../Utilities";
import * as styles from "./Styles.scss";

type Props = {
    toggleButton?: {
        frontLabel: string;
        backLabel: string;
    }
    children?: any;
};

type State = {
    flipped: boolean;
};

const initialState: State = {
    flipped: false
};

export class FlipCard extends Component<Props, State> {
    state = initialState;

    private flip = () => {
        this.setState({
            flipped: !this.state.flipped
        });
    }

    render() {
        const { children, toggleButton } = this.props;
        const { flipped } = this.state;

        return (
            <>
                {
                    toggleButton &&
                    <div className={styles.toggleButtons}>
                        <div onClick={flipped ? this.flip : () => {}} className={css(styles.toggleButton, {[styles.toggled]: !flipped })}>
                            {toggleButton.frontLabel}
                        </div>
                        <div onClick={!flipped ? this.flip : () => {}} className={css(styles.toggleButton, {[styles.toggled]: flipped })}>
                            {toggleButton.backLabel}
                        </div>
                    </div>
                }
                <div className={styles.flipCardContainer}>
                    <div className={css(styles.flipCard, {[styles.flipped] : flipped, [styles.toggle]: !toggleButton})} onClick={!toggleButton ? this.flip : () => {}}>
                        <div className={styles.flipCardFront}>
                            {children[0]}
                        </div>
                        <div className={styles.flipCardBack}>
                            {children[1]}
                        </div>
                    </div>
                </div>
            </>
        );
    }
}
