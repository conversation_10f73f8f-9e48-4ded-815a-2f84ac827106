declare namespace StylesScssNamespace {
    export interface IStylesScss {
        active: string;
        areas: string;
        ascending: string;
        axis: string;
        chartContainer: string;
        data: string;
        dataContainer: string;
        descending: string;
        error: string;
        fullGridHeightItem: string;
        fullGridWidthItem: string;
        gridLines: string;
        gridLinesBase: string;
        highlight: string;
        inactive: string;
        label: string;
        legend: string;
        legendItem: string;
        lines: string;
        moveDown: string;
        moveUp: string;
        multiLineChart: string;
        multiLineChartContainer: string;
        negative: string;
        negativeTicketButton: string;
        neutral: string;
        positive: string;
        positiveTicketButton: string;
        raidInsightsContainer: string;
        raidInsightsWidget: string;
        raidInsightsWidgetHeading: string;
        sortCaret: string;
        tickerLink: string;
        ticketButton: string;
        title: string;
        transparentBtn: string;
        twoColumnGrid: string;
        warn: string;
        xAxis: string;
        xAxisLabel: string;
        yAxis: string;
        yAxisLabel: string;
    }
}

declare const StylesScssModule: StylesScssNamespace.IStylesScss & {
    /** WARNING: Only available when `css-loader` is used without `style-loader` or `mini-css-extract-plugin` */
    locals: StylesScssNamespace.IStylesScss;
};

export = StylesScssModule;
