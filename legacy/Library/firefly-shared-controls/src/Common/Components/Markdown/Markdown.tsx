import React, { useMemo } from 'react';
import { marked } from 'marked'

import { sanitizeHtml } from '../../Utilities/SanitizeHtml';

import { Container } from './Container';

const renderer: marked.RendererObject = {
    heading(text, level) {
        const escapedText = text.toLowerCase().replace(/[^\w]+/g, '');

        return `
            <h${level} id="${escapedText}">
            ${text}
            </h${level}>`;
    },
    link(href, title, text) {
        if (href.startsWith("#")) {
            return text
        }

        return false;
    }
};

marked.use({ renderer });

type MarkdownProps = {
    markdown: string;
    className?: string;
};

export const Markdown = ({ markdown, className }: MarkdownProps) => {
    const html = useMemo(() => (markdown && sanitizeHtml(marked.parse(markdown, {
        smartLists: true,
        smartypants: true,
        headerIds: true
    }))) || "", [markdown]);

    return (
        <Container className={className} dangerouslySetInnerHTML={{ __html: html }} />
    )
};

