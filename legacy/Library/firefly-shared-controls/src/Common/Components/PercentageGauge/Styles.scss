@import "../../Styles/Colors.scss";

.gaugeContainer {
    font-weight: 100;
}

.svgContainer {
    backface-visibility: hidden;
    margin: auto;
    position: relative;
    text-align: initial;

    svg {
        backface-visibility: hidden;
        position: absolute;
        z-index: 3;
    }
}

.percentageGaugeGradient {
    backface-visibility: hidden;
    position: absolute;
    border-top-left-radius: 100px;
    border-top-right-radius: 100px;
    background: conic-gradient(from -180deg, $donutGradientStart, $donutGradientEnd);
    z-index: 1;
}

.percentageGaugeMask {
    backface-visibility: hidden;
    background: $backgroundColorLight;
    position: absolute;
    border-top-left-radius: 100px;
    border-top-right-radius: 100px;
    z-index: 2;
}

.outerArc {
    fill: #494B50;
}

.percentageArc {
    fill: #666;
}

.needle {
    fill: #CCC;
}

.needleCircle {
    fill: $backgroundColorLight;
    stroke: #666;
}

.gaugeLabels {
    display: flex;
    flex-flow: row;
    justify-content: space-between;
    align-items: baseline;
    width: 100%;
    margin-top: 5px;

    span {
        flex: 1 0 33.3%;
        font-size: 20px;
        color: #879ba6;

        &:nth-of-type(2) {
            text-align: center;
            font-size: 30px;
            color: #999;
        }

        &:last-of-type {
            text-align: right;
        }
    }
}