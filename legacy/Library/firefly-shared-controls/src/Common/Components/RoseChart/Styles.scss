@import "../../Styles/Colors.scss";

.roseChartContainer {
    position: relative;
    width: 100%;
    height: 100%;
    flex: 1;
    display: flex;

    svg {
        width: 100%;
        height: 100%;
    }

    .boundaries {
        circle {
            fill: none;
            stroke: gray;
            opacity: 0.1;

            &:last-of-type {
                opacity: 0.2;
                stroke: white;
            }
        }
    }

    .boundaryLabels {
        div {
            position: absolute;
            display: flex;
            justify-content: center;
            left: 0;
            width: 100%;
            pointer-events: none;

            span {
                background-color: rgba(0, 0, 0, 0.5);
                color: white;
                padding: 1px 4px;
                font-size: 9px;
                border-radius: 2px;
            }
        }
    }

    .sliceLabels {
        div {
            display: flex;
            align-items: center;
        }

        span {
            max-width: 120px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: pre;
        }

        div,
        span {
            position: absolute;
        }
    }

    .sliceSections {
        line {
            stroke: white;
            opacity: 0.7;
            stroke-dasharray: 2px;
        }
    }

    .slices {
        path {
            fill: #0b80b3;
            opacity: 0.35;
            transition: opacity 0.2s;

            &:hover {
                opacity: 1;
            }
        }
    }
}