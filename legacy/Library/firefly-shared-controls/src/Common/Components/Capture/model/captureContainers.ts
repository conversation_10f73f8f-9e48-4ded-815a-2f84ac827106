import { domToImageBlob, Options } from "../domToImageBlob";
import { CaptureAPIOptions } from "../useCaptureAPI";
import { CaptureArea } from "./captureArea";
import { CaptureAreas } from "./captureAreas";
import { CaptureContainer } from "./captureContainer";

export type CaptureContainers = {
    capture: () => Promise<CaptureAreas>;
    filter: (cb: (container: CaptureContainer) => boolean, index?: number) => CaptureContainers;
    sort: (cp: (a: CaptureContainer, b: CaptureContainer) => number) => CaptureContainers;
    getContainers: () => CaptureContainer[];
};

export const CaptureContainers = (containers: CaptureContainer[], captureOptions: CaptureAPIOptions): CaptureContainers => {
    const {setIsBusy, setErrors, setStatus} = captureOptions;

    return {
        filter: (cb, i) => CaptureContainers(containers.filter(cb, i), captureOptions),
        sort: (cb) => CaptureContainers([...containers].sort(cb), captureOptions),
        capture: async (options?: Options) => {
            try {
                setIsBusy(true);

                setStatus({
                    count: containers.length,
                    captureCount: 0,
                    combineCount: 0,
                    downloadCount: 0
                });

                const areas = await containers.reduce(async (previous, container) => {
                    const areas = await previous;

                    const blob = await domToImageBlob(container, {
                        ...options,
                        backgroundColor: container.backgroundColor
                    });

                    setStatus(status => ({
                        ...status,
                        captureCount: status.captureCount + 1
                    }));

                    const area = {
                        container,
                        blob,
                        getImage: () =>  new Promise<HTMLImageElement>((resolve, reject) => {
                            const url = URL.createObjectURL(blob);
                            const img = new Image();
                            img.onload = () => {
                                URL.revokeObjectURL(url);
                                resolve(img);
                            }
                            img.onerror = reject;
                            img.src = url;
                        })
                    };

                    return [...areas, area];
                }, Promise.resolve([] as CaptureArea[]));

                return CaptureAreas(areas, captureOptions)
            } catch (ex) {
                setErrors([ex]);
                throw ex;
            } finally {
                setIsBusy(false);
            }
        },
        getContainers: () => containers
    };
};
