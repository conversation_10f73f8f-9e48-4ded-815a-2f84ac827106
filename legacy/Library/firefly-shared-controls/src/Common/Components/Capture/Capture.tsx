import React, { ReactNode, useRef } from "react";
import classnames from "classnames";
import styled from "styled-components";

import { CaptureContext } from "./useCapture";
import { useCaptureAPI, CaptureOptions } from "./useCaptureAPI";

type CaptureProps = {
  children: ReactNode;
  className?: string;
} & CaptureOptions;
  
export const Capture = ({children, formatFilename, className}: CaptureProps) => {
  const container = useRef<HTMLDivElement>();
  const api = useCaptureAPI(container, {formatFilename});

  return (
    <CaptureContext.Provider value={api}>
      <Container ref={container} className={classnames({capturing: api.isBusy}, className)}>
        {children}
      </Container>
    </CaptureContext.Provider>
  );
};

const Container = styled.div`
  position: relative;
`;
