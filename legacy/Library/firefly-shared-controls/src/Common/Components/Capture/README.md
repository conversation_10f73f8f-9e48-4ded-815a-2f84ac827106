# Capture

> Simple DOM -> image capture. Based on [html-to-image](https://www.npmjs.com/package/html-to-image).

```tsx
...
<Capture>
    <header>
        <CaptureOption>
            {({capture, isBusy}) => (
                <button
                    onClick={capture}
                    disabled={isBusy}
                >
                    Capture
                </button>
            )}
        </CaptureOption>
    </header>
    <CaptureArea name="my-capture-area">
        ...
        <div>Some content</div>
        ...
    </CaptureArea>
</Capture>
...
```

* ```name``` will be used as the filename
* ```<CaptureArea/>``` can be include multiple times and can be saved as it's own image

## Setting options

See [html-to-image](https://www.npmjs.com/package/html-to-image) for available options.

```tsx
...
<CaptureOption>
    {({isBusy, setOptions}) => (
        <button
            onClick={() => setOptions({backgroundColor: "red"}).capture())}
            disabled={isBusy}
        >
            Capture
        </button>
    )}
</CaptureOption>
...
```
