import React from "react";
import {
    MultiBarChartDefinition,
    MultiBarChartSeriesDefinition,
    MultiBarChartPoint,
    MultiBarChartData
} from "./Models";
import { flatMap } from "lodash";

import * as styles from "./Styles.scss";
import {
    select,
    ScaleLinear,
    AxisDomain,
    Axis,
    scaleLinear,
    axisBottom,
    axisLeft,
    axisRight,
    ScaleBand,
    scaleBand,
    format,
    extent
} from "d3";
import {
    BaseSelection,
    topRoundedRect,
    getMoment,
    bottomRoundedRect,
    getShorthandValue
} from "../../Utilities";
import { BaseChartProps, BaseChartConfig, BaseChart } from "../BaseChart";

type Props = BaseChartProps & {
    data: MultiBarChartData;
    definition: MultiBarChartDefinition;
};

type State = {
    tooltip: {
        eventX: number;
        eventY: number;
        data?: DateData;
    };
};

type Config = BaseChartConfig & {
    barPadding: number;
    tooltipKeySize: number;
    yAxisMargin: number;
};

type DateData = {
    date: Date;
    values: Array<number | undefined>;
};

const initialState: State = {
    tooltip: {
        eventX: 0,
        eventY: 0
    }
};

const config: Config = {
    margin: {
        top: 30,
        right: 20,
        bottom: 22,
        left: 20
    },
    barPadding: 2,
    tooltipKeySize: 8,
    yAxisMargin: 40
};

export class MultiBarChart extends BaseChart<Props, State> {
    state = initialState;

    private series: string[] = [];

    private xScale: ScaleBand<Date>;
    private xAxis: Axis<AxisDomain>;
    private xAxisSelection: BaseSelection;

    private yScale: ScaleLinear<number, number>;
    private yAxis: Axis<AxisDomain>;
    private yAxisSelection: BaseSelection;
    private yAxisGridlines: BaseSelection;

    private chartData: BaseSelection;

    constructor(props: Props) {
        super(props, config);
    }

    componentDidMount() {
        if (this.props.data) {
            this.series = Object.keys(this.props.data);
        }
        super.componentDidMount();
    }

    componentDidUpdate(prevProps: Props) {
        if (!this.props.data) {
            return;
        }
        if (prevProps.data !== this.props.data || prevProps.definition !== this.props.definition) {
            if (prevProps.data !== this.props.data) {
                this.series = Object.keys(this.props.data);
            }
            this.updateChart();
        }
        super.componentDidUpdate(prevProps);
    }

    protected initChart(): void {
        const svg = select(this.svg.current);

        this.xScale = scaleBand<Date>();
        this.xAxis = axisBottom(this.xScale).ticks(10);

        this.xAxisSelection = svg
            .append("g")
            .attr("class", styles.axis)
            .attr("transform", `translate(0, ${this.props.height - config.margin.bottom})`);

        this.yScale = scaleLinear();

        if (this.props.definition.yAxisOnLeft) {
            this.yAxis = axisLeft(this.yScale).tickFormat((x: number) => getShorthandValue(x));

            this.yAxisSelection = svg
                .append("g")
                .attr("class", styles.axis)
                .attr(
                    "transform",
                    `translate(${
                        config.margin.left +
                        (this.props.definition.yAxisOnLeft ? config.yAxisMargin : 0)
                    }, 0)`
                );
        } else {
            this.yAxis = axisRight(this.yScale).tickFormat((x: number) => getShorthandValue(x));

            this.yAxisSelection = svg
                .append("g")
                .attr("class", styles.axis)
                .attr(
                    "transform",
                    `translate(${
                        this.props.width -
                        config.margin.right -
                        (this.props.definition.yAxisOnLeft ? 0 : config.yAxisMargin)
                    }, 0)`
                );
        }

        this.yAxisGridlines = svg.append("g").attr("class", `${styles.gridLines}`);

        this.chartData = svg.append("g");
    }

    protected disposeChart(): void {
        select(this.svg.current).selectAll("g").remove();
    }

    protected updateChart(): void {
        this.updateScaleAndAxis();
        this.updateSeries();
    }

    private updateScaleAndAxis = () => {
        const { showNthTicks } = this.props.definition;
        const flattenedData = flatMap(this.series, s => this.props.data[s]);
        const sortedDates = flattenedData
            .map(p => p.date)
            .sort((a, b) => a.getTime() - b.getTime());

        //pad x-scale
        sortedDates.unshift(getMoment(sortedDates[0]).subtract(1, "day").toDate());
        sortedDates.push(
            getMoment(sortedDates[sortedDates.length - 1])
                .add(1, "day")
                .toDate()
        );

        const sortedValues = extent(flattenedData.map(p => p.value!));

        this.xScale
            .domain(sortedDates)
            .range([
                config.margin.left + (this.props.definition.yAxisOnLeft ? config.yAxisMargin : 0),
                this.props.width -
                    config.margin.right -
                    (this.props.definition.yAxisOnLeft ? 0 : config.yAxisMargin)
            ]);

        this.xAxis
            .tickValues(this.xScale.domain().filter((_, i) => !(i % (showNthTicks || 1))))
            .tickFormat((d: Date) =>
                d.toLocaleDateString("en-GB", {
                    day: "2-digit",
                    month: "short"
                })
            );

        this.xAxisSelection.transition().call(this.xAxis as any);

        this.yScale
            .domain([
                Math.min(0, sortedValues[0]!),
                Math.max(0, sortedValues[sortedValues.length - 1]!)
            ])
            .range([this.props.height - config.margin.bottom, config.margin.top])
            .nice();

        this.yAxisSelection.transition().call(this.yAxis as any);

        this.yAxisGridlines.selectAll("line").remove();

        this.yScale.ticks().forEach((t, i) => {
            const yPosition = this.yScale(t);
            const line = this.yAxisGridlines
                .append("line")
                .attr(
                    "x1",
                    config.margin.left +
                        (this.props.definition.yAxisOnLeft ? config.yAxisMargin : 0)
                )
                .attr("y1", yPosition)
                .attr(
                    "x2",
                    this.props.width -
                        config.margin.right -
                        (this.props.definition.yAxisOnLeft ? 0 : config.yAxisMargin)
                )
                .attr("y2", yPosition);

            if (t === 0) {
                line.attr("class", styles.baseline);
            }
        });
    };

    private updateSeries = () => {
        const dataMap: { [key: string]: DateData } = {};
        if (this.series.length === 0) return;
        for (const seriesName of this.series) {
            const series = this.props.data[seriesName];
            for (const point of series) {
                const rawDate = point.date.getTime();
                if (dataMap[rawDate] === undefined) {
                    dataMap[rawDate] = {
                        date: point.date,
                        values: []
                    };
                }

                dataMap[rawDate].values.push(point.value);
            }
        }

        const chartData = Object.keys(dataMap).map(k => dataMap[k]);

        const bandWidth = this.xScale.bandwidth();
        const barPadding = bandWidth < 6 ? 0 : config.barPadding;
        const barWidth = Math.min((bandWidth - barPadding * 2) / this.series.length, 35);

        this.chartData.selectAll("g").remove();

        const datePoints = this.chartData.selectAll("g").data(chartData);

        const datePoint = datePoints
            .enter()
            .append("g")
            .attr("class", styles.datePoint)
            .on("mouseover", this.showTooltip)
            .on("mouseout", this.hideTooltip);

        for (let i = 0; i < this.series.length; i++) {
            const baseLineY = this.yScale(0);
            datePoint
                .append("path")
                .attr("d", d => {
                    const value = d.values[i];
                    if (!value) {
                        return "";
                    }

                    const dateX = this.xScale(d.date)!;
                    const seriesOffset = barWidth * i;
                    const startX = dateX + config.barPadding + seriesOffset;
                    const startY = value < 0 ? baseLineY : this.yScale(value);

                    const height =
                        value < 0 ? this.yScale(value) - baseLineY : baseLineY - this.yScale(value);

                    if (value < 0) {
                        return bottomRoundedRect(startX, startY, barWidth, height, 0);
                    }

                    return topRoundedRect(startX, startY, barWidth, height, 0);
                })
                .style("fill", _ => {
                    const seriesName = this.series[i];
                    const series = this.props.definition.series[seriesName];
                    return series.fillGradient
                        ? `url(#${series.fillGradient.id})`
                        : series.fillColor!;
                });
        }

        datePoints.exit().remove();
    };

    private showTooltip = (event, data: DateData) => {
        console.log({ event, data });
        this.setState({
            tooltip: {
                eventX: event.offsetX + 15,
                eventY: event.offsetY - 15,
                data
            }
        });
    };

    private hideTooltip = () => {
        this.setState({
            tooltip: {
                ...this.state.tooltip,
                data: undefined
            }
        });
    };

    private drawSeriesKey = (
        series: MultiBarChartSeriesDefinition,
        height: number,
        width: number
    ) => {
        return (
            <svg
                width={width}
                height={height}
            >
                {series.fillGradient && (
                    <linearGradient
                        id={series.fillGradient.id}
                        gradientUnits="objectBoundingBox"
                        x1={series.fillGradient.options.x1}
                        y1={series.fillGradient.options.y1}
                        x2={series.fillGradient.options.x2}
                        y2={series.fillGradient.options.y2}
                    >
                        {series.fillGradient.stops.map(stop => (
                            <stop
                                key={stop.offset + stop.color}
                                offset={stop.offset}
                                stopColor={stop.color}
                            ></stop>
                        ))}
                    </linearGradient>
                )}
                <line
                    x1="0%"
                    y1="50%"
                    x2="100%"
                    y2="50.00001%"
                    stroke={
                        series.fillGradient ? `url(#${series.fillGradient.id})` : series.fillColor
                    }
                />
            </svg>
        );
    };

    render() {
        const { tooltip } = this.state;
        return (
            <div className={styles.multiBarChartContainer}>
                <div className={styles.legend}>
                    {this.props.definition.showLegend &&
                        this.series &&
                        this.series.map(seriesName => {
                            const series = this.props.definition.series[seriesName];
                            return (
                                <div
                                    key={series.label}
                                    className={styles.legendItem}
                                >
                                    <span>{series.label}</span>
                                    {this.drawSeriesKey(series, 4, 40)}
                                </div>
                            );
                        })}
                </div>
                <div
                    className={styles.tooltip}
                    style={{
                        opacity: tooltip.data ? 0.9 : 0,
                        top: tooltip.eventY,
                        left: tooltip.eventX
                    }}
                >
                    {this.series &&
                        this.series.map((seriesName, i) => {
                            const series = this.props.definition.series[seriesName];
                            return (
                                <div
                                    key={series.label}
                                    className={styles.tooltipItem}
                                    style={{ strokeWidth: config.tooltipKeySize }}
                                >
                                    {this.drawSeriesKey(
                                        series,
                                        config.tooltipKeySize,
                                        config.tooltipKeySize
                                    )}
                                    {tooltip.data && (
                                        <span className={styles.tooltipItemValue}>
                                            {this.props.definition.tooltipFormatter
                                                ? this.props.definition.tooltipFormatter(
                                                      tooltip.data.values[i]!
                                                  )
                                                : tooltip.data.values[i]}
                                        </span>
                                    )}
                                </div>
                            );
                        })}
                </div>
                {super.render()}
            </div>
        );
    }
}
