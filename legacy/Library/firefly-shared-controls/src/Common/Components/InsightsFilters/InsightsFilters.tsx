import React, { useRef } from "react";
import { useElementSize, DatePresetPicker, DatePicker, css, useInsightFilters } from "../../..";
import { EntitySelect } from "./EntitySelect";

import "react-datepicker/dist/react-datepicker.css";
import * as styles from "./Filters.scss";
import { getMoment } from "Common/Utilities";

type Props = {
    hideDatePicker?: boolean;
    includeToday?: boolean;
};

export const InsightsFilters = ({ hideDatePicker, includeToday = false }: Props) => {
    const {fromDate, toDate, updateDateRange } = useInsightFilters();
    const containerRef = useRef<HTMLDivElement>(null);
    const [width] = useElementSize(containerRef);
    const isCompact = width < 1252;

    const onFromDateChange = (newFromDate: Date) => {
        updateDateRange(undefined, newFromDate, undefined);
    };

    const onToDateChange = (newToDate: Date) => {
        updateDateRange(undefined, undefined, newToDate);
    };

    const minDate = new Date("2018-06-01");
    const maxDate = new Date();

    return (
        <div className={styles.filtersContainer} ref={containerRef}>
            <div className={css(styles.filters, styles.filtersFundSelect, { [styles.compact]: isCompact })}>
                <EntitySelect />
                {
                    !hideDatePicker && 
                    <>
                        <DatePicker
                            selected={fromDate}
                            onChange={onFromDateChange}
                            showMonthYearDropdown
                            minDate={minDate}
                            maxDate={maxDate}
                            label="FROM"

                        />
                        <DatePicker
                            selected={toDate}
                            onChange={onToDateChange}
                            minDate={minDate}
                            maxDate={maxDate}
                            label="TO"
                        />
                        <DatePresetPicker isCompact={isCompact} />
                    </>
                }
            </div>
        </div>
    );
};