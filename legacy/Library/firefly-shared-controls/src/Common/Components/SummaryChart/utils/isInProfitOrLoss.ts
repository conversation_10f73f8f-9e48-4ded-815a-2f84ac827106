import { LongShort, ProfitLoss } from "../models";

const isGreaterEqualTo = (value, level) => value >= level;
const isLessEqualTo = (value, level) => value <= level;

export type IsInProfitOrLoss = {
    ptLevelMark: ProfitLoss;
    slLevelMark: ProfitLoss;
};

export const isInProfitOrInLoss = (
    value,
    ptLevel,
    slLevel,
    longShort: LongShort
): IsInProfitOrLoss => {
    switch (longShort) {
        case LongShort.long:
            return {
                ptLevelMark: isGreaterEqualTo(value, ptLevel) ? ProfitLoss.profit : ProfitLoss.none,
                slLevelMark: isLessEqualTo(value, slLevel) ? ProfitLoss.loss : ProfitLoss.none
            };

        case LongShort.short:
            return {
                ptLevelMark: isLessEqualTo(value, ptLevel) ? ProfitLoss.profit : ProfitLoss.none,
                slLevelMark: isGreaterEqualTo(value, slLevel) ? ProfitLoss.loss : ProfitLoss.none
            };

        default:
            return {
                ptLevelMark: ProfitLoss.none,
                slLevelMark: ProfitLoss.none
            };
    }
};
