import React from "react";
import { scaleLinear, min, max, format } from "d3";
import { css } from "../..";
import { DataPoint } from "./utils";

import styles from "./styles.scss";

export const ExposureLowHighLast = ({
  curve,
  height,
  left,
  bottom,
  presenter = (val) => format(".2f")(val),

  useCurrent = true,
  showHighLow = true,
  currentToScale = false,
}: {
  curve: DataPoint[] | any;
  height: number;
  left: number;
  bottom: number;
  presenter?: Function;
  useCurrent?: boolean;
  showHighLow?: boolean;
  currentToScale?: boolean;
}) => {
  if (!curve || !curve.length) return <></>;

  const high = (max(curve.map((c) => c.value)) as unknown) as number;
  const low = (min(curve.map((c) => c.value)) as unknown) as number;

  const last = curve[curve.length - 1].value;

  const scale = scaleLinear()
    .domain([low, high])
    .range([0, height]);

  const lastY = scale(last) ?? 0;

  return (
    <g className={css(styles.backLabel)}>
      {showHighLow && (
        <text
          y={bottom - 2}
          alignmentBaseline="text-after-edge"
          textAnchor="start"
          x={left + 5}
        >
          {presenter(low)}
        </text>
      )}

      {showHighLow && (
        <text
          y={bottom - height}
          alignmentBaseline="middle"
          textAnchor="start"
          x={left + 5}
        >
          {presenter(high)}
        </text>
      )}
      {/*Add a background coloured aura to maintain visibility if close to other labels*/}
      {currentToScale && (
        <text
          y={bottom - lastY}
          alignmentBaseline="middle"
          textAnchor="start"
          style={{ stroke: "#0c1117", strokeWidth: "0.7em" }}
          x={left + 5}
        >
          {presenter(last)}
        </text>
      )}
      {useCurrent && (
        <text
          y={currentToScale ? bottom - lastY : bottom - height / 2}
          alignmentBaseline="middle"
          textAnchor="start"
          style={{ fill: "white" }}
          x={left + 5}
        >
          {presenter(last)}
        </text>
      )}
    </g>
  );
};
