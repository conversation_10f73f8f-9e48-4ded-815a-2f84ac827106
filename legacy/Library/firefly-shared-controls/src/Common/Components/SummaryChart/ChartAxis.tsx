import React from "react";
import { css } from "../..";
import styles from "./styles.scss";

export type ChartAxisProps = {
    x: number;
    y: number;
    width: number;
    label: React.ReactNode;
    className?: string;
    textPosition?: number;
    color?: string;
};

export const ChartAxis = ({
    x,
    y,
    width,
    label,
    className,
    textPosition,
    color = "#FFF",
}: ChartAxisProps) => {
    const textY = textPosition ?? -4;
    return (
        <g
            id="chart-axis"
            className={css(styles.sectionAxis, className)}
        >
            <text
                y={y + textY}
                x={x + 5}
                style={{ fill: color }}
            >
                {label}
            </text>
            <line
                x1={x}
                y1={y}
                x2={width}
                y2={y}
            />
        </g>
    );
};
