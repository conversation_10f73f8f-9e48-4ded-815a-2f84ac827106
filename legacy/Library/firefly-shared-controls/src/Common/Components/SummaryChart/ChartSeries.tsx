import React, { useRef, useEffect, memo } from "react";
import { select, line, area, ScaleLinear } from "d3";
import { DataPoint, CandleStickDataPoint } from "./utils";

import styles from "./styles.scss";

export type ChartSeriesProps = {
    id?: string;
    data: DataPoint[] & CandleStickDataPoint[];
    dateScale: ScaleLinear<number, number>;
    yscale: ScaleLinear<number, number>;
    yOffset: number;
    color: string;
    drawShadow?: boolean;
    strokeDasharray?: string;
};

const ChartSeriesRaw = ({
    id,
    data,
    dateScale,
    yscale,
    yOffset,
    color,
    drawShadow = false,
    strokeDasharray = ""
}: ChartSeriesProps) => {
    const series = useRef<SVGGElement>(null);
    const gradientId = `gradient${color}`;

    const makeGradient = () => {
        if (!series.current) return;

        select(series.current).select("linearGradient").remove();

        const gradient = select(series.current)
            .append("linearGradient")
            .attr("id", gradientId)
            .attr("x1", 0)
            .attr("x2", 0)
            .attr("y1", 0)
            .attr("y2", "100%");

        gradient
            .append("svg:stop")
            .attr("offset", "0%")
            .attr("stop-color", color)
            .attr("stop-opacity", 0.2);

        gradient.append("svg:stop").attr("offset", "100%").attr("stop-opacity", 0.2);
    };

    const lineFn = line<DataPoint>()
        .defined(x => x.value && x.value !== 0)
        .x((_, i) => dateScale!(i))
        .y(d => yscale!(d.value));

    const [y1, y2] = yscale.range();
    const height = y2 - y1;

    const areaFn = area<DataPoint>()
        .defined(x => x.value && x.value !== 0)
        .x((_, i) => dateScale(i))
        .y0(height)
        .y1(d => yscale(d.value));

    const updateSeries = () => {
        if (!data || !data.length) return;

        select(series.current!)
            .select("#line1")
            .attr("transform", `translate(0, ${yOffset})`)
            .attr("d", lineFn(data));

        if (!drawShadow) return;

        select(series.current)
            .select("#bar")
            .attr("fill", `url(#${gradientId})`)
            .attr("transform", `translate(0, ${yOffset})`)
            .attr("d", areaFn(data));
    };

    useEffect(() => {
        if (!series.current) {
            return;
        }
        makeGradient();
        updateSeries();
    }, [data]);

    return (
        <g
            ref={series}
            id={id}
        >
            <path
                id="line1"
                className={styles.chartLine}
                stroke={color}
                strokeDasharray={strokeDasharray}
            />
            <path
                id="bar"
                className={styles.areas}
            />
        </g>
    );
};

export const ChartSeries = memo(ChartSeriesRaw);
