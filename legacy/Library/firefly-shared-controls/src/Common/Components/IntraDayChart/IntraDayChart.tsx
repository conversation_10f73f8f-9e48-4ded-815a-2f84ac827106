import React, { useRef, useState, useMemo, useCallback, useEffect } from "react";

import {
    IntraDayHover,
    IntraDayCrossHair,
    DayDelimiters,
    IntradayLegend,
    Settings,
    CustomSettings,
    VLine,
    DateAxis,
    ZoomHandler,
    ZoomOverlay,
    ChartType,
    PriceView,
    HoverType,
} from "./ChartComponents/";
import {
    ChartSectionData,
    ChartSectionDataLookup,
    IntraDayChartConfig,
    IntraDayChartXScale,
    IntraDayDataPoint,
    IntraDayPNLDataPoint,
} from "./types";
import {
    computeXScale,
    DateRange,
    MultiDayDateRange,
    openCloseLookup,
    stripEquity,
    todayDateRange,
    addChange,
    getDateRangeForDay,
} from "./util";
import { useStickyState, useFilterVisibleData } from "./IntraDayChartHooks";
import { StyledContainer, StyledSvg } from "./IntraDayChart.styled";
import { PercentagesLines } from "./ChartComponents/PercentagesLines";
import { PricesLines } from "./ChartComponents/PricesLines";
import { VolumeBars } from "./ChartComponents/VolumeBars";
import { INTRADAY_PNL_FILL, MARKET_FILL, PRICE_FILL, SECTOR_FILL } from "./constants";

const defaultConfig: IntraDayChartConfig = {
    padding: {
        top: 30,
        bottom: 25,
        left: 75,
        right: 50,
    },
    axisPadding: 20,
};

type UseZoomDateRangeProps = {
    zoomSelectionOnly?: boolean;
    onZoomRangeChanged?: (range: DateRange | null) => void;
    zoomSelectionDateRangeProp?: DateRange | null;
};

export const useZoomDateRange = ({
    zoomSelectionOnly,
    onZoomRangeChanged,
    zoomSelectionDateRangeProp,
}: UseZoomDateRangeProps) => {
    const [zoomDateRange, setZoomDateRangeInternal] = useState<DateRange | null>(null);
    const [zoomSelectionDateRange, setZoomSelectionDateRangeInternal] = useState<DateRange | null>(
        zoomSelectionDateRangeProp || null,
    );

    const setZoomDateRange = useCallback(
        (dateRange: DateRange | null) => {
            if (onZoomRangeChanged) {
                onZoomRangeChanged(dateRange);
            }
            if (zoomSelectionOnly) {
                setZoomSelectionDateRangeInternal(dateRange);
            } else {
                setZoomDateRangeInternal(dateRange);
            }
        },
        [onZoomRangeChanged, zoomSelectionOnly],
    );

    useEffect(() => {
        setZoomSelectionDateRangeInternal(zoomSelectionDateRangeProp || null);
    }, [zoomSelectionDateRangeProp]);

    return {
        zoomDateRange,
        zoomSelectionDateRange,
        setZoomDateRange,
    };
};

export type ItemsMap = {
    sector?: {
        text: string;
        color: string;
        data: IntraDayDataPoint[];
    };
    market?: {
        text: string;
        color: string;
        data: IntraDayDataPoint[];
    };
    intraday?: {
        text: string;
        color: string;
        data: IntraDayPNLDataPoint[];
    };
    price: {
        text: string;
        color: string;
        data: IntraDayDataPoint[];
    };
};

export interface IntraDayChartProps {
    // tickers
    instrumentId: string;
    intradayPNLId?: string;
    marketId?: string;
    sectorId?: string;
    specificDate?: Date,
    // series
    data: IntraDayDataPoint[];
    intradayPNL?: IntraDayPNLDataPoint[];
    market: IntraDayDataPoint[];
    sector: IntraDayDataPoint[];

    // flags
    showHover: boolean;
    showIntradayPNL?: boolean;
    showMarket?: boolean;
    showSector?: boolean;
    showSettings?: boolean;

    // zoom
    onZoomRangeChanged?: (range: DateRange | null) => void;
    zoomSelectionDateRange?: DateRange;
    additionalSelectionDateRangesFill?: string,
    additionalSelectionDateRanges?: DateRange[];
    zoomSelectionOnly?: boolean;

    // misc
    chartType?: ChartType; // If showSettings is false, this value determines the chart type rendered
    barMinutes: 1 | 3 | 5 | 15 | 30;
    className?: string;
    config?: IntraDayChartConfig;
    gip: 1 | 3 | 5;
    height: number;
    sectionCount?: number;
    width: number;
}
export const IntraDayChart = ({
    // tickers
    instrumentId,
    intradayPNLId: intradayPNLIdProp,
    marketId: marketIdProp,
    sectorId: sectorIdProp,

    // series
    data: dataProp = [],
    intradayPNL: intradayPNLProp = [],
    market: marketProp = [],
    sector: sectorProp = [],

    // flags
    showHover,
    showIntradayPNL,
    additionalSelectionDateRanges,
    additionalSelectionDateRangesFill,
    showMarket,
    showSector,
    showSettings = true,
    specificDate,
    // zoom
    onZoomRangeChanged,
    zoomSelectionDateRange: zoomSelectionDateRangeProp,
    zoomSelectionOnly,

    // misc
    chartType: chartTypeProp = "price",
    barMinutes = 5,
    className,
    config: chartConfig,
    gip = 3,
    height,
    sectionCount = 4,
    width,
}: IntraDayChartProps) => {
    const baseConfig = chartConfig || defaultConfig;
    const config = {
        ...baseConfig,
        padding: {
            top: baseConfig?.padding?.top ?? 0,
            bottom: baseConfig?.padding?.bottom ?? 0,
            left: baseConfig?.padding?.left ?? 0,
            right: baseConfig?.padding?.right ?? 0,
        },
    };

    const effectiveHeight = height - (config.padding.top + config.padding.bottom);
    const effectiveWidth = width - (config.padding.left + config.padding.right);
    const sectionRatio = 1 / sectionCount;

    const [chartTypeRaw, setChartType] = useStickyState<ChartType>("price", "chartType");
    const chartType = showSettings ? chartTypeRaw : chartTypeProp;

    const isChartTypePercentage = chartType === "percentage";
    const [priceView, setPriceView] = useStickyState<PriceView>("ohlc", "priceView");
    const [hoverType, setHoverType] = useStickyState<HoverType>("crosshair", "hoverType");

    const { zoomDateRange, zoomSelectionDateRange, setZoomDateRange } = useZoomDateRange({
        zoomSelectionOnly,
        onZoomRangeChanged,
        zoomSelectionDateRangeProp,
    });

    const openClose = openCloseLookup(instrumentId);
    const dateRange =  useMemo(
        () => (specificDate ? getDateRangeForDay(specificDate, openClose) :  gip === 1 ? todayDateRange(openClose) : MultiDayDateRange(openClose, gip)),
        [gip, openClose,specificDate],
    );
    const { xScale, xScaleUnclamped, days, dayWidths }: IntraDayChartXScale = useMemo(
        () => computeXScale(zoomDateRange || dateRange, openClose, effectiveWidth),
        [dateRange, zoomDateRange, openClose, effectiveWidth],
    );

    const svgRef = useRef(null);
    const [hoverHidden, setHoverHidden] = useState(false);

    const [sectionData, setSectionData] = useState<ChartSectionDataLookup>({});
    const updateSection = useCallback(
        (sectionId: string, newSection: Partial<ChartSectionData>) => {
            setSectionData(prev => ({
                ...prev,
                [sectionId]: {
                    ...((prev[sectionId] || {}) as ChartSectionData),
                    ...newSection,
                },
            }));
        },
        [],
    );

    const dataVisible = useFilterVisibleData({ data: dataProp, xScale, xScaleUnclamped });
    const sectorVisible = useFilterVisibleData({ data: sectorProp, xScale, xScaleUnclamped });
    const marketVisible = useFilterVisibleData({ data: marketProp, xScale, xScaleUnclamped });
    const intradayPNLVisible = useFilterVisibleData({
        data: intradayPNLProp,
        xScale,
        xScaleUnclamped,
    });

    const data = useMemo(() => addChange(dataVisible), [dataVisible]);
    const sector = useMemo(() => addChange(sectorVisible), [sectorVisible]);
    const market = useMemo(() => addChange(marketVisible), [marketVisible]);
    const intradayPNL = useMemo(() => addChange(intradayPNLVisible), [intradayPNLVisible]);

    const sectorId = sectorIdProp ?? sector?.[0]?.identifier ?? "";
    const marketId = marketIdProp ?? market?.[0]?.identifier ?? "";
    const intradayPNLId = intradayPNLIdProp ?? "Intraday PNL";

    const itemsMap = useMemo(
        () => ({
            ...(showSector
                ? {
                      sector: {
                          text: `${stripEquity(sectorId) || ""} (LHS)`,
                          color: SECTOR_FILL,
                          data: sector,
                      },
                  }
                : undefined),
            ...(showMarket
                ? {
                      market: {
                          text: `${stripEquity(marketId) || ""} (LHS)`,
                          color: MARKET_FILL,
                          data: market,
                      },
                  }
                : undefined),
            ...(!isChartTypePercentage && showIntradayPNL
                ? {
                      intraday: {
                          text: intradayPNLId,
                          color: INTRADAY_PNL_FILL,
                          data: intradayPNL,
                      },
                  }
                : undefined),
            price: {
                text: `${stripEquity(instrumentId) || ""} (RHS)`,
                color: PRICE_FILL,
                data: data,
            },
        }),
        [
            instrumentId,
            sectorId,
            marketId,
            intradayPNLId,
            showSector,
            showMarket,
            showIntradayPNL,
            isChartTypePercentage,
            data,
            sector,
            market,
            intradayPNL,
        ],
    );

    return (
        <>
            <StyledContainer className={className}>
                {showSettings && (
                    <Settings
                        svgRef={svgRef}
                        onSettingsOpen={() => setHoverHidden(true)}
                        onSettingsClose={() => setHoverHidden(false)}
                    >
                        <CustomSettings
                            priceView={priceView}
                            setPriceView={setPriceView}
                            hoverType={hoverType}
                            setHoverType={setHoverType}
                            chartType={chartType}
                            setChartType={setChartType}
                        />
                    </Settings>
                )}
                <StyledSvg
                    ref={svgRef}
                    width={width}
                    height={height}
                >
                    <IntradayLegend
                        config={config}
                        items={Object.values(itemsMap)}
                        x={width / 2}
                    />
                    <g
                        id="offsetContainer"
                        transform={`translate(${config.padding.left})`}
                    >
                        <VLine
                            config={config}
                            effectiveHeight={effectiveHeight}
                            offset={0}
                        />
                        <VLine
                            config={config}
                            effectiveHeight={effectiveHeight}
                            offset={effectiveWidth}
                        />
                        <DateAxis
                            xScale={xScale}
                            effectiveHeight={effectiveHeight}
                            config={config}
                            openClose={openClose}
                            days={days}
                        />
                        <DayDelimiters
                            days={days}
                            dayWidths={dayWidths}
                            effectiveHeight={effectiveHeight}
                            config={config}
                        />

                        {isChartTypePercentage ? (
                            <PercentagesLines
                                config={config}
                                data={data}
                                effectiveHeight={effectiveHeight}
                                effectiveWidth={effectiveWidth}
                                items={Object.values(itemsMap)}
                                market={market}
                                sectionData={sectionData}
                                sectionRatio={sectionRatio}
                                sector={sector}
                                showMousePos={hoverType !== "standard"}
                                svgRef={svgRef}
                                updateSection={updateSection}
                                xScale={xScale}
                                spanSection={3}
                            />
                        ) : (
                            <PricesLines
                                barMinutes={barMinutes}
                                config={config}
                                data={data}
                                effectiveHeight={effectiveHeight}
                                effectiveWidth={effectiveWidth}
                                intradayPNL={intradayPNL}
                                market={market}
                                priceView={priceView}
                                sectionData={sectionData}
                                sectionRatio={sectionRatio}
                                sector={sector}
                                showIntradayPNL={showIntradayPNL}
                                showMarket={showMarket}
                                showMousePos={hoverType !== "standard"}
                                showSector={showSector}
                                svgRef={svgRef}
                                updateSection={updateSection}
                                xScale={xScale}
                                xScaleUnclamped={xScaleUnclamped}
                            />
                        )}

                        <VolumeBars
                            barMinutes={barMinutes}
                            config={config}
                            data={data}
                            effectiveHeight={effectiveHeight}
                            effectiveWidth={effectiveWidth}
                            sectionData={sectionData}
                            sectionRatio={sectionRatio}
                            showMousePos={hoverType !== "standard"}
                            svgRef={svgRef}
                            updateSection={updateSection}
                            xScale={xScale}
                            xScaleUnclamped={xScaleUnclamped}
                        />

                        {showHover && !hoverHidden && (
                            <>
                                {["crosshair", "both"].includes(hoverType) && (
                                    <IntraDayCrossHair
                                        config={config}
                                        dataIntervalMinutes={barMinutes}
                                        effectiveHeight={effectiveHeight}
                                        effectiveWidth={effectiveWidth}
                                        sectionData={sectionData}
                                        svgRef={svgRef}
                                        xScale={xScale}
                                        relevantSectionId={
                                            isChartTypePercentage ? "change" : undefined
                                        }
                                    />
                                )}
                                {["standard", "both"].includes(hoverType) && (
                                    <IntraDayHover
                                        config={config}
                                        dataIntervalMinutes={barMinutes}
                                        effectiveHeight={effectiveHeight}
                                        effectiveWidth={effectiveWidth}
                                        itemsMap={itemsMap}
                                        svgRef={svgRef}
                                        xScale={xScale}
                                        hasVerticalLine={hoverType === "standard"}
                                    />
                                )}
                            </>
                        )}

                        <ZoomHandler
                            svgRef={svgRef}
                            xScale={xScale}
                            openClose={openClose}
                            effectiveWidth={effectiveWidth}
                            effectiveHeight={effectiveHeight}
                            updateDateRange={setZoomDateRange}
                            config={config}
                        />

                        <ZoomOverlay
                            dateRange={zoomSelectionDateRange}
                            xScale={xScale}
                            effectiveHeight={effectiveHeight}
                            config={config}
                        />

                        
{
                            additionalSelectionDateRanges == null ? null : additionalSelectionDateRanges.map(adRange =>  <ZoomOverlay
                                dateRange={adRange}
                                xScale={xScale}
                                fill={additionalSelectionDateRangesFill}
                                effectiveHeight={effectiveHeight}
                                config={config}
                            /> )
                        }
                    </g>
                </StyledSvg>
            </StyledContainer>
        </>
    );
};
