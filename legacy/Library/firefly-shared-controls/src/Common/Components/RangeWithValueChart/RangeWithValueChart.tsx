import { select, AxisDomain, Axis, ScaleLinear, ScaleBand, scaleBand, axisLeft, scaleLinear, axisBottom } from "d3";
import React from "react";
import { BaseChartProps, BaseChart, BaseChartConfig, RangeWithValueChartDefinition, RangeWithValueChartRow } from "..";
import { BaseSelection, getShorthandValue } from "../../Utilities";
import * as styles from "./Styles.scss";

type Props = BaseChartProps & {
    definition: RangeWithValueChartDefinition;
    data: RangeWithValueChartRow[] | null;
    config?: Config;
};

type State = {};

type Config = BaseChartConfig & {
    rowHeight: number;
    rowPadding: number;
    valuePadding: number;
};

const config: Config = {
    margin: {
        top: 10,
        left: 120,
        bottom: 50,
        right: 15
    },
    rowHeight: 26,
    rowPadding: 8,
    valuePadding: 4,
};

const initialState = {};

export class RangeWithValueChart extends BaseChart<Props, State> {
    state = initialState;

    xScale: ScaleLinear<number, number>;
    xAxis: Axis<AxisDomain>;
    xAxisSelection: BaseSelection;
    xAxisLabel: BaseSelection;
    xAxisGridlines: BaseSelection;

    yScale: ScaleBand<string>;
    yAxis: Axis<AxisDomain>;
    yAxisSelection: BaseSelection;
    yAxisGridlines: BaseSelection;

    rowsSelection: BaseSelection;

    constructor(props: Props) {
        super(props, {
            ...config,
            ...props.config || {}
        });
    }

    componentDidUpdate(prevProps: Props) {
        
        super.componentDidUpdate(prevProps);
        if (prevProps.definition !== this.props.definition || prevProps.data !== this.props.data) {
            this.disposeChart();
            this.initChart();
            this.updateChart();
        }
    }

    protected initChart(): void {
        const svg = select(this.svg.current);

        // X
        this.xScale = scaleLinear();

        this.xAxis = axisBottom(this.xScale)
            .tickFormat((x: number) => x === 0 ? "0" : getShorthandValue(x));

        this.xAxisSelection = svg.append("g")
            .attr("class", styles.axis);

        this.xAxisLabel = svg.append("g")
            .attr("class", styles.axisLabel);

        this.xAxisGridlines = svg.append("g")
            .attr("class", `${styles.gridLines}`);

        // Y
        this.yScale = scaleBand<string>();

        this.yAxis = axisLeft(this.yScale)
            .tickPadding(10)
            .tickSizeInner(0)
            .tickSizeOuter(0);

        this.yAxisSelection = svg.append("g")
            .attr("class", styles.axis)
            .attr("transform", `translate(${this.config.margin.left}, 0)`);

        this.yAxisGridlines = svg.append("g")
            .attr("class", `${styles.gridLines}`);

        // Rows
        this.rowsSelection = svg.append("g");
    }

    protected updateChart(): void {
        this.updateScaleAndAxis();
        this.updateRows();
    }

    private updateScaleAndAxis = () => {
        const { data, definition } = this.props;
        if (!data || !data.length) { return; }

        const height = data.length * (this.config as any).rowHeight + this.config.margin.top + this.config.margin.bottom;

        // X
        const maxAbsolute = Math.max(...data.map(x => Math.abs(x.min)), ...data.map(x => Math.abs(x.max)));

        this.xScale
            .domain([-maxAbsolute, maxAbsolute])
            .range([this.config.margin.left, this.props.width - this.config.margin.right])
            .nice();

        this.xAxis
            .ticks(10)
            .tickPadding(10)
            .tickSizeInner(0)
            .tickSizeOuter(0);

        if (definition.xAxisFormatter) {
            this.xAxis.tickFormat(definition.xAxisFormatter);
        }

        this.xAxisSelection
            .attr("transform", `translate(0, ${height - this.config.margin.bottom})`)
            .transition()
            .call(this.xAxis as any);

        this.xAxisLabel
            .selectAll("text")
            .remove();

        if (this.props.definition.xAxisLabel !== undefined) {
            this.xAxisLabel
                .append("text")
                .attr("transform", `translate(${this.config.margin.left + this.chartWidth * 0.48}, ${height - this.config.margin.bottom * 0.15})`)
                .text(this.props.definition.xAxisLabel);
        }

        this.xAxisGridlines
            .selectAll("line")
            .remove();

        this.xScale.ticks().forEach((t, i) => {
            if (i === 0) {
                return;
            }

            const xPosition = this.xScale(t);
            const line = this.xAxisGridlines.append("line")
                .attr("x1", xPosition)
                .attr("y1", this.config.margin.top)
                .attr("x2", xPosition)
                .attr("y2", height - this.config.margin.bottom);

            if (t !== 0) {
                line.attr("stroke-dasharray", "3 3");
            }
        });

        // Y
        this.yScale
            .domain(data.map(x => x.label))
            .range([this.config.margin.top, height - this.config.margin.bottom]);

        this.yAxisSelection
            .transition()
            .call(this.yAxis as any);

        this.yAxisGridlines
            .selectAll("line")
            .remove();

        data.forEach(row => {
            const yPosition = this.yScale(row.label)!;
            this.yAxisGridlines.append("line")
                .attr("x1", this.config.margin.left)
                .attr("y1", yPosition)
                .attr("x2", this.props.width - this.config.margin.right)
                .attr("y2", yPosition);
        });
    }

    private updateRows = () => {
        if (!this.props.data || !this.props.data.length) { return; }

        const rowSelections = this.rowsSelection
            .selectAll("g")
            .data(this.props.data);

        const rowSelectionEnter = rowSelections
            .enter()
            .append("g");

        rowSelectionEnter
            .append("rect");

        rowSelectionEnter
            .append("line");

        const onUpdate = rowSelections.merge(rowSelectionEnter)
            .transition();

        onUpdate
            .select("rect")
            .attr("x", d => this.xScale(d.min))
            .attr("y", d => this.yScale(d.label)! + (this.config as any).rowPadding)
            .attr("width", d => this.xScale(d.max) - this.xScale(d.min))
            .attr("height", (this.config as any).rowHeight - ((this.config as any).rowPadding * 2))
            .attr("fill", "url(#rangeWithValueChartGradient)");

        onUpdate
            .select("line")
            .attr("x1", d => this.xScale(d.value))
            .attr("y1", d => this.yScale(d.label)! + (this.config as any).valuePadding)
            .attr("x2", d => this.xScale(d.value))
            .attr("y2", d => this.yScale(d.label)! - (this.config as any).valuePadding + (this.config as any).rowHeight)
            .attr("class", styles.rangeValueLine);

        rowSelections
            .exit()
            .remove();
    }

    protected disposeChart(): void {
        select(this.svg.current)
            .selectAll("g")
            .remove();
    }

    render() {
        const { data } = this.props;
        if (!data) {
            return <></>;
        }

        const height = data.length * (this.config as any).rowHeight + this.config.margin.top + this.config.margin.bottom;
        return (
            <svg
                className={this.config.className}
                style={{ visibility: data.length ? "visible" : "hidden" }}
                width={this.props.fixedSize ? this.props.width : undefined}
                height={this.props.fixedSize ? height : undefined}
                viewBox={`0 0 ${this.props.width} ${height}`}
                ref={this.svg}>
                <linearGradient
                        id="rangeWithValueChartGradient"
                        gradientUnits="userSpaceOnUse"
                        x1={this.config.margin.left}
                        y1={0}
                        x2={this.containerWidth - this.config.margin.right}
                        y2={0}
                    >
                        <stop offset={0} stopColor="#8B2631"></stop>
                        <stop offset={0.5} stopColor="#8B2631"></stop>
                        <stop offset={0.5} stopColor="#0B9C0F"></stop>
                        <stop offset={1} stopColor="#0B9C0F"></stop>
                </linearGradient>
            </svg>
        );
    }
}
