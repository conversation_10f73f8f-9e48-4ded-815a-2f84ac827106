import { Lookback } from "../../Common/Models";

export type WinnerLoser = {
    Ticker: string;
    PL: number;
    Exposure: number;
    Return: number;
};

export type AttributionApiRequest = {
    fundGroupId?: number;
    fundId?: number;
    traderId?: number;
    lookback: Lookback;
};

export type InstrumentStatistics = {
    InstrumentClass: string;
    TotalPL: number;
    Return: number;
    AnnSharpe: number;
    AnnVolatility: number;
    AvgGrossExposure: number;
    AvgNetExposure: number;
    AvgNumberOfPositions: number;
    MaxDrawdown: number;
    MaxDollarLoss: number;
};

export type StrategyStatistics = {
    Strategy: string | null;
    SubStrategy: string | null;
    TotalPL: number;
    Return: number;
    AnnSharpe: number;
    AnnVolatility: number;
    AvgGrossExposure: number;
    AvgNetExposure: number;
    AvgNumberOfPositions: number;
    MaxDrawdown: number;
    MaxDollarLoss: number;
    HitRate: number;
};
