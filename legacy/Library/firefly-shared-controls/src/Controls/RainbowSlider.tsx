import styled from "styled-components";
import { Slider } from "antd";

export const RainbowSlider = styled(Slider).attrs(
    ({ style, value = 0, min = 0, max = 100 }: any) => ({
        style: {
            ...style,
            "--left": `${((100 * (value - min)) / (max - min)).toFixed(4)}%`
        }
    })
)`
    .ant-slider-track,
    .ant-slider-rail,
    .ant-slider-step {
        height: 8px;
        border-radius: 1000px;
    }

    .ant-slider-rail {
        background: black !important;
    }

    .ant-slider-step {
        display: none;
    }

    .ant-slider-track {
        left: 0 !important;
        width: 100% !important;
        background: linear-gradient(to right, #f50000, #f8c236, #00ff00);
        clip-path: inset(0 calc(100% - var(--left)) 0 0);
    }

    .ant-slider-handle {
        width: 20px;
        height: 20px;
        margin-top: -6px;
        margin-left: -10px;
        background: white;
        border: none;
    }

    .ant-slider-mark {
        top: 18px;
    }
`;

export default RainbowSlider;
