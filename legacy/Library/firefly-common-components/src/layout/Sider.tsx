import styled from "styled-components";
import { Layout } from "antd";
import { theme, keyframes } from "../theme";

const AntdSider = Layout.Sider;

const pulse = keyframes`
    from { background: #00FF00; }
    to { background: transparent; }
`;
export const Sider = styled(AntdSider)`
    && {
        background: inherit;
        overflow: hidden;

        .ant-layout-sider-zero-width-trigger {
            top: -25px;
        }
        .ant-tabs {
            height: calc(100vh - ${theme.layout.headerHeight});
        }
        &.ant-layout-sider-collapsed .ant-layout-sider-trigger i {
            transform: rotate(180deg);
        }
        .ant-layout-sider-trigger {
            animation: ${pulse} 1s;
            animation-iteration-count: 3;
            background: inherit;
            border-top: 2px solid ${theme.layout.backgroundColor};
            & i {
                transition: all 0.4s;
            }
        }
    }
`;
export default Sider;
