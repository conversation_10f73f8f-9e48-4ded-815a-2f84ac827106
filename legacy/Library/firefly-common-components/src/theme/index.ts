import * as styledComponents from "styled-components";

/** 
 * @depreciated use from styled-components 
 */
export const {default: styled, css, createGlobalStyle, keyframes, ThemeProvider,} = styledComponents as styledComponents.ThemedStyledComponentsModule<IThemeInterface>;

export interface IThemeInterface {
  layout: {
    backgroundColor: string,
    headerHeight: string,
    sidePanel: { collapsedWidth: string, width?: string, background: string, menuBackgroundSelected: string, menuBackgroundActive: string}
  };
  panel: {
    backgroundColor: string
  };
}

export const theme = {
  layout: {
    backgroundColor: "#0C1117",
    headerHeight: "64px",
    sidePanel: {
      background: "#24272D",
      collapsedWidth: "50",
      width: "188",
      menuBackgroundSelected: "#097DAD",
      menuBackgroundActive: "rgb(4,174,245, 0.11)"
    }
  },
  panel: {
    backgroundColor: "#1A1D24"
  }
};


export default styled;