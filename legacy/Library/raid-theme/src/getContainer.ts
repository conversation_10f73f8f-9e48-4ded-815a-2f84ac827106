/*
Ant Design puts modals and tooltips as direct descendants of <body> by default

In old raid, this means they aren't styled, as they aren't part of .with-antd

We add this container for the styling in old raid

We also have to unset the width and height, as they are set to 100% as part of .with-antd
*/

const container = document.createElement("div");
container.className = "with-antd raid-modal-container";
requestAnimationFrame(() => {
    // Not sure why we need to RAF here, but we get an exception if we don't
    document.body.appendChild(container);
});
export const getContainer = () => container;

const tooltipContainer = document.createElement("div");
tooltipContainer.className = "with-antd raid-tooltip-container";
requestAnimationFrame(() => {
    // Not sure why we need to RAF here, but we get an exception if we don't
    document.body.appendChild(tooltipContainer);
});

export const getTooltipContainer = () => tooltipContainer;
