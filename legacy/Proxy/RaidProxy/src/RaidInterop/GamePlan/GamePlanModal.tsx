declare const Tipped: any;
import React, { useCallback, useMemo, useEffect } from "react";
import { BaseStyles } from "Theming/index";
import { Modal } from "antd";
import { getContainer } from "@reddeer/raid-theme";
import { useGamePlan } from "GamePlan/index";
import { createGlobalStyle } from "styled-components";
import { GamePlanPanels } from "./GamePlanPanels";

const GameplanModalStyles = createGlobalStyle`&& .gameplan-posex-modal.gameplan-posex-modal {
    .ant-modal {
        top: 50px;
    }
    .ant-modal-content {
        background-color: initial;
        box-shadow: initial;
    }
    .ant-modal-body {
        display: flex;
        flex-direction: row;
        justify-content: center;
        gap: var(--spacing--x-large);
        /* vh less 50px top and 50px bottom */
        max-height: calc(100vh - 100px);
    }
}`;

type GamePlanModalProps = { isVisible: boolean; onClose: any; onCloseTradeRationale: any };

/** @deprecated */
export const GamePlanModal = ({
    isVisible,
    onClose,
    onCloseTradeRationale
}: GamePlanModalProps) => {
    const {
        gamePlanCommands,
        tradeRationaleCommands,
        resetGamePlanAndTradeRationale,
        gamePlanValidationComments,
        orderSummary,
        allowChecklist
    } = useGamePlan();
    const buttonText = "Close";
    const showTradeRationale = !!orderSummary;

    const handleClose = useCallback(() => {
        resetGamePlanAndTradeRationale();
        onClose();
    }, [gamePlanValidationComments]);
    const handleSubmit = useCallback(() => {
        resetGamePlanAndTradeRationale();
        onClose();
    }, [gamePlanCommands, tradeRationaleCommands, gamePlanValidationComments]);

    const width = useMemo(() => {
        return 600 + (showTradeRationale ? 600 : 0) + (allowChecklist ? 600 : 0);
    }, [showTradeRationale, allowChecklist]);

    useEffect(() => {
        if (isVisible && !!Tipped && Tipped?.hideAll) {
            Tipped.hideAll();
        }
    }, [isVisible]);

    return (
        <BaseStyles sandboxed={false}>
            <Modal
                width={width}
                closable={false}
                getContainer={getContainer}
                destroyOnClose={true}
                bodyStyle={{ padding: 4 }}
                style={{ backgroundColor: "initial" }}
                wrapClassName={"gameplan-posex-modal"}
                visible={isVisible}
                cancelButtonProps={{ style: { display: "none" } }}
                onCancel={handleClose}
                onOk={handleSubmit}
                okText={buttonText}
                maskClosable={false}
            >
                <GameplanModalStyles />
                {isVisible && (
                    <GamePlanPanels
                        showChecklist={allowChecklist}
                        showTradeRationale={showTradeRationale}
                        onClose={onClose}
                        onCloseTradeRationale={onCloseTradeRationale}
                    />
                )}
            </Modal>
        </BaseStyles>
    );
};
