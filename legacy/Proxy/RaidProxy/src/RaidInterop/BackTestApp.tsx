import React from "react";
import { BacktestProvider, BacktestAppContent } from "@reddeer/backtest-js-client/build/App";
// tslint:disable-next-line no-duplicate-imports
import * as widgets from "@reddeer/backtest-js-client/build/App";
import { TickerTooltipProvider } from "PrimitiveComponents/index";
import { useServerStore } from "TransportGateway/index";
import { CompanySummary } from "./CompanySummary";
import { RaidTickerContextMenu } from "./RaidTickerContextMenu";

type Props = {
    widget?: any;
    filters: any;
};

const getBacktestId = (filters: any): string | undefined => {
    if (!filters) {
        return;
    }
    const filter = filters.get("sBacktestID");
    const backtestId = filter !== undefined ? filter.get("value") : undefined;
    return backtestId;
};

export const BackTestApp = ({ widget, filters }: Props) => {
    const [backtestId, setBacktestId] = React.useState<string | undefined>(() => getBacktestId(filters));
    const viewServerStore = useServerStore();

    React.useEffect(() => {
        const handler = () => setBacktestId(getBacktestId(filters));
        handler();

        if(filters){
            filters.on("all", handler);
            return () => filters.off("all", handler);
        }
        return () => {};
    }, [filters]);

    const Component = (widgets as any)[widget] || BacktestAppContent;

    return backtestId !== undefined ? (
        <TickerTooltipProvider ToolTipComponent={CompanySummary} ContextMenuComponent={RaidTickerContextMenu}>
            <BacktestProvider store={viewServerStore}>
                <Component backtestId={backtestId} />
            </BacktestProvider>
        </TickerTooltipProvider>
    ) : (
        <p>No data</p>
    );
};
