import React, { useState } from "react";
import { useInsightFilters, InsightsFiltersProvider, InsightsFilters, LazyComponent, Lookback, TradeSnapshotModalContext } from "@reddeer/firefly-shared-controls";
import styled from "styled-components";
import { Modal } from "antd";

const modalMarginLeftRight = 40;
const modalMarginTopBottom = 20;

const StyledModal = styled(Modal)`
    &[role=document] {
        height: calc(100vh - ${modalMarginTopBottom * 2}px);
        top: ${modalMarginTopBottom}px;
        left: ${modalMarginLeftRight}px;
        margin: 0;

        .ant-modal-content {
            background-color: #0C1117;
            height: 100%;
        }
    
        .ant-modal-body {
            padding: 10px;
            height: 100%;
            overflow: auto;
        }

        button.ant-modal-close {
            top: 12px;
            right: 20px;
        }
    }
`;

const StyledModalContent = styled.div`
    display: grid;
    grid-template-rows: max-content 1fr;
    grid-gap: 10px;
    height: 100%;
`;

const TradeSnapshotApp = React.lazy(() => import("TradeSnapshot"));

export type InitialiseSnapshotProps = {
    instrumentIdentifier?: string | number; // can be either instrumentId or bbgTicker
};

type Props = InitialiseSnapshotProps & {
    show: boolean;
    onClose: () => void;
};

const TradeSnapshotModal = ({ show, instrumentIdentifier, onClose }: Props) => {
    const {
        traderId: insightsTraderId,
        fundId: insightsFundId,
        fundGroupId: insightsFundGroupId,
        lookback: insightsLookback,
    } = useInsightFilters();

    const idAsNumber = Number(instrumentIdentifier)
    const instrumentId = Number.isInteger(idAsNumber) ? idAsNumber : undefined;

    const v2Props = {
        bbgTicker: instrumentId ? undefined : instrumentIdentifier as string,
        instrumentId,
        disableUrlSearchParamsUpdate: true
    };

    // width has to be passed in as a prop instead of in the styled-component because AntD sets a default of 520 on the element style
    return (
        <StyledModal visible={show} onCancel={onClose} closable footer={null} width={`calc(100vw - ${modalMarginLeftRight * 2}px)`}>
            {
                !show ? null :
                <InsightsFiltersProvider disableUrlSearchParamsUpdate={true} traderId={insightsTraderId} fundId={insightsFundId} fundGroupId={insightsFundGroupId} lookback={insightsLookback} >
                    <StyledModalContent>
                        <InsightsFilters />
                        <LazyComponent component={TradeSnapshotApp} {...v2Props} />
                    </StyledModalContent>
                </InsightsFiltersProvider>
            }
        </StyledModal>
    );
};

export const TradeSnapshotModalProvider = ({ children }: any) => {
    const [snapshotProps, setSnapshotProps] = useState<InitialiseSnapshotProps | undefined>(undefined);

    return (
        <TradeSnapshotModalContext.Provider value={{
            showSnapshotModal: d => setSnapshotProps(d)
        }}>
            {children}
            <TradeSnapshotModal {...snapshotProps} show={!!snapshotProps} onClose={() => setSnapshotProps(undefined)} />
        </TradeSnapshotModalContext.Provider>
    );
};