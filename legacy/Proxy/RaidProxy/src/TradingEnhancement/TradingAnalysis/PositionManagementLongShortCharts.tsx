import React, { FunctionComponent, useEffect, useState } from "react";
import { TradingEnhancementProps } from "../../TradingEnhancement/Models";
import {
    withLoadingOverlay,
    LoadableComponentProps,
    ChangedValue,
    useInsightFilters,
    useTradeSnapshotModal
} from "@reddeer/firefly-shared-controls";
import {
    PositiveVsNegativeChart,
    PositiveVsNegativeChartDefinition,
    TooltipComponentProps
} from "./PositiveVsNegative";
import {
    PositionManagementStackedBarChartData,
    createGetPositionManagementStackedBarChartData
} from "../../Api";
import styles from "./Styles.scss";

type Props = TradingEnhancementProps;

const LongShortChartTooltip = ({
    selectedItem
}: TooltipComponentProps<PositionManagementStackedBarChartData>) => {
    if (!selectedItem) {
        return null;
    }

    const { data, value } = selectedItem;
    return (
        <div className={styles.longShortTooltip}>
            <div className={styles.tooltipTicker}>{data.Ticker}</div>
            <ChangedValue formatType="dollar0dp" value={value} />
        </div>
    );
};

const getPositionManagementStackedBarData = createGetPositionManagementStackedBarChartData({
    suppressDuplicateRequest: true
});

const LongShortChartsComponent: FunctionComponent<Props & LoadableComponentProps> = ({
    traderId,
    lookback,
    beginLoad
}) => {
    const { fundId, fundGroupId } = useInsightFilters();
    const [shortChartData, setShortChartData] = useState<PositionManagementStackedBarChartData[]>(
        []
    );
    const [longChartData, setLongChartData] = useState<PositionManagementStackedBarChartData[]>([]);
    const { showSnapshotModal: show } = useTradeSnapshotModal();

    const chartDefinition: PositiveVsNegativeChartDefinition<PositionManagementStackedBarChartData> = {
        showNSections: 3,
        categories: [
            { label: "Initiation", valueAccessor: d => d.Initiation },
            { label: "Adding to Winners", valueAccessor: d => d.AddingToWinners },
            { label: "Adding to Losers", valueAccessor: d => d.AddingToLosers },
            { label: "Profit Taking", valueAccessor: d => d.ProfitTaking },
            { label: "Cutting Losers", valueAccessor: d => d.CuttingLosers }
        ],
        tooltipComponent: LongShortChartTooltip,
        onClick: data => {
            show({ instrumentIdentifier: data.InstrumentID });
        }
    };

    useEffect(() => {
        if (
            (fundGroupId === undefined && fundId === undefined && traderId === undefined) ||
            lookback === undefined
        ) {
            return;
        }

        let mounted = true;

        const promise = getPositionManagementStackedBarData
            .invoke({ fundGroupId, fundId, traderId, lookback })
            .then(rawData => {
                const shortData: PositionManagementStackedBarChartData[] = [];
                const longData: PositionManagementStackedBarChartData[] = [];
                for (const data of rawData) {
                    data.Direction === "L" ? longData.push(data) : shortData.push(data);
                }

                if (mounted) {
                    setShortChartData(shortData);
                    setLongChartData(longData);
                }
            });

        beginLoad(promise);

        return () => {
            mounted = false;
        };
    }, [fundGroupId, fundId, traderId, lookback]);

    useEffect(() => () => getPositionManagementStackedBarData.cancel(), []);

    return (
        <>
            <div className={styles.longShortChart}>
                <div className={styles.chartLabelContainer}>
                    <div className={styles.chartLabel}>Long</div>
                </div>
                <PositiveVsNegativeChart definition={chartDefinition} data={longChartData} />
            </div>
            <div className={styles.longShortChart}>
                <div className={styles.chartLabelContainer}>
                    <div className={styles.chartLabel}>Short</div>
                </div>
                <PositiveVsNegativeChart definition={chartDefinition} data={shortChartData} />
            </div>
        </>
    );
};

export const PositionManagementLongShortCharts = withLoadingOverlay<Props>(
    LongShortChartsComponent
);
