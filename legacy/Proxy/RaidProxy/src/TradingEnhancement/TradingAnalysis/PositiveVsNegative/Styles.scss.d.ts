declare namespace StylesScssNamespace {
  export interface IStylesScss {
    axis: string;
    bars: string;
    baseLine: string;
    categoryLabels: string;
    gridLines: string;
    hideTicks: string;
    negative: string;
    positive: string;
    positiveVsNegativeChartContainer: string;
    tooltip: string;
    xAxisGridLines: string;
  }
}

declare const StylesScssModule: StylesScssNamespace.IStylesScss & {
  /** WARNING: Only available when `css-loader` is used without `style-loader` or `mini-css-extract-plugin` */
  locals: StylesScssNamespace.IStylesScss;
};

export = StylesScssModule;
