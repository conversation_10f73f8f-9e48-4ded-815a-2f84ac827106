import { LongShortRatioModel } from "@reddeer/firefly-shared-controls";

//Api Models
export type ScenarioStatisticsApiModel = {
    Scenario: string;
    MarketValue: number;
    TodayPL: number;
    ReduceAttrib: number;
};

export type PositionSizingDataApiModel = {
    Date: Date;
    Trader: string;
    AttributionToUntraded: number;
    AttributionToSizeAdjustment: number;
    AttributionToPassiveEntryExit: number;
    TotalPL: number;
};

export type StopLossSummaryApiModel = {
    DaysSinceStopLoss: number;
    Long: number;
    Short: number;
    LongNumPos: number;
    ShortNumPos: number;
    OverallNumPos: number;
    LongNumActioned: number;
    ShortNumActioned: number;
    OverallNumActioned: number;
    LongHitRate: number;
    ShortHitRate: number;
    OverallHitRate: number;
    LongPL: number;
    ShortPL: number;
    OverallPL: number;
    MeanDailyN: number;
};

export type SummaryStatsByTurnoverApiModel = {
    Date: Date;
    PL: number;
    MarketValue: number;
    Volatility: number;
    Turnover: number;
    PLPercentile: number;
    MarketValuePercentile: number;
    VolatilityPercentile: number;
    TurnoverPercentile: number;
    PLDecile: number;
    MarketValueDecile: number;
    VolatilityDecile: number;
    TurnoverDecile: number;
    ReferenceMarketReturn: number;
};

export type HistoricTurnoverApiModel = {
    Date: string,
    Turnover: number;
};

export type HoldingPeriodApiModel = {
    Long: number;
    Short: number;
    Overall: number;
};

export type PLAndSizeApiModel = {
    InstrumentId: number;
    Strategy: string;
    Ticker: string;
    Visit: number;
    Direction: "S" | "L";
    Size: number;
    FinalPL: number;
};

export type PLAndSwingApiModel = {
    InstrumentId: number;
    Visit: number;
    Strategy: string;
    Ticker: string;
    Swing: number;
    FinalPL: number;
    Direction: "S" | "L";
};

export type TradingPositionsModel = {
    average?: LongShortRatioModel;
    previousPeriod?: LongShortRatioModel;
    change?: LongShortRatioModel;
};

export type TradingHoldingPeriodModel = {
    average?: LongShortRatioModel;
    longest?: LongShortRatioModel;
    shortest?: LongShortRatioModel;
};

export type PerformanceCorrelationModel = {
    long: number;
    short: number;
};

export type PositionNumberSummmaryApiModel = {
    Period: number;
    NumPosLong: number;
    NumPosShort: number;
    WeightLong: number;
    WeightShort: number;
};
