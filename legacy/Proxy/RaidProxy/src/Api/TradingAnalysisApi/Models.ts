import { Lookback } from "@reddeer/firefly-shared-controls";

export type TradingAnalysisRequest = {
    fundGroupId?: number;
    fundId?: number;
    traderId?: number;
    lookback: Lookback;
    absoluteRelative?: AbsoluteRelative;
};

export type TradingAnalysisDataDirection = "S" | "L" | "All";

export type PositionData = {
    Direction: TradingAnalysisDataDirection;
    Initiation: number;
    AddingToWinners: number;
    AddingToLosers: number;
    ProfitTaking: number;
    CuttingLosers: number;
    Total: number;
};

export type PositionManagementData = [PositionData, PositionData, PositionData]; // [Long, Short, All]

export enum TradingAnalysisDataType {
    PL,
    Count,
    HitRate
}

export enum TimeseriesDataType {
    PL,
    Return
}

export enum AbsoluteRelative {
    Absolute,
    Relative
}

export type TradingAnalysisDataRequest = TradingAnalysisRequest & {
    type: TradingAnalysisDataType
};

export type TradingAnalysisTimeseriesRequest = TradingAnalysisRequest & {
    type: TimeseriesDataType;
};

export type PositionManagementStackedBarChartData = PositionData & {
    InstrumentID: number;
    Ticker: string;
    Direction: "S" | "L";
};

export type PositionManagementTimeseriesData = PositionData & {
    TimeStamp: number;
    Direction: "S" | "L";
};

export type StopLossData = {
    Direction: TradingAnalysisDataDirection;
    Actioned: number;
    Unactioned: number;
    Total: number;
};

export type StopLossDataRows = [StopLossData, StopLossData, StopLossData]; // [Long, Short, All]

export type StopLossTimeseriesData = StopLossData & {
    TimeStamp: number;
    Direction: "S" | "L";
};

export type StopLossStackedBarChartData = StopLossData & {
    InstrumentID: number;
    Ticker: string;
    Direction: "S" | "L";
};