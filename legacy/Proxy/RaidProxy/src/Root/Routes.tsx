import React from "react";
import { withRouter, Switch, Route, Redirect } from "react-router";
import { Pages } from "../pages";
import { App as NewTradeTicket } from "@reddeer/orderform-js-client";
import { BookingTicketApp as BookingTicket } from "@reddeer/orderform-js-client";
import { PortfolioRulesAdminApp } from "@reddeer/portfolio-rules-admin-js-client";
import { TradingEnhancement } from "TradingEnhancement";
import { NotFound } from "NotFound";
import { getSearchParameters } from "@reddeer/firefly-shared-controls/build/Common/Utilities/Location";
import { TradeSnapshotStandalone } from "TradingEnhancement/TradeSnapshot/TradeSnapshotStandalone";
import { App as CustomIndexApp } from "@reddeer/customindex-js-client";
import { PortfolioMonitorApp } from "@reddeer/portfolio-monitor-js-client";
import { withLazyComponent, LazyComponent } from "PrimitiveComponents/index";


const CrossOrder = withLazyComponent(() => import("CrossOrder/index"), "CrossOrderApp");

export const Routes = withRouter(({ location }) => (
    <Switch>
        <Route
            path={`/${Pages.TradeTicket}`}
            component={NewTradeTicket}
        />
        <Route
            path={`/${Pages.NewTradeTicket}`}
            component={NewTradeTicket}
        />
        <Route
            path={`/${Pages.BookingTicket}`}
            component={BookingTicket}
        />
        <Route
            path={`/${Pages.PortfolioRulesAdmin}`}
            component={PortfolioRulesAdminApp}
        />
        <Route
            path={`/${Pages.TradingEnhancement}`}
            component={TradingEnhancement}
        />
        <Route
            path={`/${Pages.CustomIndex}`}
            component={CustomIndexApp}
        />
        <Route
            path={`/${Pages.PortfolioMonitor}`}
            component={PortfolioMonitorApp}
        />
        <Route
            path={`/${Pages.TradeSnapshot}`}
            render={(props: any) => (
                <TradeSnapshotStandalone
                    {...props}
                    instrumentIdentifier={getSearchParameters(location as any).instrumentId}
                />
            )}
        />
        <Route
            path={`/${Pages.RiskUtil}`}
            render={(props: any) => (
                <TradingEnhancement
                    showNavbar={false}
                    {...props}
                />
            )}
        />
        <Route
            path={`/${Pages.CrossOrder}`}
            component={CrossOrder}
        />
        <Redirect
            from="/"
            to={{ pathname: "/trade-ticket", search: location.search }}
            exact
        />
        <Route component={NotFound} />
    </Switch>
));
