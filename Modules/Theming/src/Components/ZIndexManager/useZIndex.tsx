import { useContext, useMemo } from "react";

import { ZIndex } from "../../consts/ZIndex";

import { ZIndexContext } from "./context";

// Ensure all z-index values are 'sandboxed', e.g. a nested modal is always a higher z-index than a parent popover/tooltip
const maxZindex = Math.max(
    ...(Object.values(ZIndex).filter(v => typeof v === "number") as number[]),
);

// Most browsers use a 32-bit signed integer
const MaxAllowedZIndex = 2147483647;

type ZIndexWithMax = typeof ZIndex & { Max: number };

const isNumber = (value: any): value is number =>
    !Number.isNaN(parseFloat(value)) && !Number.isNaN(value - 0);

export const useZIndex = (offset?: number) => {
    const parentZIndex = useContext(ZIndexContext);

    const activeZIndex = useMemo(() => {
        if (parentZIndex || typeof offset === "number") {
            return Object.entries(ZIndex)
                .filter(([, value]) => isNumber(value))
                .reduce(
                    (z, [name, value]) => ({
                        ...z,
                        [name]: Math.min(
                            MaxAllowedZIndex,
                            ((value as number) + 1 + maxZindex) *
                                (typeof offset === "number" ? offset : 1),
                        ),
                    }),
                    {
                        Max: MaxAllowedZIndex,
                    } as ZIndexWithMax,
                );
        }

        return {
            Max: MaxAllowedZIndex,
            ...ZIndex,
        };
    }, [parentZIndex, offset]);

    return activeZIndex;
};
