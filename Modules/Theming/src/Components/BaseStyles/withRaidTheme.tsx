import React, { useEffect, useRef, useState, ComponentType, useLayoutEffect } from "react";

const baseStyles = async () => [import("@reddeer/raid-theme/dist/style.css?insertCSSAtTop")];

export const withRaidTheme = <P,>(Component: ComponentType<P>) => {
    const WrappedComponent = (props: P) => {
        const isMounted = useRef(true);
        const [complete, setComplete] = useState(false);

        useEffect(() => {
            return () => {
                isMounted.current = false;
            };
        }, []);

        useLayoutEffect(() => {
            if (isMounted.current) {
                // @TODO: find better way to handle this is tests
                if (process.env.NODE_ENV !== "test") baseStyles().then(() => setComplete(true));
                if (process.env.NODE_ENV === "test") setComplete(true);
            }
        }, [setComplete, isMounted]);

        return (complete && <Component {...props} />) || null;
    };

    WrappedComponent.displayName = `withRaidTheme(${Component.displayName || Component.name})`;

    return WrappedComponent;
};
