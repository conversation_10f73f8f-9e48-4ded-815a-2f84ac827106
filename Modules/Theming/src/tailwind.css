@layer theme, base, components, utilities;

@import "tailwindcss/theme.css" layer(theme);
@import "tailwindcss/utilities.css" layer(utilities);

@source "../../Analytics/src";
@source "../../Charting/src";
@source "../../ClientApi/src";
@source "../../Components/src";
@source "../../ConnectedComponents/src";
@source "../../Contracts/src";
@source "../../Grid/src";
@source "../../LaunchPad/src";
@source "../../Layouts/src";
@source "../../Notifications/src";
@source "../../PrimitiveComponents/src";
@source "../../Raid/src";
@source "../../ReportEngine/src";
@source "../../ReportEditor/src";
@source "../../Schema/src";
@source "../../Theming/src";
@source "../../TransportGateway/src";
@source "../../Utilities/src";

@source "../../../legacy/Modules/Attribution/src";
@source "../../../legacy/Modules/CompanyPage/src";
@source "../../../legacy/Modules/ComponentLibrary/src";
@source "../../../legacy/Modules/CrossOrder/src";
@source "../../../legacy/Modules/DeepDive/src";
@source "../../../legacy/Modules/GamePlan/src";
@source "../../../legacy/Modules/TradeSnapshot/src";
@source "../../../legacy/Modules/ViewServerHealthMonitor/src";

@source "../../../legacy/Component/OrderForm/src";

@source "../../../legacy/Library/raid-theme/src/";
@source "../../../legacy/Library/firefly-common-components/src/";
@source "../../../legacy/Library/firefly-shared-controls/src/";

@source "../../../legacy/Proxy/RaidProxy/src";

@theme {
    /* https://tailwindcss.com/docs/theme#theme-variable-namespaces */

    /* .bg-* */
    --color-order-buy: var(--color__order-buy);
    --color-order-sell: var(--color__order-sell);
    --color-main: var(--color__background);
    --color-overlay: var(--color__background--overlay);
    --color-overlay-content: var(--color__background--overlay-content);
    --color-container: var(--color__background--container);
    --color-container-level-2: var(--color__background--container-level-2);
    --color-contrast: var(--color__background--contrast);
    --color-input: var(--input__background-color);
    --color-table: var(--table__background-color);
    --color-chart-seris-line-1: var(--charts__series__line__1);
    --color-chart-seris-line-2: var(--charts__series__line__2__50);
    --color-chart-seris-line-3: var(--charts__series__line__3__50);

    /* .text-{color} */
    --color-primary: var(--color);
    --color-secondary: var(--color--secondary);
    --color-tertiary: var(--color--tertiary);
    --color-highlighted: var(--color__highlight);
    --color-highlighted-inversed: var(--color__highlight--inverse);
    --color-highlighted-secondary: var(--color__highlight--secondary);
    --color-muted: var(--color--muted);
    --color-muted-secondary: var(--color--muted--secondary);
    --color-positive: var(--number__positive__color);
    --color-negative: var(--number__negative__color);
    --color-neutral: var(--number__neutral__color);
    --color-cautious: var(--number__cautious__color);
    --color-ticker: var(--ticker__color);

    /* .border-{color} */
    --color-table-header: var(--table__header__border-color);
    --color-table-row: var(--table__row__border-color);
    --color-button: var(--button__border-color);
    --color-button-hover: var(--button__border-color--hover);

    /* .text-{size} */
    --text-2xs: var(--font__size--xx-small);
    --text-xs: var(--font__size--x-small);
    --text-sm: var(--font__size--small);
    --text-md: var(--font__size--medium);
    --text-lg: var(--font__size--large);
    --text-xl: var(--font__size--x-large);
    --text-2xl: var(--font__size--xx-large);

    /* .text-btn-{size} */
    --text-btn-xs: var(--button__font__size--x-small);
    --text-btn-sm: var(--button__font__size--small);
    --text-btn-md: var(--button__font__size);
    --text-btn-lg: var(--button__font__size--large);
    --text-btn-xl: var(--button__font__size--x-large);

    /* .m-* .p-* */
    --spacing-2xs: var(--spacing--xx-small);
    --spacing-xs: var(--spacing--x-small);
    --spacing-sm: var(--spacing--small);
    --spacing-md: var(--spacing--medium);
    --spacing-lg: var(--spacing--large);
    --spacing-xl: var(--spacing--x-large);
    --spacing-2xl: var(--spacing--xx-large);
    --spacing-3xl: var(--spacing--xxx-large);
    --spacing-4xl: var(--spacing--xxxx-large);
}

/* Set default border color to Raid2 color */
@utility border-r {  border-width: 0;  }
@utility border { border-color: var(--border__color--outline); }
@utility border-t {  border-color: var(--border__color--outline);  }
@utility border-b {  border-color: var(--border__color--outline);  }
@utility border-l {  border-color: var(--border__color--outline);  }
@utility border-r {  border-color: var(--border__color--outline);  }

/* .bg-container-auto */
.bg-container-auto { background-color: var(--color__background--container); }
.bg-container-auto .bg-container-auto,
.bg-container .bg-container-auto,
.bg-container-level-2 .bg-container-auto {
    background-color: var(--color__background--container-level-2);
}

/* Fix class collision with Raid1 */
.dataTableCell.fixed { position: static; }
.dataTableCell.fixed > .collapse { visibility: visible; }
table.grid { display: table; }
