import React, { CSSProperties, SVGProps } from "react";

import HomeIcon from "./icons/home.svg?react";
import ManagementIcon from "./icons/management.svg?react";
import IdeaIcon from "./icons/idea.svg?react";
import PosexIcon from "./icons/posex.svg?react";
import RiskIcon from "./icons/risk.svg?react";
import TicketIcon from "./icons/ticket.svg?react";
import PerformanceIcon from "./icons/performance.svg?react";
import InsightsIcon from "./icons/insights.svg?react";
import DownArrowIcon from "./icons/downArrow.svg?react";
import DownChevronIcon from "./icons/downChevron.svg?react";
import DownChevronIconSelect from "./icons/downChevronSelect.svg?react";
import AlertIcon from "./icons/alert.svg?react";
import Close from "./icons/close.svg?react";
import CloseCircle from "./icons/close-circle.svg?react";
import MenuIcon from "./icons/menu.svg?react";
import UserIcon from "./icons/user.svg?react";
import FlipIcon from "./icons/rightLeftArrow.svg?react";
import BarChart from "./icons/barChart.svg?react";
import Chart from "./icons/chart.svg?react";
import Export from "./icons/export.svg?react";
import Download from "./icons/download.svg?react";
import Back from "./icons/back.svg?react";
import Info from "./icons/info.svg?react";
import Error from "./icons/error.svg?react";
import Success from "./icons/success.svg?react";
import Complete from "./icons/complete.svg?react";
import Incomplete from "./icons/incomplete.svg?react";
import Warning from "./icons/warning.svg?react";
import Check from "./icons/check.svg?react";
import CheckCircle from "./icons/check-circle.svg?react";
import Subscription from "./icons/subscription.svg?react";
import Disconnect from "./icons/disconnect.svg?react";
import Loading from "./icons/loading.svg?react";
import Copy from "./icons/copy.svg?react";
import Clear from "./icons/clear.svg?react";
import Add from "./icons/add.svg?react";
import Arrow from "./icons/arrow.svg?react";
import ArrowDown from "./icons/arrowDown.svg?react";
import ArrowLeft from "./icons/arrowLeft.svg?react";
import ArrowRight from "./icons/arrowRight.svg?react";
import UTurnUp from "./icons/uTurnUp.svg?react";
import UTurnDown from "./icons/uTurnDown.svg?react";
import Edit from "./icons/edit.svg?react";
import Attachment from "./icons/attachment.svg?react";
import File from "./icons/file.svg?react";
import PMFolder from "./icons/pmFolder.svg?react";
import Folder from "./icons/folder.svg?react";
import Admin from "./icons/admin.svg?react";
import ResetZoom from "./icons/resetZoom.svg?react";
import ZoomIn from "./icons/zoomIn.svg?react";
import ZoomOut from "./icons/zoomOut.svg?react";
import GamePlan from "./icons/gamePlan.svg?react";
import Link from "./icons/link.svg?react";
import Unlink from "./icons/unlink.svg?react";
import Todo from "./icons/todo.svg?react";
import Pinned from "./icons/pinned.svg?react";
import Rule from "./icons/rule.svg?react";
import BarChartWithAxis from "./icons/barChartWithAxis.svg?react";
import News from "./icons/news.svg?react";
import SpeechBubble from "./icons/speechBubble.svg?react";
import Dock from "./icons/dock.svg?react";
import UnDock from "./icons/undock.svg?react";
import DragVertically from "./icons/dragVertically.svg?react";
import Locked from "./icons/locked.svg?react";
import UnLocked from "./icons/unlocked.svg?react";
import Indicator from "./icons/indicator.svg?react";
import Jockeying from "./icons/jockeying.svg?react";
import Portfolio from "./icons/portfolio.svg?react";
import Calendar from "./icons/calendar.svg?react";
import Search from "./icons/search.svg?react";
import ExternalLink from "./icons/externalLink.svg?react";
import Delete from "./icons/delete.svg?react";
import MacroObservation from "./icons/macroObservation.svg?react";
import Settings from "./icons/settings.svg?react";
import Components from "./icons/components.svg?react";
import Config from "./icons/config.svg?react";
import Modal from "./icons/modal.svg?react";
import ChartUp from "./icons/chartUp.svg?react";
import ChartDown from "./icons/chartDown.svg?react";
import FastTrade from "./icons/fastTrade.svg?react";
import Impersonate from "./icons/impersonate.svg?react";
import Basket from "./icons/basket.svg?react";
// source https://icon-sets.iconify.design/fluent/document-text-20-filled/
import DocumentSolid from "./icons/documentSolid.svg?react";
import TemperatureBalanced from "./icons/temperatureBalanced.svg?react";
import TemperatureComprehensive from "./icons/temperatureComprehensive.svg?react";
import TemperatureStrict from "./icons/temperatureStrict.svg?react";
import Grid from "./icons/grid.svg?react";
import GridAdd from "./icons/gridAdd.svg?react";
import GridRowGroup from "./icons/gridRowGroup.svg?react";
import Undo from "./icons/undo.svg?react";
import Template from "./icons/template.svg?react";
import GroupBy from "./icons/groupBy.svg?react";
import Play from "./icons/play.svg?react";
import Bin from "./icons/bin.svg?react";
import Save from "./icons/save.svg?react";
import Paint from "./icons/paint.svg?react";
import Send from "./icons/send.svg?react";
import Minimize from "./icons/minimize.svg?react";
import Maximize from "./icons/maximize.svg?react";
import Eye from "./icons/eye.svg?react";
import EyeOff from "./icons/eyeOff.svg?react";

const FlipIconTyped = FlipIcon as unknown as React.ComponentType<SVGProps<SVGSVGElement>>;
const FlipVerticalIcon = ({ style, ...rest }: { style?: CSSProperties }) => (
    <FlipIconTyped
        style={{
            transform: "rotate(90deg)",
            ...style,
        }}
        {...rest}
    />
);

export const icons = {
    home: HomeIcon,
    cockpit: HomeIcon,
    management: ManagementIcon,
    idea: IdeaIcon,
    posex: PosexIcon,
    risk: RiskIcon,
    ticket: TicketIcon,
    execution: TicketIcon,
    performance: PerformanceIcon,
    insights: InsightsIcon,
    downArrow: DownArrowIcon,
    downChevron: DownChevronIcon,
    downChevronSelect: DownChevronIconSelect,
    alert: AlertIcon,
    close: Close,
    closeCircle: CloseCircle,
    menu: MenuIcon,
    user: UserIcon,
    flip: FlipIcon,
    flipVertical: FlipVerticalIcon,
    barChart: BarChart,
    barChartWithAxis: BarChartWithAxis,
    chart: Chart,
    export: Export,
    download: Download,
    back: Back,
    info: Info,
    error: Error,
    success: Success,
    complete: Complete,
    incomplete: Incomplete,
    warning: Warning,
    check: Check,
    checkCircle: CheckCircle,
    subscription: Subscription,
    disconnect: Disconnect,
    loading: Loading,
    copy: Copy,
    arrow: Arrow,
    arrowDown: ArrowDown,
    arrowLeft: ArrowLeft,
    arrowRight: ArrowRight,
    uTurnUp: UTurnUp,
    uTurnDown: UTurnDown,
    clear: Clear,
    add: Add,
    edit: Edit,
    attachment: Attachment,
    file: File,
    documentSolid: DocumentSolid,
    pmFolder: PMFolder,
    folder: Folder,
    admin: Admin,
    resetZoom: ResetZoom,
    zoomIn: ZoomIn,
    zoomOut: ZoomOut,
    gamePlan: GamePlan,
    link: Link,
    unlink: Unlink,
    process: GamePlan,
    todo: Todo,
    pinned: Pinned,
    rule: Rule,
    news: News,
    speechBubble: SpeechBubble,
    dock: Dock,
    undock: UnDock,
    dragVertically: DragVertically,
    locked: Locked,
    unlocked: UnLocked,
    indicator: Indicator,
    jockeying: Jockeying,
    portfolio: Portfolio,
    calendar: Calendar,
    move: ArrowRight,
    search: Search,
    externalLink: ExternalLink,
    delete: Delete,
    macroObservation: MacroObservation,
    settings: Settings,
    components: Components,
    config: Config,
    modal: Modal,
    chartUp: ChartUp,
    chartDown: ChartDown,
    fastTrade: FastTrade,
    impersonate: Impersonate,
    basket: Basket,
    temperatureBalanced: TemperatureBalanced,
    temperatureComprehensive: TemperatureComprehensive,
    temperatureStrict: TemperatureStrict,
    grid: Grid,
    gridAdd: GridAdd,
    gridRowGroup: GridRowGroup,
    undo: Undo,
    template: Template,
    layout: HomeIcon, // @TODO: update icon
    groupBy: GroupBy,
    play: Play,
    bin: Bin,
    save: Save,
    paint: Paint,
    send: Send,
    minimize: Minimize,
    maximize: Maximize,
    eye: Eye,
    eyeOff: EyeOff,
};
