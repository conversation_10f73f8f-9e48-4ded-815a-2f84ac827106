// prioritise using variables instead of specific values
// @TODO: set correct colours...
export default {
    charts__axis__color: "#B1B3B7",
    charts__tooltip__color: "var(--charts__axis__color)",

    "charts__label--color": "var(--color)",
    "charts__label--color_muted": "var(--color--tertiary)",
    "charts__label--font-family": "var(--font__family)",
    "charts__label--font-size": "9px",

    charts__positive: "var(--number__positive__color)",
    "charts__positive--em": "var(--number__positive__color--em)",
    charts__optimistic: "var(--number__optimistic__color)",
    "charts__optimistic--em": "var(--number__optimistic__color--em)",
    charts__negative: "var(--number__negative__color)",
    "charts__negative--em": "var(--number__negative__color--em)",
    charts__neutral: "var(--number__neutral__color)",
    "charts__neutral--em": "var(--number__neutral__color--em)",
    charts__cautious: "var(--number__cautious__color)",
    "charts__cautious--em": "var(--number__cautious__color--em)",

    "charts__positive--scale_start": "#053D06",
    "charts__positive--scale_end": "#18CB1B",
    "charts__negative--scale_start": "#7B1818",
    "charts__negative--scale_end": "#FF4A4A",

    "charts__series__line--reference": "rgba(255, 255, 255, 0.4)",
    "charts__reference-line": "var(--border__color--primary)",
    "charts__reference-lines": "hsl(37,91%,55%, 0.5)",
    "charts__reference-lines--alternative": "#88ccf3",

    charts__series__bubble__positive: "var(--charts__positive--scale_start)",
    charts__series__bubble__neutral: "var(--charts__neutral)",
    charts__series__bubble__negative: "var(--charts__negative--scale_start)",
    "charts__series__bubble--highlight__positive": "var(--charts__positive--em)",
    "charts__series__bubble--highlight__negative": "var(--charts__negative--em)",

    "charts__treemap-hierarchical__area--positive": "#0c8a0a",
    "charts__treemap-hierarchical__area--neutral": "#1b2223",
    "charts__treemap-hierarchical__area--negative": "#c1070a",
    "charts__treemap-hierarchical__text--color": "#ffffff",
    "charts__treemap-hierarchical__text--font": "12px monospace",

    charts__series__line__1: "rgb(255,255,255)",
    charts__series__line__1__50: "rgba(255,255,255,0.5)",
    charts__series__line__2: "rgba(6,198,221, 1)",
    charts__series__line__2__50: "rgba(6,198,221, 0.5)",
    charts__series__line__3: "rgb(200, 0, 255)",
    charts__series__line__3__50: "rgba(200, 0, 255, 0.5)",
    charts__series__line__4: "#00ff00",
    charts__series__line__5: "#f5a623",
    charts__series__line__6: "#FF9103",
    charts__series__line__7: "#0985B1",
    charts__series__line__8: "#10C4C1",
    charts__series__line__9: "#00dd88",
    charts__series__line__10: "#ED7D31",
    charts__series__line__11: "#417505",
    charts__series__line__12: "#FF0000FF",
    charts__series__line__13: "#A1A1A1",

    charts__series__pie__1: "#3b82f6",
    charts__series__pie__2: "#0ea5e9",
    charts__series__pie__3: "#06b6d4",
    charts__series__pie__4: "#14b8a6",
    charts__series__pie__5: "#14b8a6",
    charts__series__pie__6: "#10b981",
    charts__series__pie__7: "#22c55e",
    charts__series__pie__8: "#84cc16",
    charts__series__pie__9: "#eab308",
    charts__series__pie__10: "#f59e0b",
    charts__series__pie__11: "#f97316",
    charts__series__pie__12: "#ef4444",
    charts__series__pie__13: "#f43f5e",
    charts__series__pie__14: "#ec4899",
    charts__series__pie__15: "#d946ef",
    charts__series__pie__16: "#a855f7",
    charts__series__pie__17: "#8b5cf6",
    charts__series__pie__18: "#6366f1",

    "charts__series__line--bar__1": "var(--color--tertiary)",

    charts__series__block__1: "rgba(21, 100, 140, 0.94)",
    charts__series__block__2: "#8884d8",
    charts__series__block__3: "#82ca9d",
    charts__series__block__4: "#E46000",
    charts__series__block__5: "yellow",
    charts__series__block__6: "orange",
    charts__series__block__7: "#FF9103",
    charts__series__block__8: "aqua",
    charts__series__block__9: "hsl(0,0%,50%)",

    charts__series__area__1: "rgba(10, 152, 195, 0.25)",
    charts__series__area__2: "#8884d8",
    charts__series__area__3: "#82ca9d",
    charts__series__area__4: "#E46000",
    charts__series__area__5: "yellow",
    charts__series__area__6: "orange",
    charts__series__area__7: "orangered",
    charts__series__area__8: "aqua",
    charts__series__area__9: "white",
    "charts__series__area--line__1": "rgba(10, 152, 195, 1)",
    "charts__series__area--line__2": "var(--charts__series__area__2)",
    "charts__series__area--line__3": "var(--charts__series__area__3)",
    "charts__series__area--line__4": "var(--charts__series__area__4)",
    "charts__series__area--line__5": "var(--charts__series__area__5)",
    "charts__series__area--line__6": "var(--charts__series__area__6)",
    "charts__series__area--line__7": "var(--charts__series__area__7)",
    "charts__series__area--line__8": "var(--charts__series__area__8)",
    "charts__series__area--line__9": "var(--charts__series__area__9)",

    "charts__price-marker--font-family": "var(--font__family)",
    "charts__price-marker--font-size": "9px",
    "charts__price-marker--text-color": "#ffffff",
    "charts__price-marker--margin-left": "3",
    "charts__price-marker--padding": "0",
    "charts__price-marker--positive": "var(--charts__positive--scale_end)",
    "charts__price-marker--neutral": "var(--charts__price-marker--text-color)",
    "charts__price-marker--negative": "var(--charts__negative--scale_end)",
};
