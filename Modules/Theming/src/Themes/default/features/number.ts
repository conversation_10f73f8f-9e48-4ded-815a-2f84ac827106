// prioritise using variables instead of specific values
export default {
    number__positive__color: "#0fdd0f",
    "number__positive__color--em": "var(--number__positive__color)",
    "number__positive__color--invert": "#1A1D24",

    number__optimistic__color: "#99ff99",
    "number__optimistic__color--em": "var(--number__optimistic__color)",
    "number__optimistic__color--invert": "#fff",

    number__negative__color: "#ff3333",
    "number__negative__color--em": "var(--number__negative__color)",
    "number__negative__color--invert": "#fff",

    number__neutral__color: "#ffff00",
    "number__neutral__color--em": "var(--number__neutral__color)",
    "number__neutral__color--invert": "#1A1D24",

    number__cautious__color: "orange",
    "number__cautious__color--em": "var(--number__cautious__color)",
    "number__cautious__color--invert": "#fff"
};
