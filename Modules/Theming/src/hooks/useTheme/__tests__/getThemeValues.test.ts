import { AllThemeVars, ThemeVars } from "../../../Themes";

import { getThemeValues } from "../getThemeValues";

const mockThemes = {
    default: {
        alert__color: "red",
        "input__padding-v": "10px",
        "input__padding-h": "5px",
        input__padding: "var(--input__padding-v) var(--input__padding-h)"
    } as ThemeVars
} as unknown as AllThemeVars;

const themeGetThemeValue = getThemeValues(mockThemes, "default");

describe("getThemeValues", () => {
    it("should return single value", async () => {
        expect(themeGetThemeValue("alert__color")).toEqual("red");
    });

    it("should return multi value", async () => {
        expect(themeGetThemeValue(["alert__color", "input__padding"])).toEqual(["red", "10px 5px"]);
    });
});
