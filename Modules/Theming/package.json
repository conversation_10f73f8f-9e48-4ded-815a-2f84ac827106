{"name": "@tradinglabs/theming", "license": "UNLICENSED", "version": "1.0.0", "main": "src/index.ts", "scripts": {"start": "raid-build start", "build": "yarn build-app", "build-app": "raid-build build", "build-typescript": "yarn g:tsc && yarn make-types", "make-types": "yarn run -T make-federated-types", "test:ci": "jest --passWithNoTests --ci --detectOpenHandles", "lint:all": "yarn lint:eslint && yarn lint:stylelint", "lint:all:fix": "yarn eslint --fix && yarn lint:stylelint", "lint:eslint": "node ../../scripts/eslint/runEslint.js --paths='./src/**/*.+(js|jsx|ts|tsx)'", "lint:stylelint": "node ../../scripts/stylelint/runStylelint.js --paths='./src/**/*.+(ts|tsx)'"}, "dependencies": {"@emotion/stylis": "^0.8.5", "@reddeer/raid-theme": "workspace:*", "antd": "3.26.20", "classnames": "^2.3.2", "clsx": "2.1.1", "lodash": "~4.17.21", "react": "18.2.0", "react-dom": "18.2.0", "react-helmet": "^6.1.0", "styled-components": "5.3.11", "tailwind-merge": "2.5.5"}, "devDependencies": {"@tradinglabs/raid-build": "workspace:*", "@types/lodash": "~4.14.202", "@types/react": "18.0.25", "@types/react-dom": "18.0.9", "@types/react-helmet": "^6.1.11", "@types/styled-components": "5.1.29", "typescript": "5.2.2"}}