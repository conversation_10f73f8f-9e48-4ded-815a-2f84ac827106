import { prefixByPropertyValues } from "../prefixByPropertyValues";

import type { SchemaProps } from "../../../withSchemaProps";

describe("prefixByPropertyValues", () => {
    const data = [
        { id: 1, name: "A" },
        { id: 3, name: "C" },
        { id: 2, name: "B" }
    ];
    it("should prefix the array correctly", () => {
        const result = prefixByPropertyValues(
            data,
            [],
            {} as SchemaProps,
            {}
        )(" > ", "name", ["A", "B"]);

        expect(result.data.map(x => x.name)).toStrictEqual([" > A", "C", " > B"]);
    });
});
