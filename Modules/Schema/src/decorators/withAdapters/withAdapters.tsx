import React, { ComponentType, useMemo, ReactNode } from "react";
import { castArray, isNil } from "lodash";

import { HOC } from "PrimitiveComponents/index";
import { isStringType, math, parseFunctionString, complement, isNull } from "Utilities/index";
import type { Schema } from "Contracts/index";

import type { SchemaContext } from "../../Components";

import { getSchemaProps, SchemaProps } from "../withSchemaProps";

import * as adaptersMap from "./adapters";

type AdapterResult<R extends Record<string, unknown>> = {
    data?: R[];
    schema?: Schema;
    context?: SchemaContext<R>;
};

export type AdapterFn<R extends Record<string, unknown> = Record<string, unknown>> = (
    data: Record<string, unknown>[],
    schema: Schema,
    schemaProps: SchemaProps,
    props: Record<string, unknown>
) => (...args: unknown[]) => AdapterResult<R>;

type Props<D extends {}> = {
    data?: D[] | D;
    schema?: Schema;
    context?: SchemaContext<D>;
    children?: ReactNode;
};

type OuterProps = {
    customAdapters?: Record<string, AdapterFn>;
};

export const withAdapters = <P extends Props<D>, D extends {}, R extends D>(
    Component: ComponentType<P>
) => {
    const WrappedComponent = ({
        children,
        data,
        schema,
        customAdapters,
        ...props
    }: P & OuterProps) => {
        const { context } = props;

        const manipulatedDataSchema = useMemo<AdapterResult<R> | undefined>(() => {
            const schemaProps = getSchemaProps(schema);

            if (isNil(schemaProps?.allProps?.adapters)) {
                return undefined; // Don't waste memory by memorizing data/schema if not applying any adapters
            }

            const filtered = castArray(schemaProps.allProps.adapters)
                .map(adapter => {
                    if (isStringType(adapter)) {
                        return adapter;
                    }

                    if (adapter.if) {
                        const conditions = castArray(adapter.if).every(cond =>
                            math.evaluate(cond, {
                                data,
                                schema,
                                schemaProps,
                                props
                            })
                        );

                        if (!conditions) {
                            return null;
                        }
                    }

                    return adapter.adapters;
                })
                .flat()
                .filter(complement(isNull));

            if (filtered.length === 0) {
                return undefined; // Don't waste memory by memorizing data/schema if not applying any adapters
            }

            return filtered.reduce(
                (acc, curr) => {
                    const { functionName, params } = parseFunctionString(curr);
                    const fn: AdapterFn<R> =
                        adaptersMap[functionName] ?? customAdapters[functionName];
                    if (fn) {
                        const result = fn(acc.data, acc.schema, schemaProps, props)(...params);

                        return {
                            data: result.data ?? acc.data,
                            schema: result.schema ?? acc.schema,
                            context: result.context ?? acc.context
                        };
                    }

                    console.warn(`[withAdapters] Adapter "${functionName}" not found.`);

                    return acc;
                },
                { data, schema, context } as AdapterResult<R>
            );
        }, [customAdapters, data, props, schema, context]);

        const newData = manipulatedDataSchema?.data ?? data;
        const newSchema = manipulatedDataSchema?.schema ?? schema;
        const newContext = manipulatedDataSchema?.context ?? context;

        return (
            <Component
                {...(props as P)}
                {...(newData && { data: newData })}
                {...(newSchema && { schema: newSchema })}
                {...(newContext && { context: newContext })}
            >
                {children}
            </Component>
        );
    };

    return HOC("withAdapters", WrappedComponent, Component);
};
