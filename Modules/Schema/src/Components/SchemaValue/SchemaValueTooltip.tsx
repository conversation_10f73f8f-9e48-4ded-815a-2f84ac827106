import React, { createContext, useContext } from "react";

import { ConnectedComponentStatus } from "TransportGateway/index";
import { Tooltip } from "PrimitiveComponents/index";

import { SchemaValueWrapper } from "./SchemaValueWrapper";
import { SchemaValueChildren } from "./SchemaValueChildren";
import type { SchemaValueTooltipProps } from "./types";

// Context wrapper to ensure nested tooltips are handled correctly
const TooltipContext = createContext<boolean>(false);

export const SchemaValueTooltip = <D extends {}>({
    data,
    context,
    columnSchema,
    schema,
    className,
    defaultValue,
    children,
    TooltipComponent,
    basePath,
    coordinate
}: SchemaValueTooltipProps<D>) => {
    const hasParentTooltip = useContext(TooltipContext);

    if (!columnSchema || hasParentTooltip) {
        return <>{children}</>;
    }

    const schemaBasePath = basePath ?? columnSchema?.name;

    return (
        <SchemaValueWrapper
            data={data}
            context={context}
            columnSchema={columnSchema}
            schema={schema}
            className={className}
            defaultValue={defaultValue}
            propertyPrefix="componentTooltip"
            Component={TooltipComponent}
            basePath={schemaBasePath}
        >
            {(Component, outerProps, props, ValueComponent, valueProps) => {
                if (outerProps) {
                    // Make sure height/width are not passed to the component
                    const {
                        height,
                        width,
                        maxHeight,
                        hasChildren = true,
                        placement,
                        ...rest
                    } = outerProps;

                    return (
                        <Tooltip
                            width={width}
                            height={height}
                            maxHeight={maxHeight}
                            placement={placement}
                            content={
                                <ConnectedComponentStatus>
                                    <Component {...rest}>
                                        {(hasChildren && (
                                            <SchemaValueChildren
                                                ValueComponent={ValueComponent}
                                                valueProps={valueProps}
                                                {...props}
                                            >
                                                {value => <>{value}</>}
                                            </SchemaValueChildren>
                                        )) ||
                                            null}
                                    </Component>
                                </ConnectedComponentStatus>
                            }
                            coordinate={coordinate}
                        >
                            <TooltipContext.Provider value>{children}</TooltipContext.Provider>
                        </Tooltip>
                    );
                }

                return <>{children}</>;
            }}
        </SchemaValueWrapper>
    );
};
