import React, { ReactNode } from "react";

import type { DotPath } from "Utilities/index";
import { DataBindingBase, LayoutHorizontalGroup, LayoutContainer } from "PrimitiveComponents/index";

import type { SchemaContext } from "../../SchemaValue";

// Recursive:
// eslint-disable-next-line import/no-cycle
import { ObjectSpec } from "../ObjectSpec";
import type { JSONSchemaImplementationDefinition, View } from "../types";
import type { WithLayoutChildWrapperProps } from "../LayoutFactory";

type ArrayItemProps = {
    getPath: (name?: string) => DotPath;
    definition: JSONSchemaImplementationDefinition;
    view?: View;
    actions?: ReactNode[];
    parentDefinition: JSONSchemaImplementationDefinition;
    rootDefinition: JSONSchemaImplementationDefinition;
    name: string;
    disableChangeState: boolean;
    context: SchemaContext<never>;
    className?: string;
} & DataBindingBase &
    WithLayoutChildWrapperProps;

export const ArrayItem = ({
    schemaPath,
    definition,
    getPath,
    view,
    disabled,
    actions,
    ChildWrapper,
    disableChangeState,
    name,
    parentDefinition,
    rootDefinition,
    context,
    className
}: ArrayItemProps) => (
    <LayoutHorizontalGroup
        compact
        spacing="x-large"
        className={className}
        divider="tertiary"
    >
        <LayoutContainer flex>
            <ObjectSpec
                schemaPath={schemaPath}
                name={name}
                view={view}
                disabled={disabled}
                parentDefinition={parentDefinition}
                rootDefinition={rootDefinition}
                definition={definition}
                path={getPath()}
                context={context}
                disableChangeState={disableChangeState}
                ChildWrapper={ChildWrapper}
                label=""
            />
        </LayoutContainer>

        {actions && (
            <LayoutHorizontalGroup
                alignContent="center"
                alignSelf="center"
                compact
            >
                {actions}
            </LayoutHorizontalGroup>
        )}
    </LayoutHorizontalGroup>
);
