import React, { ComponentType, ReactNode, useContext, useMemo, useState } from "react";

import type { SchemaResolvers } from "../types";

import { ScopeContext } from "./context";
import type { ScopeApi } from "./types";

type ScopeProviderProps = {
    children?: ReactNode;
    scopeData?: unknown;
} & Pick<ScopeApi, "modelContext" | "scopedPath">;

export const ScopeProvider = ({
    children,
    scopeData,
    modelContext,
    scopedPath
}: ScopeProviderProps) => {
    const parentApi = useContext(ScopeContext);

    const [schemaResolvers, setSchemaResolvers] = useState<SchemaResolvers>();

    const api = useMemo<ScopeApi>(
        () => ({
            schemaResolvers: parentApi?.schemaResolvers || schemaResolvers,
            setSchemaResolvers: parentApi?.setSchemaResolvers || setSchemaResolvers,

            // Default to nearest value:
            modelContext: modelContext || parentApi?.modelContext || "none",
            data: scopeData ?? parentApi?.data,

            // Default to outer value:
            scopedPath: parentApi?.scopedPath ?? scopedPath
        }),
        [
            parentApi?.schemaResolvers,
            parentApi?.setSchemaResolvers,
            parentApi?.data,
            parentApi?.modelContext,
            parentApi?.scopedPath,
            schemaResolvers,
            scopeData,
            scopedPath,
            modelContext
        ]
    );

    return <ScopeContext.Provider value={api}>{children}</ScopeContext.Provider>;
};

export const withScopeProvider = <P,>(Component: ComponentType<P>) => {
    const WrappedComponent = ({ scopeData, modelContext, ...props }: P & ScopeProviderProps) => (
        <ScopeProvider
            scopeData={scopeData}
            modelContext={modelContext}
        >
            <Component {...(props as P)} />
        </ScopeProvider>
    );

    return WrappedComponent;
};
