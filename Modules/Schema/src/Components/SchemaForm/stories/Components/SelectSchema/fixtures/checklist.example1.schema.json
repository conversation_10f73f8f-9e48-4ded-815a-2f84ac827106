{"$schema": "https://json-schema.org/draft/2019-09/schema", "title": "", "type": "object", "readOnly": false, "extension": {"actions": {"reset": {"disabled": false, "props": {"title": "Cancel"}}, "submit": {"disabled": false, "props": {"title": "Save"}}}, "layout": "grid", "constReferencePropertyName": "checklistType", "gridTemplateColumns": "1fr 14em 1fr", "displayOrder": ["checklistType"]}, "required": ["checklistType"], "properties": {"checklistType": {"type": "string", "title": "", "default": "alpha", "oneOf": [{"const": "alpha", "title": "Alpha"}, {"const": "hedge", "title": "He<PERSON>"}], "extension": {"layout": "inline", "targetColumn": "1/", "selectType": "radio"}}}, "oneOf": [{"$ref": "#/definitions/alpha", "type": "object", "properties": {"checklistType": {"const": "alpha"}}}, {"$ref": "#/definitions/hedge", "type": "object", "properties": {"checklistType": {"const": "hedge"}}}], "definitions": {"alpha": {"type": "object", "properties": {"alphaPositions": {"type": "array", "title": "Type of position", "items": {"type": "string", "oneOf": [{"const": "paradigmShift", "title": "Paradigm Shift"}, {"const": "opaque", "title": "Opaque"}, {"const": "specialSituation", "title": "Special situation"}]}, "extension": {"layout": "inline", "targetColumn": "2/3", "selectType": "default", "placeholder": "Select position(s)"}}, "thesis": {"type": "object", "title": "<PERSON><PERSON>", "extension": {"layout": "grid", "targetColumn": "1/3", "gridTemplateColumns": "1fr 14em 1fr", "displayOrder": ["why", "what", "when"]}, "properties": {"why": {"type": "string", "title": "Why is there a pricing inefficiency?", "extension": {"layout": "inline", "targetColumn": "2/3", "rows": 2}}, "what": {"type": "string", "title": "What needs to happen for stock to re-rate/pricing to correct?", "extension": {"layout": "inline", "targetColumn": "2/3", "rows": 2}}, "when": {"type": "string", "title": "When is this expected to happen?", "extension": {"layout": "inline", "targetColumn": "2/3", "rows": 2}}}}, "checklist": {"type": "object", "title": "Checklist", "extension": {"layout": "grid", "targetColumn": "1/3", "gridTemplateColumns": "1fr 14em 1fr", "displayOrder": ["priced", "consensusExpectations", "shortInterest", "circumstances", "risk", "factor", "earnings", "perf"]}, "properties": {"priced": {"type": "string", "title": "What is priced in?", "extension": {"layout": "inline", "targetColumn": "2/3", "rows": 2}}, "consensusExpectations": {"type": "string", "title": "Where and why is my expectations different from consensus?", "extension": {"layout": "inline", "targetColumn": "2/3", "rows": 2}}, "shortInterest": {"type": "string", "title": "What is short interest/crowdedness?", "extension": {"layout": "inline", "targetColumn": "2/3", "rows": 2}}, "circumstances": {"type": "string", "title": "What circumstances would suggest thesis deviation?", "extension": {"layout": "inline", "targetColumn": "2/3", "rows": 2}}, "risk": {"type": "string", "title": "what are the risk to trade, know and tail?", "extension": {"layout": "inline", "targetColumn": "2/3", "rows": 2}}, "factor": {"type": "string", "title": "What is specific and factor exposure of stock?", "extension": {"layout": "inline", "targetColumn": "2/3", "rows": 2}}, "earnings": {"type": "string", "title": "What do earnings revisions look like?", "extension": {"layout": "inline", "targetColumn": "2/3", "rows": 2}}, "perf": {"type": "string", "title": "How does abs & rel 6m perf look like?", "extension": {"layout": "inline", "targetColumn": "2/3", "rows": 2}}}}}}, "hedge": {"type": "object", "properties": {"hedgePositions": {"type": "array", "title": "Type of position", "items": {"type": "string", "oneOf": [{"const": "1", "title": "One"}, {"const": "2", "title": "Two"}, {"const": "3", "title": "Three"}]}, "extension": {"layout": "inline", "targetColumn": "2/3", "selectType": "default", "placeholder": "Select position(s)"}}, "earnings": {"type": "string", "title": "What do earnings revisions look like?", "extension": {"layout": "inline", "targetColumn": "2/3", "rows": 2}}}}}}