{"$schema": "https://json-schema.org/draft/2019-09/schema", "title": "Checklist Example - **<PERSON>**", "type": "object", "readOnly": false, "extension": {"actions": {"reset": {"disabled": false, "props": {"title": "Cancel"}}, "submit": {"disabled": false, "props": {"title": "Save"}}}, "layout": "grid", "gridTemplateColumns": "1fr 14em 1fr", "displayOrder": ["positions", "thesis", "checklist"]}, "required": ["positions"], "properties": {"expression": {"type": "string", "title": "How is it expressed?", "oneOf": [{"const": "absolute", "title": "Absolute"}, {"const": "relative", "title": "Relative"}], "extension": {"layout": "inline", "targetColumn": "2/"}}, "typeOfBet": {"type": "string", "title": "Type of bet?", "oneOf": [{"const": "factor", "title": "Factor"}, {"const": "sector", "title": "Sector"}, {"const": "market", "title": "Market"}, {"const": "singleStock", "title": "Single Stock"}], "extension": {"layout": "inline", "targetColumn": "2/"}}, "technicalSetup": {"type": "string", "title": "Technical setup?", "oneOf": [{"const": "breakout", "title": "Breakout"}, {"const": "pullback", "title": "<PERSON><PERSON><PERSON>"}, {"const": "counterTrend", "title": "Counter Trend"}], "extension": {"layout": "inline", "targetColumn": "2/"}}, "momentum": {"type": "string", "title": "What is the momentum?", "extension": {"layout": "inline", "targetColumn": "2/", "rows": 2}}, "sentiment": {"type": "string", "title": "What is the sentiment?", "extension": {"layout": "inline", "targetColumn": "2/", "rows": 2}}, "supportingObversations": {"type": "string", "title": "Supporting observations?", "extension": {"layout": "inline", "targetColumn": "2/", "rows": 2}}}}