{"$schema": "https://json-schema.org/draft/2019-09/schema", "title": "Person", "type": "object", "required": ["testBoolean"], "extension": {"actions": {"reset": {"disabled": false}}}, "properties": {"firstName": {"type": "string", "title": "First **name**", "description": "The person's **first name**."}, "lastName": {"type": "string", "title": "The LAST name", "description": "The person's **last name**.", "pattern": "^[a-zA-Z\\s]*$", "extension": {"schemaProperties": {"componentTooltip": "ConnectedComponents.DriversExposureChart", "componentTooltipPropsHeight": 200, "componentTooltipPropsWidth": 400, "componentTooltipPropsLabel": "#ref(.)", "componentTooltipPropsRiskModelName": "xxx", "componentTooltipPropsFactorDescription": "zzz"}}}, "integerTest": {"type": "integer", "minimum": 0, "extension": {"schemaProperties": {"componentTooltip": "PrimitiveComponents.Dial", "componentTooltipPropsHasChildren": false, "componentTooltipPropsHeight": 100, "componentTooltipPropsWidth": 100, "componentTooltipPropsValue": "@calc[#ref(.) / 100]"}}}, "floatTest": {"description": "Floating point number", "type": "number", "minimum": 0}, "selectTest": {"type": "object", "title": "", "required": ["testEnumRequired"], "extension": {"layoutColumns": 3}, "properties": {"testEnum": {"description": "Example of a `enum` property", "type": "string", "enum": ["One", "Two", "Three"]}, "testEnumRequired": {"description": "Example of a required `enum` property that cannot be cleared", "type": "string", "enum": ["One", "Two", "Three"]}, "testSelectLabel": {"description": "Example of a `oneOf` for select with label/value", "type": "number", "oneOf": [{"const": 1, "title": "Option 1"}, {"const": 2, "title": "Option 2"}, {"const": 3, "title": "Option 3"}]}}}, "testReadOnly": {"description": "Example of a `readOnly` property", "type": "string", "readOnly": true}, "testBoolean": {"description": "Example of a `boolean` property", "type": "boolean", "readOnly": false}, "test": {"description": "Example of an `optional description` shown against an object with **markdown**.\n```tsx\nconst x = (a: number) => a;\n```", "type": "object", "title": "Test Container", "extension": {"layoutColumns": 2}, "required": ["testSlider"], "properties": {"testSlider": {"type": "number", "title": "Slider Component Test", "minimum": 0, "maximum": 75, "description": "Schema value component factory", "extension": {"schemaProperties": {"component": "PrimitiveComponents.SliderWithLabel", "componentTooltip": "PrimitiveComponents.Dial", "componentTooltipPropsLabel": false, "componentTooltipPropsHasChildren": false, "componentTooltipPropsHeight": 100, "componentTooltipPropsWidth": 100, "componentTooltipPropsValue": "#ref(.)"}}}, "testSelect": {"type": "number", "title": "Select Component Test", "description": "Select with static options", "enum": [1, 2, 3, 4]}, "testSelect2": {"type": "number", "title": "Select Component Test", "description": "Select with static `value` / `label` options", "oneOf": [{"const": 1, "title": "Value 1"}, {"const": 2, "title": "Value 2"}, {"const": 3, "title": "Value 3"}]}, "prop1": {"type": "string", "extension": {"rows": 4}}, "prop2": {"type": "string", "pattern": "^[a-zA-Z]\\d*$", "description": "Pattern test - letter followed by numbers", "extension": {"validation": {"errorMessage": {"pattern": "Must be `<letter><number,>`"}}}}}}}}