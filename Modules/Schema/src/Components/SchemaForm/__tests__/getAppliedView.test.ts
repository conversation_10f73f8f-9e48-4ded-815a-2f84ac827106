import { getAppliedView } from "../utils";

describe("getAppliedView", () => {
    it("should correctly apply view", () => {
        expect(
            getAppliedView({ extension: { view: "grouped,readOnly" } }, "editable")
        ).toStrictEqual("grouped,readOnly");

        expect(
            getAppliedView({ extension: { view: "grouped,readOnly" } }, "readOnly,grouped")
        ).toStrictEqual("grouped,readOnly");

        expect(
            getAppliedView({ extension: { view: "grouped,readOnly" } }, undefined)
        ).toStrictEqual("grouped,readOnly");

        expect(
            getAppliedView({ extension: { view: "grouped,editable" } }, "editable")
        ).toStrictEqual("grouped,editable");

        expect(getAppliedView({ extension: { view: "grouped" } }, "readOnly")).toStrictEqual(
            "grouped,readOnly"
        );

        expect(getAppliedView({ extension: { view: "grouped" } }, "editable")).toStrictEqual(
            "grouped,editable"
        );

        expect(
            getAppliedView({ extension: { view: "grouped,editable" } }, "readOnly")
        ).toStrictEqual("grouped,editable");

        expect(
            getAppliedView({ extension: { view: "grouped,readOnly,editable" } }, "readOnly")
        ).toStrictEqual("grouped,readOnly");
    });
});
