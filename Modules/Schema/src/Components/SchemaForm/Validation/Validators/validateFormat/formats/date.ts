import type { Validator } from "../../../types";

export const validateFormatDate: Validator = async (value, { definition }) => {
    if (definition?.format === "date" && value) {
        if (Number.isNaN(Date.parse(value?.toString()))) {
            return {
                errorCode: "format:date",
                errorMessage: "Please enter a valid date in the format `YYYY-MM-DD`"
            };
        }
    }

    return undefined;
};
