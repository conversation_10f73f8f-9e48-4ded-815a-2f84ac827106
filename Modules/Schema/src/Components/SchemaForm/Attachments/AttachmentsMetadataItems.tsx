import React, { ComponentType } from "react";

import type { AttachmentItem } from "./types";
import {
    AttachmentsMetadataItem,
    type AttachmentsMetadataItemProps
} from "./AttachmentsMetadataItem";

type AttachmentsMetadataItemsProps<
    M extends AttachmentItem,
    P extends AttachmentsMetadataItemProps<M>
> = {
    items?: P["metadata"][];
    Component?: ComponentType<P>;
} & Pick<AttachmentsMetadataItemProps, "onDelete">;

export const AttachmentsMetadataItems = <
    M extends AttachmentItem,
    P extends AttachmentsMetadataItemProps<M>
>({
    items,
    onDelete,
    Component
}: AttachmentsMetadataItemsProps<M, P>) => {
    const ItemComponent = (Component as typeof AttachmentsMetadataItem) || AttachmentsMetadataItem;

    return (
        <>
            {items?.map(item => (
                <ItemComponent
                    key={item.key}
                    metadata={item}
                    onDelete={onDelete}
                />
            ))}
        </>
    );
};
