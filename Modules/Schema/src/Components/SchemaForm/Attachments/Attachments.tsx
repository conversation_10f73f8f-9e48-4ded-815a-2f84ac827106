import React, { ReactNode, useEffect } from "react";

import { AppIcon } from "Theming/index";
import {
    UploadButton,
    type ControlProps,
    LayoutHorizontal,
    LayoutVertical,
    useNotification,
    LayoutContainer,
    Typography,
    Markdown
} from "PrimitiveComponents/index";
import { type DotPath, pluralise } from "Utilities/index";

import { useAttachmentsPath } from "./AttachmentsProvider";
import type { AttachmentItem } from "./types";
import { AttachmentsMetadata } from "./AttachmentsMetadata";

type AttachmentsProps = ControlProps<
    AttachmentItem[],
    {
        path?: DotPath;
        multiple?: boolean;
        buttonLabel?: ReactNode;
        error?: string;
        className?: string;
        readOnly?: boolean;
    }
>;

export const Attachments = ({
    path,
    multiple = true,
    value,
    onChange,
    disabled = false,
    readOnly,
    buttonLabel,
    error,
    className
}: AttachmentsProps) => {
    const { addAttachments, pendingAttachments } = useAttachmentsPath({ path });
    const notification = useNotification();

    const handleAttachments = (files: File[]) => {
        addAttachments(files);
    };

    useEffect(() => {
        if (error) {
            notification.open({
                message: "There was a problem uploading attachments",
                description: <Markdown markdown={error} />,
                severity: "error",
                isSticky: true
            });
        }
    }, [error, notification]);

    const attachmentCount = (value?.length || 0) + (pendingAttachments?.length || 0);

    return (
        <LayoutContainer
            background={(readOnly && "container-level-2") || undefined}
            as="article"
            className={className}
        >
            <LayoutVertical
                marginH={readOnly ? "large" : "none"}
                marginV={readOnly ? "medium" : "none"}
            >
                <LayoutContainer as="header">
                    <Typography.Text
                        muted
                        fontSize="xx-small"
                        fontWeight="header"
                        emphasized
                    >
                        {attachmentCount || 0}{" "}
                        {pluralise(attachmentCount || 0, "Attachment", "Attachments")}
                    </Typography.Text>
                </LayoutContainer>
                <LayoutHorizontal
                    gap="medium"
                    spacing="small"
                >
                    <AttachmentsMetadata
                        disabled={disabled}
                        path={path}
                        value={value}
                        onChange={onChange}
                    />
                </LayoutHorizontal>

                {!disabled && !readOnly && (
                    <LayoutHorizontal
                        gap="medium"
                        spacing="small"
                        justifyContent="flex-end"
                    >
                        <UploadButton
                            onClick={handleAttachments}
                            multiple={multiple}
                        >
                            <AppIcon type="attachment" />
                            {buttonLabel ?? "Attach File"}
                        </UploadButton>
                    </LayoutHorizontal>
                )}
            </LayoutVertical>
        </LayoutContainer>
    );
};
