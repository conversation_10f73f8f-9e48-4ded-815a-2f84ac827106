import React, { ReactNode } from "react";

import { LayoutContainer, Image as ImageBase } from "PrimitiveComponents/index";

import type { ThumbnailProps } from "./types";

type ImageProps = {
    caption?: ReactNode;
} & ThumbnailProps;

export const Image = ({ metadata, caption }: ImageProps) => (
    <LayoutContainer className="thumbnail">
        <ImageBase
            src={metadata.url}
            alt={metadata.filename}
            captionDetail={caption}
            padding="none"
        />
    </LayoutContainer>
);
