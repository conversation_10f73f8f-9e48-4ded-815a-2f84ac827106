import { coerce } from "../coerce";

describe("coerce", () => {
    it("should coerce a string value to a primitive", () => {
        expect(coerce("42")).toStrictEqual(42);

        expect(coerce("42.123")).toStrictEqual(42.123);

        expect(coerce("42 not number")).toStrictEqual("42 not number");

        expect(coerce("not number 42")).toStrictEqual("not number 42");

        expect(coerce("a string")).toStrictEqual("a string");

        expect(coerce("true")).toStrictEqual(true);

        expect(coerce("True")).toStrictEqual(true);

        expect(coerce("TRUE")).toStrictEqual(true);

        expect(coerce("true with string")).toStrictEqual("true with string");

        expect(coerce("false")).toStrictEqual(false);

        expect(coerce("False")).toStrictEqual(false);

        expect(coerce("FALSE")).toStrictEqual(false);

        expect(coerce("false with string")).toStrictEqual("false with string");

        expect(coerce("null")).toStrictEqual(null);

        expect(coerce("Null")).toStrictEqual(null);

        expect(coerce("NULL")).toStrictEqual(null);

        expect(coerce("null with string")).toStrictEqual("null with string");

        expect(coerce("undefined")).toStrictEqual(undefined);

        expect(coerce("undefined")).toStrictEqual(undefined);

        expect(coerce("UNDEFINED")).toStrictEqual(undefined);

        expect(coerce("undefined with string")).toStrictEqual("undefined with string");

        expect(coerce(42)).toStrictEqual(42);

        expect(coerce({ a: 42 })).toStrictEqual({ a: 42 });
    });

    it("should coerce a string value to an object", () => {
        expect(coerce("{ 'a': 42 }")).toStrictEqual({ a: 42 });

        expect(coerce("  {     'a':   42 } ")).toStrictEqual({ a: 42 });

        expect(coerce("  {     INVALID   42 } ")).toStrictEqual("  {     INVALID   42 } ");
    });

    it("should coerce a string value to an array", () => {
        expect(coerce("[23, 'd']")).toStrictEqual([23, "d"]);

        expect(coerce("  [23, 'd'] ")).toStrictEqual([23, "d"]);

        expect(coerce("[23, { 'a': 42, 'b': 'd'}]")).toStrictEqual([23, { a: 42, b: "d" }]);

        expect(coerce("[23, { INVALIE: 42, 'b': 'd'}]")).toStrictEqual(
            "[23, { INVALIE: 42, 'b': 'd'}]"
        );
    });
});
