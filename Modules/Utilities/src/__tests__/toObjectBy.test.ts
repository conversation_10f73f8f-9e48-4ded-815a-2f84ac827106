import { toObjectByFn, toObjectByProp } from "../toObjectBy";

const mock = [
    { id: 123, bar: "a" },
    { id: 234, bar: "b" }
];

describe("toObjectBy", () => {
    it("toObjectByProp", () => {
        expect(toObjectByProp("id", mock)).toStrictEqual({
            123: { id: 123, bar: "a" },
            234: { id: 234, bar: "b" }
        });
        expect(toObjectByProp("bar", mock)).toStrictEqual({
            a: { id: 123, bar: "a" },
            b: { id: 234, bar: "b" }
        });
    });

    it("toObjectByFn", () => {
        expect(toObjectByFn(x => x.bar + x.id, mock)).toStrictEqual({
            a123: { id: 123, bar: "a" },
            b234: { id: 234, bar: "b" }
        });
    });
});
