import { parseFunctionString } from "../parseFunctionString";

describe("parseFunctionString", () => {
    it("should return undefined for empty input", () => {
        expect(parseFunctionString("")).toBeUndefined();
    });

    it("should return undefined for input without function call", () => {
        expect(parseFunctionString("some string")).toBeUndefined();
    });

    it("should return the function name and empty parameters for function with no parameters", () => {
        expect(parseFunctionString("@fn()")).toEqual({
            functionName: "fn",
            params: []
        });
    });

    it("should return the correct result for @fn(param1, param2)", () => {
        expect(parseFunctionString("@fn(param1, param2)")).toEqual({
            functionName: "fn",
            params: ["param1", "param2"]
        });
    });

    it("should return the correct result for @fn(1, 2.5, true, hello, equal(a.id, b.id))", () => {
        expect(parseFunctionString("@fn(1, 2.5, true, hello, equal(a.id, b.id))")).toEqual({
            functionName: "fn",
            params: [1, 2.5, true, "hello", "equal(a.id, b.id)"]
        });
    });

    it("should return the correct result for @fn(1, [1, 'a', true]", () => {
        const result = parseFunctionString("@fn(1, [1, 'a', true])");
        expect(result).toEqual({
            functionName: "fn",
            params: [1, [1, "a", true]]
        });
    });

    it("should return the correct result for @fn(1, filter(name, [1, 'a', true]))", () => {
        const result = parseFunctionString("@fn(1, filter(name, [1, 'a', true]))");
        expect(result).toEqual({
            functionName: "fn",
            params: [1, "filter(name, [1, 'a', true])"]
        });
    });

    it("should return the correct result for @fn(1, ' > ')", () => {
        const result = parseFunctionString("@fn(1, ' > ')");
        expect(result).toEqual({
            functionName: "fn",
            params: [1, " > "]
        });
    });

    it("should return the correct result for @fn(and(includes(datum.group, 'Industry'), equal(datum.group.length, 3)))", () => {
        const result = parseFunctionString(
            "@fn(and(includes(datum.group, 'Industry'), equal(datum.group.length, 3)))"
        );
        expect(result).toEqual({
            functionName: "fn",
            params: ["and(includes(datum.group, 'Industry'), equal(datum.group.length, 3))"]
        });
    });
});
