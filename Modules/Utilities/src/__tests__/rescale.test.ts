import { rescaleValue, rescaleRange } from "../rescale";

describe("rescale utils", () => {
    describe("rescaleValue", () => {
        it("should rescale the value from the input range to the output range", () => {
            expect(rescaleValue([0, 100], [0, 50], 50)).toStrictEqual(25);
            expect(rescaleValue([-100, 200], [0, 50], 50)).toStrictEqual(25);
            expect(rescaleValue([-400, -300], [0, 50], -350)).toStrictEqual(25);
            expect(rescaleValue([100, 0], [0, 50], 50)).toStrictEqual(25);
            expect(rescaleValue([200, -100], [0, 50], 50)).toStrictEqual(25);
            expect(rescaleValue([-300, -400], [0, 50], -350)).toStrictEqual(25);
            expect(rescaleValue([0, 100], [0, -50], 50)).toStrictEqual(-25);
            expect(rescaleValue([0, 100], [-100, -50], 50)).toStrictEqual(-75);
        });
    });

    describe("rescaleRange", () => {
        it("0.1, [0, 100]", () => {
            expect(rescaleRange(0.1, [0, 100])).toEqual([10, 90]);
        });
        it("0.1, [-100, 100]", () => {
            expect(rescaleRange(0.1, [-100, 100])).toEqual([-80, 80]);
        });
        it("0.1, [-150, 50]", () => {
            expect(rescaleRange(0.1, [-150, 50])).toEqual([-130, 30]);
        });
        it("0.1, [-190, 10]", () => {
            expect(rescaleRange(0.1, [-190, 10])).toEqual([-170, -10]);
        });
        it("0.1, [-400, -200]", () => {
            expect(rescaleRange(0.1, [-400, -200])).toEqual([-380, -220]);
        });
    });
});
