import type { RefObject } from "react";

import { NOTHING_TO_FLAG } from "../consts";

const isNumberType = (val: unknown): val is number => typeof val === "number";
const isStringType = (val: unknown): val is string => typeof val === "string";
const isBoolean = (val: unknown): val is boolean => typeof val === "boolean";
const isUndefined = (val: unknown): val is undefined => typeof val === "undefined";
const isDefined = <T>(x: T | undefined): x is T => typeof x !== "undefined";
const isNull = (val: unknown): val is null => val === null;
const isNullish = value =>
    value?.trim?.() === "" ||
    typeof value === "undefined" ||
    value === "NULL" ||
    value == null ||
    value === NOTHING_TO_FLAG;

const isObject = <T extends Record<string, unknown>>(
    x: T | string | number | boolean | undefined | null | unknown,
): x is T => typeof x === "object" && x !== null;
const isArray = (x: unknown): x is unknown[] => Array.isArray(x);
const isFunction = (val: unknown): val is (args?: unknown) => unknown => typeof val === "function";
const isPromise = (o: any): o is Promise<any> => o && o.then && o.catch;
const isError = (o: any): o is Error => o && o.name && o.message;
const isRefObject = (o: any): o is RefObject<unknown> =>
    o && Object.prototype.hasOwnProperty.call(o, "current");
const isEmptyObject = (o: any): boolean => Object.keys(o).length === 0;
const isEmptyArray = (array: any): boolean => !array?.length;
const isNonEmptyArray = (array: any): boolean => !isEmptyArray(array);
const isNonEmptyObject = (o: any): boolean => !isEmptyObject(o);

export {
    isNumberType,
    isStringType,
    isBoolean,
    isUndefined,
    isFunction,
    isNull,
    isNullish,
    isDefined,
    isPromise,
    isError,
    isObject,
    isArray,
    isRefObject,
    isEmptyObject,
    isEmptyArray,
    isNonEmptyArray,
    isNonEmptyObject,
};
