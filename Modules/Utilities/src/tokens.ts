import { camelCase } from "lodash";

import type { RaidFilterType, RaidFilters } from "Contracts/index";

export const tokenToFilter = (token: string): RaidFilterType => {
    const normalizedToken = camelCase(token);

    switch (normalizedToken) {
        case "trader":
            return "traderInitials";
        default:
            return normalizedToken as RaidFilterType;
    }
};

export const tokensToFilters = (token: string | string[]): RaidFilterType | RaidFilterType[] =>
    Array.isArray(token) ? token.map(tokenToFilter) : tokenToFilter(token);

/**
 * Map an object to `RaidFilters`
 */
export const objectToFilters = (o: Record<string, unknown>): RaidFilters =>
    Object.entries(o || {}).reduce(
        (tokens, [name, value]) => ({
            ...tokens,
            [tokenToFilter(name)]: value
        }),
        {}
    );
