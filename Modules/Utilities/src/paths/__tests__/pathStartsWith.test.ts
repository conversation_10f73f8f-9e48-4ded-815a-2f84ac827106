import { pathStartsWith } from "../pathStartsWith";

describe("pathStartsWith", () => {
    it("should test path starts with path fragment", async () => {
        expect(pathStartsWith("", "a")).toEqual(false);
        expect(pathStartsWith("", "")).toEqual(true);
        expect(pathStartsWith("a.b.c", "a")).toEqual(true);
        expect(pathStartsWith("a.b.c", "a.b")).toEqual(true);
        expect(pathStartsWith("a.b.c", "a.b.c")).toEqual(true);
    });

    it("should test path starts with path fragment and partial path part", async () => {
        expect(pathStartsWith("a.b.cprop", "a")).toEqual(true);
        expect(pathStartsWith("a.b.cprop", "a.b")).toEqual(true);
        expect(pathStartsWith("a.b.cprop", "a.b.c")).toEqual(false);
        expect(pathStartsWith("a.b.cprop", "a.b.cp")).toEqual(false);
        expect(pathStartsWith("a.b.cprop", "a.b.cprop")).toEqual(true);
    });

    it("should test path does not start with path fragment", async () => {
        expect(pathStartsWith("a.b.c", "b")).toEqual(false);
        expect(pathStartsWith("a.b.c", "b.c")).toEqual(false);
        expect(pathStartsWith("a.b.c", "b.c.d")).toEqual(false);
    });

    it("should test path with empty or nullish path parts", async () => {
        expect(pathStartsWith(undefined, "")).toEqual(true);
        expect(pathStartsWith(undefined, "b")).toEqual(false);
        expect(pathStartsWith(undefined, undefined)).toEqual(false);
        expect(pathStartsWith(undefined, null)).toEqual(false);

        expect(pathStartsWith(null, "")).toEqual(true);
        expect(pathStartsWith(null, "b")).toEqual(false);
        expect(pathStartsWith(null, null)).toEqual(false);
        expect(pathStartsWith(null, undefined)).toEqual(false);

        expect(pathStartsWith("a.b.c", null)).toEqual(false);
        expect(pathStartsWith("a.b.c", undefined)).toEqual(false);
        expect(pathStartsWith("a.b.c", "")).toEqual(true);
    });
});
