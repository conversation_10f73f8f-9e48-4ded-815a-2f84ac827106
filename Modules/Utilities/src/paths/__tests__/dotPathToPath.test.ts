import { dotPathToPath } from "../dotPathToPath";

describe("dotPathToPath", () => {
    it("should correctly create absolute path", () => {
        expect(dotPathToPath(undefined)).toStrictEqual([]);

        expect(dotPathToPath("d.e.f")).toStrictEqual(["d", "e", "f"]);

        expect(dotPathToPath("d.[a=3].f")).toStrictEqual(["d", "[a=3]", "f"]);

        expect(dotPathToPath("d.[a=3/2].f")).toStrictEqual(["d", "[a=3/2]", "f"]);

        expect(dotPathToPath("d.[a=3/2.1].f")).toStrictEqual(["d", "[a=3/2.1]", "f"]);

        expect(dotPathToPath("d.[a=3/2/1].f")).toStrictEqual(["d", "[a=3/2/1]", "f"]);

        expect(dotPathToPath("d.[a=3.2].f")).toStrictEqual(["d", "[a=3.2]", "f"]);

        expect(dotPathToPath("d.[a=3.2.1].f")).toStrictEqual(["d", "[a=3.2.1]", "f"]);

        expect(dotPathToPath("d.[a=3.2/1].f")).toStrictEqual(["d", "[a=3.2/1]", "f"]);
    });
});
