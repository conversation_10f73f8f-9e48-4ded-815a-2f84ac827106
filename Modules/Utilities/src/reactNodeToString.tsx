import React, { ReactNode } from "react";
import { renderToStaticMarkup } from "react-dom/server";

import { sanitizeHtml } from "./sanitizeHtml";

export const reactNodeToString = (el?: ReactNode) => {
    try {
        return (
            (el && sanitizeHtml(renderToStaticMarkup(<>{el}</>), { allowedTags: [] })) || undefined
        );
    } catch (ex) {
        console.error(ex);
        return "";
    }
};
