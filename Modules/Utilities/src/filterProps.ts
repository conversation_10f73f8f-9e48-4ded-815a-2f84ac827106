import { omit, pickBy } from "lodash";

const validHTMLElementProps = [
    ...Object.keys(Element.prototype),
    ...Object.keys(HTMLElement.prototype)
].reduce((a, v) => ({ ...a, [v.toLowerCase()]: v }), {});

const validHTMLInputElementProps = omit(
    {
        ...validHTMLElementProps,
        ...[...Object.keys(HTMLInputElement.prototype)].reduce(
            (a, v) => ({ ...a, [v.toLowerCase()]: v }),
            {}
        )
    },
    "children"
);

export const filterValidHTMLInputElementProps = <P extends {}>(props: P) =>
    props &&
    (pickBy(
        props,
        (value, key) => key.startsWith("data-") || validHTMLInputElementProps[key.toLowerCase()]
    ) as P);

const validHTMLTextAreaElementProps = {
    ...validHTMLElementProps,
    ...[...Object.keys(HTMLTextAreaElement.prototype)].reduce(
        (a, v) => ({ ...a, [v.toLowerCase()]: v }),
        {}
    )
};

export const filterValidHTMLTextAreaElementProps = <P extends {}>(props: P) =>
    props &&
    (pickBy(
        props,
        (value, key) => key.startsWith("data-") || validHTMLTextAreaElementProps[key.toLowerCase()]
    ) as P);

const validHTMLButtonProps = {
    ...validHTMLElementProps,
    ...[...Object.keys(HTMLButtonElement.prototype)].reduce(
        (a, v) => ({ ...a, [v.toLowerCase()]: v }),
        {}
    )
};

export const filterValidHTMLButtonProps = <P extends {}>(props: P) =>
    props &&
    (pickBy(
        props,
        (value, key) => key.startsWith("data-") || validHTMLButtonProps[key.toLowerCase()]
    ) as P);
