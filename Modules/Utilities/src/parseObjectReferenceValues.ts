import { parseReferenceValues } from "./parseReferenceValues";
import type { DotPath } from "./paths";

export const parseObjectReferenceValues = <
    F extends (value: unknown, ...args: unknown[]) => unknown = (
        value: unknown,
        ...args: unknown[]
    ) => unknown
>(
    value: unknown,
    data: unknown,
    context?: unknown,
    basePath?: DotPath,
    formatter?: F
) => {
    if (Array.isArray(value)) {
        return value.map(item => {
            return parseObjectReferenceValues(item, data, context, basePath, formatter);
        });
    }

    if (value !== null && typeof value === "object") {
        return Object.entries(value).reduce((o, [name, itemValue]) => {
            return {
                ...o,
                [name]: parseObjectReferenceValues(itemValue, data, context, basePath, formatter)
            };
        }, {});
    }

    if (typeof value === "string") {
        return parseReferenceValues(value, data, context, basePath, formatter);
    }

    return value;
};
