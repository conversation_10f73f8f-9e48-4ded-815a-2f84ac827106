import { pad<PERSON><PERSON> } from "../pad<PERSON>ero";

const cases = [
    ["e", undefined, "0e"],
    ["#efefef", undefined, "ef"],
    ["fff", 3, "fff"],
    ["ef", 3, "0ef"]
];

describe("padZero", () => {
    it.each(cases)(
        "should give correct padding %s",
        (value: string, length: number, result: string) => {
            expect(padZero(value, length)).toEqual(result);
        }
    );
});
