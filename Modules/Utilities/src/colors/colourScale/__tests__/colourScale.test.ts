import { rgb, type RGBColor } from "d3-color";
import { rgbaValues, colourScaleRange } from "@test/mocks";
import { colourScale } from "../colourScale";

const cases: [number, RGBColor][] = colourScaleRange.map(value => [
    value,
    rgbaValues[value] as RGBColor
]);

describe("colourScale", () => {
    it.each(cases)("should give correct rgb values for %s", (value: number, expected) => {
        const result = colourScale(value) as RGBColor;
        const rgbExpected = rgb(expected.r, expected.g, expected.b).toString();
        const rgbResult = rgb(result.r, result.g, result.b).toString();
        expect(rgbResult).toEqual(rgbExpected);
    });
});
