export const rescaleValue = (
    inputRange: [number, number],
    outputRange: [number, number],
    value: number
): number => {
    const [inputMin, inputMax] = inputRange;
    const [outputMin, outputMax] = outputRange;
    const inputDiff = inputMax - inputMin;
    const outputDiff = outputMax - outputMin;
    return ((value - inputMin) * outputDiff) / inputDiff + outputMin;
};

export const rescaleRange = (ratio: number, range: [number, number]): [number, number] => {
    const [start, end] = range;
    const diff = (end - start) * ratio;
    return [start + diff, end - diff];
};
