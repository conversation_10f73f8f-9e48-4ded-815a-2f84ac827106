import type { Section } from "Contracts/index";

export const findSectionByUrl = <T extends Section>(url: T["url"], sections: T[]): T[] => {
    // Normalise the section using URL to make sure paths will correctly match
    const normalizedUrl = new URL(url, window.location.origin).pathname;
    const foundSection = sections?.find(
        section => new URL(section.url, window.location.origin).pathname === normalizedUrl
    );

    if (foundSection) {
        return [foundSection];
    }

    return sections?.reduce((breadcrumb, section) => {
        const foundSubSection = findSectionByUrl(url, section?.sections);
        if (foundSubSection?.length > 0) {
            return [...breadcrumb, section, ...foundSubSection];
        }
        return breadcrumb;
    }, []);
};
