import type { DatePreset, DateFrameValues } from "Contracts/index";

export const dateFrameToDatePresetMap: Partial<{
    [key in DateFrameValues]: DatePreset;
}> = {
    2001: "1D",
    2002: "2D",
    2003: "3D",
    2004: "4D",
    2005: "5D",
    2007: "1W",
    1: "1M",
    3: "3M",
    6: "6M",
    12: "12M",
    24: "24M",
};

export const datePresetToDateFrame = Object.entries(dateFrameToDatePresetMap).reduce(
    (acc, [key, value]) => {
        acc[value as any] = key;
        return acc;
    },
    {} as { [key in DatePreset]: DateFrameValues },
);
