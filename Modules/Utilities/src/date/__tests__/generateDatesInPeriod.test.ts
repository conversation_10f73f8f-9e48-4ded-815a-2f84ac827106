import { generateDatesInPeriod } from "../generateDatesInPeriod";
import type { Period } from "../periodToMilliseconds";

const testCases = [
    ["today", "yyyy-MM-dd", "1D", true],
    ["yesterday", "yyyy-MM-dd", "7D", false],
    ["today", "MM/dd/yyyy", "1M", true]
] as ["today" | "yesterday", string, Period, boolean][];

describe("generateDatesInPeriod", () => {
    test.each(testCases)(
        'should generate dates correctly for generateDatesInPeriod("%s", "%s", "%s", %s)',
        (endPoint, dateFormat, interval, omitWeekends) => {
            const result = generateDatesInPeriod(
                endPoint,
                dateFormat,
                interval,
                "1M",
                omitWeekends
            );
            expect(result).toBeDefined();
            expect(Array.isArray(result)).toBe(true);
            expect(result.every(d => typeof d === "string")).toBe(true);
        }
    );

    test('should handle "today" and "yesterday" correctly', () => {
        const result1 = generateDatesInPeriod("today", "MM/dd/yyyy", "1D", "1D", false);
        const result2 = generateDatesInPeriod("yesterday", "MM/dd/yyyy", "1D", "1D", false);
        expect(result1[0]).not.toBe(result2[0]);
    });

    test("should return empty array for invalid interval", () => {
        const result = generateDatesInPeriod("today", "yyyy-MM-dd", "invalid" as any, "1M", false);
        expect(result).toEqual([]);
    });

    test("should handle omitting weekends correctly", () => {
        const result = generateDatesInPeriod("today", "yyyy-MM-dd", "1D", "1W", true);
        expect(result.length).toBe(5);
        result.forEach(dateString => {
            const date = new Date(dateString);
            expect(date.getDay()).not.toBe(0);
            expect(date.getDay()).not.toBe(6);
        });
    });

    describe("with Date mocked", () => {
        const OriginalDate = global.Date;

        beforeAll(() => {
            const mockedDate = new OriginalDate("2023-11-01");
            global.Date = function () {
                return mockedDate;
            } as any;
            global.Date.UTC = OriginalDate.UTC;
            global.Date.now = OriginalDate.now;
        });

        afterAll(() => {
            global.Date = OriginalDate;
        });

        test("should format dates correctly", () => {
            const result = generateDatesInPeriod("today", "MM/yyyy/dd", "1D", "1D", false);
            expect(result[0]).toBe("11/2023/01");
        });

        test("should return correct number of intervals", () => {
            const result = generateDatesInPeriod("today", "yyyy-MM-dd", "1D", "1W", false);
            expect(result.length).toBe(7);
        });
    });
});
