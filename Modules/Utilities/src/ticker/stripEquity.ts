import { tickerTypes } from "./tickerTypes";

export const stripEquity = (instrId: string) => {
    if (!instrId || !instrId.toLowerCase) return instrId;

    const lower = instrId.toLowerCase();
    const postfix = tickerTypes.find(t => lower.includes(t.toLowerCase()));
    if (postfix === undefined) return instrId;

    const index = lower.indexOf(` ${postfix.toLowerCase()}`);

    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/substr
    // eslint-disable-next-line no-bitwise
    return ~index ? instrId.slice(0, index) : instrId;
};
