import type { CSSDividerType } from "./types";

export const CSSDivider = (type: CSSDividerType = "none"): string => {
    switch (type) {
        case "default": {
            return "1px solid var(--border__color--separator)";
        }
        case "section": {
            return "1px dashed var(--border__color--separator)";
        }
        case "secondary": {
            return "1px solid var(--border__color--outline)";
        }
        case "tertiary": {
            return "1px solid var(--border__color--muted)";
        }
        default: {
            return "";
        }
    }
};

export const hasCSSDivider = (divider: CSSDividerType) =>
    typeof divider === "string" && divider !== "none";
