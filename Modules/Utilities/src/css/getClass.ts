import type { Size } from "./types";

/**
 * Get the class name for a button size (class version of CSSButtonSize)
 */
export const getButtonSizeClass = (size?: Size) => {
    switch (size) {
        case "x-small":
            return "text-btn-xs";
        case "small":
        case "smaller":
            return "text-btn-sm";
        case "large":
        case "larger":
            return "text-btn-lg";
        case "x-large":
            return "text-btn-xl";
        case "default":
            return "text-btn-md";
        default:
            return undefined;
    }
};
