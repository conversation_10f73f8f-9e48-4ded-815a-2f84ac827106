import { cssSpacingSize } from "@test/mocks";
import { CSSGap } from "../cssGap";

const defaultPx = {
    [cssSpacingSize.sm]: "4px",
    [cssSpacingSize.md]: "6px",
    [cssSpacingSize.lg]: "10px",
    [cssSpacingSize.xl]: "20px",
    [cssSpacingSize.xxl]: "25px",
    [cssSpacingSize.xxxl]: "30px"
};

const cases = Object.values(cssSpacingSize);

describe("cssGap", () => {
    it.each(cases)("should give correct css var for gap %s", gap => {
        expect(CSSGap({ gap })).toContain(
            `gap: ${
                gap !== cssSpacingSize.none ? `var(--spacing--${gap}, ${defaultPx[gap]})` : "0px"
            }`
        );
    });

    it.each(cases)("should give correct css var for row gap %s", gap => {
        const action = CSSGap({ rowGap: gap });
        const result = `${
            gap !== cssSpacingSize.none ? `var(--spacing--${gap}, ${defaultPx[gap]})` : "0px"
        }`;

        expect(action).toContain(`row-gap: ${result};`);
    });

    it.each(cases)("should give correct css var for column gap %s", gap => {
        const action = CSSGap({ columnGap: gap });
        const result = `${
            gap !== cssSpacingSize.none ? `var(--spacing--${gap}, ${defaultPx[gap]})` : "0px"
        }`;

        expect(action).toContain(`column-gap: ${result};`);
    });

    it("should return an empty string if no gap specified", () => {
        const action = CSSGap({});
        expect(action).toEqual("");
    });
});
