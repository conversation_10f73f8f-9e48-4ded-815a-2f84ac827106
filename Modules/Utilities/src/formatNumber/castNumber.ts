import { isNumberType, isStringType } from "../isType";

export const castNumber = (value: unknown) => {
    const castedValue = Number(value);
    const isNumber =
        // value is already a number
        (isNumberType(value) && !Number.isNaN(value)) ||
        // check if value is a string that can be parsed to a number
        (isNumberType(castedValue) &&
            !Number.isNaN(castedValue) &&
            isStringType(value) &&
            value.trim() !== "");
    return {
        isNumber,
        castedValue
    };
};
