/**
 * @TODO: this should really be in a more appropriate place but this is currently the only sensible place to put it
 * to avoid circular deps to expose these utility functions...
 */
import type { IconType } from "Theming/index";

import type { NotificationCategoryType } from "../workflow/notifications";

const mapGroupTypeToIcon: Partial<{
    [categoryType in NotificationCategoryType]: IconType;
}> = {
    "Portfolio Construction & Market Awareness": "portfolio",
    "Sourcing & Ideation": "idea",
    "Trading/Jockeying": "jockeying",
    Indicator: "indicator",
    Risk: "risk",
    Process: "process",
    Commentary: "macroObservation",
    Information: "info"
};

export const getNotificationIconType = (categoryType: NotificationCategoryType): IconType =>
    (categoryType && mapGroupTypeToIcon[categoryType]) || undefined;

const mapColor: Partial<{
    [categoryType in NotificationCategoryType]: {
        primary: string;
        secondary: string;
        tertiary: string;
        color: string;
    };
}> = {
    "Portfolio Construction & Market Awareness": {
        primary: "#3E43A7",
        secondary: "#121549",
        tertiary: "#0c0e2b",
        color: "#fff"
    },
    "Sourcing & Ideation": {
        primary: "#3E43A7",
        secondary: "#121549",
        tertiary: "#0c0e2b",
        color: "#fff"
    },
    "Trading/Jockeying": {
        primary: "#3E43A7",
        secondary: "#121549",
        tertiary: "#0c0e2b",
        color: "#fff"
    },
    Indicator: {
        primary: "#3E43A7",
        secondary: "#121549",
        tertiary: "#0c0e2b",
        color: "#fff"
    },
    Risk: {
        primary: "#9D383E",
        secondary: "unset", // @TODO: get correct color?
        tertiary: "unset", // @TODO: get correct color?
        color: "#fff"
    },
    Process: {
        primary: "#D18A13",
        secondary: "unset", // @TODO: get correct color?
        tertiary: "unset", // @TODO: get correct color?
        color: "#fff"
    },
    Commentary: {
        primary: "#70389D",
        secondary: "unset", // @TODO: get correct color?
        tertiary: "unset", // @TODO: get correct color?
        color: "#fff"
    },
    Information: {
        primary: "#197FB6",
        secondary: "#0e5175",
        tertiary: "#083046",
        color: "#fff"
    }
};

export const getNotificationAccentColors = (categoryType: NotificationCategoryType) =>
    (categoryType && mapColor[categoryType]) || undefined;
