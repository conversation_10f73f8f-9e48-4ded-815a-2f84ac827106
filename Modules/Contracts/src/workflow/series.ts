import { Token } from "./token";

export type SeriesType<D extends Record<string, unknown>, T extends string | Token<unknown>> = {
    type: "token";

    id: string;

    token: T;

    definition: D;
};

export type SeriesGroupType<
    D extends Record<string, unknown>,
    T extends string | Token<unknown>,
    G extends Record<string, unknown> = Record<string, unknown>
> =
    | SeriesType<D, T>
    | ({
          type: "group";

          id: string;

          group: string;

          seriesCollection: SeriesGroupType<D, T>[];
      } & G);

export type SeriesDefinition<
    P extends Record<string, unknown>,
    T extends string | Token<unknown>,
    D extends Record<string, unknown>,
    S = SeriesType<D, T>
> = {
    dataSource: number;
    seriesCollection?: S[];
} & P;
