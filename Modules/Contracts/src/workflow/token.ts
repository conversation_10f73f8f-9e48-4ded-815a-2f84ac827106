import { ContentType } from "../Schema";

export type TokenParamsDefinitionConfig<T = unknown> = {
    props?: {
        isClearable?: boolean;
        periods?: number[];
        options?: {
            label: string;
            value: T;
        }[];
    };
};

export type TokenParamsDefinition<T = unknown> = {
    name: string;
    contentType: string;
    default?: T;
    required?: boolean;
    config?: TokenParamsDefinitionConfig<T>;
};

export type Token<T> = {
    id: string;

    category?: string;
    subCategory?: string;

    valueFormat?: T;
    contentType?: ContentType;

    /**
     * Description of parameters available for this token
     */
    paramsDefinition?: TokenParamsDefinition[];
    lastUpdated?: string;
};

export type TokenParamsValues = Record<string, unknown>;
