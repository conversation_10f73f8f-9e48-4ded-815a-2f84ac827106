import { RowNode } from "ag-grid-community";

import { isElementFocusable } from "Utilities/index";

const { selectThisNode } = RowNode.prototype;

/**
 * Wrap ag-grid `selectThisNode` so we can determine if the row should be selected on a focusable element (e.g. button)
 */
RowNode.prototype.selectThisNode = function selectThisNodeOverride(newValue, e) {
    // this version of ag-grid  performs the _actual_ row selection _before_ the cell click handler
    // so we have no chance of preventing default. so instead we handle it here -
    if (newValue && e?.target) {
        // if the target is focusable then return not selectable
        if (isElementFocusable(e.target as HTMLElement)) {
            return false;
        }

        // if the target _contains_ a focusable element then return not selectable - this for the case where a focusable element
        // has been wrapped by e.g. `SimpleLoader` to provide a disabled state
        const siblings = (e.target as HTMLElement).querySelectorAll("*");
        if (siblings && [...siblings].some(isElementFocusable)) {
            return false;
        }
    }

    // otherwise defer to default ag-grid behaviour
    return selectThisNode.call(this, newValue, e);
};
