import { ModuleRegistry } from "ag-grid-community";

/**
 * By default ag-grid agSetColumnFilter clears the search query when the dataset updates. Override this behaviour
 * and keep the current query using ```gridOptions.disableResetFloatingFilterQueryOnDataChange```.
 */
export const setFilterModule = ModuleRegistry.getRegisteredModules().find(
    ({ moduleName }) => moduleName === "@ag-grid-enterprise/set-filter"
);

if (setFilterModule) {
    const setColumnFilterComponent = setFilterModule.userComponents.find(
        ({ componentName }) => componentName === "agSetColumnFilter"
    );

    if (setColumnFilterComponent) {
        setColumnFilterComponent.componentClass.prototype.resetUiToDefaults =
            function resetUiToDefaults() {
                if (
                    !this.gridOptionsWrapper.gridOptions.defaultColDef.filterParams
                        .disableResetFloatingFilterQueryOnDataChange
                ) {
                    this.setMiniFilter(null);
                }

                return this.valueModel.setModel(null, true);
            };
    }
}
