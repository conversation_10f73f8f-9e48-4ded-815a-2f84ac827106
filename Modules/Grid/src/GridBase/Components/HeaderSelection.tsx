import React, { forwardRef, useEffect, useImperativeHandle, useState, ReactElement } from "react";
import { Events } from "ag-grid-community";
import styled from "styled-components";

type HeaderSelectionChildProps = {
    selectedRows: any[];
};

type HeaderSelectionProps = {
    api: any;
    children: (props: HeaderSelectionChildProps) => ReactElement;
};

const Container = styled.div`
    display: flex;
    justify-content: center;
`;

export const HeaderSelection = forwardRef(function HeaderSelection(
    { api, children }: HeaderSelectionProps,
    ref
) {
    const [selectedRows, setSelectedRows] = useState<any[]>([]);

    useImperativeHandle(ref, () => {
        return {
            isFilterActive() {
                return true;
            }
        };
    });

    const handleSelectionChanged = ({ api: actions }) => setSelectedRows(actions.getSelectedRows());

    useEffect(() => {
        api.addEventListener(Events.EVENT_SELECTION_CHANGED, handleSelectionChanged);

        return () => {
            api.removeEventListener(Events.EVENT_SELECTION_CHANGED, handleSelectionChanged);
        };
    }, [api]);

    return <Container>{children({ selectedRows })}</Container>;
});
