import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import { Theme<PERSON>ontainer, ThemeIterator, LayoutVertical, LayoutContainer } from "PrimitiveComponents/index";
import { Grid } from "../../Grid";

import fixtureData from "./fixtures/data.json";
import fixtureSchema from "./fixtures/schema.json";


<Meta title="Grid/Grid" component={Grid} />

export const Template = ({ transpose }) => (
    <LayoutVertical spacing="xx-large">
        <LayoutContainer>
            <h3>The "transpose" flag is set on the schema of the column that you want to transpose.
            See <a href="https://github.com/firefly-trading-labs/Firefly.Backbone.JS.Client/blob/master/Modules/Grid/src/Components/Grid/stories/GridTransposed/fixtures/schema.json">./fixtures/schema.json</a></h3>
            <h3>Here we are using #context within the schema so we can toggle the "transpose" flag on and off.</h3>
        </LayoutContainer>
        <ThemeIterator>
            {() => (
                <ThemeContainer style={{ height: 160, width: 600 }} flex>
                    <Grid
                        data={fixtureData}
                        schema={fixtureSchema}
                        context={{transpose}}
                    />
                </ThemeContainer>
            )}
        </ThemeIterator>
    </LayoutVertical>
);

# Grid

<Description of={Grid} />

<Canvas>
    <Story
        name="Grid Transposed"
        parameters={{
            controls: {
                disable: false,
                include: ["transpose"]
            }
        }}
        argTypes={{
            transpose: {
                type: "boolean"
            }
        }}
        args={{
            transpose: false
        }}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes of={Grid} sort="requiredFirst" />
