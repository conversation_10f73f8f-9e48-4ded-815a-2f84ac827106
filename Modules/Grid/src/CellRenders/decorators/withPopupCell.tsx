import React, { ComponentType, forwardRef, useImperative<PERSON>andle } from "react";

import { ICellRendererSchemaParams } from "../../types";

export const withPopupCell = <P extends ICellRendererSchemaParams>(Component: ComponentType<P>) => {
    const WrappedComponent = forwardRef<any, P>((props, ref) => {
        const { column, node } = props;

        const isPopup = () => true;
        const getValue = () => node.data[column.getColId()];

        useImperativeHandle(ref, () => ({ isPopup, getValue }));

        const handleUpdateValue = value =>
            node.setData({
                ...node.data,
                [column.getColId()]: value
            });

        const handleUpdateRowData = data => node.setData(data);

        return (
            <div style={{ minWidth: column.getActualWidth() }}>
                <Component
                    updateValue={handleUpdateValue}
                    updateRowData={handleUpdateRowData}
                    {...props}
                    {...props.api?.gridOptionsWrapper?.gridOptions}
                />
            </div>
        );
    });

    WrappedComponent.displayName = `withPopupCell(${
        Component.displayName || Component.name || "<unknown>"
    })`;

    return WrappedComponent;
};
