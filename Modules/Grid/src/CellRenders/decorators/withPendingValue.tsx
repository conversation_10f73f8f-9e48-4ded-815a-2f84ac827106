import React, { ComponentType, useMemo } from "react";

import { ICellRendererParams } from "../../GridBase";
import { useMetadata } from "../../metadata";

export const withPendingValue = (Component: ComponentType<ICellRendererParams>) => {
    const WrappedComponent = (props: ICellRendererParams) => {
        const { colDef, node, formatValue } = props;
        const { getMetadata } = useMetadata();
        const { pendingValue } = getMetadata(colDef.field, node);

        const valueProps = useMemo(
            () =>
                pendingValue !== undefined && {
                    value: pendingValue,
                    valueFormatted: formatValue(pendingValue),
                    getValue: () => pendingValue
                },
            [formatValue, pendingValue]
        );

        return (
            <Component
                {...props}
                {...valueProps}
            />
        );
    };

    WrappedComponent.displayName = `withPendingValue(${
        Component.displayName || Component.name || "<unknown>"
    })`;

    return WrappedComponent;
};
