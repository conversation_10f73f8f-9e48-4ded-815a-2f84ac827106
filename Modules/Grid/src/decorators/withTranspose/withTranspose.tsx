import React, { ComponentType, Ref, useMemo } from "react";

import type { Schema } from "Contracts/index";
import { parseReferenceValues } from "Utilities/index";

import { GridBaseProps } from "../../GridBase";

import { transposeData } from "./transposeData";
import { transposeSchema } from "./transposeSchema";

type Props<D extends Record<string, unknown>> = {
    schema?: Schema;
    ref?: Ref<ComponentType<D>>;
} & GridBaseProps<D>;

export const withTranspose = <D extends Record<string, unknown>, P extends Props<D>>(
    Component: ComponentType<P>
) => {
    const WrappedComponent = ({ data, schema, context, ref, ...rest }: P) => {
        const transposeField = useMemo(() => {
            const schemaToTranspose = schema?.find(s => s?.metaData?.properties?.transpose);
            const isTransposed =
                schemaToTranspose &&
                parseReferenceValues(
                    schemaToTranspose?.metaData?.properties?.transpose as string,
                    data,
                    context
                );
            return isTransposed && schemaToTranspose?.name;
        }, [context, data, schema]);

        const transposeValues = useMemo(() => {
            if (transposeField && data?.length) {
                return data.map(d => d[transposeField] as string);
            }
            return undefined;
        }, [data, transposeField]);

        const transposedData = useMemo(() => {
            if (transposeField && data?.length && schema?.length) {
                return transposeData(data, transposeField, schema);
            }
            return undefined;
        }, [data, schema, transposeField]);

        const transposedSchema = useMemo(() => {
            if (transposeField && schema?.length && transposeValues?.length) {
                return transposeSchema(schema, transposeField, transposeValues);
            }
            return undefined;
        }, [schema, transposeValues, transposeField]);

        return (
            <Component
                {...(rest as P)}
                ref={ref}
                context={context}
                schema={transposedSchema || schema}
                data={transposedData || data}
            />
        );
    };

    WrappedComponent.displayName = `withTranspose(${
        Component.displayName || Component.name || "<unknown>"
    })`;

    return WrappedComponent;
};
