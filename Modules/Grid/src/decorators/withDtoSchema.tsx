import React, { ComponentType, createContext, forwardRef, Ref, useContext, useMemo } from "react";
import { uniq } from "lodash";

import type { Schema } from "Contracts/index";

import { type GridBaseProps } from "../GridBase";
import {
    type ColDefs,
    flattenColDefs,
    createColumnDefFromSchema,
    type ColDef
} from "../GridSchema";

const SchemaContext = createContext<Schema>(undefined);

export type WithDtoSchemaProps<D extends Record<string, unknown>> = {
    schema?: Schema;
    columnDefs?: ColDefs;
} & Pick<GridBaseProps<D>, "components">;

export const withDtoSchema = <D extends Record<string, unknown>, P extends GridBaseProps<D>>(
    Grid: ComponentType<P & WithDtoSchemaProps<D>>
) => {
    const WrappedComponent = forwardRef(
        (
            {
                schema,
                columnDefs,
                onCellValueChanged,
                hasGrouping,
                ...props
            }: P & WithDtoSchemaProps<D>,
            ref: Ref<ComponentType<GridBaseProps>>
        ) => {
            if (schema && !Array.isArray(schema)) {
                throw new TypeError(
                    `Invalid schema passed to Grid. Expected schema array, got ${typeof schema}.`
                );
            }

            const mappedColumnDefs = useMemo(() => {
                const mapColumnDefs = (schema: Schema, level = 0): ColDef[] => {
                    return (
                        (Array.isArray(schema) &&
                            schema
                                ?.map(col => {
                                    if (col.schema && hasGrouping === "nested") {
                                        const children = mapColumnDefs(col.schema, level + 1);

                                        const allGroupColumnShow = uniq(
                                            children?.map(col => col.columnGroupShow || undefined)
                                        );
                                        const addDefaultGroupColumn =
                                            level === 0 &&
                                            allGroupColumnShow.length === 1 &&
                                            children?.length > 0;

                                        return {
                                            headerName: col.metaData?.displayName || col.name,
                                            openByDefault:
                                                col.metaData?.properties
                                                    ?.columnGroupOpenByDefault ?? true,
                                            groupId: col.columnId,
                                            columnGroupShow: "open",
                                            order:
                                                typeof col.metaData?.properties?.order === "number"
                                                    ? col.metaData?.properties?.order
                                                    : parseInt(col.metaData?.properties?.order, 10),
                                            children: [
                                                addDefaultGroupColumn &&
                                                    ({
                                                        headerName: "",
                                                        autoHeaderHeight: true,
                                                        width: 100,
                                                        minWidth: 100,
                                                        maxWidth: 100,
                                                        columnGroupShow:
                                                            allGroupColumnShow?.[0] === "open"
                                                                ? "closed"
                                                                : undefined
                                                    } as ColDef),
                                                ...(children || [])
                                            ].filter(col => col)
                                        } as ColDef;
                                    }

                                    return createColumnDefFromSchema(col);
                                })
                                .sort((a, b) => {
                                    if (a.order === undefined || a.order === null) {
                                        return 1;
                                    }
                                    if (b.order === undefined || b.order === null) {
                                        return -1;
                                    }
                                    return (a.order ?? 0) - (b.order ?? 0);
                                })) ||
                        undefined
                    );
                };

                return mapColumnDefs(schema);
            }, [hasGrouping, schema]);

            const allColumnDefs = useMemo(() => flattenColDefs(columnDefs), [columnDefs]);
            const multiLineHeader = useMemo(
                () => allColumnDefs?.some(({ multiLineHeader: mLHeader }) => mLHeader),
                [allColumnDefs]
            );
            const floatingFilter = useMemo(
                () => props.floatingFilter ?? allColumnDefs?.some(({ filter }) => filter),
                [allColumnDefs, props.floatingFilter]
            );
            return (
                <SchemaContext.Provider value={schema}>
                    <Grid
                        {...(props as P)}
                        ref={ref}
                        columnDefs={mappedColumnDefs ?? columnDefs}
                        schema={schema}
                        floatingFilter={floatingFilter}
                        multiLineHeader={multiLineHeader}
                        hasGrouping={hasGrouping}
                    />
                </SchemaContext.Provider>
            );
        }
    );

    WrappedComponent.displayName = `withDtoSchema(${Grid.displayName || Grid.name || "<unknown>"})`;

    return WrappedComponent;
};

export const useGridSchema = () => useContext(SchemaContext);
