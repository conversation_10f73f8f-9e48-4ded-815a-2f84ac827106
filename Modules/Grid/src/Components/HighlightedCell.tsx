import React from "react";

import { isNumberType, cn, getPrefixedProps } from "Utilities/index";

export const HighlightedCell = ({ value, data, context, colDef }) => {
    const cellRendererProps = getPrefixedProps("cellRendererProps", colDef, data, context);
    const { colorValue } = cellRendererProps ?? {};

    if (!isNumberType(colorValue)) return value;

    return (
        <div className="leading-none">
            <div
                className={cn("px-xs pb-xs bg-gradient-to-r", {
                    "from-red-900": colorValue < 0,
                    "from-green-900": colorValue > 0,
                })}
            >
                {value}
            </div>
        </div>
    );
};
