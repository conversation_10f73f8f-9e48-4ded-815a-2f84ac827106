import React, { useCallback, useState, useMemo } from "react";

import { LayoutContainer, PersistSettingsProviderApi } from "PrimitiveComponents/index";
import { componentFactory } from "Utilities/ComponentFactory";

import { ReportConfig } from "./Components";
import { ReportEngineApp } from "./ReportEngineApp";

type ReportSettings = {
    reports?: {
        [name: string]: ReportConfig["ParameterValues"];
    };
};

type ReportProps = {
    reportKey: string;
    templateName: string;
    parameterValues?: ReportConfig["ParameterValues"];
    contextMenuComponent?: string;
    tooltipComponent?: string;
    /*
        @TODO - need a better way to persist settings context available here
        so then we wouldn't have to pass in as props
    */
    settings: PersistSettingsProviderApi<ReportSettings>["settings"];
    settingUpdaterFactory?: PersistSettingsProviderApi<ReportSettings>["settingUpdaterFactory"];
};

export const Report = ({
    reportKey,
    templateName,
    parameterValues,
    contextMenuComponent = "ConnectedComponents.ContextMenuTicker",
    tooltipComponent = "CompanyPage.CompanySummary",
    settings,
    settingUpdaterFactory,
}: ReportProps) => {
    const [currentConfig, setCurrentConfig] = useState<ReportConfig>({
        ReportKey: reportKey,
        selectedTemplateName: templateName,
        ParameterValues: {
            ...parameterValues,
            ...settings?.reports?.[reportKey],
        },
    });

    const getConfig = useCallback(() => currentConfig, [currentConfig]);
    const setConfig = useCallback(
        (config: ReportConfig) => {
            setCurrentConfig(config);
            settingUpdaterFactory?.(`reports.${config.ReportKey}`)?.(config.ParameterValues);
        },
        [settingUpdaterFactory],
    );
    const ContextMenuComponent = useMemo(
        () => componentFactory(contextMenuComponent),
        [contextMenuComponent],
    );
    const TooltipComponent = useMemo(() => componentFactory(tooltipComponent), [tooltipComponent]);

    // @TODO: fix container styling for report templates
    return (
        <LayoutContainer style={{ margin: "-5px 10px 0px 10px" }}>
            <ReportEngineApp
                getConfig={getConfig}
                setConfig={setConfig}
                ToolTipComponent={TooltipComponent}
                ContextMenuComponent={ContextMenuComponent}
            />
        </LayoutContainer>
    );
};
