import { useCapture } from "ComponentLibrary/index";

export const useEmailTemplate = ({ formatTitle, formatFilename, template }) => {
    const { sort } = useCapture();

    return async () => {
        const image = (
            await (await sort((a, b) => a.order - b.order).capture()).combineAreas()
        ).getImage();

        const title = formatTitle();

        const emailTo = "";

        const emlHeader = `data:message/rfc822 eml;charset=utf-8,To: ${emailTo}\nSubject: ${title}\nX-Unsent: 1\nContent-Type: text/html\n\n`;

        const a = document.createElement("a");
        a.href = encodeURI(emlHeader + template(title, image)).replace(/#/g, "%23");
        a.download = `${formatFilename()}.eml`;
        a.click();
    };
};
