import React, { useState } from "react";
import styled from "styled-components";
import moment from "moment";

import { Capture, CaptureArea } from "ComponentLibrary/index";
import { withStandardErrorBoundary } from "PrimitiveComponents/index";

import { withConnectedComponentStatus } from "TransportGateway/index";

import { ReportTemplateStandalone, ReportTemplateStandaloneBaseProps } from "..";
import { CaptureAction, CaptureOptions } from "./capture/CaptureOptions";
import { TemplateRegionComponentProps } from "../region";

const CaptureReport = styled(Capture)`
    flex-grow: 0;
    width: fit-content;
`;

const CaptureAreaReport = styled(CaptureArea)`
    visibility: visible;
    width: fit-content;
`;

type ReportTemplateCaptureProps<P extends TemplateRegionComponentProps> = {
    onStart?: () => void;
    disabled?: boolean;
} & ReportTemplateStandaloneBaseProps<P>;

const ReportTemplateCaptureInner = <P extends TemplateRegionComponentProps>({
    disabled,
    onStart,
    onComplete,
    ...props
}: ReportTemplateCaptureProps<P>) => {
    const [isReady, setIsReady] = useState(false);
    const [captureAction, setCaptureAction] = useState<CaptureAction>(undefined);

    const formatCaptureFilename = (name?: string, index?: number) =>
        [
            moment(Date.now()).format("YYYY-MM-DD-HH:mm"),
            props.reportKey,
            index?.toString().padStart(2, "0"),
            name
        ]
            .filter(a => a)
            .join("-");

    return (
        <CaptureReport formatFilename={formatCaptureFilename}>
            <CaptureOptions
                action={captureAction}
                formatTitle={() =>
                    `${props.reportKey} - Report ${moment(Date.now()).format("YYYY-MM-DD-HH:mm")}`
                }
                formatFilename={formatCaptureFilename}
                className="generate"
                isReady={isReady}
                disabled={disabled}
                onSelectOption={action => {
                    if (onStart) {
                        onStart();
                    }
                    setCaptureAction(action);
                }}
                onComplete={() => {
                    setCaptureAction(undefined);
                    setIsReady(false);
                    if (onComplete) {
                        onComplete(false);
                    }
                }}
            />
            {captureAction && (
                <div style={{ height: 0, width: 0, overflow: "hidden", visibility: "hidden" }}>
                    <CaptureAreaReport
                        name="screen"
                        order={1}
                        backgroundColor="rgba(12, 17, 23, 0)"
                    >
                        <ReportTemplateStandalone
                            {...(props as any)}
                            onComplete={() => setIsReady(true)}
                            onError={() => {
                                setCaptureAction(undefined);
                                setIsReady(false);
                                if (onComplete) {
                                    onComplete(true);
                                }
                            }}
                        />
                    </CaptureAreaReport>
                </div>
            )}
        </CaptureReport>
    );
};

export const ReportTemplateCapture = withStandardErrorBoundary(
    withConnectedComponentStatus(ReportTemplateCaptureInner, {
        propagateChildStatus: true,
        hasStatus: false
    })
) as typeof ReportTemplateCaptureInner;
