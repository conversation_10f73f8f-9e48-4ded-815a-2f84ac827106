import { Meta, ArgTypes, Description } from "@storybook/addon-docs";

import {  ReportTemplate } from "./Components/ReportTemplate";

<Meta
    title="ReportEngine/Components/ReportTemplate"
    component={ReportTemplate}
    parameters={{
        viewMode: "docs",
        previewTabs: {
            canvas: { hidden: true }
        }
    }}
/>

# ReportTemplate

<Description of={ReportTemplate} />

<ArgTypes of={ReportTemplate} sort="requiredFirst" />

### Example

This exmaple will embed a template called `Repeated Item Template` from the current report and pass a `viewName`
and `identifierColumnName` as `instrumentandsource`.

```tsx
<ReportTemplate
    templateName="Repeated Item Template"
    options={{
        viewName: "Idea View"
    }}
    identifierColumnName="instrumentandsource"
    outputOperatorName="ProjectionNodeOut"
/>
```

This exmaple will embed a template called `Repeated Item Template` from a report called `SomeOtherReport`.

```tsx
<ReportTemplate
    reportKey="SomeOtherReport"
    templateName="Repeated Item Template"
/>
```
