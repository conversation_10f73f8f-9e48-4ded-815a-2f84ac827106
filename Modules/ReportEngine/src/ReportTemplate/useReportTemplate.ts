import { ComponentType, useMemo } from "react";

import { useStateIfMounted } from "PrimitiveComponents/index";
import { IReport, ITemplate } from "TransportGateway/index";

import { getTemplate } from "../Templates";
import {
    createTemplateRegion,
    TemplateRegionComponentProps,
    TemplateRegionDefs,
    TemplateRegions
} from "./region";
import { ReportTemplateSubscriptionSpec } from "./utils";

export const useReportTemplate = (
    report: IReport,
    template: ITemplate,
    specCreator: ReportTemplateSubscriptionSpec,
    filterExpression?: string,
    outputOperatorName?: string,
    maxItems?: number,
    RepeatedItemRegionTemplateComponent?: ComponentType<TemplateRegionComponentProps>
) => {
    const [error, setError] = useStateIfMounted<Error | undefined>();

    // Fallback to template.Id for backwards compatibility with old reports
    const templateType = template?.TemplateType || template?.Id;

    const TemplateControl = useMemo(
        () => templateType && getTemplate(templateType),
        [templateType]
    );

    const templateRegions = useMemo<TemplateRegions>(() => {
        try {
            if (template?.Id && template?.Regions) {
                const templateRegions = {
                    templateId: template.Id,
                    props: template.Regions?.reduce(
                        (props, region) => ({
                            ...props,
                            [region.Id]: createTemplateRegion({
                                region,
                                report,
                                specCreator,
                                RepeatedItemRegionTemplateComponent,
                                filterExpression,
                                outputOperatorName,
                                maxItems,
                                isPending: !!(
                                    template?.TemplateReference?.reportKey &&
                                    !template?.TemplateReference?.resolved
                                )
                            })
                        }),
                        {} as TemplateRegionDefs
                    )
                };

                return templateRegions;
            }

            return {
                templateId: template?.Id
            };
        } catch (ex) {
            setError(ex);
            console.error(ex);
        }
    }, [template, specCreator, filterExpression, RepeatedItemRegionTemplateComponent]);

    return {
        templateRegions,
        TemplateControl,
        error
    };
};
