import { sortBy } from "lodash";

import { withComponentProps, SelectWithLabel } from "PrimitiveComponents/index";

import { RaidFilterTypes } from "../Components/RaidFilters";

const PriorityFilters = ["lTraderID", "lInstrumentID"];

const { priorityFilters, filters } = Object.entries(RaidFilterTypes).reduce(
    (filterGroups, [name, value]) => {
        const i = PriorityFilters.indexOf(name);

        return {
            priorityFilters:
                i === -1
                    ? filterGroups.priorityFilters
                    : [...filterGroups.priorityFilters, [name, value]],
            filters: i !== -1 ? filterGroups.filters : [...filterGroups.filters, [name, value]]
        };
    },
    {
        priorityFilters: [],
        filters: []
    }
);

export const SelectRaidFilterWithLabel = withComponentProps(
    SelectWithLabel,
    {
        label: "Raid Filter Mapping",
        placeholder: "Select Raid Filter…"
    },
    {
        options: [...priorityFilters, ...sortBy(filters, ([, label]) => label.toLowerCase())].map(
            ([value, label]) => ({
                label,
                value
            })
        )
    }
);
