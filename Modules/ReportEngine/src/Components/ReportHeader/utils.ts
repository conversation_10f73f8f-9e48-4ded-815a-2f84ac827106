import { formatLabel } from "Utilities/index";
import { with<PERSON><PERSON>y<PERSON>omponent, HeaderSpacer } from "PrimitiveComponents/index";
import { IParameter } from "TransportGateway/index";

import type { ReportConfig } from "../..";

import { HeaderOption, HeaderOptionHash, HeaderOptions, HeaderToolbarItem } from "./types";
import { DraftEditUpdateWrapper } from "./DraftEditUpdateWrapper";

const ReportParamControlWithLabel = withLazyComponent(
    () => import("ComponentLibrary/index"),
    "ReportParamControlWithLabel"
);

export const isHeaderOption = (o: any): o is HeaderOption => typeof o === "object";

const buildParameters = (
    reportConfig: ReportConfig,
    parameters: IParameter[],
    optionsHash: HeaderOptionHash,
    onChange?: (config: ReportConfig) => void
): HeaderOption[] => {
    if (!parameters?.length) return [];
    return [
        ...parameters.map(
            parameter =>
                ({
                    name: parameter.Name,
                    isBoundToUpdate: true,
                    // @legacy - If a template option has the same name as a report parameter, replace it with the control
                    Component:
                        optionsHash?.[parameter.Name]?.Component || ReportParamControlWithLabel,
                    props: {
                        parameter,
                        path: `ParameterValues.${parameter.Name}`,
                        isClearable: true,
                        defaultOptions: true,
                        label: formatLabel(parameter.Name),
                        value: reportConfig.ParameterValues?.[parameter.Name],
                        onChange: (value: any) =>
                            onChange({
                                ...reportConfig,
                                ParameterValues: {
                                    ...reportConfig?.ParameterValues,
                                    [parameter.Name]: value
                                }
                            })
                    }
                } as HeaderOption)
        )
    ];
};

export const buildOptions = (reportConfig: ReportConfig, options: HeaderOptions) =>
    options?.map(option => {
        if (!isHeaderOption(option)) {
            return {
                name: option.displayName || option.name,
                isBoundToUpdate: false,
                Component: option,
                props: {
                    value: reportConfig
                }
            } as HeaderOption;
        }

        return {
            name: option?.Component?.displayName || option?.Component?.name,
            ...option,
            props: {
                value: reportConfig,
                ...option?.props
            }
        };
    }) ?? [];

export const createOptionFactory = (
    reportConfig: ReportConfig,
    parameters: IParameter[],
    options: HeaderOption[],
    onChange?: (config: ReportConfig) => void
) => {
    const optionsHash = options?.reduce(
        (optHash, option) => ({
            ...optHash,
            [option.name]: option
        }),
        {} as HeaderOptionHash
    );

    const parametersHash = buildParameters(reportConfig, parameters, optionsHash, onChange).reduce(
        (optHash, option) => ({
            ...optHash,
            [option.name]: option
        }),
        {} as HeaderOptionHash
    );

    const allOptionsHash = {
        ...optionsHash, // Merge options first so that options are correctly overridden
        ...parametersHash
    };

    const optionsHashExtended: HeaderOptionHash = {
        ...allOptionsHash,
        [HeaderToolbarItem.update]: {
            name: HeaderToolbarItem.update,
            Component: DraftEditUpdateWrapper
        },
        [HeaderToolbarItem.spacer]: {
            name: HeaderToolbarItem.spacer,
            Component: HeaderSpacer
        }
    };

    return (name: string) => optionsHashExtended[name];
};
