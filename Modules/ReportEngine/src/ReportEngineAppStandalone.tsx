import React from "react";

import { GatewayProvider } from "TransportGateway/index";
import { ClientApiProvider } from "ClientApi/index";

import { ReportTemplateStandalone, ReportTemplateStandaloneBaseProps } from "./ReportTemplate";
import { RepeatedItemStaticRegion, RepeatedItemStaticRegionProps } from "./Regions";

export type ReportEngineAppStandaloneProps = Omit<
    ReportTemplateStandaloneBaseProps<RepeatedItemStaticRegionProps>,
    "RepeatedItemRegionTemplateComponent"
>;

export const ReportEngineAppStandalone = (props: ReportEngineAppStandaloneProps) => (
    <ClientApiProvider>
        <GatewayProvider includeServiceWSViewserver>
            <ReportTemplateStandalone
                RepeatedItemRegionTemplateComponent={RepeatedItemStaticRegion}
                {...props}
            />
        </GatewayProvider>
    </ClientApiProvider>
);
