import React, { useEffect, useMemo } from "react";
import { pick } from "lodash";

import {
    ComponentData,
    ComponentDataWrapper,
    isComponentDataItem,
    useComponentData
} from "ComponentLibrary/index";
import type { Schema } from "Contracts/index";

import { useReportConfig } from "..";
import { useHeaderAPI, useReportContext } from "../Components";
import { TemplateRegionComponentProps } from "../ReportTemplate/region";
import { getIdentifier } from "../utils";

type SingleItemRegionProps = TemplateRegionComponentProps & {
    data?: any[];
    schema?: Schema;
    items?: ComponentData;
};

export const SingleItemRegion = (props: SingleItemRegionProps) => {
    const { Component, ...rest } = props;
    const { reportState, setReportState } = useReportContext();
    const { config, getConfig, setOptionsInConfig } = useReportConfig();

    const componentData = useComponentData();

    const identifier =
        componentData?.identifier ??
        (props?.parameterValues && getIdentifier(props?.parameterValues));

    const { setHeaderState } = useHeaderAPI();

    useEffect(() => {
        setHeaderState?.(pick(props, [...Object.keys(props.properties || {}), "data"]));
    }, [props, setHeaderState]);

    const data =
        (props.data ?? (isComponentDataItem(props?.items) ? props?.items?.data : props?.items)) ||
        undefined;
    const schema =
        (props?.schema ?? (isComponentDataItem(props?.items) && props?.items?.schema)) || undefined;
    const dataItem = useMemo(
        () =>
            (identifier &&
                Array.isArray(data) &&
                data.find(row => getIdentifier(row) === identifier)) ||
            undefined,
        [identifier, data]
    );

    return (
        (Component && (
            <ComponentDataWrapper
                identifier={identifier}
                componentOptions={reportState ?? {}}
                setComponentOptions={setReportState}
                data={dataItem}
                schema={schema}
            >
                <Component
                    identifier={identifier}
                    config={config}
                    getConfig={getConfig}
                    options={config.options}
                    setOption={setOptionsInConfig}
                    componentOptions={reportState ?? {}}
                    setComponentOptions={setReportState}
                    {...rest}
                />
            </ComponentDataWrapper>
        )) ||
        null
    );
};
