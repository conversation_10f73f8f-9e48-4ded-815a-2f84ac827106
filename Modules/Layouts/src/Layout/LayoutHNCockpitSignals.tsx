import React from "react";

import { withLazyComponent } from "PrimitiveComponents/index";
import { withAnalyticsData, withAnalyticsInteractionViewport } from "Analytics/index";

const ReportEngineAppStandalone = withLazyComponent(
    () => import("ReportEngine/index"),
    "ReportEngineAppStandalone"
);

type LayoutHNCockpitSignalsProps = {
    signal: string;
};
const LayoutHNCockpitSignalsBase = ({ signal }: LayoutHNCockpitSignalsProps) => (
    <ReportEngineAppStandalone
        reportKey="DSPage_Cockpit_Signals_v2"
        templateName="DSP"
        config={{
            ParameterValues: {
                Signal: signal
            }
        }}
    />
);

export const LayoutHNCockpitSignals = withAnalyticsData(
    withAnalyticsInteractionViewport(LayoutHNCockpitSignalsBase),
    { type: "dimension", name: "component", value: "Layouts.LayoutHNCockpitSignals" }
);
