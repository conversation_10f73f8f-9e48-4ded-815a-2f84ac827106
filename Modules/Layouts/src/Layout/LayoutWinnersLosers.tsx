import { WinnersLosersGrids, WinnersGrid, LosersGrid } from "ConnectedComponents/index";
import { withAnalyticsData, withAnalyticsInteractionViewport } from "Analytics/index";

export const LayoutWinnersLosersGrids = withAnalyticsData(
    withAnalyticsInteractionViewport(WinnersLosersGrids),
    { type: "dimension", name: "component", value: "Layouts.LayoutWinnersLosersGrids" }
);

export const LayoutWinnersGrid = withAnalyticsData(withAnalyticsInteractionViewport(WinnersGrid), {
    type: "dimension",
    name: "component",
    value: "Layouts.LayoutWinnersGrid"
});

export const LayoutLosersGrid = withAnalyticsData(withAnalyticsInteractionViewport(LosersGrid), {
    type: "dimension",
    name: "component",
    value: "Layouts.LayoutLosersGrid"
});
