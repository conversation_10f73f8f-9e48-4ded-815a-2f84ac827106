import React, { useMemo } from "react";

import {
    EarningsChartStampViewServer,
    CorrelationsStampViewServer,
    TechnicalsStampViewServer,
    ShortInterestStampViewServer,
    CatalystStampViewServer,
    ValuationStampViewServer,
    PositioningAndSentimentStampViewServer,
    ResultsStampViewServer,
    ConvictionStampViewServer,
    PeersStampViewServer,
    SectorChartStampViewServer,
    SentimentStampViewServer,
    CompanyNewsStampViewServer,
    RedFlagsStampViewServer,
    CTSStampViewServer,
    useFeature,
} from "ConnectedComponents/index";
import { LayoutGrid, BreakpointPropPairs, Flex, Tile } from "PrimitiveComponents/index";

const STAMP_HEIGHT = 230;
const MIN_STAMP_WIDTH = 265;

const GRID_STAMP_BREAKPOINTS: BreakpointPropPairs<typeof LayoutGrid> = [
    [MIN_STAMP_WIDTH * 2, { columns: 2 }],
    [MIN_STAMP_WIDTH * 3, { columns: 3 }],
];

export const LayoutDSPWidgetsNew = () => {
    const { hasFeature } = useFeature();
    const isDSPHNVersion = hasFeature("dsp.hn-version");

    const mainBreakpoints: BreakpointPropPairs<typeof LayoutGrid> = useMemo(() => {
        const columns = isDSPHNVersion ? 5 : 4;
        return [
            [
                MIN_STAMP_WIDTH * columns,
                {
                    gridTemplateColumns: `repeat(${columns}, 1fr)`,
                    css: `& > div:first-child { grid-column: span ${columns - 1}; }`,
                },
            ],
        ];
    }, [isDSPHNVersion]);

    const screeningBreakpoints: BreakpointPropPairs<typeof LayoutGrid> = useMemo(() => {
        const columns = isDSPHNVersion ? 4 : 3;

        return [
            [
                MIN_STAMP_WIDTH * columns,
                {
                    gridTemplateColumns: `repeat(${columns}, 1fr)`,
                    css: "& > div:last-child { grid-column: span 2; }",
                },
            ],
        ];
    }, [isDSPHNVersion]);

    return (
        <LayoutGrid
            gap="large"
            columns={1}
            breakpointPropsPairs={mainBreakpoints}
        >
            <Flex
                flex="1"
                direction="column"
                w="100%"
                gap="x-small"
            >
                <Tile title="Screening" />
                <LayoutGrid
                    gap="x-small"
                    columns={1}
                    breakpointPropsPairs={screeningBreakpoints}
                >
                    {isDSPHNVersion && (
                        <Flex
                            flex="1"
                            w="100%"
                            direction="column"
                            gap="x-small"
                        >
                            <Tile
                                title="Overview"
                                fontSize="large"
                            />

                            <LayoutGrid
                                gap="x-small"
                                columns={1}
                                rowHeight={STAMP_HEIGHT}
                                breakpointPropsPairs={GRID_STAMP_BREAKPOINTS}
                            >
                                <ConvictionStampViewServer
                                    title="Conviction"
                                    h="100%"
                                    background="container"
                                />
                                <RedFlagsStampViewServer
                                    title="Red Flags"
                                    h="100%"
                                    background="container"
                                />
                                <ResultsStampViewServer
                                    title="Results"
                                    h="100%"
                                    background="container"
                                />
                            </LayoutGrid>
                        </Flex>
                    )}

                    <Flex
                        flex="1"
                        w="100%"
                        direction="column"
                        gap="x-small"
                    >
                        <Tile
                            title="Conviction"
                            fontSize="large"
                        />

                        <LayoutGrid
                            gap="x-small"
                            columns={1}
                            rowHeight={STAMP_HEIGHT}
                            breakpointPropsPairs={GRID_STAMP_BREAKPOINTS}
                        >
                            <TechnicalsStampViewServer
                                title="Technicals"
                                h="100%"
                                background="container"
                            />
                            <EarningsChartStampViewServer
                                title="Earnings"
                                background="container"
                            />
                            <CTSStampViewServer
                                title="CTS"
                                background="container"
                            />
                        </LayoutGrid>
                    </Flex>

                    <Flex
                        flex="1"
                        w="100%"
                        direction="column"
                        gap="x-small"
                    >
                        <Tile
                            title="Checks"
                            fontSize="large"
                        />
                        <LayoutGrid
                            gap="x-small"
                            columns={1}
                            rowHeight={STAMP_HEIGHT}
                            breakpointPropsPairs={GRID_STAMP_BREAKPOINTS}
                        >
                            <PositioningAndSentimentStampViewServer
                                title="Positioning"
                                h="100%"
                                background="container"
                            />
                            <ValuationStampViewServer
                                title="Valuation"
                                h="100%"
                                background="container"
                            />
                            <ShortInterestStampViewServer
                                title="Short Interest"
                                h="100%"
                                background="container"
                            />
                            <PeersStampViewServer
                                title="Peers"
                                h="100%"
                                background="container"
                            />
                            <CatalystStampViewServer
                                title="Catalyst"
                                h="100%"
                                background="container"
                            />
                            <CompanyNewsStampViewServer
                                title="Company News"
                                h="100%"
                                background="container"
                            />
                        </LayoutGrid>
                    </Flex>
                </LayoutGrid>
            </Flex>
            <Flex
                flex="1"
                w="100%"
                direction="column"
                gap="x-small"
            >
                <Tile title="Macro" />

                <Tile
                    title="Checks"
                    fontSize="large"
                />

                <LayoutGrid
                    gap="x-small"
                    columns={1}
                    rowHeight={STAMP_HEIGHT}
                    breakpointPropsPairs={GRID_STAMP_BREAKPOINTS}
                >
                    <SectorChartStampViewServer
                        title="Sector"
                        h="100%"
                        background="container"
                    />
                    <CorrelationsStampViewServer
                        title="Correlations"
                        h="100%"
                        background="container"
                    />
                    <SentimentStampViewServer
                        title="Sentiment"
                        h="100%"
                        background="container"
                    />
                </LayoutGrid>
            </Flex>
        </LayoutGrid>
    );
};
