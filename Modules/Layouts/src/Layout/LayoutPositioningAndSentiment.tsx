import React from "react";
import { Flex } from "PrimitiveComponents/index";
import {
    PositioningAndSentimentDial,
    PositioningCrowdedScores,
    type PositioningCrowdedScoresProps
} from "Components/index";
import { PositioningStampViewServer } from "ConnectedComponents/index";
import { withAnalyticsData, withAnalyticsInteractionViewport } from "Analytics/index";

type LayoutPositioningAndSentimentProps = {
    stampData: PositioningCrowdedScoresProps;
};

const LayoutPositioningAndSentimentBase = ({ stampData }: LayoutPositioningAndSentimentProps) => (
    <Flex
        direction="column"
        gap="x-large"
        p="large"
    >
        <Flex
            direction="column"
            gap="x-large"
        >
            <PositioningAndSentimentDial
                size={120}
                gap="xx-large"
                {...stampData}
            />
            <PositioningCrowdedScores {...stampData} />
        </Flex>
        <Flex
            h={320}
            justify="center"
        >
            <Flex w={320}>
                <PositioningStampViewServer
                    h="100%"
                    background="container"
                    pv="none"
                    ph="none"
                />
            </Flex>
        </Flex>
    </Flex>
);

export const LayoutPositioningAndSentiment = withAnalyticsData(
    withAnalyticsInteractionViewport(LayoutPositioningAndSentimentBase),
    { type: "dimension", name: "component", value: "Layouts.LayoutPositioningAndSentiment" }
);
