import React, { useMemo, useState, type ComponentType } from "react";
import moment from "moment";

import { withSchemaOverrides } from "@mocks/api-gateway";
import {
    ValuationStampViewServer,
    ManagementStampViewServer,
    RedFlagsStampViewServer,
    StackedChartViewServer,
    ANRChartViewServer,
    adjustIntradayData,
} from "ConnectedComponents/index";
import {
    Tile,
    type TileProps,
    NoData,
    withSkeletonComponentDefinition,
    SkeletonRect,
    withComponentProps,
} from "PrimitiveComponents/index";
import { withFilterValues, Conviction } from "Components/index";
import {
    withCommandState,
    createConfig,
    withConnectedSkeleton,
    reportSubscriptionCommandSpec,
} from "TransportGateway/index";
import { isNumberType, cn, toObjectByProp } from "Utilities/index";
import { SchemaValue } from "Schema/index";
import { Typography, Flex, CheckMark, Ticker, Modal, Change } from "PrimitiveComponents/index";
import { Chart, type ChartProps } from "Charting/index";
import { Momo<PERSON>hart, EarningsChangesGrid } from "ComponentLibrary/index";
import { Grid } from "Grid/index";
import type { Schema } from "Contracts/index";

type RobConvictionProps = {
    data?: { conviction: number }[];
    className?: string;
    disableCheckRequest?: boolean;
    bbgTicker?: string;
};

const RobConvictionTileBase = withSkeletonComponentDefinition(({ data }: RobConvictionProps) => {
    return (
        <Tile
            title="Rob View"
            fontSize="medium"
            className="h-[260px]"
            pb="none"
        >
            {data?.length > 0 ? (
                <div className="flex flex-col flex-1 justify-center items-center gap-3xl">
                    <div className="pl-[8px]">{data[0].summary}</div>

                    <Conviction
                        value={data[0].conviction}
                        className="pl-0"
                        label=""
                    />
                </div>
            ) : (
                <NoData />
            )}
        </Tile>
    );
})(SkeletonRect);

export const RobConvictionTile = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            RobConvictionTileBase,
            createConfig({
                props: reportSubscriptionCommandSpec(
                    "HN_Cockpit_AnalystPopOut",
                    ({
                        bbgTicker,
                        disableCheckRequest,
                    }: {
                        bbgTicker?: string;
                        disableCheckRequest?: boolean;
                    }) =>
                        bbgTicker && !disableCheckRequest ? { BBGTicker: bbgTicker } : undefined,
                    (data, props, schema) => ({ data, schema }),
                    "SortNode",
                ),
            }),
        ),
    ),
    ["bbgTicker"],
    {
        bbgTicker: {
            required: true,
        },
    },
);

// [e-com] this is duplicated Modules/ConnectedComponents/src/ViewServer/Components/PositioningAndSentimentStampViewServer/schemaOverride.ts:13
export const schemaOverrideBrokerChanges = {
    countUpgrades: {
        displayName: "Upgrades",
    },
    countDowngrades: {
        displayName: "Downgrades",
    },
};

// [e-com] this is duplicated from Modules/ConnectedComponents/src/ViewServer/Components/PositioningAndSentimentStampViewServer/schemaOverride.ts:22
export const schemaOverrideScore = (data: { barColourChange?: number }) => ({
    aNR: {
        properties: {
            chartType: "bar",
            chartCartesianGridPropsShow: false,
            chartReferenceLinePropsShow: true,

            chartYAxisLeftPropsShow: true,
            chartYAxisLeftPropsHide: true,
            chartYAxisLeftPropsClassName: "hide-line",
            chartYAxisLeftPropsDomain: "[0, 5]",
            chartYAxisLeftPropsValueFormat: "number1dp",

            chartXAxisPropsShow: true,
            chartXAxisPropsLabelsMode: "bar-values",

            chartTooltipPropsTitle: false,
            chartTooltipPropsDisableColor: true,
            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "number1dp",
            chartTooltipPropsSortBy:
                "aNR, medianRatio3m, aNR1mChange, countBrokersString, dayssinceANRString",

            chartSeriesPropsStroke:
                // eslint-disable-next-line no-nested-ternary
                data?.barColourChange === 2
                    ? "var(--color__highlight)"
                    : data?.barColourChange === -2
                    ? "var(--color__gradient-negative)"
                    : "var(--charts__series__line__13)",
        },
    },
    medianRatio3m: {
        properties: {
            chartType: "bar",
            chartSeriesPropsStroke: "var(--charts__series__line__13)",
            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "number1dp",
            chartTooltipPropsDisableColor: true,
        },
    },
    aNR1mChange: {
        hidden: true,
        properties: {
            chartTooltipPropsFormatterType: "percent",
            chartTooltipPropsValueFormat: "percent1dp",
        },
    },
    countBrokersString: {
        displayName: "Analysts No.",
        properties: {
            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "number0dp",
        },
    },
    dayssinceANRString: {
        displayName: "Days > ANR (4.2)",
        properties: {
            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "number0dp",
        },
    },
});

const SentimentTileTop = ({ score, brokerChanges }) => {
    const scoreSchema = useMemo(
        () => withSchemaOverrides(score?.schema ?? [], schemaOverrideScore(score?.data?.[0])),
        [score],
    );

    const brokerChangesSchema = useMemo(
        () => withSchemaOverrides(brokerChanges?.schema ?? [], schemaOverrideBrokerChanges),
        [brokerChanges],
    );

    const renderBrokerChanges = (prop: keyof BrokerChangesData, checkMarkValue: boolean) => (
        <SchemaValue
            key={prop}
            name={prop}
            data={brokerChanges?.data}
            schema={brokerChangesSchema}
        >
            {(children, displayName, column, schema, dataValue) =>
                isNumberType(dataValue) &&
                dataValue !== 0 && (
                    <Flex
                        gap
                        items="center"
                    >
                        <CheckMark
                            value={checkMarkValue}
                            type="arrowUpDown"
                            size={15}
                            highlighted
                        />
                        <Typography.Text
                            fontWeight="header"
                            highlightedInversed
                        >
                            {children}
                        </Typography.Text>
                        <span>{displayName}</span>
                    </Flex>
                )
            }
        </SchemaValue>
    );

    return (
        <div className="flex flex-1 px-xl gap-xl">
            <div className="flex flex-col flex-1 justify-center items-center">
                <div className="grid gap-sm">
                    <div>{renderBrokerChanges("countUpgrades", true)}</div>
                    <div>{renderBrokerChanges("countDowngrades", false)}</div>
                </div>
            </div>
            <div className="flex flex-col flex-1 pt-md">
                <div className="text-center">Analysts</div>
                <Chart
                    data={score?.data}
                    schema={scoreSchema}
                    compactWidth={0}
                    compactHeight={0}
                />
            </div>
        </div>
    );
};

export const SentimentTileTopViewServer = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            SentimentTileTop,
            createConfig({
                score: reportSubscriptionCommandSpec(
                    "HN_Cockpit_Positioning_CrowdedScore_SingleTicker",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data, schema }),
                    "ProjectionNodeWidget",
                ),
                brokerChanges: reportSubscriptionCommandSpec(
                    "HN_Cockpit_Positioning_BrokerChangesSinceFigs",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data: data?.[0], schema }),
                    "GroupByNode",
                ),
            }),
        ),
    ),
    ["bbgTicker"],
    {
        bbgTicker: {
            required: true,
        },
    },
);

type SentimentTileProps = TileProps;
const SentimentTile = ({ className, ...rest }: SentimentTileProps) => {
    return (
        <Tile
            className={cn("h-[260px]", className)}
            title="Sentiment"
            fontSize="medium"
            {...rest}
        >
            <div className="flex flex-col flex-1">
                <SentimentTileTopViewServer />
            </div>
            <div className="flex flex-col flex-1">
                <ANRChartViewServer hideLegend />
            </div>
        </Tile>
    );
};

// const TechnicalCharts = props => {
//     console.log(`[e-log] [TechnicalCharts]`, { props });
//
//     return <div>TechnicalCharts</div>;
// };
// const TechnicalChartsViewServer = withFilterValues(
//     withCommandState(
//         TechnicalCharts,
//         createConfig({
//             technical: reportSubscriptionCommandSpec(
//                 "TechnicalChartData",
//                 ({
//                     bbgTicker,
//                     dateFrame = 6,
//                 }: {
//                     instrumentId: string;
//                     dateFrame: number;
//                 }): TechnicalChartDataRequest => ({
//                     BbgTicker: bbgTicker,
//                     DateFrame: dateFrame,
//                 }),
//                 (data, props, schema) => ({ data, schema }),
//                 "FetchData",
//             ),
//         }),
//     ),
//     ["bbgTicker"],
// );

type PositioningTileProps = TileProps;
const PositioningTile = withFilterValues(
    ({ bbgTicker, className, ...rest }: PositioningTileProps) => (
        <Tile
            className={cn("h-[260px]", className)}
            title="Positioning"
            fontSize="medium"
            {...rest}
        >
            <div className="flex flex-col flex-1 pl-lg">
                <StackedChartViewServer
                    bbgTicker={bbgTicker}
                    dateFrame={6}
                    chartTypes={["crowding", "daysToCover"]}
                    headerTypes={[]}
                />
            </div>
        </Tile>
    ),
    ["bbgTicker"],
);

const OwnershipTileBase = ({ data, schema }) => {
    console.log(`[e-log] [OwnershipTileBase]`, { data, schema });
    return (
        <Tile
            title="Ownership"
            fontSize="medium"
            className="h-[260px]"
        >
            <div className="flex flex-col flex-1 justify-center items-center">
                <div className="flex flex-col gap-sm">
                    {data?.slice(0, 5)?.map(d => (
                        <div className="flex gap-2xl items-center">
                            <div className="flex-1 text-lg">
                                {d.top20HoldersPublicFilings_HolderName
                                    .replace(/holdings.*/gi, "")
                                    .replace(/corp.*/gi, "")
                                    .replace(/inc.*/gi, "")}
                            </div>
                            <div className="text-xl">
                                <Change
                                    value={d.top20HoldersPublicFilings_PercentOutstanding}
                                    colorValue={
                                        d.top20HoldersPublicFilings_LatestChange.includes("-")
                                            ? -1
                                            : 1
                                    }
                                    formatterType="percent"
                                    valueFormat="percent1dp1m"
                                    showPositiveIndicator={false}
                                />
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </Tile>
    );
};

const OwnershipTile = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            OwnershipTileBase,
            createConfig({
                props: reportSubscriptionCommandSpec(
                    "BloombergReferenceDataRequest_BloombergReferenceDataResponse",
                    ({ bbgTicker }) => ({
                        bbgTickers: [bbgTicker],
                        fields: ["TOP_20_HOLDERS_PUBLIC_FILINGS"],
                        cacheResultsFor: "2:00:00",
                    }),
                    (data, props, schema) => ({ data, schema }),
                ),
            }),
        ),
    ),
    ["bbgTicker"],
    {
        bbgTicker: {
            required: true,
        },
    },
);

// copied from: legacy/Modules/ComponentLibrary/src/Components/Stamp/utils/maps/mapFY.ts
const getHeader = (h, i) => {
    let part = h.match(/\((.+)\)/g);
    if (!part) return h;
    part = part && part[0].replace("(", "").replace(")", "");
    const rest = [" L2 IBES", " L1 IBES"];
    return part + (i < 2 && rest[i]);
};

const EarningsTileGridBase = ({ data: dataProp, schema: schemaProp, ...rest }) => {
    const schema = useMemo(
        () =>
            withSchemaOverrides(schemaProp, {
                header: {
                    width: "#unset",
                    displayName: "EPS",
                    properties: {
                        flex: 1,
                    },
                },
                fY1Change: {
                    width: "#unset",
                    displayName: "FY1 Chg",
                    contentTypeForDisplay: "ChangeValuePercentage",
                    properties: {
                        flex: 1,
                        maxWidth: 75,
                        formatterType: "number",
                        valueFormat: "percent2dp1m",
                    },
                },
                fY2Change: {
                    width: "#unset",
                    displayName: "FY2 Chg",
                    contentTypeForDisplay: "ChangeValuePercentage",
                    properties: {
                        flex: 1,
                        maxWidth: 75,
                        formatterType: "number",
                        valueFormat: "percent2dp1m",
                    },
                },
                fY1: { hidden: true },
                fY2: { hidden: true },
                fY1Min: { hidden: true },
                fY2Min: { hidden: true },
                fY1Max: { hidden: true },
                fY2Max: { hidden: true },
            }),
        [schemaProp],
    );

    const data = useMemo(
        () =>
            dataProp
                ?.filter(d => {
                    if (d.header.includes("Since Results")) return true;
                    if (d.header === "3 Months") return true;
                    return false;
                })
                ?.map((d, i) => ({
                    ...d,
                    header: getHeader(d.header, i),
                })),
        [dataProp],
    );

    return (
        <Grid
            data={data}
            schema={schema}
            {...rest}
        />
    );
};
const EarningsTileGrid = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            EarningsTileGridBase,
            createConfig({
                props: reportSubscriptionCommandSpec(
                    "InstrumentRequestWithMeasureCode_IConsensusEstimateChangeSchema",
                    ({ bbgTicker, dateFrame = 1, measureCode = 9 }) => ({
                        bbgTicker,
                        dateFrame,
                        measureCode,
                    }),
                    (data, props, schema) => ({ data, schema }),
                ),
            }),
        ),
    ),
    ["bbgTicker", "longShort"],
    {
        bbgTicker: {
            required: true,
        },
    },
);

const ModalTrigger = ({ children, className, modalContent, ...rest }) => {
    const [open, setOpen] = useState(false);
    return (
        <>
            <div
                className={cn(
                    modalContent &&
                        "border border-transparent hover:border-button-hover cursor-pointer",
                    className,
                )}
                onClick={() => modalContent && setOpen(true)}
            >
                {children}
            </div>
            <Modal
                visible={open}
                onClose={() => modalContent && setOpen(false)}
                width="90vw"
                withViewportHeight
                {...rest}
            >
                {modalContent}
            </Modal>
        </>
    );
};

type EarningsTileProps = TileProps;
const earningsTileSchemaOverride = {
    dateTime: {
        properties: {
            chartPropsMargin: { top: 0, right: 50, bottom: 0, left: -25 },
        },
    },
    momo: {
        properties: {
            chartYAxisLeftPropsPadding: { top: 5, bottom: 5 },
            chartCartesianGridPropsShow: false,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "momo",
            chartPriceMarkerPropsFormatterType: "percent",
            chartPriceMarkerPropsValueFormat: "percent2dp",
            chartPriceMarkerPropsTextColor: "minmax",
            chartYAxisLeftLabelPropsFontSize: "11px",
            chartYAxisLeftLabelPropsValue: "",
        },
    },
};
const EarningsTile = withFilterValues(
    ({ bbgTicker, className, ...rest }: EarningsTileProps) => (
        <Tile
            className={cn("h-[260px]", className)}
            title="Earnings"
            fontSize="medium"
            pb="none"
            {...rest}
        >
            <div className="flex flex-col flex-2 pt-lg">
                <MomoChart
                    report="momoCommand"
                    period={24}
                    instrumentId={bbgTicker}
                    compactWidth={0}
                    compactHeight={0}
                    schemaOverride={earningsTileSchemaOverride}
                />
            </div>
            <ModalTrigger
                className="flex flex-col h-[85px] p-sm"
                modalContent={<EarningsChangesGrid instrumentId={bbgTicker} />}
            >
                <EarningsTileGrid />
            </ModalTrigger>
        </Tile>
    ),
    ["bbgTicker"],
);

// [e-com] duplicated from Modules/ConnectedComponents/src/ViewServer/Components/TechnicalsStampViewServer/schemaOverride.ts:111
export const createChartSchema = (bbgTicker: string) => [
    {
        columnId: 1,
        name: "dateTime",
        contentType: 9,
        metaData: {
            displayName: "Date",
            properties: {
                chartCartesianGridPropsShow: false,
                chartReferenceLinePropsShow: true,
                chartLegendPropsShow: false,
                chartPropsLabel: "sign",
                chartPropsMargin: { right: 50, left: -25 },

                chartXAxisPropsShow: true,
                chartXAxisPropsFormatterType: "date",
                chartXAxisPropsValueFormat:
                    "@calc[eq('#context(dateRange)', '1D') ? 'HH:mm' : 'DD MMM']",
                chartXAxisPropsTicksMode: "default",
                chartXAxisPropsInterval: "preserveEnd",
                chartXAxisPropsMinTickGap: 0 /*25*/,
                chartXAxisPropsHeight: 40 /*20*/,

                chartTooltipPropsValueColumnName: "dateTime",
                chartTooltipPropsLabelFormatterType: "date",
                chartTooltipPropsLabelValueFormat: "ddd, Do MMM YYYY HH:mm",
                chartTooltipPropsSortBy: "intraDay, intraDayMarket, intraDaySector",
            },
        },
    },
    {
        columnId: 2,
        name: "intraDay",
        contentType: 8,
        metaData: {
            displayName: bbgTicker,
            properties: {
                order: 12,
                chartType: "line",

                chartSeriesPropsStrokeWidth: "2px",
                chartSeriesPropsConnectNulls: true,
                chartSeriesPropsStroke: "var(--charts__series__line__1)",

                chartTooltipPropsFormatterType: "number",
                chartTooltipPropsValueFormat: "significant4dp",
                chartTooltipPropsDisableColor: true,

                chartYAxisLeftPropsShow: true,
                chartYAxisLeftPropsPadding: { top: 15, bottom: 15 },
                chartYAxisLeftPropsDataKey: "intraDay",
                chartYAxisLeftPropsDomain: "['dataMin', 'dataMax']",
                chartYAxisLeftPropsFormatterType: "significant",
                chartYAxisLeftPropsValueFormat: "significant3dp",
                // chartYAxisLeftPropsTick: "null",
                // chartYAxisLeftPropsClassName: "hide-line",
                // chartYAxisLeftLabelPropsValue: "#context(yLabel)",
                // chartYAxisLeftLabelPropsPosition: "insideLeft",
                // chartYAxisLeftLabelPropsClassName: "anchor-middle",
                // chartYAxisLeftLabelPropsOffset: 45,
                // chartYAxisLeftLabelPropsFontSize: "11px",

                chartPriceMarkerPropsShow: true,
                chartPriceMarkerPropsDataKey: "intraDay",
                chartPriceMarkerPropsValueFormat: "significant4dp",
                chartPriceMarkerPropsPadding: 0,
                chartPriceMarkerPropsBorderWidth: 0,
            },
        },
    },
    {
        columnId: 3,
        name: "intraDayMarket",
        contentType: 8,
        metaData: {
            displayName: " Market",
            properties: {
                order: 3,
                chartType: "line",

                chartSeriesPropsConnectNulls: true,
                chartSeriesPropsYAxisId: "right-axis",
                chartSeriesPropsStroke: "var(--charts__series__line__2__50)",

                chartTooltipPropsFormatterType: "number",
                chartTooltipPropsValueFormat: "significant4dp",
                chartTooltipPropsDisableColor: true,

                chartYAxisRightPropsShow: true,
                chartYAxisRightPropsHide: true,
                chartYAxisRightPropsPadding: { top: 15, bottom: 15 },
                chartYAxisRightPropsDataKey: "intraDayMarket",
                chartYAxisRightPropsYAxisId: "right-axis",
                chartYAxisRightPropsDomain: "['dataMin', 'dataMax']",

                chartPriceMarkerPropsShow: true,
                chartPriceMarkerPropsDataKey: "intraDayMarket",
                chartPriceMarkerPropsValueFormat: "significant4dp",
                chartPriceMarkerPropsPadding: 0,
                chartPriceMarkerPropsBorderWidth: 0,
                chartPriceMarkerPropsTextColor: "var(--charts__series__line__2__50)",
            },
        },
    },
    {
        columnId: 4,
        name: "intraDaySector",
        contentType: 8,
        metaData: {
            displayName: "Sector",
            properties: {
                order: 4,
                chartType: "line",

                chartSeriesPropsConnectNulls: true,
                chartSeriesPropsYAxisId: "right-axis-2",
                chartSeriesPropsStroke: "var(--charts__series__line__3__50)",

                chartTooltipPropsFormatterType: "number",
                chartTooltipPropsValueFormat: "significant4dp",
                chartTooltipPropsDisableColor: true,

                chartYAxisRight2PropsShow: true,
                chartYAxisRight2PropsHide: true,
                chartYAxisRight2PropsPadding: { top: 15, bottom: 15 },
                chartYAxisRight2PropsDataKey: "intraDaySector",
                chartYAxisRight2PropsYAxisId: "right-axis-2",
                chartYAxisRight2PropsDomain: "['dataMin', 'dataMax']",

                chartPriceMarkerPropsShow: true,
                chartPriceMarkerPropsDataKey: "intraDaySector",
                chartPriceMarkerPropsValueFormat: "significant4dp",
                chartPriceMarkerPropsPadding: 0,
                chartPriceMarkerPropsBorderWidth: 0,
                chartPriceMarkerPropsTextColor: "var(--charts__series__line__3__50)",
            },
        },
    },
];

const IntradayRawChart = Chart as ComponentType<
    ChartProps & {
        yLabel?: string;
    }
>;

export type IntradayChartProps = {
    bbgTicker?: string;
    barMinutes?: number;
    dateFrame?: number;
    intraDay?: {
        data: {
            dateTime?: string;
            bloombergTicker?: string;
            close?: number;
        }[];
        schema: Schema;
    };
    intraDayMarket?: {
        data: { close?: number; dateTime?: string; bloombergTicker?: string }[];
        schema: Schema;
    };
    intraDaySector?: {
        data: { close?: number; dateTime?: string; bloombergTicker?: string }[];
        schema: Schema;
    };
};
const IntradayChart = ({
    bbgTicker,
    dateFrame,
    barMinutes,
    intraDay,
    intraDayMarket,
    intraDaySector,
}: IntradayChartProps) => {
    const chartData = useMemo(() => {
        if (!intraDay?.data) return;
        if (!(intraDay.data.length > 0)) return [];
        if (intraDay.data[0].bloombergTicker !== bbgTicker) return [];

        const intraDayMarketMap = toObjectByProp("dateTime", intraDayMarket?.data);
        const intraDaySectorMap = toObjectByProp("dateTime", intraDaySector?.data);

        const merged = intraDay.data?.map(value => ({
            ...value,
            dateTime: moment(value.dateTime, "YYYY-MM-DD-HH.mm.ss").toString(),
            intraDay: value.close,
            intraDayMarket: intraDayMarketMap[value.dateTime]?.close,
            intraDaySector: intraDaySectorMap[value.dateTime]?.close,
        }));

        return adjustIntradayData({
            data: merged,
            bbgTicker,
            dateFrame,
            barMinutes,
        });
    }, [
        barMinutes,
        bbgTicker,
        dateFrame,
        intraDay?.data,
        intraDayMarket?.data,
        intraDaySector?.data,
    ]);

    const chartSchema = useMemo(() => createChartSchema(bbgTicker), [bbgTicker]);

    const marketIcker = intraDayMarket?.data[0]?.bloombergTicker;
    const sectorTicker = intraDaySector?.data[0]?.bloombergTicker;

    return (
        <div className="flex flex-col flex-1 gap-md">
            <div className="flex justify-evenly items-center h-[24px]">
                {marketIcker && sectorTicker && (
                    <>
                        <div className="flex items-center gap-sm">
                            <Ticker
                                bbgTicker={bbgTicker}
                                disabled={false}
                                prefixLabel={
                                    <div className="size-2 mr-sm bg-chart-seris-line-1"></div>
                                }
                            />
                        </div>
                        <div className="flex items-center gap-sm">
                            <Ticker
                                bbgTicker={marketIcker}
                                disabled={false}
                                prefixLabel={
                                    <div className="size-2 mr-sm bg-chart-seris-line-2/50"></div>
                                }
                            />
                        </div>
                        <div className="flex items-center gap-sm">
                            <Ticker
                                bbgTicker={sectorTicker}
                                disabled={false}
                                prefixLabel={
                                    <div className="size-2 mr-sm bg-chart-seris-line-3/50"></div>
                                }
                            />
                        </div>
                    </>
                )}
            </div>
            <IntradayRawChart
                data={chartData}
                schema={chartSchema}
                yLabel={`Intraday ${dateFrame}`}
            />
        </div>
    );
};
const IntradayChartViewSever = withFilterValues(
    withComponentProps(
        withConnectedSkeleton(
            withCommandState(
                IntradayChart,
                createConfig({
                    intraDay: reportSubscriptionCommandSpec(
                        "IntradayPriceHistoryLookup",
                        ({ bbgTicker, dateFrame, barMinutes }) =>
                            bbgTicker && {
                                BBGTicker: bbgTicker,
                                dateFrame,
                                barMinutes,
                                lookupColumn: "Instrument",
                            },
                        (data, props, schema) => ({ data, schema }),
                    ),
                    intraDayMarket: reportSubscriptionCommandSpec(
                        "IntradayPriceHistoryLookup",
                        ({ bbgTicker, showMarket = true, dateFrame, barMinutes }) =>
                            showMarket &&
                            bbgTicker && {
                                BBGTicker: bbgTicker,
                                dateFrame,
                                barMinutes,
                                lookupColumn: "Market",
                            },
                        (data, props, schema) => ({ data, schema }),
                    ),
                    intraDaySector: reportSubscriptionCommandSpec(
                        "IntradayPriceHistoryLookup",
                        ({ bbgTicker, showSector = true, dateFrame, barMinutes }) =>
                            showSector &&
                            bbgTicker && {
                                BBGTicker: bbgTicker,
                                dateFrame,
                                barMinutes,
                                lookupColumn: "Sector",
                            },
                        (data, props, schema) => ({ data, schema }),
                    ),
                }),
            ),
        ),
        {
            dateFrame: 3,
            barMinutes: 5,
        },
    ),
    ["bbgTicker", "longShort"],
    {
        bbgTicker: {
            required: true,
        },
    },
);

export const LayoutDSPWidgetsV3 = () => (
    <div className="grid gap-xs">
        <Tile title="Technicals" />

        <div className="grid grid-cols-5 gap-xs">
            <Tile
                title="Post Results"
                fontSize="medium"
                className="h-[260px]"
            >
                <div className="flex flex-col flex-1 pl-lg">
                    <StackedChartViewServer
                        dateFrame={3}
                        chartTypes={["specific", "price", "relative"]}
                        headerTypes={["correlation"]}
                    />
                </div>
            </Tile>
            <Tile
                title="ST Technicals"
                fontSize="medium"
                className="h-[260px]"
            >
                <div className="flex flex-col flex-1 pl-lg">
                    <StackedChartViewServer
                        dateFrame={6}
                        chartTypes={["specific", "price", "relative"]}
                        headerTypes={["correlation"]}
                    />
                </div>
            </Tile>
            <Tile
                title="MT Technicals"
                fontSize="medium"
                className="h-[260px]"
            >
                <div className="flex flex-col flex-1 pl-lg">
                    <StackedChartViewServer
                        dateFrame={12}
                        chartTypes={["specific", "price", "relative"]}
                        headerTypes={["correlation"]}
                    />
                </div>
            </Tile>
            <Tile
                title="Extension"
                fontSize="medium"
                className="h-[260px]"
            >
                <div className="flex flex-col flex-1 pl-lg">
                    <StackedChartViewServer
                        dateFrame={6}
                        chartTypes={[
                            "technicalDistFrom50d",
                            "technicalRel1mReturn",
                            "technicalRel3mReturn",
                        ]}
                        headerTypes={[]}
                    />
                </div>
            </Tile>
            <Tile
                title="Intraday"
                fontSize="medium"
                className="h-[260px]"
            >
                <div className="flex flex-1 pl-lg pt-md">
                    <IntradayChartViewSever />
                </div>
            </Tile>

            <EarningsTile />

            <RobConvictionTile />

            <ValuationStampViewServer
                title="Valuation"
                h="100%"
                background="container"
            />

            <SentimentTile />

            <PositioningTile />

            <ManagementStampViewServer
                title="Management"
                h="100%"
                background="container"
            />

            <OwnershipTile />

            <Tile
                title="Buybacks"
                fontSize="medium"
                className="h-[260px]"
            >
                <NoData noDataLabel="TODO" />
            </Tile>
            <Tile
                title="Miscellaneous"
                fontSize="medium"
                className="h-[260px]"
            >
                <NoData noDataLabel="TODO" />
            </Tile>
            <RedFlagsStampViewServer
                title="Sense Check"
                h="100%"
                background="container"
            />
        </div>
    </div>
);
