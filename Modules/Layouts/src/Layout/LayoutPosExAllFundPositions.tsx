import React from "react";
import { Flex, SelectWithLabel, useComponentsPersistState } from "PrimitiveComponents/index";
import { PosExPositions } from "ConnectedComponents/index";
import { camelCase } from "lodash";
import type { PosexRow } from "../Sections/SectionPosEx";

const POSITIONS_GROUP_BY_OPTIONS = [
    { label: "Long/Short Exposure", value: "LongShortExposure" },
    { label: "Ticker", value: "BBGTicker" },
    { label: "GamePlan", value: "GamePlanName" },
    { label: "Strategy Description", value: "StrategyDesc" },
    { label: "Sub Strategy", value: "SubStrategy" },
    { label: "Instrument Type", value: "InstrumentType" },
];

type LayoutPosExAllFundPositionsProps = {
    selectedRow: PosexRow;
    summaryGroupBy?: string;
    raid1Id?: string;
    bbgTicker?: string;
};

export const LayoutPosExAllFundPositions = ({
    selectedRow,
    summaryGroupBy,
    raid1Id,
    bbgTicker,
}: LayoutPosExAllFundPositionsProps) => {
    const persistanceId = `posex${raid1Id ? `-${raid1Id}` : ""}`;

    const [positionsGroupBy, setPositionsGroupBy] = useComponentsPersistState<string>(
        `${persistanceId}.positions-group-by`,
        undefined,
    );

    return (
        <Flex
            direction="column"
            gap
            flex="1"
        >
            <Flex justify="end">
                <SelectWithLabel
                    label="Group By"
                    options={POSITIONS_GROUP_BY_OPTIONS}
                    value={positionsGroupBy}
                    onChange={setPositionsGroupBy}
                />
            </Flex>
            <Flex
                minh={290}
                flex="1"
            >
                <PosExPositions
                    hasTransparentBackground
                    layout="x-compact"
                    groupBy={positionsGroupBy}
                    {...(selectedRow
                        ? {
                              [camelCase(summaryGroupBy)]: selectedRow?.[camelCase(summaryGroupBy)],
                          }
                        : {})}
                    bbgTicker={bbgTicker}
                />
            </Flex>
        </Flex>
    );
};
