import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";
import { ThemeIterator, LayoutGrid, ThemeContainer } from "PrimitiveComponents/index";
import { withFixtures } from "TransportGateway/index";
import { wait } from "Utilities/index";
import { LayoutRiskContribution } from "../LayoutRiskContribution";

export const LayoutRiskContributionnMock = withFixtures(LayoutRiskContribution, {
    riskContribution: async () => {
      await wait(1000)
      const res = await import("./fixtures/riskContribution.json");
      return {
        data: res.data.map(x => ({...x, netDeltaExposure: x.netDeltaExposure * (Math.random() - 0.5)})),
        schema: res.schema
      }
    }
});

<Meta
    title="Layouts/LayoutRiskContribution"
    component={LayoutRiskContribution}
/>

export const Template = args => (
    <LayoutGrid
        columns={1}
        gap="xx-large"
    >
        <ThemeIterator>
            {() => (
                <LayoutGrid
                    columns={1}
                    gap="large"
                >
                    <ThemeContainer flex>
                        <LayoutRiskContributionnMock {...args} />
                    </ThemeContainer>
                </LayoutGrid>
            )}
        </ThemeIterator>
    </LayoutGrid>
);

# LayoutRiskContribution

<Description of={LayoutRiskContribution} />

<Canvas>
    <Story
        name="LayoutRiskContribution"
        parameters={{
            controls: {
                disable: true,
                include: []
            }
        }}
        args={{}}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes
    of={LayoutRiskContribution}
    sort="requiredFirst"
/>