import React from "react";

import { LayoutContainer, LayoutColumns, LayoutVertical } from "PrimitiveComponents/index";
import { Panel } from "ConnectedComponents/index";

import {
    LayoutPMBaseballCard,
    LayoutPMBio,
    LayoutPMHogan,
    LayoutPMStrategyDescription
} from "../Layout";

export const SectionPMSummary = () => (
    <LayoutVertical spacing="large">
        <LayoutColumns
            gap="medium"
            containerBreakpointMin="medium-auto"
        >
            <LayoutContainer
                minWidth={400}
                maxWidth={500}
                flexBasis="30%"
            >
                <Panel
                    title="PM Bio"
                    id="pm.pmBio"
                    makeCollapsible={false}
                    autoGrow
                >
                    <LayoutPMBio />
                </Panel>
            </LayoutContainer>
            <LayoutContainer>
                <Panel
                    title="Strategy Description"
                    id="pm.strategyDescription"
                    makeCollapsible={false}
                    autoGrow
                >
                    <LayoutPMStrategyDescription />
                </Panel>
            </LayoutContainer>
        </LayoutColumns>

        <LayoutColumns
            gap="medium"
            containerBreakpointMin="medium-auto"
        >
            <LayoutContainer flexBasis="50%">
                <Panel
                    title="Baseball Card"
                    id="pm.baseballCard"
                    makeCollapsible={false}
                >
                    <LayoutPMBaseballCard />
                </Panel>
            </LayoutContainer>

            <LayoutContainer flexBasis="50%">
                <Panel
                    title="Hogan"
                    id="pm.hogan"
                >
                    <LayoutPMHogan />
                </Panel>
            </LayoutContainer>
        </LayoutColumns>
    </LayoutVertical>
);
