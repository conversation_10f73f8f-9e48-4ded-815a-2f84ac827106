import React, { type ComponentType } from "react";

import {
    Flex,
    useComponentsPersistState,
    Tile,
    Typography,
    withLazyComponent,
    IconButton,
} from "PrimitiveComponents/index";
import { withFilterValues, FiltersProvider, Filters } from "Components/index";
import { isIndex, isStringType, cn } from "Utilities/index";
import {
    CompanyPageSummaryChartGatewayStampViewServer,
    StreetAccountLinkViewServer,
    AnalystButtonViewServer,
    BacktestingTokensGridViewServer,
    useFeature,
    SendToAnalystButton,
} from "ConnectedComponents/index";
import { AppIcon } from "Theming/index";

import type { ReportEngineAppStandaloneProps } from "ReportEngine/index";

import {
    LayoutHNCockpit,
    LayoutDSPWidgetsV2,
    LayoutRiskProfileTradeSuggestionsWidgets,
    LayoutDSPIndexWidgets,
    LayoutDSPWidgetsV3,
} from "../../Layout";
import type { SelectedPosition } from "./types";

const ReportEngineAppStandalone = withLazyComponent(
    () => import("ReportEngine/index"),
    "ReportEngineAppStandalone",
) as ComponentType<
    ReportEngineAppStandaloneProps & {
        hideCharts?: boolean;
        hideSearch?: boolean;
    }
>;

const STAMP_HEIGHT = 300;

const EARNINGS_SCRAPER_VIEWS = ["hidden", "minimized", "maximized"] as const;
type EarningsScraperView = (typeof EARNINGS_SCRAPER_VIEWS)[number];

type SectionDspInnerProps = {
    bbgTicker: string;
    earningsScraperIndex: EarningsScraperView;
    version: "2" | "3";
};
const SectionDspInner = withFilterValues(
    ({ bbgTicker, earningsScraperIndex, version }: SectionDspInnerProps) => {
        const [selectedPosition, setSelectedPosition] = useComponentsPersistState<SelectedPosition>(
            "dsp-v2.selected-position",
        );

        const position = selectedPosition?.bbgTicker === bbgTicker ? selectedPosition : undefined;

        const hasTicker = isStringType(bbgTicker);
        const isEquity = !isIndex(bbgTicker);

        const { hasFeature } = useFeature();
        const isDSPHNVersion = hasFeature("dsp.hn-version");

        const content = (() => {
            if (!hasTicker) return null;

            if (!isEquity) return <LayoutDSPIndexWidgets />;

            return (
                <>
                    {isDSPHNVersion && (
                        <>
                            <Tile title="Backtest Tokens">
                                <div className="flex flex-col min-h-[125px] px-md">
                                    <BacktestingTokensGridViewServer />
                                </div>
                            </Tile>

                            <div
                                className={cn({
                                    hidden:
                                        EARNINGS_SCRAPER_VIEWS[earningsScraperIndex] === "hidden",
                                    "overflow-y-auto h-[350px]":
                                        EARNINGS_SCRAPER_VIEWS[earningsScraperIndex] ===
                                        "minimized",
                                    "min-h-[350px]":
                                        EARNINGS_SCRAPER_VIEWS[earningsScraperIndex] ===
                                        "maximized",
                                })}
                            >
                                <ReportEngineAppStandalone
                                    maxItems={10000}
                                    reportKey="BestEarnings_ExampleReportForTicker"
                                    templateName="DSP template"
                                    config={{ ParameterValues: { ticker: bbgTicker } }}
                                />
                            </div>
                        </>
                    )}
                    {version === "2" && <LayoutDSPWidgetsV2 />}
                    {version === "3" && <LayoutDSPWidgetsV3 />}
                    <LayoutRiskProfileTradeSuggestionsWidgets position={position} />
                </>
            );
        })();

        return (
            <Flex
                flex="1"
                gap="large"
            >
                <Flex
                    direction="column"
                    h="100%"
                    w="20%"
                    minw={500}
                    maxw={600}
                    gap="x-small"
                >
                    <Tile title="Cockpit" />

                    <Flex h={STAMP_HEIGHT}>
                        <CompanyPageSummaryChartGatewayStampViewServer
                            title={
                                <Typography.Text lineHeight="none">
                                    <Flex
                                        justify="center"
                                        items="center"
                                        flex="1"
                                        gap="large"
                                    >
                                        <span>Company Page</span>
                                        <span>
                                            <AnalystButtonViewServer bbgTicker={bbgTicker} />
                                        </span>

                                        <span className="mr-sm">
                                            <SendToAnalystButton bbgTicker={bbgTicker} />
                                        </span>

                                        <span>
                                            <StreetAccountLinkViewServer />
                                        </span>
                                    </Flex>
                                </Typography.Text>
                            }
                            background="container"
                            contentOverflow="initial"
                        />
                    </Flex>

                    <Flex
                        flex="1"
                        background="container"
                        style={{ overflow: "hidden auto" }}
                    >
                        <LayoutHNCockpit
                            position={position}
                            selectPosition={setSelectedPosition}
                        />
                    </Flex>
                </Flex>

                <div className="grid gap-lg w-full">{content}</div>
            </Flex>
        );
    },
    ["bbgTicker"],
    {
        bbgTicker: {
            required: true,
        },
    },
);

const SectionDsp = props => {
    const [earningsScraperIndex, setEarningsScraperIndex] = useComponentsPersistState(
        "dsp-v2.hn-dsp",
        1,
    );
    const { hasFeature } = useFeature();
    const isDSPHNVersion = hasFeature("dsp.hn-version");
    return (
        <FiltersProvider>
            <div className="gap-md flex flex-1 flex-col">
                <div>
                    <Filters>
                        {isDSPHNVersion && (
                            <div className="flex flex-col">
                                <IconButton
                                    title="Earnings Scraper View Mode"
                                    onClick={() =>
                                        setEarningsScraperIndex(
                                            x => (x + 1) % EARNINGS_SCRAPER_VIEWS.length,
                                        )
                                    }
                                >
                                    <AppIcon
                                        type={(function iife() {
                                            switch (EARNINGS_SCRAPER_VIEWS[earningsScraperIndex]) {
                                                case "hidden":
                                                    return "eyeOff";
                                                case "minimized":
                                                    return "minimize";
                                                case "maximized":
                                                    return "maximize";
                                            }
                                        })()}
                                    />
                                </IconButton>
                            </div>
                        )}
                    </Filters>
                </div>

                <SectionDspInner
                    earningsScraperIndex={earningsScraperIndex}
                    {...props}
                />
            </div>
        </FiltersProvider>
    );
};

export const SectionDspV2 = props => (
    <SectionDsp
        {...props}
        version="2"
    />
);

export const SectionDspV3 = props => (
    <SectionDsp
        {...props}
        version="3"
    />
);
