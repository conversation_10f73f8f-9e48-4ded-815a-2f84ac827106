import React, { type ComponentType } from "react";

import {
    Flex,
    useComponentsPersistState,
    Tile,
    Typography,
    withLazyComponent,
    IconButton,
} from "PrimitiveComponents/index";
import { withFilterValues, FiltersProvider, Filters } from "Components/index";
import { isIndex, isStringType, cn } from "Utilities/index";
import {
    CompanyPageSummaryChartGatewayStampViewServer,
    StreetAccountLinkViewServer,
    AnalystButtonViewServer,
    BacktestingTokensGridViewServer,
    useFeature,
    SendToAnalystButton,
} from "ConnectedComponents/index";
import { AppIcon } from "Theming/index";

import type { ReportEngineAppStandaloneProps } from "ReportEngine/index";

import {
    LayoutHNCockpit,
    LayoutDSPWidgetsNew,
    LayoutRiskProfileTradeSuggestionsWidgets,
    LayoutDSPIndexWidgets,
} from "../../Layout";
import type { SelectedPosition } from "./types";

const ReportEngineAppStandalone = withLazyComponent(
    () => import("ReportEngine/index"),
    "ReportEngineAppStandalone",
) as ComponentType<
    ReportEngineAppStandaloneProps & {
        hideCharts?: boolean;
        hideSearch?: boolean;
    }
>;

const STAMP_HEIGHT = 300;

const SectionDspV2Base = withFilterValues(
    ({ bbgTicker, isHNView }: { bbgTicker: string; isHNView: boolean }) => {
        const [selectedPosition, setSelectedPosition] = useComponentsPersistState<SelectedPosition>(
            "dsp-v2.selected-position",
        );

        const position = selectedPosition?.bbgTicker === bbgTicker ? selectedPosition : undefined;

        const hasTicker = isStringType(bbgTicker);
        const isEquity = !isIndex(bbgTicker);

        const { hasFeature } = useFeature();
        const isDSPHNVersion = hasFeature("dsp.hn-version");

        const content = (() => {
            if (!hasTicker) return null;

            if (!isEquity) return <LayoutDSPIndexWidgets />;

            return (
                <>
                    {isDSPHNVersion && (
                        <>
                            <Tile title="Backtest Tokens">
                                <div className="flex flex-col min-h-[125px] px-md">
                                    <BacktestingTokensGridViewServer />
                                </div>
                            </Tile>

                            <div
                                className={cn(
                                    isHNView ? "min-h-[350px]" : "overflow-y-auto h-[350px]",
                                )}
                            >
                                <ReportEngineAppStandalone
                                    maxItems={10000}
                                    reportKey="BestEarnings_ExampleReportForTicker"
                                    templateName="DSP template"
                                    config={{ ParameterValues: { ticker: bbgTicker } }}
                                />
                            </div>
                        </>
                    )}
                    <LayoutDSPWidgetsNew />
                    <LayoutRiskProfileTradeSuggestionsWidgets position={position} />
                </>
            );
        })();

        return (
            <Flex
                flex="1"
                gap="large"
            >
                <Flex
                    direction="column"
                    h="100%"
                    w="20%"
                    minw={500}
                    maxw={600}
                    gap="x-small"
                >
                    <Tile title="Cockpit" />

                    <Flex h={STAMP_HEIGHT}>
                        <CompanyPageSummaryChartGatewayStampViewServer
                            title={
                                <Typography.Text lineHeight="none">
                                    <Flex
                                        justify="center"
                                        items="center"
                                        flex="1"
                                        gap="large"
                                    >
                                        <span>Company Page</span>
                                        <span>
                                            <AnalystButtonViewServer bbgTicker={bbgTicker} />
                                        </span>

                                        <span className="mr-sm">
                                            <SendToAnalystButton bbgTicker={bbgTicker} />
                                        </span>

                                        <span>
                                            <StreetAccountLinkViewServer />
                                        </span>
                                    </Flex>
                                </Typography.Text>
                            }
                            background="container"
                            contentOverflow="initial"
                        />
                    </Flex>

                    <Flex
                        flex="1"
                        background="container"
                        style={{ overflow: "hidden auto" }}
                    >
                        <LayoutHNCockpit
                            position={position}
                            selectPosition={setSelectedPosition}
                        />
                    </Flex>
                </Flex>
                <div className="grid gap-lg w-full">{content}</div>
            </Flex>
        );
    },
    ["bbgTicker"],
    {
        bbgTicker: {
            required: true,
        },
    },
);

export const SectionDspV2 = ({ bbgTicker }) => {
    const [isHNView, setIsHNView] = useComponentsPersistState("dsp-v2.hn-dsp", false);
    const { hasFeature } = useFeature();
    const isDSPHNVersion = hasFeature("dsp.hn-version");
    return (
        <FiltersProvider>
            <div className="gap-md flex flex-1 flex-col">
                <div>
                    <Filters>
                        {isDSPHNVersion && (
                            <div className="flex flex-col">
                                <IconButton
                                    title="IconButton"
                                    selected={isHNView}
                                    onClick={() => setIsHNView(x => !x)}
                                >
                                    <AppIcon type="maximize" />
                                </IconButton>
                            </div>
                        )}
                    </Filters>
                </div>

                <SectionDspV2Base
                    bbgTicker={bbgTicker}
                    isHNView={isHNView}
                />
            </div>
        </FiltersProvider>
    );
};
