import React, { useState } from "react";
import { InputWithLabel } from "PrimitiveComponents/index";

export const InputWithLabelDemo = () => {
    const [stringControlled, setStringControlled] = useState("Some Value");
    const [stringUncontrolled, setStringUncontrolled] = useState("Some Value");
    const [numberControlled, setNumberControlled] = useState("123456");
    const [numberUncontrolled, setNumberUncontrolled] = useState("123456");
    const [percentControlled, setPercentControlled] = useState("0.12345");
    const [percentUncontrolled, setPercentUncontrolled] = useState("0.12345");
    const [tenorUncontrolled, setTenorUncontrolled] = useState("1234567");
    return (
        <div className="gap-lg flex flex-col">
            <div className="gap-lg flex items-center">
                <InputWithLabel
                    label="string controlled"
                    value={stringControlled}
                    onChange={setStringControlled}
                />
                <div>{stringControlled}</div>
            </div>
            <div className="gap-lg flex items-center">
                <InputWithLabel
                    label="string uncontrolled"
                    defaultValue={stringUncontrolled}
                    onChange={setStringUncontrolled}
                />
                <div>{stringUncontrolled}</div>
            </div>
            <div className="gap-lg flex items-center">
                <InputWithLabel
                    label="number controlled"
                    type="number"
                    value={numberControlled}
                    onChange={setNumberControlled}
                />
                <div>{numberControlled}</div>
            </div>
            <div className="gap-lg flex items-center">
                <InputWithLabel
                    label="number uncontrolled"
                    type="number"
                    defaultValue={numberUncontrolled}
                    onChange={setNumberUncontrolled}
                />
                <div>{numberUncontrolled}</div>
            </div>
            <div className="gap-lg flex items-center">
                <InputWithLabel
                    label="percent controlled"
                    type="percent"
                    value={percentControlled}
                    onChange={setPercentControlled}
                />
                <div>{percentControlled}</div>
            </div>
            <div className="gap-lg flex items-center">
                <InputWithLabel
                    label="percent uncontrolled"
                    type="percent"
                    defaultValue={percentUncontrolled}
                    onChange={setPercentUncontrolled}
                />
                <div>{percentUncontrolled}</div>
            </div>
            <div className="gap-lg flex items-center">
                <InputWithLabel
                    label="tenor uncontrolled"
                    type="tenor"
                    defaultValue={tenorUncontrolled}
                    onChange={setTenorUncontrolled}
                />
                <div>{tenorUncontrolled}</div>
            </div>
        </div>
    );
};
