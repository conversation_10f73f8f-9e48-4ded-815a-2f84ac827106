import React from "react";
import {
    Cartesian<PERSON><PERSON>,
    <PERSON>,
    Line,
    LineChart,
    ResponsiveContainer,
    <PERSON>Axis,
    <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";

export const ChartVanilla = () => {
    const VANILLA_CHART_DATA = [
        // { uv: 2000, pv: 9800, default: true },
        { uv: 1890, pv: 4800 },
        { uv: 1950, pv: 6000 },

        { uv: 2390, pv: 3800, default: true },

        { uv: 2780, pv: 3908 },

        { uv: 3000, pv: 1398, default: true },
        { uv: 3490, pv: 4300, default: true },
        { uv: 4000, pv: 2400, default: true },

        { uv: 4250, default: true },

        { uv: 4500, pv: 3400, optimal: true },
        { uv: 5000, pv: 5400, optimal: true },
        { uv: 5500, pv: 6400, optimal: true },

        { uv: 6000, pv: 7400 },
        { uv: 2000, pv: 9800, default: true },
    ];
    return (
        <ResponsiveContainer
            width="100%"
            height="100%"
        >
            <LineChart
                data={VANILLA_CHART_DATA}
                margin={{ right: 30 }}
            >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                    dataKey="uv"
                    // type="number"
                />
                <YAxis padding={{ top: 30, bottom: 0 }} />
                <Legend />
                <Line
                    dataKey="pv"
                    isAnimationActive={false}
                    dot={null}
                    connectNulls
                    stroke="hsl(205,100%,41%)"
                />

                {/*<Customized component={MinMaxPriceMarker} />*/}
            </LineChart>
        </ResponsiveContainer>
    );
};
