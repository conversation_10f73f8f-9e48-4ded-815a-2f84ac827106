import React from "react";

import { SelectWithLabel, useComponentsPersistState } from "PrimitiveComponents/index";
import { StackedChartViewServer } from "ConnectedComponents/index";

const REGRESSION_CHART_VIEW_SERVER_DEMO_OPTIONS = [
    { label: "Small Charts", value: "small" },
    { label: "BbgTicker", value: "bbgTicker" },
    { label: "BbgTicker vs RelativeTicker", value: "bbgTickerVsRelativeTicker" },
    { label: "RelativeTicker", value: "relativeTicker" },
    { label: "Specific Price Relative", value: "specificPriceRelative" },
    { label: "Crowding Days To Cover", value: "crowdingDaysToCover" },
    { label: "Technicals", value: "technicals" },
];

export const StackedChartViewServerDemo = () => {
    const bbgTicker = "VOD LN Equity";
    const relativeTicker = "SXKP Index";

    const [features, setFeatures] = useComponentsPersistState(
        "ermes-demo.stacked-chart-view-server-demo-features",
        [],
    );
    const handleSelect = val => setFeatures(val);
    const featuresMap =
        features?.reduce?.((acc, curr) => {
            acc[curr] = true;
            return acc;
        }, {}) ?? {};

    return (
        <div className="flex flex-col flex-1 gap-md">
            <div>
                <SelectWithLabel
                    label="StackedChartViewServerDemo Features"
                    options={REGRESSION_CHART_VIEW_SERVER_DEMO_OPTIONS}
                    onChange={handleSelect}
                    value={features}
                    width="250px"
                    isMulti
                    selectType="checkbox"
                />
            </div>

            <div className="flex flex-wrap gap-xs">
                {featuresMap.bbgTicker && (
                    <div className="flex flex-1 flex-col gap-xs">
                        {featuresMap.small && (
                            <div className="flex flex-col pl-lg pt-lg pr-md pb-md bg-container w-[267px] h-[250px]">
                                <StackedChartViewServer
                                    bbgTicker={bbgTicker}
                                    dateFrame={6}
                                    chartTypes={["price", "rsi", "exposure"]}
                                    headerTypes={["correlation"]}
                                />
                            </div>
                        )}
                        <div className="flex flex-col pl-lg pt-lg pr-md pb-md bg-container w-[400px] h-[330px]">
                            <StackedChartViewServer
                                bbgTicker={bbgTicker}
                                dateFrame={6}
                                chartTypes={["price", "rsi", "exposure"]}
                                headerTypes={["correlation"]}
                            />
                        </div>
                    </div>
                )}
                {featuresMap.relativeTicker && (
                    <div className="flex flex-1 flex-col gap-xs">
                        {featuresMap.small && (
                            <div className="flex flex-col pl-lg pt-lg pr-md pb-md bg-container w-[267px] h-[250px]">
                                <StackedChartViewServer
                                    bbgTicker={relativeTicker}
                                    dateFrame={6}
                                    chartTypes={["price", "rsi", "relative"]}
                                    headerTypes={["correlation"]}
                                />
                            </div>
                        )}
                        <div className="flex flex-col pl-lg pt-lg pr-md pb-md bg-container w-[400px] h-[330px]">
                            <StackedChartViewServer
                                bbgTicker={relativeTicker}
                                dateFrame={6}
                                chartTypes={["price", "rsi", "relative"]}
                                headerTypes={["correlation"]}
                            />
                        </div>
                    </div>
                )}
                {featuresMap.bbgTickerVsRelativeTicker && (
                    <div className="flex flex-1 flex-col gap-xs">
                        {featuresMap.small && (
                            <div className="flex flex-col pl-lg pt-lg pr-md pb-md bg-container w-[267px] h-[250px]">
                                <StackedChartViewServer
                                    bbgTicker={bbgTicker}
                                    relativeTicker={relativeTicker}
                                    dateFrame={6}
                                    chartTypes={["price", "rsi", "correlation"]}
                                    headerTypes={["correlation"]}
                                />
                            </div>
                        )}
                        <div className="flex flex-col pl-lg pt-lg pr-md pb-md bg-container w-[400px] h-[330px]">
                            <StackedChartViewServer
                                bbgTicker={bbgTicker}
                                relativeTicker={relativeTicker}
                                dateFrame={6}
                                chartTypes={["price", "rsi", "correlation"]}
                                headerTypes={["correlation"]}
                            />
                        </div>
                    </div>
                )}
                {featuresMap.specificPriceRelative && (
                    <div className="flex flex-1 flex-col gap-xs">
                        {featuresMap.small && (
                            <div className="flex flex-col pl-lg pt-lg pr-md pb-md bg-container w-[267px] h-[250px]">
                                <StackedChartViewServer
                                    bbgTicker={bbgTicker}
                                    dateFrame={6}
                                    chartTypes={["specific", "price", "relative"]}
                                    headerTypes={["correlation"]}
                                />
                            </div>
                        )}
                        <div className="flex flex-col pl-lg pt-lg pr-md pb-md bg-container w-[400px] h-[330px]">
                            <StackedChartViewServer
                                bbgTicker={bbgTicker}
                                dateFrame={6}
                                chartTypes={["specific", "price", "relative"]}
                                headerTypes={["correlation"]}
                            />
                        </div>
                    </div>
                )}

                {featuresMap.crowdingDaysToCover && (
                    <div className="flex flex-1 flex-col gap-xs">
                        {featuresMap.small && (
                            <div className="flex flex-col pl-lg pt-lg pr-md pb-md bg-container w-[267px] h-[250px]">
                                <StackedChartViewServer
                                    bbgTicker={bbgTicker}
                                    dateFrame={6}
                                    chartTypes={["crowding", "daysToCover"]}
                                    headerTypes={[]}
                                />
                            </div>
                        )}
                        <div className="flex flex-col pl-lg pt-lg pr-md pb-md bg-container w-[400px] h-[330px]">
                            <StackedChartViewServer
                                bbgTicker={bbgTicker}
                                dateFrame={6}
                                chartTypes={["crowding", "daysToCover"]}
                                headerTypes={[]}
                            />
                        </div>
                    </div>
                )}

                {featuresMap.technicals && (
                    <div className="flex flex-1 flex-col gap-xs">
                        {featuresMap.small && (
                            <div className="flex flex-col pl-lg pt-lg pr-md pb-md bg-container w-[267px] h-[250px]">
                                <StackedChartViewServer
                                    bbgTicker={bbgTicker}
                                    dateFrame={6}
                                    chartTypes={[
                                        "technicalDistFrom50d",
                                        "technicalRel1mReturn",
                                        "technicalRel3mReturn",
                                    ]}
                                    headerTypes={[]}
                                />
                            </div>
                        )}
                        <div className="flex flex-col pl-lg pt-lg pr-md pb-md bg-container w-[400px] h-[330px]">
                            <StackedChartViewServer
                                bbgTicker={bbgTicker}
                                dateFrame={6}
                                chartTypes={[
                                    "technicalDistFrom50d",
                                    "technicalRel1mReturn",
                                    "technicalRel3mReturn",
                                ]}
                                headerTypes={[]}
                            />
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};
