{"name": "@tradinglabs/layouts", "license": "UNLICENSED", "version": "1.0.0", "main": "src/index.ts", "scripts": {"start": "raid-build start", "build": "yarn build-app", "build-app": "raid-build build", "build-typescript": "yarn g:tsc && yarn make-types", "make-types": "yarn run -T make-federated-types", "test:ci": "jest --passWithNoTests --ci --detectOpenHandles", "lint:all": "yarn lint:eslint && yarn lint:stylelint", "lint:eslint": "node ../../scripts/eslint/runEslint.js --paths='./src/**/*.+(js|jsx|ts|tsx)'", "lint:stylelint": "node ../../scripts/stylelint/runStylelint.js --paths='./src/**/*.+(ts|tsx)'"}, "dependencies": {"@mocks/api-gateway": "workspace:*", "@reddeer/gameplan-js-client": "workspace:*", "@tradinglabs/analytics": "workspace:*", "@tradinglabs/charting": "workspace:*", "@tradinglabs/client-api": "workspace:*", "@tradinglabs/company-page": "workspace:*", "@tradinglabs/component-library": "workspace:*", "@tradinglabs/components": "workspace:*", "@tradinglabs/connected-components": "workspace:*", "@tradinglabs/contracts": "workspace:*", "@tradinglabs/grid": "workspace:*", "@tradinglabs/primitive-components": "workspace:*", "@tradinglabs/report-engine": "workspace:*", "@tradinglabs/schema": "workspace:*", "@tradinglabs/theming": "workspace:*", "@tradinglabs/trade-snapshot": "workspace:*", "@tradinglabs/transport-gateway": "workspace:*", "@tradinglabs/utilities": "workspace:*", "classnames": "2.3.2", "date-fns": "^2.30.0", "lodash": "~4.17.21", "moment": "2.29.4", "react": "18.2.0", "react-dom": "18.2.0", "styled-components": "5.3.11"}, "devDependencies": {"@tradinglabs/raid-build": "workspace:*", "@types/lodash": "~4.14.202", "@types/react": "18.0.25", "@types/styled-components": "5.1.29", "typescript": "5.2.2"}}