import {
    HttpTransportType,
    HubConnection,
    HubConnectionBuilder,
    HubConnectionState,
    LogLevel
} from "@microsoft/signalr";
import { Subject } from "rxjs";

import { resolveEndpoint } from "../../utils/resolveEndpoint";
import { ApiGatewayHubRequest, ApiGatewayHubResponse, CommandMessage } from "../../types";
import { connectionstatusAction } from "../../actions";

import { TransportApi } from "../types";

export const WebSocket = <R, P>(
    url: string,
    connectionName: string,
    dispatch: Subject<CommandMessage>
): TransportApi<ApiGatewayHubRequest<P>, ApiGatewayHubResponse<R>> => {
    let connection: HubConnection;
    let connectionRequested = false;
    let retry: ReturnType<typeof setInterval>;
    const dataSink = new Subject<ApiGatewayHubResponse<R>>();

    const connect = async () => {
        connectionRequested = true;
        clearInterval(retry);

        if (connection) {
            console.info(`[apiGatewayHub]: Re-establishing connection to '${connection.baseUrl}'`);
        } else {
            connection = new HubConnectionBuilder()
                .withUrl(url, {
                    skipNegotiation: true,
                    transport: HttpTransportType.WebSockets
                })
                .configureLogging(
                    process.env.NODE_ENV === "development" ? LogLevel.Debug : LogLevel.Error
                )
                .withAutomaticReconnect([
                    0, 2000, 2000, 2000, 2000, 2000, 4000, 4000, 4000, 6000, 6000, 6000, 6000,
                    10000, 10000, 10000, 10000, 10000
                ])
                .build();

            connection.on("ReceiveMessage", message => {
                if (process.env.NODE_ENV === "development") {
                    console.debug("[apiGatewayHub]: Received update", message);
                }
                dataSink.next(message);
            });

            connection.onreconnecting(error => {
                console.info("[apiGatewayHub]: Connection RECONNECTING...");

                dispatch.next(
                    connectionstatusAction({
                        connectionName,
                        status: {
                            isConnected: connection.state === HubConnectionState.Connected,
                            isConnecting: connection.state === HubConnectionState.Reconnecting,
                            statusMessage: "ApiGatewayHub reconnecting",
                            errorMessage: error?.message
                        }
                    })
                );
            });

            connection.onreconnected(() => {
                console.info("[apiGatewayHub]: Connection RECONNECTED");

                dispatch.next(
                    connectionstatusAction({
                        connectionName,
                        status: {
                            isConnected: connection.state === HubConnectionState.Connected,
                            isConnecting: connection.state === HubConnectionState.Connecting,
                            statusMessage: "ApiGatewayHub reconnected"
                        }
                    })
                );
            });

            connection.onclose(error => {
                console.info("[apiGatewayHub]: Connection CLOSED");

                dispatch.next(
                    connectionstatusAction({
                        connectionName,
                        status: {
                            isConnected: connection.state === HubConnectionState.Connected,
                            isConnecting: connection.state === HubConnectionState.Connecting,
                            statusMessage: "ApiGatewayHub connection closed",
                            errorMessage: error?.message
                        }
                    })
                );
            });
        }

        const start = async () => {
            if (connection.state === HubConnectionState.Disconnected) {
                try {
                    console.info(`[apiGatewayHub]: Connecting to '${url}'...`);

                    dispatch.next(
                        connectionstatusAction({
                            connectionName,
                            status: {
                                isConnected: false,
                                isConnecting: true,
                                statusMessage: "ApiGatewayHub Connecting"
                            }
                        })
                    );

                    await connection.start();

                    console.info(`[apiGatewayHub]: Connected to '${url}'`);

                    dispatch.next(
                        connectionstatusAction({
                            connectionName,
                            status: {
                                isConnected: true,
                                isConnecting: false,
                                statusMessage: "ApiGatewayHub Connected"
                            }
                        })
                    );
                } catch (ex) {
                    console.error("[apiGatewayHub]: Connection ERROR", ex.message);

                    dispatch.next(
                        connectionstatusAction({
                            connectionName,
                            status: {
                                isConnected: false,
                                isConnecting: false,
                                statusMessage: "ApiGatewayHub Connection Failed",
                                errorMessage: ex?.message || ex
                            }
                        })
                    );
                    throw ex;
                }
            }
        };

        try {
            await start();
        } catch (ex) {
            console.error(ex);
        } finally {
            // @TODO: reconnect logic with back off
            retry = setInterval(() => {
                // console.log("###:STATUS", connection.state, connection);
                if (connectionRequested && connection.state === HubConnectionState.Disconnected) {
                    console.info("[apiGatewayHub]: Attempting connection re-establish");
                    start(); // @TODO: await before retry
                }
            }, 2000);
        }

        return connection;
    };

    const disconnect = async () => {
        connectionRequested = false;
        clearInterval(retry);

        try {
            if (connection?.state !== HubConnectionState.Disconnected) {
                await connection.stop();
            }

            console.info("[apiGatewayHub]: Connection DISCONNECTED");

            dispatch.next(
                connectionstatusAction({
                    connectionName,
                    status: {
                        isConnected: false,
                        isConnecting: false,
                        statusMessage: "ApiGatewayHub Disconnected",
                        isInactive: true
                    }
                })
            );
        } catch (ex) {
            dispatch.next(
                connectionstatusAction({
                    connectionName,
                    status: {
                        isConnected: false,
                        isConnecting: false,
                        statusMessage: "ApiGatewayHub Disconnected",
                        errorMessage: ex.message
                    }
                })
            );

            throw ex;
        }
    };

    return {
        connectionName,
        connect: async () => {
            await connect();

            return {
                invoke: (method, payload) => {
                    if (connection?.state !== HubConnectionState.Connected) {
                        throw new TypeError(
                            "[apiGatewayHub]: Unable to invoke cancel subscription with no valid connection"
                        );
                    }

                    const { commandUrl, params } = resolveEndpoint(method, payload);
                    return connection.invoke(commandUrl, params);
                }
            };
        },
        disconnect,
        dataSink
    };
};
