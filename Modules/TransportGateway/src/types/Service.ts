import { Subject } from "rxjs";

import { MessageDto, TraceApi } from "@tradinglabs/viewserver-core";

import { AuthHeaders } from "./AuthHeaders";
import { ICommandState } from "./Command";

export type Message<P extends any = any> = {
    type: string;
    payload?: P;
};

export type CommandMessage<P extends any = any> = {
    commandState?: Partial<ICommandState>;
} & Message<P>;

export type MessageEvent<D extends Message = Message> = {
    data: D;
};

export const typeSymbol = Symbol("type");

export type MessageCreatorBase<P extends any> = {
    (payload: P): Message<P>;
    getPayload: (message: Message<any>) => P;
};

export type MessageCreator<P extends any> = {
    [typeSymbol]: string;
} & MessageCreatorBase<P>;

export type ServiceClient<TMessage = MessageDto, R = any, Options = {}> = {
    invokeClientCommand: (
        id?: string,
        abortSignal?: AbortSignal
    ) => <P = any>(
        command: string,
        commandPayload: P,
        onStatus?: (status: string, message: TMessage) => void,
        options?: Options
    ) => Promise<R>;
};

export type CommandStates = {
    [connectionName: string]: Partial<ICommandState>;
};

export type ServiceClients<TMessage = MessageDto, R = any> = {
    services?: {
        [serviceName: string]: ServiceClient<TMessage, R>;
    };
    getTrace?: () => TraceApi;
};

export type ServiceApi<TMessage = MessageDto, R = any, Options = {}> = {
    connect: (headers?: AuthHeaders) => Promise<void>;
    dispatch: (message: Message, commandState?: CommandStates) => void;
    dataSink: Subject<Message>;
} & ServiceClient<TMessage, R, Options>;

export type MiddlewareServiceApi = {
    getState: () => any;
    dispatch: <P extends any>(message: Message<P>) => Message<P>;
};

export type MiddlewareService = (
    service: MiddlewareServiceApi
) => (next: <T>(next: T) => T) => (message: Message) => void;
