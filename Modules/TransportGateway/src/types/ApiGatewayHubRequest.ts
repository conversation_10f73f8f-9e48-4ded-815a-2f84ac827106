import { ApiGatewayRequest } from "./ApiGatewayRequest";

export type ApiGatewayHubRequest<Request> = {
    subscriptionId: string;
} & ApiGatewayRequest<Request>;

export type ApiGatewayHubRequestCancel = string;

export const isApiGatewayHubRequest = (message: any): message is ApiGatewayHubRequest<unknown> =>
    !!message?.subscriptionId;

export const isApiGatewayHubRequestCancel = (message: any): message is string =>
    typeof message === "string";
