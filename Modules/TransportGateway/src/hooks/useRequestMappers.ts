import { useContext, useMemo, useRef } from "react";
import { isEqual } from "lodash";
import { useDispatch } from "react-redux";

import { hash } from "Utilities/index";

import { ViewServerTransitionContext } from "context";
import {
    CommandScopedConfig,
    ConnectedComponentProps,
    IDataGetter,
    IDataGetterDecorated,
    VSTransitionProps
} from "../types";

export const normalizeTransactionId = (
    id: ConnectedComponentProps["$transactionId"]
): string | number => {
    if (Array.isArray(id)) {
        return hash(id.map(normalizeTransactionId).join());
    }

    if (typeof id === "object") {
        return hash(JSON.stringify(id));
    }

    return id;
};

export const useRequestMappers = <P>(config: CommandScopedConfig, enabled = true) =>
    function useRequestMappersInner(props: P & ConnectedComponentProps): IDataGetterDecorated[] {
        const prevSpecs = useRef<IDataGetterDecorated[]>();
        const activePrevSpecs = prevSpecs.current;
        const dispatch = useDispatch();

        const { dataGetterSpec } = config;
        const { connectionName } = useContext<VSTransitionProps>(ViewServerTransitionContext);
        const specs = useMemo(() => {
            if (!enabled) {
                return activePrevSpecs;
            }

            const createDecoratedSpec = (
                spec: IDataGetter,
                request,
                propName: string,
                hasChanges = false,
                isStale = false
            ) => ({
                ...spec,
                request,
                propName,
                hasChanges,
                loadSpecData: (isVolatile = false) => {
                    if (request) {
                        const { $transactionId, ...commandRequest } = request;
                        return spec.loadDataWithRequest(dispatch, isVolatile, isStale)(
                            commandRequest,
                            propName,
                            spec.connectionName || connectionName
                        );
                    }

                    return spec.loadDataWithRequest(dispatch, isVolatile)(
                        undefined,
                        propName,
                        spec.connectionName || connectionName
                    );
                }
            });

            const activeSpecs: IDataGetterDecorated[] = Object.keys(dataGetterSpec).map((el, i) => {
                const spec = dataGetterSpec[el];
                const prevSpec = activePrevSpecs?.[i];

                const mappedRequest = spec.getRequest(props);
                const $transactionId = normalizeTransactionId(props?.$transactionId);
                const request =
                    $transactionId !== undefined && mappedRequest
                        ? {
                              ...mappedRequest,
                              $transactionId
                          }
                        : mappedRequest;
                const prevRequest = prevSpec?.request;

                const { $transactionId: tr, ...r } = request || {};
                const { $transactionId: tp, ...p } = prevRequest || {};

                const hasChangedRequest = !isEqual(r, p);
                const hasChangedTransactionId = tr?.toString() !== tp?.toString();

                const hasChanged =
                    spec?.commandName !== prevSpec?.commandName ||
                    hasChangedRequest ||
                    hasChangedTransactionId;

                return createDecoratedSpec(
                    spec,
                    request,
                    el,
                    hasChanged,
                    request !== undefined && !hasChangedRequest && prevRequest
                );
            });

            return activeSpecs;
        }, [dataGetterSpec, activePrevSpecs, props, dispatch, connectionName]);

        prevSpecs.current = specs;

        return specs;
    };
