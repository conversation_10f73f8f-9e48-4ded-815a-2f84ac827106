import { useContext, useEffect, useState } from "react";

import { TraceItem } from "@tradinglabs/viewserver-core";

import { ServerContext } from "../context";

export const useTrace = (commandId: string | number) => {
    const client = useContext(ServerContext);
    const trace = client?.getTrace?.();
    const [traceItem, setTraceItem] = useState<TraceItem[] | undefined>(
        trace?.getTraces(commandId)
    );

    useEffect(() => {
        const unsubscribe = trace?.subscribe(commandId, setTraceItem);
        return () => {
            if (unsubscribe) {
                unsubscribe();
            }
        };
    }, [commandId, trace]);

    if (traceItem?.length > 0) {
        const isComplete = () => traceItem?.some(({ type }) => type === "complete") ?? false;
        const isInitialised = () => traceItem?.some(({ type }) => type === "init") ?? false;

        return {
            commandId,
            traceItem,
            isInitialised,
            isPending: () =>
                isInitialised() &&
                !isComplete() &&
                (traceItem?.some(({ type }) => type === "message") ?? false),
            isComplete,
            hasError: () => traceItem?.some(({ type }) => type === "error") ?? false,
            getTiming: () => {
                const epoc = traceItem?.find(({ type }) => type === "init");
                const firstMessage = traceItem?.find(({ type }) => type === "message") || epoc;
                const complete = traceItem?.find(({ type }) => type === "complete");

                if (epoc && firstMessage && complete) {
                    return {
                        pending: firstMessage.timestamp - epoc.timestamp,
                        complete: complete.timestamp - epoc.timestamp
                    };
                }
            }
        };
    }
};
