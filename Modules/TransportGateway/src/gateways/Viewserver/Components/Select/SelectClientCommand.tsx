import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";

import {
    SelectAsyncProps,
    Option,
    SelectAsync,
    OptionGroup,
    isGroupOptions,
    getArrayValue,
    formatOptionsByGroup,
} from "PrimitiveComponents/index";

import { VIEWSERVER_CONNECTION_NAME } from "gateways/Viewserver/consts";
import type { InvokeClientCommandApi } from "../../../../hooks/useInvokeCommand";
import {
    useInvokeViewserverCommand,
    useInvokeViewserverCommandForConnection,
} from "../../hooks/useInvokeViewserverCommand";

export type ResponseElementDto = {
    Name: string;
    Value: string;
    Description?: string;
    Group?: string;
};

const SearchFunctionFactory =
    (
        invoke: InvokeClientCommandApi["invoke"],
        reportContentType?: string,
        mapValueToOption?: (value: ResponseElementDto) => Option,
        commandName = "GetValuesOfTypeHandler",
        commandPayload?: any,
        commandResponseMapper?: (response: any) => Option[],
    ) =>
    async (query: string): Promise<Option[]> => {
        try {
            const valuesForProperty = await invoke<unknown, { Values: ResponseElementDto[] }>(
                commandName,
                commandPayload || {
                    FieldType: reportContentType,
                    Categories: [reportContentType],
                    SearchString: query,
                },
            );

            return commandResponseMapper
                ? commandResponseMapper(valuesForProperty)
                : valuesForProperty.Values?.map(value =>
                      mapValueToOption
                          ? mapValueToOption(value)
                          : {
                                label: value.Name,
                                value: value.Value,
                                description: value.Description,
                                group: value.Group,
                            },
                  )
                      // Not all handlers actually perform a search on the query and return all results - so lets filter in the UI...
                      ?.filter(
                          ({ label }) =>
                              typeof label === "string" &&
                              label.toLocaleLowerCase().indexOf(query.toLocaleLowerCase()) !== -1,
                      );
        } catch (err) {
            console.error(err);
        }
    };

export type SelectClientCommandProps = {
    commandName?: string;
    commandPayload?: any;
    commandResponseMapper?: (response: any) => Option[];
    reportContentType?: string;
    mapValueToOption?: (value: ResponseElementDto) => Option;
    isGrouped?: boolean;
    onSearchComplete?: (options: Option[]) => Option[] | void;
} & SelectAsyncProps;

export const SelectClientCommand = ({
    commandName,
    commandPayload,
    commandResponseMapper,
    reportContentType,
    mapValueToOption,
    value,
    isGrouped,
    onSearchComplete,
    ...props
}: SelectClientCommandProps) => {
    const { invoke } = useInvokeViewserverCommandForConnection(VIEWSERVER_CONNECTION_NAME);
    const [availableOptions, setAvailableOptions] = useState<Option[] | OptionGroup[]>();
    const mounted = useRef(true);

    useEffect(() => {
        return () => {
            mounted.current = false;
        };
    }, []);

    const search = useCallback(
        async (query: string) => {
            const searchOptions = await SearchFunctionFactory(
                invoke,
                reportContentType,
                mapValueToOption,
                commandName,
                commandPayload,
                commandResponseMapper,
            )(query);
            // @TODO: Currently assume that the  initial search returns all available options... :( need to replace with explicit query
            if (mounted.current) {
                setAvailableOptions(options => options ?? searchOptions);
            }
            const options = (onSearchComplete && onSearchComplete(searchOptions)) || searchOptions;
            return isGrouped ? formatOptionsByGroup(options) : options;
        },
        [
            invoke,
            reportContentType,
            mapValueToOption,
            commandName,
            commandPayload,
            commandResponseMapper,
            onSearchComplete,
            isGrouped,
        ],
    );

    const optionsHash = useMemo(
        () =>
            (isGroupOptions(availableOptions)
                ? (Array.isArray(availableOptions) &&
                      availableOptions.flatMap<Option>(option => option.options as Option[])) ||
                  undefined
                : availableOptions
            )?.reduce(
                (hash, option) => ({
                    ...hash,
                    [option.value?.toString()]: option,
                }),
                {} as {
                    [key: string]: Option;
                    [key: number]: Option;
                },
            ),
        [availableOptions],
    );

    const selectedValue = useMemo(() => {
        if (value !== undefined && value !== null && optionsHash) {
            return getArrayValue(value).map(value => ({
                ...optionsHash[value],
                label: optionsHash[value?.toString()]?.label ?? value?.toString(),
                value,
            }));
        }
        return (
            (value && {
                label: value,
                value,
            }) ||
            undefined
        );
    }, [value, optionsHash]);

    return (
        <SelectAsync
            {...props}
            value={selectedValue}
            loadOptions={search}
        />
    );
};
