import { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { get, isEqual } from "lodash";

import {
    viewserverSubscriptionAction,
    viewServerRequestAction
} from "@tradinglabs/viewserver-redux";

import { useSelector } from "../../../hooks/useSelector";

export enum RequestState {
    Loading,
    Done,
    Error
}

export type CommandState<T> =
    | { status: RequestState.Loading }
    | { status: RequestState.Done; data: T }
    | { status: RequestState.Error; message: string };

export const useViewServerCommand = <T>(
    connectionName: string,
    commandName: string,
    props: any
): CommandState<T> => {
    const isRequestResponse = commandName.indexOf("RequestResponse_Handler") > -1;
    const [isSubscribed, setSubscribed] = useState<boolean>(false);
    const isConnected = useSelector(state =>
        get(state, ["connection", "connections", connectionName, "status", "isConnected"])
    );
    const data = useSelector(
        state => get(state, ["subscriptions", connectionName, commandName, "data"]),
        isEqual
    );
    const error = useSelector(
        state => get(state, ["subscriptions", connectionName, commandName, "error"]),
        isEqual
    );
    const dispatch = useDispatch();

    useEffect(() => {
        if (isConnected && !isSubscribed) {
            setSubscribed(true);
            const action = { commandName, key: commandName, payload: props, connectionName };
            if (isRequestResponse) {
                dispatch(viewServerRequestAction({ ...action, isVolatile: false }));
            } else {
                dispatch(viewserverSubscriptionAction(action));
            }
        }
    }, [
        commandName,
        connectionName,
        props,
        isConnected,
        isSubscribed,
        isRequestResponse,
        dispatch
    ]);

    return {
        data,
        // eslint-disable-next-line no-nested-ternary
        status: error ? RequestState.Error : data ? RequestState.Done : RequestState.Loading,
        message: error
    };
};
