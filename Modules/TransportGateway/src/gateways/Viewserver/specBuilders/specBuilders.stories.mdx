import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

<Meta title="TransportGateway/Spec Builders/Viewserver" />

# Viewserver Specs

## `reportSubscriptionCommandSpec`

Subscribe to a report.

#### Example

```js
export const ExampleConnectedComponent = withCommandState(
    MyComponent,
    createConfig({
        props: reportSubscriptionCommandSpec(
            "Template-Examples-md",
            ({ strategyLongShort }) => ({ strategyLongShort }),
            (data, props, schema) => ({ data, schema })
        )
    })
);
```