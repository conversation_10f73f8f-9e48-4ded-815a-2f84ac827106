import { debounce } from "lodash";

import {
    viewServerRequestAction,
    VIEW_SERVER_TEARDOWN_PAYLOAD,
    VIEW_SERVER_UNSUBSCRIBE_PAYLOAD
} from "@tradinglabs/viewserver-redux";

import { DataMapper, IDataGetter, RequestMapper } from "../../../types";

import { VIEWSERVER_CONNECTION_NAME } from "../consts";

export const buildRequestResponseSpec =
    (isVolatile = false) =>
    <RequestProps extends any, MapperProps extends any, D extends any>(
        commandName: string,
        requestMapper: RequestMapper<RequestProps> = _ => ({}),
        dataMapper: DataMapper<MapperProps, D> = data => data,
        connectionName?: string
    ): IDataGetter<MapperProps & RequestProps, D> => {
        const getRequest = (props: RequestProps) => (props ? requestMapper(props) : undefined);

        return {
            connectionName: connectionName ?? VIEWSERVER_CONNECTION_NAME,
            commandName,
            commandKey: commandName,
            getDataFromState: (commandState, props) =>
                dataMapper(commandState.data, props, commandState.schema, commandState.metaData),
            getRequest,
            loadDataWithRequest(dispatch, isVolatileSpec) {
                const { commandKey } = this;

                return debounce((request, propName) => {
                    dispatch(
                        viewServerRequestAction({
                            commandName,
                            key: commandKey,
                            payload: request || VIEW_SERVER_TEARDOWN_PAYLOAD,
                            connectionName: connectionName ?? VIEWSERVER_CONNECTION_NAME,
                            isVolatile: isVolatileSpec || isVolatile,
                            propName
                        })
                    );
                }, 10);
            },
            unsubscribe(dispatch) {
                const { commandKey } = this;

                return () => {
                    dispatch(
                        viewServerRequestAction({
                            commandName,
                            key: commandKey,
                            payload: VIEW_SERVER_UNSUBSCRIBE_PAYLOAD,
                            connectionName: connectionName ?? VIEWSERVER_CONNECTION_NAME
                        })
                    );
                };
            }
        };
    };
