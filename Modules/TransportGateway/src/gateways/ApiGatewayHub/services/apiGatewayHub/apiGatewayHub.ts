import { Subject } from "rxjs";
import { filter, take } from "rxjs/operators";

import { TraceApi } from "@tradinglabs/viewserver-core";

import { CommandMessage, Message, ServiceApi } from "../../../../types";
import { getType, traceMessage } from "../../../../utils/message";

import { apiGatewayHubInitCompleteMessage, apiGatewayHubInitMessage } from "./messages";

export const ApiGatewayHub = (url: string, trace?: TraceApi): ServiceApi => {
    const worker = new Worker(new URL("./apiGatewayHub.worker", (import.meta as any).url));

    const messages = new Subject<Message>();

    const postMessage = (message: CommandMessage) => worker.postMessage(message);

    if (trace) {
        messages.pipe(filter(({ type }) => type === getType(traceMessage))).subscribe(message => {
            const { type, commandId, payload } = traceMessage.getPayload(message);
            if (type === "ADD") {
                trace?.addTrace(commandId, payload.timestamp, payload.traceItem);
            } else if (type === "REMOVE") {
                trace?.removeTrace(commandId);
            }
        });
    }

    return {
        connect: () =>
            new Promise<void>((resolve, reject) => {
                worker.addEventListener("message", ({ data: message }: MessageEvent) =>
                    messages.next(message)
                );

                const init = messages
                    .pipe(
                        filter(({ type }) => type === getType(apiGatewayHubInitCompleteMessage)),
                        take(1)
                    )
                    .subscribe(message => {
                        init.unsubscribe();
                        if (message.payload.errorMessage) {
                            reject(new Error(message.payload.errorMessage));
                        } else {
                            resolve();
                        }
                    });

                postMessage(
                    apiGatewayHubInitMessage({
                        url,
                        tracingEnabled: !!trace
                    })
                );
            }),
        dispatch: (message, commandState) => postMessage({ ...message, commandState }),
        dataSink: messages,
        invokeClientCommand: () => (_commandName, _commandPayload, _onStatus) =>
            Promise.reject(new Error("@TODO: not implemented"))
    };
};
