import React, { ReactNode, createContext, useCallback, useContext, useEffect } from "react";

import { getDuration, wait } from "Utilities/index";

import { resolveEndpoint } from "../../../utils/resolveEndpoint";
import { useServiceConfig } from "../../../hooks/useServiceConfig";
import { getResponse } from "../../../transport/implementations/Request/getResponse";
import { ApplicationError } from "../../../ApplicationError";

const HeartbeatContext = createContext(false);

type HeartbeatProps = {
    children: ReactNode;
    intervalMS?: number;
};

const HeartbeatSchedule = ({ intervalMS, children }: HeartbeatProps) => {
    const { apiGatewayEndpoint } = useServiceConfig();

    const invoke = useCallback(async () => {
        const { commandUrl } = resolveEndpoint("user.keep-alive", {}, apiGatewayEndpoint);

        return getResponse(
            await fetch(commandUrl, {
                method: "GET"
            })
        );
    }, [apiGatewayEndpoint]);

    const scheduleInvoke = useCallback(async () => {
        await wait(intervalMS || getDuration("MINUTE", 10));
        return invoke();
    }, [intervalMS, invoke]);

    const run = useCallback(async () => {
        try {
            await scheduleInvoke();
        } catch (ex) {
            const { errorCode } = ex as ApplicationError;
            if (errorCode === "401") {
                window.location.reload();
            } else {
                console.error("There was a problem with the heartbeat", ex);
            }
        } finally {
            run();
        }
    }, [scheduleInvoke]);

    useEffect(() => {
        run();
    }, [run]);

    return <>{children}</>;
};

export const Heartbeat = ({ children, ...rest }: HeartbeatProps) => {
    const hasHeartbeat = useContext(HeartbeatContext);

    // Only add a heartbeat to the outermost component in the branch
    if (hasHeartbeat) {
        return <>{children}</>;
    }

    return (
        <HeartbeatContext.Provider value>
            <HeartbeatSchedule {...rest}>{children}</HeartbeatSchedule>
        </HeartbeatContext.Provider>
    );
};
