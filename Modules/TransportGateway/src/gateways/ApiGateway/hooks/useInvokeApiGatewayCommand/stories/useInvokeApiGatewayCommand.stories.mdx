import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import { useInvokeApiGatewayCommand } from "../useInvokeApiGatewayCommand";

import { useInvokeApiGatewayCommandDocMethods } from "./storybook.doc";

<Meta title="TransportGateway/Hooks/useInvokeApiGatewayCommand" component={useInvokeApiGatewayCommandDocMethods} />

# useInvokeApiGatewayCommand

<Description of={useInvokeApiGatewayCommand} />

<ArgTypes of={useInvokeApiGatewayCommandDocMethods} sort="requiredFirst" />

**Prefer using connected commponents `withCommandState` and spec builders where possible**.

## Lifecycle

Invoked commands behave in the same way as connected commands; they are queued and the retry strategy is applied
on error. Invoking a command when a previous request is in-flight will abort the active request
(throws abort exception) and the new requests is added to the queue.

In-flight commands will be aborted when the hook un-mounts.

**NOTE: All requests are invoked with `POST`**

## `invoke`

#### Examples

##### Simple invocation with hook

```tsx
export const MyComponent = () => {
    const { invoke } = useInvokeApiGatewayCommand();

    const testInvokeCommand = async () => {
        try {
            const { data, schema } = await invoke<
                ApiGatewayService.Position.OverviewRequest,
                ApiGatewayService.Position.OverviewResponse
            >(
                "position.overview-table",
                {
                    traderId: 9
                }
            );

            console.log("Got response", data, schema);
        } catch (ex) {
            console.error(ex);
        }
    };

    return (
        <button onClick={testInvokeCommand}>INVOKE</button>
    );
};
```

##### Example with status

The `onStatus` callback can be used to retrieve the current state of the invocation:

```tsx
export const MyComponent = () => {
    const { invoke } = useInvokeApiGatewayCommand();

    const testInvokeCommand = async () => {
        try {
            const { data, schema } = await invoke<
                ApiGatewayService.Position.OverviewRequest,
                ApiGatewayService.Position.OverviewResponse
            >(
                "position.overview-table",
                {
                    traderId: 9
                },
                (status, message) => {
                    console.log("Status", status, message);
                }
            );

            console.log("Got response", data, schema);
        } catch (ex) {
            console.error(ex);
        }
    };

    return (
        <button onClick={testInvokeCommand}>INVOKE</button>
    );
};
```
