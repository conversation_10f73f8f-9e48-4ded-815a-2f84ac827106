import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

<Meta title="TransportGateway/Spec Builders/ApiGateway" />

# ApiGateway Specs

## `apiGatewaySpec`

Request / response to a `ApiGateway` api command with optional long-polling.

#### Example

```js
export const ExampleConnectedComponent = withCommandState(
    MyComponent,
    createConfig({
        props: apiGatewaySpec<
            ApiGatewayService.Position.OverviewRequest,
            ApiGatewayService.Position.OverviewResponse,
            PositionOverviewProps
        >(
            "position.overview-table",
            ({ traderId, fundGroupId, strategyId, strategyLongShort }) => ({
                traderId,
                fundGroupId,
                strategyId,
                strategyLongShort
            }),
            (data, props, schema) => ({ data: data?.[0], schema }),
            {
                longPolling: true,
                longPollingDelayMS: getDuration("MINUTE")
            }
        )
    })
);
```