import React, { ComponentType } from "react";
import { Provider } from "react-redux";

import { HOC } from "PrimitiveComponents/index";

import { useServerStore } from "../hooks/useServerStore";
import { ServerContext } from "../context";

export const withGatewayProvider = <P,>(Component: ComponentType<P>) => {
    const WrappedComponent = (props: P) => {
        const store = useServerStore();

        return (
            <Provider store={store}>
                <ServerContext.Provider value={store.client}>
                    <Component {...props} />
                </ServerContext.Provider>
            </Provider>
        );
    };

    return HOC("withGatewayProvider", WrappedComponent, Component);
};
