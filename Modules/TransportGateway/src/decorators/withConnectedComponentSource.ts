import { ComponentType, ReactElement } from "react";

import { createConfig } from "../utils/createConfig";
import { withCommandStateFactory } from "./withCommandStateFactory";

import { reportSubscriptionCommandSpec } from "../gateways/Viewserver";
import { apiGatewaySpec, ApiSpecOptions } from "../gateways/ApiGateway";

type Source = "viewserverReportSubscription" | "apiGateway";

type ConnectedSourceComponentViewServerProps = {
    reportKey: string;
    outputOperatorName?: string;
};

type ConnectedSourceComponentApiGatewayProps = {
    commandName: string;
    connectionOptions?: ApiSpecOptions;
};

type ConnectedSourceComponentConnectionProps<S extends Source> =
    S extends "viewserverReportSubscription"
        ? ConnectedSourceComponentViewServerProps
        : S extends "apiGateway"
        ? ConnectedSourceComponentApiGatewayProps
        : never;

type ConnectedSourceComponentProps<
    S extends Source,
    P extends ConnectedSourceComponentConnectionProps<S> = ConnectedSourceComponentConnectionProps<S>
> = {
    source?: S;
    params?: {};
} & P;

const isViewserverReportSubscriptionSource = <S extends Source>(
    props: ConnectedSourceComponentProps<S>
): props is ConnectedSourceComponentProps<S> & ConnectedSourceComponentViewServerProps =>
    !props?.source || props?.source === "viewserverReportSubscription";

const isApiGatewaySource = <S extends Source>(
    props: ConnectedSourceComponentProps<S>
): props is ConnectedSourceComponentProps<S> & ConnectedSourceComponentApiGatewayProps =>
    props?.source === "apiGateway";

export const withConnectedComponentSource = <P extends {}, S extends Source>(
    Component: ComponentType<ConnectedSourceComponentProps<S> & P>
) =>
    withCommandStateFactory(Component, (props: ConnectedSourceComponentProps<S> & P) => {
        if (isViewserverReportSubscriptionSource(props)) {
            const { reportKey, outputOperatorName } = props;
            return createConfig({
                props: reportSubscriptionCommandSpec(
                    reportKey,
                    ({ params }) => ({ ...params }),
                    (data, _, schema) => ({
                        data,
                        schema
                    }),
                    outputOperatorName
                )
            });
        }

        if (isApiGatewaySource(props)) {
            const { commandName, connectionOptions } = props;
            return createConfig({
                props: apiGatewaySpec(
                    commandName,
                    ({ params }) => ({ ...params }),
                    (data, _, schema) => ({ data, schema }),
                    connectionOptions
                )
            });
        }

        throw new TypeError(`Invalid source '${props.source || "viewserverReportSubscription"}'`);
    }) as unknown as <S extends Source = "viewserverReportSubscription">(
        props: ConnectedSourceComponentProps<S> & P
    ) => ReactElement; // Cast to retain HKT
