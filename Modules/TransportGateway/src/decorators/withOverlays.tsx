import React, { ComponentType, memo } from "react";

import { HOC } from "PrimitiveComponents/index";

import { useConfig } from "../hooks/useConfig";

type WrappedComponentProps = {};

export const withOverlays = (Component: ComponentType<any>) => {
    const componentName = Component.displayName;
    const WrappedComponent = memo((props: WrappedComponentProps) => {
        const config = useConfig();
        return (config?.overlays ?? []).reduce(
            (children, Overlay) => <Overlay {...{ ...props, componentName }}>{children}</Overlay>,
            <Component {...props} />
        );
    });

    return HOC("withOverlays", WrappedComponent, Component);
};
