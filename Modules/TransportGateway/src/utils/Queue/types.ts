export type Context = {
    tries: number;
    queuePosition?: number;
    error?: Error;
    schedule?: number;
    running?: boolean;
    abortController?: AbortController;
};

export type RetryStrategy = number[];

export type Schedule = {
    /**
     * Offset t + ms
     */
    offset: number;
};

export type JobOptions = {
    /**
     * ID used to keep track of a job for updating / cancellation
     */
    id?: string;
    /**
     * Array of ms offsets that can be configured to set the maximum number of retries
     * with a back-off stragegy if required, e.g. [500, 1000, 2000, 4000, 5000]
     */
    retryStrategy?: RetryStrategy;
    schedule?: Schedule;
    /**
     * Called once during job initialisation
     */
    onInit?: (context: Context) => void;
    onMetaData?: (context: Context) => void;
    /**
     * Called once when the job fails immediately if no retry of after the retryStrategy has played
     */
    onFail?: (context: Context) => void;
};

export type Job = {
    task: Task;
    context: Context;
    options: JobOptions;
};

export type TaskResult = {
    schedule?: number;
};

/**
 * Resolve false to prevent and rescheduling of the job
 */
export type Task = (
    context: Context,
    abortSignal: AbortSignal
) => Promise<void | undefined | boolean | TaskResult>;

export type QueueOptions = {
    /**
     * Maximum number of concurrent jobs to run
     */
    concurrency?: number;
};

export type JobList = {
    getItems: () => Jobs;
    getLength: () => number;
    find: (id: string) => void | Job;
    remove: (id: string) => void;
    add: (job: Job) => Promise<void | undefined | boolean | TaskResult>;
    cancel: (id: string) => void | Job;
};

export type Jobs = Job[];
