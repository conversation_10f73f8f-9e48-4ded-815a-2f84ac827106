import { createContext } from "react";

import { MetadataStatusSpecs } from "../../hooks/useCommandMetaState";

export type StatusBoundaryConfig = {
    hasDisabledState?: boolean;
    hasErrorState?: boolean;
    propagateChildStatus?: boolean;
};
export type StatusBoundaryApi = {
    setCommandStatus?: (specs: MetadataStatusSpecs) => void;
    removeCommandStatus?: (specs: MetadataStatusSpecs) => void;
    config?: StatusBoundaryConfig;
};

export const StatusBoundaryContext = createContext<StatusBoundaryApi>(undefined);

export type StatusBoundarySpecsApi = MetadataStatusSpecs | undefined;

export const StatusBoundarySpecsContext = createContext<StatusBoundarySpecsApi>(undefined);
