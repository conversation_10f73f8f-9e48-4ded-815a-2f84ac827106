import React, { useMemo, useState } from "react";
import styled from "styled-components";

import { ContextMenu, MenuItem, InputWithLabel } from "PrimitiveComponents/index";
import { AppIcon } from "Theming/index";
import type { Schema } from "Contracts/index";
import { Grid } from "../Grid";

const convertObjectToColumnDefs = (data: {}[]): Record<string, any>[] => {
    const allKeys = data?.reduce(
        (keys, item) => ({
            ...keys,
            ...Object.keys(item).reduce((a, v) => ({ ...a, [v]: v }), {}),
        }),
        {},
    );

    return (
        (allKeys &&
            Object.keys(allKeys ?? {})
                .sort((a, b) => a.localeCompare(b, undefined, { sensitivity: "base" }))
                .map(field => ({
                    contentType: 9,
                    field,
                    headerName: field,
                    sortable: true,
                    resizable: true,
                    valueGetter: ({ data, colDef }) => {
                        try {
                            return data?.[colDef.field] && typeof data?.[colDef.field] === "object"
                                ? JSON.stringify(data?.[colDef.field])
                                : data?.[colDef.field];
                        } catch (ex) {
                            return `format error: ${ex.message}`;
                        }
                    },
                }))) ||
        []
    );
};

const Container = styled.div`
    display: flex;
    flex-direction: column;
    height: 120px;
    position: relative;

    .meta {
        font-size: 9px;
        line-height: 1.5;
        padding: 2px 4px;
        position: absolute;
        right: 0;
        z-index: 1;
        background-color: #555;
        border-radius: 4px;
        margin: 2px 4px;
    }
`;

const Payload = styled.pre`
    &&&& {
        font-size: 10px;
        margin: 0;
        padding: 5px;
        line-height: 1;
    }
`;

const Query = styled(InputWithLabel)`
    &&&& {
        max-width: 200px;
        margin: 4px;
        font-size: 10px;

        .ant-input {
            font-size: inherit;
            margin: 0;
            padding-top: 0;
            padding-bottom: 0;
        }
    }
`;

const ObjectContextMenu = styled(ContextMenu)`
    overflow: hidden;
`;

type DataProps<D extends {}, P> = {
    data: D[] | D;
    // eslint-disable-next-line react/no-unused-prop-types
    schema: Schema;
    payload?: P;
};

export const Data = <D extends {}, P>({ data, payload }: DataProps<D, P>) => {
    const [filterColumns, setFilterColumns] = useState("");

    const columnDefs = useMemo(
        () => Array.isArray(data) && convertObjectToColumnDefs(data),
        [data],
    );

    const fileterdColumnDefs = useMemo(() => {
        if (columnDefs) {
            const filterColumnsQuery = new RegExp(filterColumns, "i");
            return columnDefs.filter(
                ({ field }) => !filterColumns || filterColumnsQuery.test(field),
            );
        }
        return undefined;
    }, [columnDefs, filterColumns]);

    return (
        <>
            {Array.isArray(data) && (
                <Query
                    size="small"
                    value={filterColumns}
                    onChange={setFilterColumns}
                    placeholder="Filter Columns…"
                />
            )}
            <Container>
                {Array.isArray(data) ? (
                    <>
                        <div className="meta">
                            {data?.length ?? 0} rows / {columnDefs?.length ?? 0} cols
                        </div>
                        <Grid
                            data={data ?? []}
                            columnDefs={fileterdColumnDefs}
                        />
                    </>
                ) : (
                    <ObjectContextMenu
                        menuItems={[
                            <MenuItem
                                key="dump-object"
                                icon={<AppIcon type="downArrow" />}
                                onSelect={() => console.log(data)}
                            >
                                Dump object to console…
                            </MenuItem>,
                        ]}
                    >
                        <pre style={{ fontSize: 10 }}>{JSON.stringify(data || null, null, 2)}</pre>
                    </ObjectContextMenu>
                )}
            </Container>
            {payload && Object.keys(payload).length > 0 && (
                <Payload>{JSON.stringify(payload, null, 2)}</Payload>
            )}
        </>
    );
};
