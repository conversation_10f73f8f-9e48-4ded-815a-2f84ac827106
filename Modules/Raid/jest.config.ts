import type { Config } from "jest";
import { jestBaseConfig } from "../../config/jest/jest.config.base";
import { getPath } from "../../scripts/jest/getPath";
import { name as displayName } from "./package.json";

const packageName = process.cwd() === __dirname ? process.cwd() : getPath(__dirname);

const config: Config = {
    ...jestBaseConfig({ displayName, packageName })
};

export default config;
