import type { RaidFilters } from "Contracts/index";
import { createPersistSettings, PersistSettingsLocalStorage } from "PrimitiveComponents/index";
import type { ReportConfig } from "ReportEngine/index";

type RaidSettings = {
    filters?: RaidFilters;
    reports?: {
        [name: string]: ReportConfig["ParameterValues"];
    };
};

export const { PersistSettingsProvider, usePersistSettings, usePersistState } =
    createPersistSettings<RaidSettings>(PersistSettingsLocalStorage("raid2-user-settings"));
