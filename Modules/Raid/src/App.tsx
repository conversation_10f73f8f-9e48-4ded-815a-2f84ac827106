import React, { useRef } from "react";
import { HashRouter } from "react-router-dom";
import styled from "styled-components";

import { BaseStyles } from "Theming/index";
import { ClientApiProvider } from "ClientApi/index";
import {
    SimpleLoader,
    PortalProvider,
    TickerTooltipProvider,
    withLazyComponent,
    withStandardErrorBoundary,
    DocumentProvider,
    ComponentsPersistSettingsProvider,
    withAppRestart,
    useComponentsPersistTheme,
} from "PrimitiveComponents/index";
import { GatewayProvider, isRustViewServerEnabled } from "TransportGateway/index";
import { getSearchParams } from "Utilities/index";
import { withFeatureProvider } from "ConnectedComponents/index";

import { RaidNavigationProvider } from "./Components";
import { PersistSettingsProvider } from "./persistSettings";
import { Raid } from "./Raid";

const ContextMenuTicker = withLazyComponent(
    () => import("ConnectedComponents/index"),
    "ContextMenuTicker",
);
const CompanySummary = withLazyComponent(
    () => import("CompanyPage/CompanySummary"),
    "CompanySummary",
);

const RaidClientApiProvider = styled(ClientApiProvider)`
    height: 100vh;
`;

const AppInner = withFeatureProvider(() => {
    const searchParams = useRef(
        getSearchParams<{
            launchPadEnabled?: boolean;
        }>(window.location.search),
    );

    const [theme] = useComponentsPersistTheme();

    return (
        <BaseStyles
            theme={theme}
            global
        >
            <TickerTooltipProvider
                ContextMenuComponent={ContextMenuTicker}
                ToolTipComponent={CompanySummary}
            >
                <RaidClientApiProvider
                    launchPadEnabled={searchParams.current.launchPadEnabled ?? true}
                >
                    <RaidNavigationProvider>
                        <PortalProvider>
                            <Raid />
                        </PortalProvider>
                    </RaidNavigationProvider>
                </RaidClientApiProvider>
            </TickerTooltipProvider>
        </BaseStyles>
    );
});

export const App = withAppRestart(
    withStandardErrorBoundary(() => {
        return (
            <DocumentProvider appName="Raid 2.0">
                <HashRouter>
                    <ComponentsPersistSettingsProvider>
                        {(_, componentsMetadata) => (
                            <PersistSettingsProvider>
                                {(_, metadata) => (
                                    <SimpleLoader
                                        isLoading={
                                            !metadata?.complete || !componentsMetadata?.complete
                                        }
                                    >
                                        {metadata?.complete && (
                                            <GatewayProvider
                                                includeServiceApiGatewayHub
                                                includeServiceWSViewserver
                                            >
                                                <AppInner />
                                            </GatewayProvider>
                                        )}
                                    </SimpleLoader>
                                )}
                            </PersistSettingsProvider>
                        )}
                    </ComponentsPersistSettingsProvider>
                </HashRouter>
            </DocumentProvider>
        );
    }),
);
