import React, { ReactNode } from "react";
import { createPortal } from "react-dom";
import { withStandardErrorBoundary } from "../../ErrorFallback";
import { usePortalAPI } from "../PortalAPI";

type PortalProps = {
    target?: string;
    children: ReactNode;
};

export const Portal = withStandardErrorBoundary(({ target = "main", children }: PortalProps) => {
    const { getPortal } = usePortalAPI();
    const mountPoint = getPortal?.(target);

    return mountPoint ? createPortal(children, mountPoint) : <>{children}</>;
});
