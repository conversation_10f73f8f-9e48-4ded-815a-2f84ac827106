import React from "react";
import styled, { css } from "styled-components";

import {
    type GetFormattedValueOptions,
    getFormattedValue,
    castNumber,
    CSSGap,
    type CSSGaps,
    useColourScale,
} from "Utilities/index";

import { Arrow } from "./Arrow";

type ContainerProps = {
    colorValue: number;
    arrowValue: number;
    highlightPositiveValue: boolean;
    gradientColor?: string;
} & CSSGaps;
const Container = styled.span<ContainerProps>`
    display: inline-flex;
    align-items: center;
    justify-content: inherit;
    text-align: inherit;
    text-decoration: inherit;
    ${CSSGap}

    svg + span {
        margin-left: 0.2em;
    }

    ${({ colorValue, highlightPositiveValue, gradientColor }) => {
        if (gradientColor) {
            return css`
                color: ${gradientColor};
            `;
        }

        if (colorValue < 0) {
            return css`
                color: var(--number__negative__color, #9f2727);
            `;
        }
        if (colorValue > 0) {
            if (!highlightPositiveValue) return "";

            return css`
                color: var(--number__positive__color, green);
            `;
        }

        return css`
            color: inherit;
        `;
    }}
    }

    ${({ arrowValue }) => {
        if (arrowValue < 0) {
            return css`
                svg {
                    transform: rotate(180deg);
                }
            `;
        }
        return "";
    }}

    span + svg {
        margin-left: 0.2em;
    }

    > .value {
        flex-grow: 1;
    }
`;

export type ChangeProps = {
    value: number | string;
    outlined?: boolean;
    className?: string;
    title?: string;

    // color
    highlightPositiveValue?: boolean;
    colorValue?: number;
    colorMode?: "positive-negative" | "gradient";
    colorMin?: number;
    colorMax?: number;

    // arrow
    showArrowIndicatorBefore?: boolean;
    showArrowIndicator?: boolean;
    arrowValue?: number;
} & GetFormattedValueOptions &
    CSSGaps;
export const Change = ({
    value,
    outlined = true,
    className,
    gap = "small",
    title,

    // color
    highlightPositiveValue = true,
    colorValue: colorValueProp,
    colorMode = "positive-negative",
    colorMin = -2,
    colorMax = 2,

    // arrow
    showArrowIndicatorBefore = false,
    showArrowIndicator = false,
    arrowValue: arrowValueProp,

    // value formatting
    formatterType = "percent",
    valueFormat,
    zeroValue,
    showPositiveIndicator,
    showNegativeIndicator,
    bracketNegativeValue,
    defaultValue,
    prefix,
    suffix,
}: ChangeProps) => {
    const { castedValue, isNumber } = castNumber(value);

    const arrowValue = arrowValueProp ?? (isNumber ? castedValue : 0);
    const colorValue = colorValueProp ?? (isNumber ? castedValue : 0);

    const gradientColor =
        useColourScale(colorValue, {
            min: colorMax,
            max: colorMin,
            highlighted: true,
        })?.toString() ?? "";

    return (
        <Container
            data-component="Change"
            className={className}
            colorValue={colorValue}
            arrowValue={arrowValue}
            highlightPositiveValue={highlightPositiveValue}
            gap={gap}
            gradientColor={colorMode === "gradient" ? gradientColor : undefined}
            title={title}
        >
            {isNumber && showArrowIndicator && showArrowIndicatorBefore && (
                <Arrow outlined={outlined} />
            )}
            <span className="value">
                {getFormattedValue(value, {
                    formatterType,
                    valueFormat,
                    zeroValue,
                    showPositiveIndicator,
                    showNegativeIndicator,
                    bracketNegativeValue,
                    defaultValue,
                    prefix,
                    suffix,
                })}
            </span>
            {isNumber && showArrowIndicator && !showArrowIndicatorBefore && (
                <Arrow outlined={outlined} />
            )}
        </Container>
    );
};
