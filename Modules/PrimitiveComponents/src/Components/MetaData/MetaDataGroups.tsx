/* eslint-disable @typescript-eslint/no-use-before-define */
import React from "react";
import styled from "styled-components";

import { Typography } from "../../Typography";

import { LayoutContainer, LayoutGrid } from "../Layout";
import { SkeletonText, withSkeletonComponentDefinition } from "../Skeleton";

import { MetaData } from "./MetaData";
import { MetaDataItem } from "./MetaDataItem";

type MetaDataGroupsItem = {
    name?: string;
    description?: string;
    childRows?: MetaDataGroupsItem[];
};

type MetaDataGroupsSecondaryProps = {
    metaData?: MetaDataGroupsItem;
};

const MetaDataGroupsSecondary = ({ metaData }: MetaDataGroupsSecondaryProps) => (
    <MetaDataItem title={metaData.name}>
        {metaData.description === "" ? "-" : metaData.description}
    </MetaDataItem>
);

const PrimaryContainer = styled(LayoutContainer)`
    &&&&&&&&&& {
        border-left: 0;
        border-right: 0;
        border-bottom: 0;
    }
`;

type MetaDataGroupsPrimaryProps = {
    metaData?: MetaDataGroupsItem;
    columns?: number;
};

const MetaDataGroupsPrimary = ({ metaData, columns }: MetaDataGroupsPrimaryProps) => (
    <PrimaryContainer
        paddingV="large"
        outline
    >
        <Typography.Header type="subheader">
            {metaData.name || <SkeletonText minWidth="6em" />}
        </Typography.Header>
        <MetaData type="secondary">
            <LayoutGrid
                rowGap="large"
                columnGap="xxx-large"
                columns={columns}
            >
                {metaData?.childRows?.map((item, index) => (
                    <MetaDataGroupsSecondary
                        key={item.name || index}
                        metaData={item}
                    />
                ))}
            </LayoutGrid>
        </MetaData>
    </PrimaryContainer>
);

// @TODO: make skeleton configurable with expected items etc.
export const SkeletonMetaDataGroups = () => (
    <MetaDataGroups
        metaData={[
            {
                childRows: [{}, {}, {}, {}, {}]
            },
            {
                childRows: [{}]
            },
            {
                childRows: [{}, {}, {}]
            },
            {
                childRows: [{}, {}]
            },
            {
                childRows: [{}, {}, {}]
            },
            {
                childRows: [{}, {}]
            }
        ]}
    />
);

type MetaDataGroupsProps = {
    metaData?: MetaDataGroupsItem[];
    // @TODO: make `columns` responsive
    columns?: number;
};

export const MetaDataGroups = withSkeletonComponentDefinition(
    ({ metaData, columns = 4 }: MetaDataGroupsProps) => (
        <>
            {metaData?.map((item, index) => (
                <MetaDataGroupsPrimary
                    key={item.name || index}
                    metaData={item}
                    columns={columns}
                />
            ))}
        </>
    )
)(SkeletonMetaDataGroups);
