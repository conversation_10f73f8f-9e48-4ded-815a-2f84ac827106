import React, { forwardRef, ReactNode, useRef } from "react";
import styled, { css } from "styled-components";

import { CSSPadding, type CSSPaddings } from "Utilities/index";

import { useElementSize } from "../hooks/useElementSize";
import { useCombinedRefs } from "../hooks/useCombinedRefs";

type ContainerProps = CSSPaddings & { manageOverflow: boolean };
const Container = styled.div<ContainerProps>`
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    ${({ manageOverflow }) =>
        (manageOverflow &&
            css`
                overflow: hidden;
            `) ||
        ""}
    ${CSSPadding}
`;

const Wrapper = styled.div`
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: stretch;
`;

type Dimensions = {
    width?: number;
    height?: number;
};

export type ResponsiveContainerProps = Partial<ContainerProps> & {
    children: (dimensions: Dimensions) => ReactNode;
    defaultDimensions?: Dimensions | false;
    className?: string;
    alwaysRenderChildren?: boolean;
    throttle?: number;
    disabled?: boolean;
    minHeight?: number | string;
    minWidth?: number | string;
};
export const ResponsiveContainer = forwardRef(
    (
        {
            children,
            alwaysRenderChildren = false,
            defaultDimensions = false,
            manageOverflow = true,
            throttle,
            className,
            disabled = false,
            minHeight,
            minWidth,
            ...rest
        }: ResponsiveContainerProps,
        ref,
    ) => {
        const container = useRef();
        const combinedRef = useCombinedRefs(ref, container);

        const { width, height, initialMeasuredHeight } = useElementSize(
            combinedRef,
            defaultDimensions === false ? 0 : defaultDimensions?.width ?? 100,
            defaultDimensions === false ? 0 : defaultDimensions?.height ?? 100,
            throttle,
            disabled,
        );

        return (
            <Container
                data-testid="responsive-container"
                ref={combinedRef}
                className={className}
                manageOverflow={manageOverflow}
                style={{
                    minHeight: initialMeasuredHeight === 0 && minHeight,
                    minWidth,
                }}
                {...rest}
            >
                {manageOverflow ? (
                    <Wrapper>
                        {(alwaysRenderChildren || (width !== 0 && height !== 0)) &&
                            children({ height, width })}
                    </Wrapper>
                ) : (
                    (alwaysRenderChildren || (width !== 0 && height !== 0)) &&
                    children({ height, width })
                )}
            </Container>
        );
    },
);
