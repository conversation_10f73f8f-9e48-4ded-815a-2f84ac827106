import React, {
    ComponentType,
    ReactNode,
    useState,
    useRef,
    useCallback,
    useImperativeHandle,
    forwardRef,
    useEffect,
} from "react";

import { HOC } from "../../../utils/HOC";
import { useClickOutside } from "../../../hooks/useClickOutside";

import { Panel } from "../../Panel/Panel";
import { Overlay } from "../../Overlay";
import { withContainerProvider } from "../../Container";

import { Tooltip, type TooltipProps } from "../Tooltip";

type Props = {
    onClick?: (event: unknown) => unknown;
    children?: ReactNode;
};

type OuterProps = {
    popoverContent?: ReactNode;
    popoverTitle?: ReactNode;
    popoverType?: "default" | "modal" | "container";
    popoverVisible?: boolean;
    popoverPlacement?: TooltipProps["placement"];
    popoverHideArrow?: boolean;
    /**
     * Scroll the popover element into view when popover is visible. Useful when the popover is controlled via `popoverVisible`, e.g. from a context menu
     */
    popoverAutoScrollIntoView?: boolean;
    onHide?: () => void;
};

export type PopoverApi = {
    hide: () => void;
    onClick?: (event: unknown) => void;
};

export const withPopover = <P extends Props>(Component: ComponentType<P>) => {
    const WrappedComponent = forwardRef<PopoverApi, P & OuterProps>(
        (
            {
                onClick,
                popoverContent,
                popoverTitle,
                popoverType = "default",
                popoverVisible,
                popoverPlacement,
                popoverAutoScrollIntoView = false,
                popoverHideArrow,
                onHide,
                ...rest
            },
            ref,
        ) => {
            const tooltipRef = useRef<HTMLElement>();
            const [visible, setVisible] = useState(popoverVisible);
            const container = useRef<HTMLDivElement>();
            const childContainer = useRef<HTMLDivElement>();
            const hide = useCallback(() => {
                setVisible(false);
                onHide?.();
            }, [onHide]);

            useClickOutside(tooltipRef, { altContainer: childContainer.current })(hide);

            useEffect(() => {
                let popOverWasVisible = false;
                setVisible(wasVisible => {
                    popOverWasVisible = wasVisible;
                    return popoverVisible;
                });
                if (!popoverVisible) {
                    if (popOverWasVisible) {
                        onHide?.();
                    }
                } else if (popoverAutoScrollIntoView) {
                    childContainer.current!.firstElementChild!.scrollIntoView({ block: "nearest" });
                }
            }, [onHide, popoverVisible, popoverAutoScrollIntoView]);

            const handleClick = useCallback(
                (event: unknown) => {
                    setVisible(true);

                    if (onClick) {
                        onClick(event);
                    }
                },
                [onClick],
            );

            useImperativeHandle(
                ref,
                () => ({
                    hide,
                    onClick: handleClick,
                }),
                [hide, handleClick],
            );

            const type = (function iife() {
                if (popoverType === "modal") {
                    return popoverTitle ? "basic" : "basic-with-padding";
                }

                return "popover";
            })();

            return (
                <Tooltip
                    {...(rest as P)}
                    type={type}
                    target={popoverType === "modal" ? null : undefined}
                    tooltipRef={tooltipRef}
                    hideArrow={popoverHideArrow}
                    content={
                        visible &&
                        popoverContent && (
                            <div
                                ref={container}
                                style={{ display: "contents" }}
                            >
                                <Panel
                                    onClose={(popoverTitle && hide) || undefined}
                                    title={popoverTitle}
                                    padding="large"
                                    manageOverflow
                                >
                                    {popoverContent}
                                </Panel>
                            </div>
                        )
                    }
                    visible={visible}
                    placement={popoverPlacement}
                >
                    {popoverType === "modal" && <Overlay visible={visible} />}

                    <div
                        style={{ display: "contents" }}
                        ref={childContainer}
                    >
                        <Component
                            onClick={handleClick}
                            selected={visible}
                            {...(rest as P)}
                        />
                    </div>
                </Tooltip>
            );
        },
    );

    return HOC("withPopover", withContainerProvider(WrappedComponent), Component);
};

export const Popover = withPopover(({ children }: { children?: ReactNode }) => <>{children}</>);
