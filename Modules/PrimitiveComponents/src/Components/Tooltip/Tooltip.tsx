import React, {
    ReactNode,
    useCallback,
    useState,
    useMemo,
    useEffect,
    useImperativeHandle,
    MutableRefObject,
} from "react";
import { createPortal } from "react-dom";
import styled from "styled-components";
import { usePopper, Modifier, PopperChildrenProps } from "react-popper";
import classnames from "classnames";

import { useActiveTheme, useDocumentContext, useZIndex, ZIndexManager } from "Theming/index";

import { useDebounce } from "../../hooks/useDebounce";
import { FormProvider } from "../Form";
import { ContainerProvider } from "../Container";

const Delay = {
    short: 150,
    default: 500,
    long: 1000,
};

type DelayType = keyof typeof Delay;

// Intentionally keep config lightweight to help force all toolbars to be consistent
export type TooltipProps = {
    content?: ReactNode;
    /** If no children the tooltip will be visible */
    children?: ReactNode;
    height?: number | string;
    minHeight?: number | string;
    maxHeight?: number | string;
    width?: number | string;
    minWidth?: number | string;
    maxWidth?: number | string;
    inline?: boolean;
    /** Controlled visibility */
    visible?: boolean;
    className?: string;
    target?: HTMLElement;
    /** Priority order of tooltip when multiple tooltips are displayed. Higher the number, the higher the priority. */
    order?: number;
    delay?: DelayType;
    type?: "default" | "popover" | "popover-with-padding" | "basic" | "basic-with-padding";
    coordinate?: { x?: number; y?: number };
    tooltipRef?: MutableRefObject<HTMLElement>;
    portalTarget?: HTMLElement;
    placement?: PopperChildrenProps["placement"];
    hideArrow?: boolean;
    offset?: number;
};

export const TooltipContainer = styled.div<{
    hasChildren: boolean;
}>`
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    ${props => (props.hasChildren ? "" : "pointer-events: none;")}
    max-width: 100vw;

    &.type--popover,
    &.type--popover-with-padding {
        > .tooltip-inner {
            border-radius: var(--border__radius--medium);

            > .tooltip-content {
                background-color: var(--color__background--container);
            }
        }

        > [data-popper-arrow] {
            .arrow {
                &:before {
                    background-color: var(--color__background--container);
                }
            }
        }
    }

    &.type--popover {
        > .tooltip-inner {
            > .tooltip-content {
                padding: 0;
            }
        }
    }

    &.type--basic {
        > .tooltip-inner {
            > .tooltip-content {
                padding: 0;
            }
        }
    }

    &.type--basic,
    &.type--basic-with-padding {
        > .tooltip-inner {
            border: 0;
            border-radius: 0;
        }
    }

    && > .tooltip-inner {
        font-size: var(--tooltip__font-size--base);
        font-family: var(--font__family);
        line-height: var(--font__line-height);
        color: var(--color);
    }

    .tooltip-inner {
        outline: 1px solid var(--tooltip__border-color, #979797);
        min-height: 2em;
        border-radius: 2px;
        //box-shadow: var(--tooltip__box-shadow, 5px 7px 10px #000);
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        overflow: hidden;
        height: auto;
        width: auto;

        > .tooltip-content {
            color: var(--tooltip__color, #fff);
            background-color: var(--tooltip__background-color, #000);
            padding: 4px 7px;
            overflow: auto;
            display: flex;
            flex-direction: column;
            flex-grow: 1;

            ::-webkit-scrollbar {
                width: var(--tooltip__scrollbar-size);
                height: var(--tooltip__scrollbar-size);
            }
        }
    }

    &[data-popper-placement^="top"] > [data-popper-arrow] {
        bottom: 0px;

        .arrow {
            transform: translate(0, 50%);

            &:before {
                transform: rotate(315deg);
            }
        }
    }

    &[data-popper-placement^="bottom"] > [data-popper-arrow] {
        top: 1px;

        .arrow {
            transform: translate(0, -50%);

            &:before {
                transform: rotate(135deg);
            }
        }
    }

    &[data-popper-placement^="right"] > [data-popper-arrow] {
        left: calc(var(--tooltip__arrow-size) * -1);

        .arrow {
            transform: translate(50%, 0);

            &:before {
                transform: rotate(45deg);
            }
        }
    }

    &[data-popper-placement^="left"] > [data-popper-arrow] {
        right: calc(var(--tooltip__arrow-size) * -1 - 1px);

        .arrow {
            transform: translate(-50%, 0);

            &:before {
                transform: rotate(225deg);
            }
        }
    }

    > [data-popper-arrow] {
        margin-right: 1px;
        margin-top: -1px;

        .arrow {
            width: var(--tooltip__arrow-size, 20px);
            height: var(--tooltip__arrow-size, 20px);
            background: transparent;
            pointer-events: ${props => (props.hasChildren ? "auto" : "none")};

            &:before {
                border: 1px solid var(--tooltip__border-color, rgba(151, 151, 151, 0.18));
                position: absolute;
                inset: 0;
                display: block;
                width: var(--tooltip__arrow-size, 20px);
                height: var(--tooltip__arrow-size, 20px);
                margin: auto;
                background-color: var(--tooltip__background-color, #000);
                content: "";
                pointer-events: none;
                clip-path: polygon(0 0, 100% 100%, 0 100%);
            }
        }
    }
`;

const Wrapper = styled.div<{ inline: boolean; hasContent: boolean }>`
    display: ${({ inline }) => (inline ? "inline-flex" : "flex")};

    flex-grow: 1;
    flex-direction: column;
    justify-content: inherit;

    text-align: inherit;
    overflow: inherit;
    text-overflow: inherit;

    ${({ hasContent }) => (hasContent ? "cursor: pointer;" : "")}
    pointer-events: all !important;
`;

export const TooltipWrapper = Wrapper;

const observePopupResizeModifier: Modifier<string, never> = {
    name: "observePopupResizeModifier",
    enabled: true,
    phase: "beforeRead",
    fn: () => {},
    effect: ({ state, instance }) => {
        const RO_PROP = "__observePopupResizeModifier__";
        const { popper } = state.elements;

        popper[RO_PROP] = new ResizeObserver(() => {
            instance.update();
        });

        popper[RO_PROP].observe(popper);

        return () => {
            popper[RO_PROP].disconnect();
            delete popper[RO_PROP];
        };
    },
};

const center: Modifier<string, never> = {
    name: "computeStyle",
    enabled: true,
    phase: "beforeWrite",
    fn: ({ state }) => ({
        ...state,
        styles: {
            ...state?.styles,
            popper: {
                ...state?.styles?.popper,
                position: "fixed",
                left: `${(window.innerWidth - state.rects.popper.width) / 2}px`,
                top: "50%",
                transform: "translateY(-50%)",
            },
        },
    }),
};

export const useTooltip = ({
    content,
    minHeight,
    maxHeight,
    minWidth,
    maxWidth,
    height,
    width,
    order = 0,
    inline,
    visible: controlledVisible,
    className,
    children,
    placement = "right",
    target,
    coordinate,
    type = "default",
    tooltipRef,
    delay: delayValue = "default",
    hideArrow = false,
    offset,
    portalTarget,
}: TooltipProps) => {
    const { theme } = useActiveTheme() || {};
    const doc = useDocumentContext();
    const ZIndex = useZIndex();
    const delay = Delay[delayValue] || Delay.default;

    const [referenceElement, setReferenceElement] = useState<HTMLDivElement>(null);
    const activeTarget = target === null ? null : target || referenceElement;
    const hasChildren = !!children || !!target || target === null;
    const hasTarget = !!activeTarget;
    const [popperElement, setPopperElement] = useState<HTMLDivElement>(null);
    const [arrowElement, setArrowElement] = useState<HTMLDivElement>(null);
    const { styles, attributes, update } = usePopper(activeTarget || doc.body, popperElement, {
        placement,
        strategy: "fixed",
        modifiers: [
            {
                // https://popper.js.org/docs/v2/modifiers/prevent-overflow/
                name: "preventOverflow",
                options: { altAxis: true, padding: 10 },
            },
            { name: "offset", options: { offset: [0, offset ?? (hideArrow ? 5 : 10)] } },
            { name: "arrow", options: { element: arrowElement, padding: 10 } },
            observePopupResizeModifier,
            !hasTarget && center,
        ],
    });

    const [visible, setVisible] = useState(!hasChildren);

    const handleShow = useCallback(() => {
        setVisible(true);
    }, []);

    const handleHide = useCallback(() => {
        setVisible(false);
    }, []);

    const show = useDebounce(handleShow, { wait: delay });
    const hide = useDebounce(handleHide, { wait: Math.min(Math.max(delay - 150, 0), 350) }); // Shorter delay to help prevent multiple tooltips showing

    useEffect(() => {
        update?.();
    }, [coordinate?.x, coordinate?.y, update]);

    const containerStyles = useMemo(
        () => ({
            height,
            minHeight,
            maxHeight: maxHeight || "calc(100vh - 30px)",
            minWidth,
            maxWidth,
            width,
            zIndex: ZIndex.Tooltip + order,
            ...styles.popper,
        }),
        [
            height,
            minHeight,
            maxHeight,
            minWidth,
            maxWidth,
            width,
            order,
            styles.popper,
            ZIndex.Tooltip,
        ],
    );

    useImperativeHandle(tooltipRef, () => popperElement, [popperElement]);

    const tooltip = useMemo(() => {
        return (
            <Wrapper
                className={className}
                ref={setReferenceElement}
                onMouseEnter={() => {
                    if (hasChildren && controlledVisible === undefined) {
                        show();
                        hide?.cancel();
                    }
                }}
                onMouseLeave={() => {
                    if (hasChildren && controlledVisible === undefined) {
                        hide();
                        show?.cancel();
                    }
                }}
                // Don't show tooltip if target is clicked
                onClick={() => {
                    if (hasChildren && controlledVisible === undefined) {
                        hide();
                        show?.cancel();
                    }
                }}
                inline={inline}
                hasContent={!!content}
                data-component-tooltip
            >
                {content &&
                    (visible || controlledVisible) &&
                    createPortal(
                        <TooltipContainer
                            ref={setPopperElement}
                            {...attributes.popper}
                            style={containerStyles}
                            className={classnames({ [`type--${type}`]: type }, theme)}
                            data-theme={theme}
                            data-tooltip
                            hasChildren={hasChildren}
                            // Stop event propagating so that the tooltip can be clicked on
                            onClick={e => e.stopPropagation()}
                        >
                            <FormProvider>
                                <ZIndexManager>
                                    {/* @TODO: `with-antd` included for legacy reasons... :( remove! */}
                                    <div className="tooltip-inner with-antd">
                                        <div className="tooltip-content">
                                            <ContainerProvider>{content}</ContainerProvider>
                                        </div>
                                    </div>
                                </ZIndexManager>
                            </FormProvider>
                            {!hideArrow && hasTarget && (
                                <div
                                    ref={setArrowElement}
                                    data-popper-arrow
                                    style={styles.arrow}
                                >
                                    <ContainerProvider>
                                        <div className="arrow" />
                                    </ContainerProvider>
                                </div>
                            )}
                        </TooltipContainer>,
                        portalTarget ?? doc.body,
                    )}
                {children}
            </Wrapper>
        );
    }, [
        className,
        inline,
        content,
        visible,
        controlledVisible,
        attributes.popper,
        containerStyles,
        type,
        theme,
        hasChildren,
        hasTarget,
        styles.arrow,
        doc.body,
        children,
        show,
        hide,
        hideArrow,
    ]);

    return {
        update,
        tooltip,
    };
};

export const Tooltip = (props: TooltipProps) => useTooltip(props).tooltip;
