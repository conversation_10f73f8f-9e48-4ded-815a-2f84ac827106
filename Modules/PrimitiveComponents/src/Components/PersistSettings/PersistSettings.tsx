import React, {
    createContext,
    ReactNode,
    useCallback,
    useContext,
    useLayoutEffect,
    useMemo,
    useState
} from "react";
import { get, kebabCase } from "lodash";
import { set } from "lodash/fp";

export type PersistSettingsProviderProps<S> = {
    settings?: S;
    settingsId?: string;
    children?: ((settings: S, metadata: PersistSettingsMetadata) => ReactNode) | ReactNode;
};

type PersistSettingsMetadata = {
    complete: boolean;
    error?: string;
};

// Note - extend string with {} so the compiler can suggest known properties
// https://github.com/microsoft/TypeScript/issues/29729
type PropertyOrPath<S> = keyof S | (string & {});
type PropertyOrPathValueType<S, P extends PropertyOrPath<S>> = P extends keyof S ? S[P] : unknown;

type PropertyValueCallback<S, P extends PropertyOrPath<S>> = (
    curr: PropertyOrPathValueType<S, P>
) => PropertyOrPathValueType<S, P>;

type PropertyValue<S, P extends PropertyOrPath<S>> =
    | PropertyOrPathValueType<S, P>
    | PropertyValueCallback<S, P>;

export type PersistSettingsProviderApi<S> = {
    settingsId?: string;
    settings: S;
    updateSettings: (updater: (settings: S) => S) => void;
    updateSetting: <P extends PropertyOrPath<S>>(path: P, value: PropertyValue<S, P>) => void;
    settingUpdaterFactory: <P extends PropertyOrPath<S>>(
        path: P
    ) => (value: PropertyValue<S, P>) => void;
    metadata: PersistSettingsMetadata;
};

export type PersistSettingsAdapter<S> = {
    get: (defaultSettings: S, settingsId: string) => Promise<S>;
    set: (settings: S, settingsId: string) => Promise<void> | void;
};

const noopApi: PersistSettingsProviderApi<unknown> = {
    metadata: { complete: true },
    settings: {},
    settingUpdaterFactory: () => () => Promise.resolve({}),
    updateSetting: () => Promise.resolve({}),
    updateSettings: () => Promise.resolve({})
};

const createPersistSettingsContext = <S,>() =>
    createContext<PersistSettingsProviderApi<S> | null>(null);

export const createPersistSettings = <S,>(
    persistor: PersistSettingsAdapter<S>,
    defaultSettings?: S
) => {
    const Context = createPersistSettingsContext<S>();

    const persistApi = {
        PersistSettingsProvider: ({
            settings,
            settingsId,
            children
        }: PersistSettingsProviderProps<S>) => {
            const [currentSettings, setCurrentSettings] = useState(settings ?? defaultSettings);
            const [error, setError] = useState<string | undefined>();
            const [complete, setComplete] = useState(false);

            useLayoutEffect(() => {
                (async function getter() {
                    try {
                        setError(undefined);
                        setComplete(false);
                        setCurrentSettings(
                            (await persistor.get(defaultSettings, settingsId)) || ({} as S)
                        );
                        setComplete(true);
                    } catch (e) {
                        setError(e.message || e);
                        console.error("[createPersistSettings] Failed to get storage settings:", e);
                    }
                })();
            }, [settingsId, setCurrentSettings, setError, setComplete]);

            const updateSettings = useCallback(
                (updater: (settings: S) => S) => {
                    setCurrentSettings(s => {
                        const updatedSettings = updater(s);

                        (async function updateStorageSettings() {
                            try {
                                await persistor.set(updatedSettings, settingsId);
                            } catch (e) {
                                console.error(
                                    "[createPersistSettings] Failed to update storage settings:",
                                    e
                                );
                            }
                        })();

                        return updatedSettings || ({} as S);
                    });
                },
                [settingsId, setCurrentSettings]
            );

            const updateSetting = useCallback(
                <P extends PropertyOrPath<S>>(path: P, value: PropertyValue<S, P>) => {
                    updateSettings(
                        s =>
                            set(
                                path,
                                typeof value === "function"
                                    ? (value as PropertyValueCallback<S, P>)(get(s, path))
                                    : value,
                                s as object
                            ) as S
                    );
                },
                [updateSettings]
            );

            const settingUpdaterFactory = useCallback(
                <P extends PropertyOrPath<S>>(property: P) =>
                    (value: PropertyValue<S, P>) => {
                        updateSetting(property, value);
                    },
                [updateSetting]
            );

            const metadata = useMemo(
                () => ({
                    complete,
                    error
                }),
                [error, complete]
            );

            const api = useMemo<PersistSettingsProviderApi<S>>(
                () => ({
                    settingsId,
                    settings: currentSettings,
                    updateSettings,
                    updateSetting,
                    settingUpdaterFactory,
                    metadata
                }),
                [
                    settingsId,
                    currentSettings,
                    updateSettings,
                    updateSetting,
                    settingUpdaterFactory,
                    metadata
                ]
            );

            const parent = useContext(Context);

            return (
                <Context.Provider value={parent || api}>
                    {typeof children === "function"
                        ? children(currentSettings, metadata)
                        : children}
                </Context.Provider>
            );
        },
        usePersistSettings: () => {
            const persistSettingsContext = useContext(Context);
            return persistSettingsContext || (noopApi as PersistSettingsProviderApi<S>);
        },
        usePersistState: <V,>(id: string, defaultValue?: V) => {
            const { settings, updateSetting } = persistApi.usePersistSettings();

            const path = useMemo(() => {
                const normalizedId = id.split(".").map(kebabCase).join("_");
                return `setting.${normalizedId}`;
            }, [id]);

            const value: V = get(settings, path) ?? defaultValue;

            const updatePathSetting = useCallback(
                (valueOrFn: V | ((currValue: V) => V)) => {
                    updateSetting(path, valueOrFn as PropertyValue<S, string>);
                },
                [path, updateSetting]
            );

            return [value, updatePathSetting] as const;
        }
    };

    return persistApi;
};
