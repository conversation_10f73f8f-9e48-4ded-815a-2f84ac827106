import React, { ReactNode } from "react";
import styled from "styled-components";
import { formatNumber, NumberFormatOptions, numberFormats } from "Utilities/index";
import { ChangedValueInfoBar } from "../ChangedValueInfoBar";

const CellContainer = styled.div`
    display: grid;

    grid-gap: 0.25rem;
    grid-template-columns: 60% 1fr;

    & > div {
        text-align: right;
    }
`;

export type ChangeInfoProps = {
    value: number;
    min?: number;
    med?: number;
    max?: number;
    barHeight?: number;
    formatValue?: NumberFormatOptions;
    children?: ReactNode;
};

/**
 * @deprecated
 */
export const ChangedInfo = ({
    value,
    min = -1,
    med = 0,
    max = 1,
    barHeight = 10,
    formatValue = numberFormats.percent1dp,
    children
}: ChangeInfoProps) => {
    return (
        <CellContainer>
            <div>
                <ChangedValueInfoBar
                    barHeight={barHeight}
                    value={value}
                    min={min}
                    max={max}
                    med={med}
                />
            </div>
            <div>{children || formatNumber(value, formatValue, "")}</div>
        </CellContainer>
    );
};
