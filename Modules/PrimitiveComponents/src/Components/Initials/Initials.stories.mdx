import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import { ThemeIterator, LayoutVertical, LayoutHorizontal, LayoutContainer, ThemeContainer } from "../..";

import { Initials } from "./Initials";

<Meta title="PrimitiveComponents/Components/Initials" component={Initials} />

export const Template = args => (
    <LayoutHorizontal spacing="large">
        <ThemeIterator>
            {() => (
                <ThemeContainer>
                    <LayoutHorizontal spacing="large">
                        <Initials>SK</Initials>
                        <Initials>WW</Initials>
                        <Initials>XYZ</Initials>
                    </LayoutHorizontal>
                </ThemeContainer>
            )}
        </ThemeIterator>
    </LayoutHorizontal>
);

# Initials

<Description of={Initials} />

<Canvas>
    <Story
        name="Initials"
        parameters={{
            controls: {
                disable: false,
                exclude: ["className"]
            }
        }}
        args={{
        }}
        argTypes={{
        }}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes of={Initials} sort="requiredFirst" />
