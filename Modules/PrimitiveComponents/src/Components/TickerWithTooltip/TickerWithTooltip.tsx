import React, { ReactNode } from "react";
import styled from "styled-components";

import { sanitiseTickerString, isNumberType } from "Utilities/index";

import { Tooltip } from "../Tooltip";
import { SimpleList } from "../SimpleList";
import { Flex } from "../Layout";
import { useTickerWithTooltip } from "./TickerTooltipProvider";
import { ScoreSymbol } from "../ScoreSymbol";

const TickerContainer = styled.div<{
    color?: string;
}>`
    color: ${props => props.color ?? "var(--ticker__color, #ffb300)"} !important;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
`;

const TickerLink = styled(TickerContainer)`
    cursor: pointer;

    &:hover {
        text-decoration: underline;
    }
`;

const TickerList = styled(SimpleList)`
    height: 100%;
`;

type TickerWithTooltipBaseProps = {
    raidInstrumentId?: string;
    traderId?: number;
    className?: string;
    height?: number | string;
    width?: number | string;
    disabled?: boolean;

    score?: number;
    scoreMin?: number;
    scoreMax?: number;

    data?: unknown;

    color?: string;
};

type TickerWithTooltipProps = {
    bbgTicker: string | string[];
    separator?: string;
} & TickerWithTooltipBaseProps;

type TickerWithTooltipSingleProps = {
    bbgTicker: string;
} & TickerWithTooltipBaseProps;

// @TODO: remove feature flag detection
const features = JSON.parse(localStorage.getItem("features") || "{}");
const debugUseNew = features.companyPageExperimental ?? false;

const NoopComponent = ({ children }) => <>{children}</>;

const TickerWithTooltipSingle = ({
    disabled = !debugUseNew,
    className,
    bbgTicker,
    raidInstrumentId,
    width = 650,
    height = 520,
    score,
    scoreMin,
    scoreMax,
    color,
    ...rest
}: TickerWithTooltipSingleProps) => {
    // the hover component is supplied via context so that we do not need to have a dependency on any particular JS client
    const {
        ToolTipComponent,
        ContextMenuComponent = NoopComponent,
        ToolTipControlComponent,
    } = useTickerWithTooltip();

    const ActiveTooltipComponent = ToolTipControlComponent || Tooltip;

    return (
        <Flex
            items="center"
            gap="x-small"
            style={{ display: "inline-flex" }}
        >
            {isNumberType(score) && (
                <ScoreSymbol
                    value={score}
                    min={scoreMin}
                    max={scoreMax}
                    symbol="triangle"
                    size={8}
                />
            )}
            {!disabled && ToolTipComponent ? (
                <ActiveTooltipComponent
                    inline
                    content={
                        // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
                        <div
                            key={bbgTicker}
                            onClick={e => e.stopPropagation()}
                            style={{
                                width,
                                minHeight: height,
                            }}
                        >
                            <ToolTipComponent
                                bbgTicker={bbgTicker}
                                {...rest}
                            />
                        </div>
                    }
                >
                    <ContextMenuComponent
                        bbgTicker={bbgTicker}
                        raidInstrumentId={raidInstrumentId}
                        {...rest}
                    >
                        <TickerLink
                            className={className}
                            color={color}
                        >
                            {sanitiseTickerString(bbgTicker)}
                        </TickerLink>
                    </ContextMenuComponent>
                </ActiveTooltipComponent>
            ) : (
                <ContextMenuComponent
                    bbgTicker={bbgTicker}
                    raidInstrumentId={raidInstrumentId}
                    {...rest}
                >
                    <TickerContainer
                        className={className}
                        color={color}
                    >
                        {sanitiseTickerString(bbgTicker)}
                    </TickerContainer>
                </ContextMenuComponent>
            )}
        </Flex>
    );
};

const BLACKLIST_VALUES = ["TOTAL"];

export const TickerWithTooltip = ({ bbgTicker, separator, ...rest }: TickerWithTooltipProps) => {
    if (bbgTicker) {
        const isArray = Array.isArray(bbgTicker);

        if (!isArray && BLACKLIST_VALUES.includes(bbgTicker)) return <>{bbgTicker}</>;

        const tickerList = isArray ? bbgTicker : bbgTicker.toString().split(",");

        return tickerList.length === 1 ? (
            <TickerWithTooltipSingle
                {...rest}
                bbgTicker={tickerList[0]}
            />
        ) : (
            <TickerList separator={separator}>
                {tickerList
                    .filter(ticker => ticker)
                    .map((ticker, i) => (
                        // use index as the key as they _can_ be duplicated tickers (which can be valid...??)
                        // eslint-disable-next-line react/no-array-index-key
                        <li key={`${i}${ticker}`}>
                            <TickerWithTooltipSingle
                                {...rest}
                                bbgTicker={ticker}
                            />
                        </li>
                    ))}
            </TickerList>
        );
    }

    return null;
};

const Container = styled.div`
    overflow: hidden;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
    white-space: pre;

    > .ticker {
        display: flex;
    }
`;

type TickerProps = {
    prefixLabel?: ReactNode;
    postfixLabel?: ReactNode;
} & TickerWithTooltipProps;

export const Ticker = ({ prefixLabel, postfixLabel, bbgTicker, ...rest }: TickerProps) =>
    bbgTicker && (
        <Container>
            {prefixLabel && <div>{prefixLabel}</div>}
            <div className="ticker">
                <TickerWithTooltip
                    disabled={false}
                    bbgTicker={bbgTicker}
                    {...rest}
                />
            </div>
            {postfixLabel && <div>{postfixLabel}</div>}
        </Container>
    );
