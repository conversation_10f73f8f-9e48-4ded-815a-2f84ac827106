import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import { ThemeIterator, LayoutVertical, LayoutHorizontal, ThemeContainer } from "../../..";

import { FlipBetween } from "../FlipBetween";

<Meta
    title="PrimitiveComponents/Components/FlipBetween"
    component={FlipBetween}
/>

export const Component = ({ flip, body }) => {
    return (
        <div>
            {flip && (
                <button
                    type="button"
                    onClick={flip}
                >
                    Flip
                </button>
            )}
            {body}
        </div>
    );
};

export const Template = () => (
    <LayoutHorizontal spacing="large">
        <ThemeIterator>
            {() => (
                <LayoutVertical spacing="xxx-large">
                    <ThemeContainer>
                        <FlipBetween>
                            <Component body="No flip" />
                        </FlipBetween>
                    </ThemeContainer>
                    <ThemeContainer flex>
                        <FlipBetween>
                            <Component body="Component 1" />
                            <Component body="Component 2" />
                            <Component body="Component 3" />
                        </FlipBetween>
                    </ThemeContainer>
                </LayoutVertical>
            )}
        </ThemeIterator>
    </LayoutHorizontal>
);

# FlipBetween

<Description of={FlipBetween} />

<Canvas>
    <Story
        name="FlipBetween"
        parameters={{
            controls: {
                disable: true
            }
        }}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes
    of={FlipBetween}
    sort="requiredFirst"
/>
