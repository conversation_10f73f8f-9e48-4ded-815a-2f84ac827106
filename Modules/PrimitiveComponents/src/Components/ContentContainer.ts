import styled from "styled-components";

export const ContentContainer = styled.div`
    word-break: break-word;

    h1 {
        font-size: 1.3em;
    }

    h2 {
        font-size: 1.2em;
    }

    h3 {
        font-size: 1.1em;
    }

    h4,
    h5,
    h6 {
        font-size: 1em;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p:not(:last-child) {
        margin-bottom: 0.5em;
        padding-bottom: 0;
    }

    pre {
        background-color: var(--color__background--container-level-2);
        padding: 5px;
        font-size: inherit;
        white-space: pre-wrap;

        code {
            background-color: inherit;
            display: block;
        }
    }

    code {
        background-color: var(--border__color--outline);
        font-size: inherit;
        padding: 2px 4px;
    }

    em {
        font-style: normal;
        filter: brightness(0.65);
    }

    blockquote {
        margin-left: 10px;
        padding-left: 10px;
        border-left: 6px solid var(--border__color--muted);
    }

    *:last-child {
        margin-bottom: 0;
    }

    table {
        margin-bottom: var(--spacing--medium);

        thead {
            border-bottom: 1px solid var(--border__color--separator);
        }

        th,
        td {
            padding: var(--spacing--small) var(--spacing--large);
            border-right: 1px solid var(--border__color--outline);

            &:first-child {
                padding-left: 0;
            }

            &:last-child {
                border-right: 0;
                padding-right: 0;
            }
        }
    }

    img {
        max-width: 100%;
    }
`;
