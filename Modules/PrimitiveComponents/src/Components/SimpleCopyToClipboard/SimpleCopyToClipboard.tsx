import React, { <PERSON><PERSON><PERSON>Hand<PERSON>, useEffect, useState } from "react";
import copy from "copy-to-clipboard";
import { Icon } from "../Icon";

type SimpleCopyToClipboardProps = {
    text: string;
    format?: string;
    onCopy?: (clipboardData: DataTransfer) => void;
    icon?: string;
    className?: string;
};

export const SimpleCopyToClipboard = ({
    text,
    format,
    onCopy,
    icon = "copy",
    className
}: SimpleCopyToClipboardProps) => {
    const [copied, setCopied] = useState(false);

    const handleCopy: MouseEventHandler = e => {
        e.preventDefault();
        e.stopPropagation();
        copy(text, { format, onCopy });

        setCopied(true);
    };

    useEffect(() => {
        if (copied) {
            const t = setTimeout(() => {
                setCopied(false);
            }, 2000);

            return () => {
                clearTimeout(t);
            };
        }

        return () => {
            // noop
        };
    }, [copied, setCopied]);

    return (
        <Icon
            className={className}
            type={copied ? "check" : icon}
            onClick={handleCopy}
            style={{ color: copied && "var(--color__success, green)" }}
        />
    );
};
