import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";
import styled from "styled-components";

import { ThemeIterator, LayoutVertical, LayoutHorizontal, LayoutContainer, ThemeContainer } from "../..";

import { Groups } from "./Groups";
import { Group } from "./Group";

<Meta title="PrimitiveComponents/Components/Groups" component={Groups} />

export const GroupsWithCssVars = styled(Groups)`
    --group-border-color: #70389D;
    --group__header-background-color: #2e1244;
    --group-background-color: #180c22;
`;

export const TemplateCSSVars = args => (
    <LayoutHorizontal spacing="large">
        <ThemeIterator>
            {() => (
                <LayoutVertical spacing="xxx-large">
                    <ThemeContainer>
                        <GroupsWithCssVars
                            title="Title"
                            subTitle="Subtitle"
                        >
                            <Group
                                title="Outside of Universe"
                                subTitle="0"
                                muted
                            >
                                <div style={{ minHeight: "10em" }}>COMPONENT</div>
                            </Group>
                            <Group
                                title="Outside of Edge"
                                subTitle="2"
                            >
                                <div style={{ minHeight: "10em" }}>COMPONENT</div>
                            </Group>
                            <Group
                                title="Selection Hit Rate Deterioration"
                                subTitle="0"
                                muted
                            >
                                <div style={{ minHeight: "10em" }}>COMPONENT</div>
                            </Group>
                        </GroupsWithCssVars>
                    </ThemeContainer>
                </LayoutVertical>
            )}
        </ThemeIterator>
    </LayoutHorizontal>
);

export const Template = args => (
    <LayoutHorizontal spacing="large">
        <ThemeIterator>
            {() => (
                <LayoutVertical spacing="xxx-large">
                    <ThemeContainer>
                        <Groups
                            title="Title"
                            subTitle="Subtitle"
                        >
                            <Group
                                title="Outside of Universe"
                                subTitle="0"
                                muted
                            >
                                <div style={{ minHeight: "10em" }}>COMPONENT</div>
                            </Group>
                            <Group
                                title="Outside of Edge"
                                subTitle="2"
                            >
                                <div style={{ minHeight: "10em" }}>COMPONENT</div>
                            </Group>
                            <Group
                                title="Selection Hit Rate Deterioration"
                                subTitle="0"
                                muted
                            >
                                <div style={{ minHeight: "10em" }}>COMPONENT</div>
                            </Group>
                        </Groups>
                    </ThemeContainer>
                </LayoutVertical>
            )}
        </ThemeIterator>
    </LayoutHorizontal>
);

# Groups

<Description of={Groups} />

<Canvas>
    <Story
        name="Groups"
        parameters={{
            controls: {
                disable: false,
                exclude: ["className"]
            }
        }}
        args={{
        }}
        argTypes={{
        }}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes of={Groups} sort="requiredFirst" />

## Styling

The group border, header background color and group inner background colors can be set using the following css variables in a `styled(Groups)` container.

* `--group-border-color`
* `--group__header-background-color`
* `--group-background-color`

```ts
export const GroupsWithCssVars = styled(Groups)`
    --group-border-color: #70389D;
    --group__header-background-color: #2e1244;
    --group-background-color: #180c22;
`;
```

<Canvas>
    <Story
        name="CSS Var Groups"
        parameters={{
            controls: {
                disable: false,
                exclude: ["className"]
            }
        }}
        args={{
        }}
        argTypes={{
        }}
    >
        {TemplateCSSVars.bind({})}
    </Story>
</Canvas>
