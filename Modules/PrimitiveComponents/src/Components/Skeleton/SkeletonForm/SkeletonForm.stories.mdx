import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";
import {
    ThemeIterator,
    ThemeContainer,
    LayoutContainer,
    LayoutVertical
} from "../../..";

import { SkeletonForm } from "../SkeletonForm";

<Meta title="PrimitiveComponents/Skeleton/SkeletonForm" component={SkeletonForm} />

export function TemplateControl(args) {
    return (
        <LayoutVertical spacing="large">
            <ThemeIterator>
                {() => (
                    <ThemeContainer>
                        <LayoutContainer minWidth="30em">
                            <SkeletonForm {...args} />
                        </LayoutContainer>
                    </ThemeContainer>
                )}
            </ThemeIterator>
        </LayoutVertical>
    );
}

# SkeletonForm

<Description of={SkeletonForm} />

<Canvas>
    <Story
        name="SkeletonForm"
        args={{
            visible: true,
            indeterminate: false
        }}
        argTypes={{
        }}
        parameters={{
            controls: {
                expanded: true,
                include: ["visible", "indeterminate"]
            }
        }}
    >
        {TemplateControl.bind({})}
    </Story>
</Canvas>

<ArgTypes of={SkeletonForm} sort="requiredFirst" />
