import React, { ReactNode, useMemo, useRef } from "react";
import styled from "styled-components";
import { CSSHeightRules, CSSHeight } from "Utilities/index";

import { UseInViewport, useInViewport, UseInViewportConfig } from "../../hooks/useInViewport";

type PlaceholderProps = {
    isContentVisible: boolean;
} & CSSHeight;
const Placeholder = styled.div<PlaceholderProps>`
    ${({ isContentVisible, minHeight }) =>
        `display: flex !important;
         flex-direction: inherit !important;
         flex: 1 1 0 !important;
         ${CSSHeightRules({ minHeight: isContentVisible ? undefined : minHeight })}`}
`;

export type ViewportProps = {
    children: ReactNode | ((props: UseInViewport) => ReactNode);
    observerOptions?: IntersectionObserverInit;
    onEnterViewport?: UseInViewportConfig["onEnterViewport"];
    onLeaveViewport?: UseInViewportConfig["onLeaveViewport"];
    disconnectOnEnter?: boolean;
    disconnectOnLeave?: boolean;
    unmountOnLeave?: boolean;
    className?: string;
} & Pick<PlaceholderProps, "minHeight">;

export const Viewport = ({
    children,
    observerOptions,
    onEnterViewport,
    onLeaveViewport,
    disconnectOnEnter = false,
    disconnectOnLeave = false,
    unmountOnLeave = true,
    className,
    minHeight
}: ViewportProps) => {
    const ref = useRef();

    const config = useMemo(
        () => ({
            disconnectOnEnter,
            disconnectOnLeave,
            onEnterViewport,
            onLeaveViewport,
            stopOnFirstEnter: !unmountOnLeave
        }),
        [disconnectOnEnter, disconnectOnLeave, onEnterViewport, onLeaveViewport, unmountOnLeave]
    );

    const observerState = useInViewport(ref, observerOptions, config);

    const isContentVisible = unmountOnLeave
        ? observerState.inViewport
        : observerState.enterCount > 0;

    return (
        <Placeholder
            ref={ref}
            className={className}
            minHeight={minHeight}
            isContentVisible={isContentVisible}
        >
            {isContentVisible &&
                (typeof children === "function" ? children(observerState) : children)}
        </Placeholder>
    );
};
