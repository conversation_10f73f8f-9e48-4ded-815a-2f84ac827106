import React, { ComponentType, useState } from "react";

import { useDebounce } from "../hooks/useDebounce";
import type { ControlProps } from "../Controls/types";

type OuterProps = {
    debounceWait?: number;
    debounceDisabled?: boolean;
};

export const withDebouncedChange = <P extends ControlProps<T, PP, C>, T, PP, C = T>(
    Component: ComponentType<P>
) => {
    const WrappedCompnent = ({
        onChange,
        value,
        debounceDisabled = false,
        debounceWait = 500,
        ...props
    }: P & OuterProps) => {
        const [innerValue, setInnerValue] = useState<C | T>(value);

        const handleChangeDebounced = useDebounce(onChange, {
            wait: debounceWait,
            disabled: debounceDisabled
        });

        const handleChange = (updateValue: C) => {
            setInnerValue(updateValue);
            handleChangeDebounced(updateValue);
        };

        return (
            <Component
                {...(props as P)}
                value={innerValue}
                onChange={handleChange}
            />
        );
    };

    return WrappedCompnent;
};
