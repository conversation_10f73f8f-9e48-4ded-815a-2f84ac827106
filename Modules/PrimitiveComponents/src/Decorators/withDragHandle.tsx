import React, { useRef, ComponentType } from "react";

type OuterProps = {
    draggable?: boolean;
    onDragStart?: ({ x, y }: { x: number; y: number }) => void;
    onDragMove?: ({ x, y }: { x: number; y: number }) => void;
    onDragEnd?: ({ x, y }: { x: number; y: number }) => void;
};

export const withDragHandle = <P,>(Component: ComponentType<P>) => {
    const WrappedComponent = ({
        draggable = true,
        onDragStart,
        onDragMove,
        onDragEnd,
        ...props
    }: P & OuterProps) => {
        const offsetTarget = useRef<{ left: number; top: number }>();

        return (
            <div
                style={{
                    cursor: draggable ? "move" : "",
                    display: "contents"
                }}
                // Use pointer events instead of drag app-region to have more control of state change during e.g. window move
                onPointerDown={e => {
                    if (draggable) {
                        (e.target as HTMLElement).setPointerCapture(e.pointerId);
                        offsetTarget.current = { left: e.clientX, top: e.clientY };
                        onDragStart?.({
                            x: e.screenX - (offsetTarget.current?.left || 0),
                            y: e.screenY - (offsetTarget.current?.top || 0)
                        });
                    }
                }}
                onPointerUp={e => {
                    if (draggable) {
                        (e.target as HTMLElement).releasePointerCapture(e.pointerId);
                        onDragEnd?.({
                            x: e.screenX - (offsetTarget.current?.left || 0),
                            y: e.screenY - (offsetTarget.current?.top || 0)
                        });
                    }
                }}
                onPointerMove={e => {
                    if (draggable && (e.target as HTMLElement).hasPointerCapture(e.pointerId)) {
                        onDragMove?.({
                            x: e.screenX - (offsetTarget.current?.left || 0),
                            y: e.screenY - (offsetTarget.current?.top || 0)
                        });
                    }
                }}
            >
                <Component {...(props as P)} />
            </div>
        );
    };

    return WrappedComponent;
};
