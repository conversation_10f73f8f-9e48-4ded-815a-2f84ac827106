import { useState } from "react";

import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import { ThemeIterator, LayoutVertical, LayoutHorizontal, ThemeContainer } from "../..";

import { NumberInputWithLabel } from "../NumberInputWithLabel";

<Meta title="PrimitiveComponents/Controls/NumberInputWithLabel" component={NumberInputWithLabel} />

export const Template = args => {
    const [value, setValue] = useState()
    return (
        <LayoutHorizontal spacing="large">
            <ThemeIterator>
                {() => (
                    <LayoutVertical spacing="xx-large">
                        <ThemeContainer>
                            <NumberInputWithLabel {...args} value={value} onChange={setValue} />
                        </ThemeContainer>
                        <ThemeContainer>
                            <NumberInputWithLabel label="Control Label" {...args} value={value} onChange={setValue} />
                        </ThemeContainer>
                        <ThemeContainer>
                            <NumberInputWithLabel
                                required
                                label="Control Label"
                                {...args}
                                value={value}
                                onChange={setValue}
                            />
                        </ThemeContainer>
                        <ThemeContainer>
                            <NumberInputWithLabel
                                error="Some error"
                                label="Control Label"
                                {...args}
                                value={value}
                                onChange={setValue}
                            />
                        </ThemeContainer>
                        <ThemeContainer>
                            <NumberInputWithLabel disabled {...args} value={value} onChange={setValue} />
                        </ThemeContainer>
                        <ThemeContainer>
                            <NumberInputWithLabel
                                disabled
                                label="Control Label (disabled)"
                                {...args}
                                value={value}
                                onChange={setValue}
                            />
                        </ThemeContainer>
                    </LayoutVertical>
                )}
            </ThemeIterator>
        </LayoutHorizontal>
    );
};

# NumberInputWithLabel

<Description of={NumberInputWithLabel} />

<Canvas>
    <Story
        name="NumberInputWithLabel"
        parameters={{
            controls: {
                disable: false,
                include: ["value", "disabled", "size"]
            }
        }}
        args={{
            size: "default"
        }}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes of={NumberInputWithLabel} sort="requiredFirst" />
