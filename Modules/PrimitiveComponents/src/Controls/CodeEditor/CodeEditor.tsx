import React from "react";
import classnames from "classnames";

import { ResponsiveContainer } from "../../Components/ResponsiveContainer";
import { withLazyComponent } from "../../Components/LazyComponent";

import type { ControlProps } from "../types";

import type { MonacoEditorProps } from "./MonacoEditor";

const MonacoEditor = withLazyComponent(() => import("./MonacoEditor"), "MonacoEditor");

export const CodeEditorLanguages = ["json", "javascript", "typescript", "csharp"] as const;

type CodeEditorLanguagesType = (typeof CodeEditorLanguages)[number];

export type CodeEditorProps = ControlProps<
    string,
    {
        className?: string;
        language: CodeEditorLanguagesType;
        rows?: number;
    } & Pick<MonacoEditorProps, "options">
>;

export const CodeEditor = ({
    className,
    value,
    onChange,
    language = "json",
    options,
    disabled,
    rows = 2
}: CodeEditorProps) => (
    <div
        className={classnames("code-editor", className)}
        style={{
            display: "flex",
            flexDirection: "column",
            flexGrow: 1,
            minHeight: `calc(${rows}em + ${rows * 4}px)`
        }}
    >
        <ResponsiveContainer
            manageOverflow
            defaultDimensions={false}
        >
            {({ height, width }) => (
                <MonacoEditor
                    language={language}
                    width={width}
                    height={height}
                    theme="vs-dark" // @TODO: pick appropiate theme base on useTheme hook
                    value={value}
                    onChange={onChange}
                    options={options}
                    disabled={disabled}
                />
            )}
        </ResponsiveContainer>
    </div>
);
