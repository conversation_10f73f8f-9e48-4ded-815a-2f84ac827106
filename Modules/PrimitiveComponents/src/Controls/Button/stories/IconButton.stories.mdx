import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import { ThemeIterator, LayoutVertical, LayoutHorizontal, ThemeContainer } from "../../..";

import { IconButton } from "../IconButton";

<Meta title="PrimitiveComponents/Controls/IconButton" component={IconButton} />

export const Template = args => (
    <LayoutHorizontal spacing="large">
        <ThemeIterator>
            {() => (
                <LayoutVertical spacing="xx-large">
                    <ThemeContainer>
                        <IconButton {...args}>
                            I
                        </IconButton>
                    </ThemeContainer>
                    <ThemeContainer>
                        <IconButton {...args} type="primary">
                            I
                        </IconButton>
                    </ThemeContainer>
                </LayoutVertical>
            )}
        </ThemeIterator>
    </LayoutHorizontal>
);

# IconButton

<Description of={IconButton} />

<Canvas>
    <Story
        name="IconButton"
        parameters={{
            controls: {
                disable: false,
                include: ["selected", "toggled"]
            }
        }}
        args={{
        }}
        argTypes={{
        }}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes of={IconButton} sort="requiredFirst" />
