import React, { forwardRef, InputHTMLAttributes, Ref } from "react";
import styled from "styled-components";

import { castNumber, filterValidHTMLInputElementProps, isDefined } from "Utilities/index";

import { withLabel } from "../Components/Label";

import type { ControlProps } from "./types";

export type InputWithLabelProps = ControlProps<
    string,
    {
        minWidth?: number | string;
        maxWidth?: number | string;
        onEnter?: (value: string) => void;
        required?: boolean;
        type?: InputHTMLAttributes<any>["type"] | "tenor" | "percent";
        disableNegatives?: boolean;
    } & Omit<InputHTMLAttributes<any>, "onChange" | "type">
>;

const StyledInput = styled.input`
    text-overflow: ellipsis;
    color: var(--color);
    background-color: var(--color__background);
    app-region: no-drag;
    width: 100%;

    &::placeholder {
        /* styling to match the Select placeholder */
        font-weight: 400;
        color: var(--color);
        opacity: 0.6;
    }
`;

const parseTenor = (value: string, disableNegatives: boolean): number => {
    const tempString = value
        .replace(/[$,]/g, "")
        .replace(disableNegatives ? "-" : "", "")
        .trim()
        .toLowerCase();

    let multiplier = 1;
    if (tempString.endsWith("b") || tempString.endsWith("bn")) {
        multiplier = 1000000000;
    } else if (tempString.endsWith("m")) {
        multiplier = 1000000;
    } else if (tempString.endsWith("k")) {
        multiplier = 1000;
    }

    return parseFloat(tempString) * multiplier;
};

const parsePercent = (value: string, disableNegatives: boolean): number => {
    const tempString = value
        .replace(/[%,]/g, "")
        .replace(disableNegatives ? "-" : "", "")
        .trim();

    return Number(tempString) / 100;
};

const getType = (type: InputWithLabelProps["type"]) => {
    switch (type) {
        case "tenor":
            return "string";
        case "percent":
            return "number";
        default:
            return type;
    }
};

const getDefaultValue = (
    type: InputWithLabelProps["type"],
    defaultValue: InputWithLabelProps["defaultValue"],
) => {
    switch (type) {
        case "percent": {
            const { castedValue, isNumber } = castNumber(defaultValue);
            return isNumber ? castedValue * 100 : defaultValue;
        }
        default:
            return defaultValue;
    }
};

const getValue = (
    type: InputWithLabelProps["type"],
    value: InputWithLabelProps["value"],
    defaultValue: InputWithLabelProps["defaultValue"],
) => {
    // The below logic is necessary to restore uncontrolled behavior using defaultValue while ensuring backward compatibility, given that "" was set as the default value.
    const valueOrDefault = value ?? (isDefined(defaultValue) ? undefined : "");
    switch (type) {
        case "tenor": {
            if (isDefined(valueOrDefault)) {
                // Controlled type=tenor is particularly annoying as it expands to "tensor" and moves the cursor to the end. For example, typing 1234 gets formatted to 1.234k, with the cursor placed at the end. Just use uncontrolled type=tenor instead (defaultValue).
                const error =
                    "[InputWithLabel] Controlled tenor type is not supported, use it as uncontrolled component instead (defaultValue)";
                console.error(error);
                throw new Error(error);
            }
            return valueOrDefault;
        }
        case "percent": {
            const { castedValue, isNumber } = castNumber(valueOrDefault);
            return isNumber ? castedValue * 100 : valueOrDefault;
        }
        default:
            return valueOrDefault;
    }
};

const callCallback = (
    type: InputWithLabelProps["type"],
    value: string,
    callback?: (value: string | number) => void,
    disableNegatives?: boolean,
) => {
    if (!callback) return;

    switch (type) {
        case "tenor": {
            const parsedValue = parseTenor(value, disableNegatives);
            const isValid = !Number.isNaN(parsedValue);
            if (isValid) {
                callback(parsedValue);
            }
            break;
        }
        case "percent": {
            const parsedValue = parsePercent(value, disableNegatives);
            const isValid = !Number.isNaN(parsedValue);
            if (isValid) {
                callback(parsedValue);
            }
            break;
        }
        default:
            callback(value);
    }
};

export const InputWithLabel = withLabel(
    forwardRef(
        (
            {
                value,
                defaultValue,
                onChange,
                onBlur,
                onEnter,
                width,
                minWidth,
                maxWidth,
                style,
                required,
                type,
                disableNegatives = false,
                ...rest
            }: InputWithLabelProps,
            ref: Ref<HTMLInputElement>,
        ) => (
            <StyledInput
                type={getType(type)}
                spellCheck={false}
                autoComplete="off"
                {...filterValidHTMLInputElementProps(rest)}
                value={getValue(type, value, defaultValue)}
                defaultValue={getDefaultValue(type, defaultValue)}
                onChange={e => callCallback(type, e.target.value, onChange, disableNegatives)}
                onBlur={e => callCallback(type, e.target.value, onBlur, disableNegatives)}
                onKeyDown={e => {
                    if (e.key === "Enter") {
                        callCallback(
                            type,
                            (e.target as HTMLInputElement).value,
                            onEnter,
                            disableNegatives,
                        );
                    }
                }}
                style={{ width, minWidth, maxWidth, ...style }}
                ref={ref}
            />
        ),
    ),
);
