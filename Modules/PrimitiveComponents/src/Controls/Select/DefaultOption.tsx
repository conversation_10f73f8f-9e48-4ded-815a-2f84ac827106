import React from "react";
import { OptionProps, components as SelectComponents } from "react-select";

import { Tooltip } from "../../Components/Tooltip";
import { Markdown } from "../../Components/Markdown";

import { Option } from "./types";

export const DefaultOption = (props: OptionProps<Option>) => (
    <SelectComponents.Option {...props}>
        {props.data.description ? (
            <Tooltip
                order={1}
                content={<Markdown markdown={props.data.description} />}
            >
                {props.data.label}
            </Tooltip>
        ) : (
            props.data.label
        )}
    </SelectComponents.Option>
);
