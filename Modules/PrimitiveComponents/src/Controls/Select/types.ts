import { ReactNode } from "react";

import type { IconType } from "Theming/index";

export type Option<T = string> = {
    label?: ReactNode;
    value: T;
    description?: ReactNode;
    group?: string;
    icon?: IconType;
    /**
     * In the GroupedTreeSelectMenu if true the option will not be displayed in the dropdown menu
     */
    hidden?: boolean;
    /**
     * In the GroupedTreeSelectMenu is used to override the label in the dropdown menu
     */
    optionLabel?: ReactNode;
    /**
     * If true the option will visible but not be selectable
     */
    isDisabled?: boolean;
};

export type OptionGroup<T = string> = {
    id: string | number;
    label: string;
    options: Array<Option<T> | OptionGroup<T>>;
    /**
     * In the GroupedTreeSelectMenu is used to override the label in the dropdown menu
     */
    optionLabel?: ReactNode;
};

export type EditableOptionGroup<T extends string | number = string> = OptionGroup<T> & {
    canAddToGroup?: boolean;
};

export type Value = string[] | string;
