import React, { ComponentType } from "react";

import { toArray } from "Utilities/index";

import { LayoutContainer } from "../../Components/Layout";

import { SimpleRadios } from "../SimpleRadio";
import { Radio } from "../Radio";

import { findOption, flattenOptions, isOption } from "./utils";
import type { SelectProps } from "./Select";
import { withValidOptionValue } from "./decorators/withValidOptionValue";

const DecoratedSimpleRadios = withValidOptionValue(SimpleRadios);
const DecoratedRadio = withValidOptionValue(Radio);

type RadiosProps<P extends SelectProps> = {
    selectType?: P["selectType"] | "radio" | "radio-grid" | "radio-buttons" | "radio-options";
};

export const withRadios = <P extends SelectProps>(Component: ComponentType<P>) => {
    const WrappedComponent = ({
        selectType,
        as,
        ...rest
    }: Omit<P, keyof RadiosProps<P>> & RadiosProps<P>) => {
        if (selectType === "radio" || selectType === "radio-grid") {
            const { value, onChange, onBlur, disabled, options } = rest;
            const singleValue = toArray(value)?.[0];
            const controlValue = isOption(singleValue) ? singleValue.value : singleValue;

            return (
                <LayoutContainer marginT="x-small">
                    <DecoratedSimpleRadios
                        {...rest}
                        value={controlValue}
                        onChange={v => onChange(findOption(options, v))}
                        onBlur={v => onBlur(findOption(options, v))}
                        options={flattenOptions(options)}
                        disabled={disabled}
                        grid={selectType === "radio-grid"}
                    />
                </LayoutContainer>
            );
        }

        if (selectType === "radio-buttons") {
            const { value, onChange, onBlur, disabled, options } = rest;
            return (
                <DecoratedRadio
                    {...rest}
                    value={value}
                    onChange={onChange}
                    onBlur={onBlur}
                    options={flattenOptions(options)}
                    disabled={disabled}
                    autoHeight
                />
            );
        }

        if (selectType === "radio-options") {
            const { value, onChange, onBlur, disabled, options } = rest;
            return (
                <DecoratedRadio
                    {...rest}
                    value={value}
                    onChange={onChange}
                    onBlur={onBlur}
                    options={flattenOptions(options)}
                    disabled={disabled}
                    autoHeight
                    type="detailed"
                />
            );
        }

        return (
            <Component
                selectType={selectType}
                as={as}
                {...(rest as P)}
            />
        );
    };

    return WrappedComponent;
};
