import React, { useMemo } from "react";
import { createPortal } from "react-dom";

import { useZIndex, useThemeContainer } from "Theming/index";

import { OverlayContainer } from "./OverlayContainer";

type PortalProps = {
    id: string;
};

export const Portal = ({ id }: PortalProps) => {
    const ZIndex = useZIndex();
    const getContainer = useThemeContainer({ className: id });
    const container = useMemo(() => getContainer(), [getContainer]);

    return createPortal(
        <OverlayContainer
            id={id}
            overlayZIndex={ZIndex.Tooltip}
        />,
        container
    );
};
