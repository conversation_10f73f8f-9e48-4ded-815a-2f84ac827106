import React, { useCallback, useEffect, useRef } from "react";
import styled from "styled-components";
import ReactDatePicker from "react-datepicker";
import { formatISO, isValid, format } from "date-fns";
import moment from "moment-business-days";

import { useActiveTheme } from "Theming/index";
import type { ISODateTimeUTC } from "Contracts/index";
import { isUndefined } from "Utilities/index";

import { withLabel } from "../../Components/Label";
import { EventSandbox } from "../../Components/EventSandbox";
import { withStandardErrorBoundary } from "../../Components/ErrorFallback";
import { withClearTextOption } from "../../Decorators/withClearTextOption";
import type { ControlProps } from "../types";

import { Header } from "./Header";
import { Portal } from "./Portal";

const Container = styled.div`
    .react-datepicker-wrapper {
        display: flex;
        flex-grow: 1;

        .react-datepicker__input-container {
            display: flex;
            flex-grow: 1;

            textarea,
            input[type="text"] {
                background-color: var(--input__background-color, transparent);
                border: 1px solid var(--input__border-color, #545456);
                border-radius: var(--input__border-radius, 3px);
                padding: var(--input__padding, 2px 6px);
                font: inherit;
                outline: none;
                width: 100%;
                text-overflow: ellipsis;
                color: var(--color);
                app-region: no-drag;
            }
        }
    }
`;

const popperModifiers = [
    {
        name: "arrow",
        options: {
            padding: 20,
        },
    },
];

const PORTAL_ID = "react-datapicker-container";

// Fix the formats and don't allow configuration for consistency
const DATE_FORMAT = "yyyy-MM-dd";
const TIME_FORMAT = "HH:mm";

const parseDateString = (value: string) => {
    if (value) {
        const date = new Date(value);
        return isValid(date) ? date : undefined;
    }

    return undefined;
};

type DatePickerProps = ControlProps<
    string,
    {
        type?: "date-time" | "date";
        monthsShown?: number;
        placeholder?: string;
        minDate?: ISODateTimeUTC;
        maxDate?: ISODateTimeUTC;
        dateFormat?: string;
        valueFormat?: string;
        width?: string;
        /**
         * Enable preselecting the current date
         */
        preselectEnabled?: boolean;
        /**
         * Number of business days to add to the current date
         */
        preselectAddedBusinessDays?: number;
    }
>;
export const DatePicker = withStandardErrorBoundary(
    ({
        value,
        onChange: onChangeProp,
        onBlur,
        disabled,
        placeholder,
        type = "date",
        monthsShown = 1,
        minDate,
        maxDate,
        dateFormat: dateFormatProp,
        width,
        valueFormat,
        preselectEnabled = false,
        preselectAddedBusinessDays = 0,
    }: DatePickerProps) => {
        const { theme } = useActiveTheme() || {};

        const formatDate = useCallback(
            date => {
                if (date && isValid(date)) {
                    if (valueFormat) {
                        return format(date, valueFormat);
                    }

                    return formatISO(date, {
                        representation: type === "date-time" ? "complete" : "date",
                    });
                }

                return null;
            },
            [valueFormat, type],
        );

        const onChange = useCallback(
            (date: Date) => onChangeProp?.(formatDate(date)),
            [onChangeProp, formatDate],
        );

        // Automatically preselects a date if not already set
        const alreadyPreselected = useRef(false);
        useEffect(() => {
            if (
                preselectEnabled &&
                (isUndefined(value) || value === "") &&
                !alreadyPreselected.current
            ) {
                alreadyPreselected.current = true;
                onChangeProp?.(
                    formatDate(
                        moment()
                            .businessAdd(preselectAddedBusinessDays || 0)
                            .toDate(),
                    ),
                );
            }
        }, [onChangeProp, formatDate, preselectEnabled, preselectAddedBusinessDays, value]);

        const portalId = `${PORTAL_ID}-${theme}`;
        const selected = parseDateString(value);
        const min = parseDateString(minDate);
        const max = parseDateString(maxDate);
        const dateFormat = (function iife() {
            if (dateFormatProp) return dateFormatProp;

            switch (type) {
                case "date":
                    return DATE_FORMAT;
                case "date-time":
                default:
                    return `${DATE_FORMAT} ${TIME_FORMAT}`;
            }
        })();

        return (
            <Container
                data-component="DatePicker"
                style={{ width }}
            >
                <EventSandbox>
                    <Portal id={portalId} />
                </EventSandbox>

                <ReactDatePicker
                    placeholderText={placeholder || dateFormat.toUpperCase()}
                    disabled={disabled}
                    onChange={onChange}
                    onBlur={() => onBlur?.(value)}
                    selected={selected}
                    showTimeSelect={type === "date-time"}
                    dateFormat={dateFormat}
                    timeFormat={TIME_FORMAT}
                    calendarStartDay={1}
                    monthsShown={monthsShown}
                    minDate={min}
                    maxDate={max}
                    renderCustomHeader={props => (
                        <Header
                            {...props}
                            monthsShown={monthsShown}
                            minDate={min}
                            maxDate={max}
                        />
                    )}
                    popperProps={{ strategy: "fixed" }}
                    popperModifiers={popperModifiers}
                    portalId={portalId}
                />
            </Container>
        );
    },
);

export const DatePickerWithLabel = withClearTextOption(withLabel(DatePicker));
