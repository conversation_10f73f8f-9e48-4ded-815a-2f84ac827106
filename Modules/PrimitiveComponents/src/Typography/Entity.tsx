import styled from "styled-components";
import classnames from "classnames";

import { withComponentProps } from "../Decorators/withComponentProps";

import { Text, type TextFontSize, type TextFontWeight } from "./Text";

type EntityProps = {
    type?: "date";
};

export const Entity = withComponentProps(
    styled(Text)<EntityProps>`
        &.type--date {
            color: var(--entity__date);
        }
    `,
    ({ type, className }) => ({
        fontSize: "inherit" as TextFontSize,
        fontWeight: "inherit" as TextFontWeight,
        block: true,
        className: classnames(className, `type--${type}`)
    })
);
