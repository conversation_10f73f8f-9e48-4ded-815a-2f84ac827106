import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import {
    ThemeContainer,
    ThemeIterator,
    LayoutVertical,
    LayoutHorizontal
} from "../../..";

import { Value } from "../Value";

<Meta title="PrimitiveComponents/Typography/Value" component={Value} />

export const Template = args => (
    <LayoutHorizontal spacing="large">
        <ThemeIterator>
            {() => (
                <LayoutVertical spacing="xx-large">
                    <ThemeContainer flex>
                        <Value {...args} type="ticker">Lorem ipsum dolor sit amet.</Value>
                        <Value {...args} type="positive">Lorem ipsum dolor sit amet.</Value>
                        <Value {...args} type="neutral">Lorem ipsum dolor sit amet.</Value>
                        <Value {...args} type="cautious">Lorem ipsum dolor sit amet.</Value>
                        <Value {...args} type="negative">Lorem ipsum dolor sit amet.</Value>
                    </ThemeContainer>
                </LayoutVertical>
            )}
        </ThemeIterator>
    </LayoutHorizontal>
);

# Value

<Description of={Value} />

<Canvas>
    <Story
        name="Component"
        args={{}}
        parameters={{}}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes of={Value} sort="requiredFirst" />
