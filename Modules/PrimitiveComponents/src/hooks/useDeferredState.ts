import { useState, useRef, useCallback, Dispatch, SetStateAction, useEffect } from "react";

export const useDeferredState = <S>(initialState?: S | (() => S), wait = 300) => {
    const [s, setS] = useState(initialState);
    const t = useRef<NodeJS.Timeout>();

    const deferSetter = useCallback<Dispatch<SetStateAction<S>>>(
        state => {
            clearTimeout(t.current);
            t.current = setTimeout(() => {
                setS(state);
            }, wait);
        },
        [wait]
    );

    const immediateSetter = useCallback<Dispatch<SetStateAction<S>>>(state => {
        clearTimeout(t.current);
        setS(state);
    }, []);

    useEffect(() => {
        return () => {
            clearTimeout(t.current);
        };
    }, []);

    return [s, deferSetter, immediateSetter] as const;
};
