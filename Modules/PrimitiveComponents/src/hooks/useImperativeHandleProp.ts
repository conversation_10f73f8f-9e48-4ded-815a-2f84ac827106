import { useEffect } from "react";

export type ImperativeHandleProp<
    A extends unknown[] = unknown[],
    R = void,
    C extends (...args: A) => R = (...args: A) => R
> = (cb: C) => void;

export type ImperativeHandlePropCallback<X extends ImperativeHandleProp> = Parameters<X>[0];

export const useImperativeHandleProp = <
    A extends unknown[],
    R,
    C extends (...args: A) => R = (...args: A) => R
>(
    prop: ImperativeHandleProp<A, R>,
    cb: C
) => {
    useEffect(() => {
        prop?.(cb);
    }, [prop, cb]);
};
