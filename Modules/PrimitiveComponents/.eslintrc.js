const baseConfig = require("../../config/eslint-config-base");

module.exports = {
    ...baseConfig,
    settings: {
        ...baseConfig.settings,
        "import/resolver": {
            typescript: {
                alwaysTryTypes: true,
                project: ["./tsconfig.eslint.json"]
            }
        }
    },
    overrides: [
        ...baseConfig.overrides,
        {
            files: ["**/*.@(ts|tsx)"],
            parserOptions: {
                tsconfigRootDir: __dirname,
                project: ["./tsconfig.eslint.json"]
            }
        }
    ]
};
