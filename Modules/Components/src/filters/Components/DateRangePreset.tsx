import React, { ComponentProps, useMemo, useRef, useCallback } from "react";
import { formatISO, subDays, subMonths, isSameDay, parseISO } from "date-fns";

import { RadioWithLabel } from "PrimitiveComponents/index";
import type { ISODateTimeUTC } from "Contracts/index";

import { useFiltersApi } from "../useFiltersApi";
import { AllFilters, PublicFiltersApi } from "../types";

const getOptionMap = () => {
    const now = new Date();
    const option = { representation: "date" } as const;
    return {
        today: {
            label: "Today",
            value: "today",
            iso: formatISO(now, option) as ISODateTimeUTC
        },
        "1W": {
            label: "1W",
            value: "1W",
            iso: formatISO(subDays(now, 7), option) as ISODateTimeUTC
        },
        "1M": {
            label: "1M",
            value: "1M",
            iso: formatISO(subMonths(now, 1), option) as ISODateTimeUTC
        },
        "3M": {
            label: "3M",
            value: "3M",
            iso: formatISO(subMonths(now, 3), option) as ISODateTimeUTC
        },
        "6M": {
            label: "6M",
            value: "6M",
            iso: formatISO(subMonths(now, 6), option) as ISODateTimeUTC
        },
        "12M": {
            label: "12M",
            value: "12M",
            iso: formatISO(subMonths(now, 12), option) as ISODateTimeUTC
        }
    };
};

type DateRangePresetProps = Omit<ComponentProps<typeof RadioWithLabel>, "options"> & {
    availableFilters: (keyof AllFilters)[];
    filters: AllFilters;
    updateFilters: PublicFiltersApi["updateFilters"];
    optionValues?: string[];
};

const DateRangePresetBase = ({
    optionValues,
    availableFilters,
    filters,
    updateFilters,
    ...rest
}: DateRangePresetProps) => {
    const optionMap = useRef(getOptionMap());

    const hasFromDateFilter = availableFilters.includes("fromDate");
    const hasToDateFilter = availableFilters.includes("toDate");
    const hasAnyDateFilter =
        availableFilters.includes("date") || hasFromDateFilter || hasToDateFilter;
    const hasFromAndToDateFilter = hasFromDateFilter && hasToDateFilter;

    const options = useMemo(() => {
        if (!hasAnyDateFilter) return undefined;

        if (optionValues?.length > 0) return optionValues.map(v => optionMap.current[v]);

        return ["today", ...(hasFromAndToDateFilter ? ["1W", "1M"] : [])].map(
            v => optionMap.current[v]
        );
    }, [hasAnyDateFilter, hasFromAndToDateFilter, optionValues]);

    const value = useMemo(() => {
        if (hasAnyDateFilter) {
            const today = parseISO(optionMap.current.today.iso);
            if (hasFromAndToDateFilter) {
                if (isSameDay(parseISO(filters?.toDate), today)) {
                    const fromDate = parseISO(filters?.fromDate);

                    const result = options?.find(option =>
                        isSameDay(parseISO(option.iso), fromDate)
                    );
                    if (result) {
                        return result.value;
                    }
                }
            }

            if (
                isSameDay(parseISO(filters?.date), today) ||
                (!hasFromDateFilter && isSameDay(parseISO(filters?.toDate), today))
            ) {
                return optionMap.current.today.value;
            }
        }

        return undefined;
    }, [
        hasFromDateFilter,
        hasAnyDateFilter,
        hasFromAndToDateFilter,
        filters?.date,
        filters?.fromDate,
        filters?.toDate,
        options
    ]);

    const handleChange = useCallback(
        (updatedValue: string) => {
            const mappedValue = optionMap.current[updatedValue];
            return mappedValue
                ? updateFilters({
                      fromDate: mappedValue.iso,
                      toDate: optionMap.current.today.iso
                  })
                : undefined;
        },
        [updateFilters]
    );

    return (
        options?.length > 0 && (
            <RadioWithLabel
                {...rest}
                value={value}
                options={options}
                onChange={handleChange}
            />
        )
    );
};

export const DateRangePreset = (props: DateRangePresetProps) => {
    const { availableFilters, filters, updateFilters } = useFiltersApi();
    return (
        <DateRangePresetBase
            availableFilters={availableFilters}
            filters={filters}
            updateFilters={updateFilters}
            {...props}
        />
    );
};
