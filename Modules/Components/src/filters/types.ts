import { ComponentProps, ComponentType, Dispatch, ReactNode, SetStateAction } from "react";

import type { RaidFilters, RaidFilterType } from "Contracts/index";
import { type ControlProps, type LabelProps, Select } from "PrimitiveComponents/index";

export type FilterSlot = "left" | "right" | "center";

export type KnownFilterProps = {
    required: boolean;
};

export type SpecialFilters = {
    dateRangePreset?: unknown;
};

export type AllFilters = RaidFilters & SpecialFilters;

export type AllFiltersType = keyof AllFilters;

type KnownFilterComponentProps = Partial<
    ControlProps<
        unknown,
        LabelProps & {
            commandParams?: AllFilters;
            width?: string | number;
        } & Pick<ComponentProps<typeof Select>, "selectType">
    >
>;

export type FilterTypeComponent<
    V = unknown,
    T = unknown,
    P extends ControlProps<unknown, unknown> = ControlProps<unknown, unknown>
> = {
    getDerivedProps?: (
        availableFilters: AllFilters,
        filters: AllFilters & { [K in keyof T]?: T[K] }
    ) => Omit<KnownFilterComponentProps, "label"> & Record<string, unknown>;
    filterProps?: {
        slot?: FilterSlot;
        group?: string;
        width?: string | number;
        transformValueToFilter?: (value: V) => V | undefined;
        transformToControlValue?: (filters: AllFilters) => P["value"];
        transformFromControlValue?: (value: P["value"]) => void;
        defaultValue?: unknown;
    };
    Component: FilterComponent<P>;
    label: ReactNode;
    filterLabel?: ReactNode;
};

export const withFilterComponent =
    <V extends keyof T, T extends AllFilters, P extends ControlProps<unknown, unknown>>(
        name: V,
        Component: ComponentType<P>
    ) =>
    (
        c: Omit<FilterTypeComponent<V, T, P>, "Component">
    ): { [name: string]: FilterTypeComponent<V, T> } => {
        return {
            [name]: {
                Component,
                ...c
            }
        };
    };

export type FilterTypeAlias = {
    alias?: RaidFilterType;
};

export type FilterType<V = unknown, T = unknown> = FilterTypeComponent<V, T> | FilterTypeAlias;

export const isFilterTypeComponent = (o: FilterType): o is FilterTypeComponent =>
    Object.prototype.hasOwnProperty.call(o || {}, "label");

export const isAliasTypeComponent = (o: FilterType): o is FilterTypeAlias =>
    Object.prototype.hasOwnProperty.call(o || {}, "alias");

export type FilterComponent<P extends ControlProps<unknown, unknown>> = ComponentType<
    KnownFilterComponentProps & P
>;

export type TransformedFilterType<V = unknown> = {
    name: AllFiltersType;
} & FilterTypeComponent<V>;

export type FiltersPropsMap<T extends AllFilters = AllFilters> = {
    [K in keyof T]: FilterType<T[K]>;
};

export type CustomFiltersPropsMap<T extends AllFilters = AllFilters> = {
    [K in keyof T]: FilterType<T[K], T> & KnownFilterProps;
};

export type FiltersFactoryPropsMap = Partial<Record<keyof AllFilters, FilterType>>;

type FnOrValue<Value> = Value | ((curr: Value) => Value);

export type PublicFiltersApi = {
    filters: AllFilters;

    updateFilter: <F extends keyof AllFilters>(
        filterName: F | undefined,
        fnOrValue: FnOrValue<AllFilters[F] | Record<string, unknown>>
    ) => void;
    updateFilters: (f: Partial<AllFilters>) => void;
};

export type AvailableFilter = {
    count: number;
    requiredCount: number;
};

export type AvailableFilters = {
    [name in AllFiltersType]: AvailableFilter;
};

export type AvailableFilterConfig = {
    [name in AllFiltersType]: KnownFilterProps;
};

export type FiltersApi = {
    allFilters: AllFilters;
    defaultFilters: AllFilters;
    availableFilters: AllFiltersType[];
    managedFilters: AllFiltersType[];
    availableFiltersMap: AvailableFilters;
    displayFilters: AllFiltersType[];

    updateFilterFactory: <F extends keyof AllFilters>(
        filterName: F
    ) => (fnOrValue: FnOrValue<AllFilters[F]>) => void;

    filtersPropsMap: FiltersPropsMap;
    setFiltersPropsMap: Dispatch<SetStateAction<FiltersPropsMap>>;
    updateFilterProps: <F extends keyof AllFilters>(
        filterName: F,
        value: Partial<FilterType>
    ) => void;
    updateFiltersProps: <F extends keyof AllFilters>(
        pairs: Array<[filterName: F, value: Partial<FilterType>]>
    ) => void;

    addAvailableFilters: (f: AvailableFilterConfig) => void;
    removeAvailableFilters: (f: AvailableFilterConfig) => void;

    hasProvider: boolean;

    onChange: (filters: AllFilters) => void | Promise<void>;
} & PublicFiltersApi;

export type GroupFilter = {
    group: string;
    items: TransformedFilterType[];
};

export type GroupedFilters = {
    [slot in FilterSlot]: GroupFilter[];
};
