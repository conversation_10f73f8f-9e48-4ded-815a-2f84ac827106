import { useState } from "react";
import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import { ThemeIterator, LayoutVertical, ThemeContainer, LayoutContainer, Panel, PanelCollapseAll, PanelsProvider } from "PrimitiveComponents/index";
import { withFixtures } from "TransportGateway/index";

import { Filters } from "../Filters";
import { FiltersProvider } from "../FiltersProvider";
import { withFilterValues } from "../decorators/withFilterValues";

<Meta title="Components/Filters" component={Filters} />

export const Fixtures = withFixtures(({ children }) => children);
export const StringifyProps = (props) => <pre>{JSON.stringify(props, null, 2)}</pre>;
export const Test = withFilterValues(
    StringifyProps,
    ["longShort", "traderIds", "fundGroupIds", "fromDate", "toDate", "query", "dateRangePreset"]
);
export const Test2 = withFilterValues(
    StringifyProps,
    ["daysAgo", "toDate", "query", "traderId", "dateRangePreset", "datePreset"],
    { toDate: { required: true }},
    { query: "mappedPropertyQuery" }
);
export const Test3 = withFilterValues(
    StringifyProps,
    ["daysAgo", "traderIds", "longShort"],
    undefined
);

export const Template = ({ inheritFilters, inheritFilters2 }) => {
    const [value, setValue] = useState();
    return (
        <LayoutVertical gap="xx-large">
            <ThemeIterator>
                {() => (
                    <ThemeContainer flex>
                        <Fixtures>
                            <PanelsProvider>
                                <FiltersProvider>
                                    <LayoutVertical>
                                        <Filters><PanelCollapseAll /></Filters>
                                        <LayoutContainer>
                                            <LayoutVertical spacing="x-large">
                                                <Panel makeCollapsible title="Panel 1" defaultExpanded>
                                                    <Test />
                                                </Panel>
                                                <Panel makeCollapsible title="Panel 2" defaultExpanded>
                                                    <Test2 />
                                                </Panel>
                                                <Panel makeCollapsible title="Panel With Nested Filters Provider">
                                                    <FiltersProvider inheritFilters={inheritFilters}>
                                                        <LayoutVertical>
                                                            <Filters />
                                                            <Test2 />
                                                            <FiltersProvider inheritFilters={inheritFilters2}>
                                                                <LayoutVertical>
                                                                    <Filters />
                                                                    <Test2 />
                                                                </LayoutVertical>
                                                            </FiltersProvider>
                                                        </LayoutVertical>
                                                    </FiltersProvider>
                                                </Panel>
                                                <Panel makeCollapsible title="Panel 3" defaultExpanded>
                                                    <FiltersProvider inheritFilters={inheritFilters}>
                                                        <LayoutVertical>
                                                            <Filters />
                                                            <Test3 />
                                                        </LayoutVertical>
                                                    </FiltersProvider>
                                                </Panel>
                                            </LayoutVertical>
                                        </LayoutContainer>
                                    </LayoutVertical>
                                </FiltersProvider>
                            </PanelsProvider>
                        </Fixtures>
                    </ThemeContainer>
                )}
            </ThemeIterator>
        </LayoutVertical>
    );
};

# Filters

<Description of={Filters} />

<Canvas>
    <Story
        name="Filters"
        parameters={{
            controls: {
                disable: false,
                include: ["inheritFilters", "inheritFilters2"]
            }
        }}
        args={{
            inheritFilters: true,
            inheritFilters2: true
        }}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes of={Test} sort="requiredFirst" />
