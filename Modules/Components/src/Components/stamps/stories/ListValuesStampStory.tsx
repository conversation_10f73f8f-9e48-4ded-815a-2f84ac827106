import React from "react";
import type { Schema } from "Contracts/index";
import { ListValuesStamp } from "../ListValuesStamp";

const PROPS = {
    title: "ListValuesStamp",
    h: "100%",
    background: "container" as const,
    data: [
        {
            rowId: "Winner",
            type: "Winner",
            position: "JPMBHGDK Index",
            dollarPL: 19549675.62653796
        },
        {
            rowId: "Loser",
            type: "Loser",
            position: "JP <PERSON> (PSIG)",
            dollarPL: -12125895.697670583
        }
    ],
    schema: [
        {
            name: "dollarPL",
            metaData: {
                properties: {
                    formatterType: "bigValue",
                    valueFormat: "$.1f"
                }
            }
        }
    ] as Schema,
    labelKey: "position",
    valueKey: "dollarPL"
};

export const ListValuesStampStory = () => <ListValuesStamp {...PROPS} />;
