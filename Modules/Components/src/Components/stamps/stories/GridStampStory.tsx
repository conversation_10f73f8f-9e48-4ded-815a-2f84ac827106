import React from "react";
import type { Schema } from "Contracts/index";
import type { GridProps } from "Grid/index";
import { GridStamp } from "../GridStamp";

const PROPS = {
    title: "GridStamp",
    h: "100%",
    background: "container" as const,
    data: [
        {
            rank: 0,
            previous: null,
            identifier: "UBXN SW Equity",
            chgtoday: 0.0119283,
            fY1ChgSinceNumbers: -2.3283093,
            absPerfSinceNumbers: 0.2390624,
            absPerfOnNumbers: 0.0465997,
            corr6m: 0.3582438,
            dTE: -33,
            rowId: 11
        },
        {
            rank: 1,
            previous: 11,
            identifier: "STMPA FP Equity",
            chgtoday: -0.013303,
            fY1ChgSinceNumbers: -0.2777377,
            absPerfSinceNumbers: 0.0379474,
            absPerfOnNumbers: 0.0112188,
            corr6m: 0.3526878,
            dTE: -34,
            rowId: 10
        },
        {
            rank: 2,
            previous: 10,
            identifier: "IFX GR Equity",
            chgtoday: 0,
            fY1ChgSinceNumbers: -0.0936582,
            absPerfSinceNumbers: 0.0584032,
            absPerfOnNumbers: 0.1292673,
            corr6m: 0.3020752,
            dTE: -27,
            rowId: 4
        },
        {
            rank: 3,
            previous: 4,
            identifier: "MELE BB Equity",
            chgtoday: -0.0140433,
            fY1ChgSinceNumbers: 0.0321444,
            absPerfSinceNumbers: 0.038379,
            absPerfOnNumbers: 0.1728045,
            corr6m: 0.297312,
            dTE: -35,
            rowId: 5
        },
        {
            rank: 4,
            previous: 5,
            identifier: "ARM US Equity",
            chgtoday: 0,
            fY1ChgSinceNumbers: 0.0043379,
            absPerfSinceNumbers: 0.501979,
            absPerfOnNumbers: -0.0233809,
            corr6m: 0.2558759,
            dTE: -24,
            rowId: 1
        }
    ],
    schema: [
        {
            columnId: 3,
            contentType: 5,
            traits: "hidden",
            name: "rank",
            metaData: {
                sortable: false,
                searchable: false,
                displayName: "Rank"
            }
        },
        {
            columnId: 4,
            contentType: 5,
            traits: "hidden,previous",
            name: "previous",
            metaData: {
                sortable: false,
                searchable: false,
                displayName: "Previous"
            }
        },
        {
            columnId: 5,
            contentType: 14,
            name: "identifier",
            metaData: {
                sortable: true,
                searchable: false,
                contentTypeForDisplay: "BloombergTicker",
                displayName: "Ticker",
                properties: {
                    order: "10",
                    expandable: "true",
                    flex: 2,
                    stampComponentName: "Components.GridStamp",
                    stampPropsGridProps: '{ "layout": "full", "hasTransparentBackground": true }',
                    stampPropsModalContentComponentName:
                        "ConnectedComponents.PeersDetailsViewServer",
                    stampPropsModalWidth: "700px",
                    stampPropsModalMinHeight: "500px"
                }
            }
        },
        {
            columnId: 6,
            contentType: 8,
            name: "chgtoday",
            metaData: {
                sortable: true,
                searchable: false,
                contentTypeForDisplay: "Notional",
                displayName: "Chg",
                properties: {
                    order: "55",
                    valueFormat: "percent1dp",
                    expandable: "true",
                    flex: 1,
                    maxWidth: 42
                }
            }
        },
        {
            columnId: 7,
            contentType: 8,
            name: "fY1ChgSinceNumbers",
            metaData: {
                width: 60,
                sortable: true,
                searchable: false,
                hidden: true,
                contentTypeForDisplay: "ChangeValuePercentage",
                displayName: "EPS FY1 Chg",
                properties: {
                    order: "60",
                    expandable: "true"
                }
            }
        },
        {
            columnId: 8,
            contentType: 8,
            name: "absPerfSinceNumbers",
            metaData: {
                width: 60,
                sortable: true,
                searchable: false,
                hidden: true,
                contentTypeForDisplay: "ChangeValuePercentage",
                displayName: "Abs Since",
                properties: {
                    order: "50",
                    expandable: "true"
                }
            }
        },
        {
            columnId: 9,
            contentType: 8,
            name: "absPerfOnNumbers",
            metaData: {
                sortable: true,
                searchable: false,
                contentTypeForDisplay: "Notional",
                displayName: "Last Figs",
                properties: {
                    order: "40",
                    multiLineHeader: "true",
                    valueFormat: "percent1dp",
                    expandable: "true",
                    flex: 1,
                    maxWidth: 42
                }
            }
        },
        {
            columnId: 10,
            contentType: 8,
            name: "corr6m",
            metaData: {
                sortable: true,
                searchable: false,
                contentTypeForDisplay: "Standard2dp",
                displayName: "Corr",
                properties: {
                    order: "20",
                    expandable: "true",
                    flex: 1,
                    maxWidth: 42
                }
            }
        },
        {
            columnId: 11,
            contentType: 5,
            name: "dTE",
            metaData: {
                sortable: true,
                searchable: false,
                displayName: "DTE",
                properties: {
                    order: "30",
                    expandable: "true",
                    flex: 1,
                    maxWidth: 42
                }
            }
        }
    ] as Schema,
    gridProps: {
        layout: "full",
        hasTransparentBackground: true
    } as GridProps
};

export const GridStampStory = () => <GridStamp {...PROPS} />;
