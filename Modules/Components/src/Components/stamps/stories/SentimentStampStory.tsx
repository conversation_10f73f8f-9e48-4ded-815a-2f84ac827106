import React from "react";
import type { Schema } from "Contracts/index";
import { SentimentStamp } from "../SentimentStamp";

const PROPS = {
    title: "SentimentStampStory",
    min: 0,
    max: 100,
    h: "100%",
    background: "container" as const,
    data: {
        rank: 0,
        sector: "Consumer Discretionary",
        industryGroup: "Consumer Discretionary Distribution & Retail",
        nMVGMV: 0.006355961677611207,
        oneMonthPercentageChangePoints: -0.00033178572160334936,
        levelFiveYearRange: 0.15023635242183705,
        grossLevelFiveYearRange: 0.027068051814560904,
        sentimentCheck: -2,
        rowId: 0
    },
    schema: [
        {
            columnId: 5,
            contentType: 5,
            traits: "hidden",
            name: "rank",
            metaData: {
                displayName: "Rank"
            }
        },
        {
            columnId: 6,
            contentType: 5,
            traits: "hidden,previous",
            name: "previous",
            metaData: {
                displayName: "Previous"
            }
        },
        {
            columnId: 7,
            contentType: 9,
            name: "sector",
            metaData: {
                width: 150,
                contentTypeForDisplay: "",
                displayName: "Sector",
                properties: {
                    order: "20"
                }
            }
        },
        {
            columnId: 8,
            contentType: 9,
            name: "industryGroup",
            metaData: {
                width: 150,
                contentTypeForDisplay: "",
                displayName: "Industry",
                properties: {
                    order: "10"
                }
            }
        },
        {
            columnId: 9,
            contentType: 8,
            name: "nMVGMV",
            metaData: {
                width: 100,
                contentTypeForDisplay: "",
                displayName: "Current Level (Net)",
                properties: {
                    order: "30",
                    multiLineHeader: "true",
                    displayType: "Notional",
                    valueFormat: "percent2dp",
                    stampComponentName: "Components.SentimentStamp"
                }
            }
        },
        {
            columnId: 10,
            contentType: 8,
            name: "oneMonthPercentageChangePoints",
            metaData: {
                width: 100,
                contentTypeForDisplay: "ChangeValuePercentage",
                displayName: "1m % Chg (Net)",
                properties: {
                    order: "40",
                    multiLineHeader: "true",
                    displayType: "Notional",
                    valueFormat: "percent2dp"
                }
            }
        },
        {
            columnId: 11,
            contentType: 8,
            name: "levelFiveYearRange",
            metaData: {
                width: 120,
                contentTypeForDisplay: "",
                displayName: "Current Level in last 5y Range",
                properties: {
                    order: "50",
                    multiLineHeader: "true",
                    displayType: "Notional",
                    valueFormat: "percent2dp"
                }
            }
        },
        {
            columnId: 12,
            contentType: 8,
            name: "grossLevelFiveYearRange",
            metaData: {
                width: 120,
                contentTypeForDisplay: "",
                displayName: "Gross Level in last 5y Range",
                properties: {
                    order: "60",
                    multiLineHeader: "true",
                    displayType: "Notional",
                    valueFormat: "percent2dp"
                }
            }
        },
        {
            columnId: 13,
            contentType: 5,
            name: "sentimentCheck",
            metaData: {
                width: 80,
                contentTypeForDisplay: "ScoreDot",
                displayName: "Sentiment Check",
                properties: {
                    order: "70",
                    multiLineHeader: "true"
                }
            }
        }
    ] as Schema
};

export const SentimentStampStory = () => <SentimentStamp {...PROPS} />;
