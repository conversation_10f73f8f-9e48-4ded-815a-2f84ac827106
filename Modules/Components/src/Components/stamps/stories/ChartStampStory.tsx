import React from "react";
import type { Schema } from "Contracts/index";
import { ChartStamp } from "../ChartStamp";

const PROPS = {
    title: "ChartStamp",
    background: "container" as const,
    data: [
        {
            rowId: "20240613_0-33%_Short",
            group: ["0-33%", "Short"],
            date: "2024-06-13",
            contributionGroup: "0-33%",
            groupName: "0-33% (Short)",
            percentageOfTotalRisk: -0.16139504328361226,
            percentageOfFactorRisk: -0.7045761370135741,
            percentageOfSpecificRisk: 0.04823506662245657,
            percentageOfGross: 0.07777762135364337,
            percentageOfNet: -0.07777762135364337,
            mctr: -0.036136337490455216,
            mcfr: -0.08324643905290002,
            longDeltaExposure: 0,
            shortDeltaExposure: 28565295.61286212,
            netDeltaExposure: -28565295.61286212,
            grossDeltaExposure: 28565295.61286212,
            valueAtRisk95: 388748.79459447257,
            contributionToPortfolioValueAtRisk95: -106957.1550938308,
            absolutePercentageOfTotalRisk: 0.16139504328361226
        },
        {
            rowId: "20240613_0-33%_Long",
            group: ["0-33%", "Long"],
            date: "2024-06-13",
            contributionGroup: "0-33%",
            groupName: "0-33% (Long)",
            percentageOfTotalRisk: 0.18307041595483778,
            percentageOfFactorRisk: 0.5224544211365162,
            percentageOfSpecificRisk: 0.05209180287747257,
            percentageOfGross: 0.053219726585050635,
            percentageOfNet: 0.053219726585050635,
            mctr: 0.05990376614825852,
            mcfr: 0.09021279984030466,
            longDeltaExposure: 19545946.454512592,
            shortDeltaExposure: 0,
            netDeltaExposure: 19545946.454512592,
            grossDeltaExposure: 19545946.454512592,
            valueAtRisk95: 291439.94044848485,
            contributionToPortfolioValueAtRisk95: 121321.5131890106,
            absolutePercentageOfTotalRisk: 0.18307041595483778
        },
        {
            rowId: "20240613_33-66%_Short",
            group: ["33-66%", "Short"],
            date: "2024-06-13",
            contributionGroup: "33-66%",
            groupName: "33-66% (Short)",
            percentageOfTotalRisk: -0.2125907415767377,
            percentageOfFactorRisk: -0.8861529457016205,
            percentageOfSpecificRisk: 0.04735740556821477,
            percentageOfGross: 0.115156093523935,
            percentageOfNet: -0.115156093523935,
            mctr: -0.03214889285035585,
            mcfr: -0.07071542468771774,
            longDeltaExposure: 0,
            shortDeltaExposure: 42293243.17051142,
            netDeltaExposure: -42293243.17051142,
            grossDeltaExposure: 42293243.17051142,
            valueAtRisk95: 486545.22098606743,
            contributionToPortfolioValueAtRisk95: -140884.7536809355,
            absolutePercentageOfTotalRisk: 0.2125907415767377
        },
        {
            rowId: "20240613_33-66%_Long",
            group: ["33-66%", "Long"],
            date: "2024-06-13",
            contributionGroup: "33-66%",
            groupName: "33-66% (Long)",
            percentageOfTotalRisk: 0.4096820326290545,
            percentageOfFactorRisk: 1.0659754715690943,
            percentageOfSpecificRisk: 0.1563984273568349,
            percentageOfGross: 0.12294223383372407,
            percentageOfNet: 0.12294223383372407,
            mctr: 0.05803024655314085,
            mcfr: 0.07967801459087052,
            longDeltaExposure: 45152849.774075,
            shortDeltaExposure: 0,
            netDeltaExposure: 45152849.774075,
            grossDeltaExposure: 45152849.774075,
            valueAtRisk95: 536944.1191043498,
            contributionToPortfolioValueAtRisk95: 271497.95812540216,
            absolutePercentageOfTotalRisk: 0.4096820326290545
        },
        {
            rowId: "20240613_66-100%_Short",
            group: ["66-100%", "Short"],
            date: "2024-06-13",
            contributionGroup: "66-100%",
            groupName: "66-100% (Short)",
            percentageOfTotalRisk: -0.449426571448669,
            percentageOfFactorRisk: -1.8570714177653218,
            percentageOfSpecificRisk: 0.09382640117553638,
            percentageOfGross: 0.2616750117616624,
            percentageOfNet: -0.2616750117616624,
            mctr: -0.029909220282332314,
            mcfr: -0.06521668890869795,
            longDeltaExposure: 0,
            shortDeltaExposure: 96105074.1251669,
            netDeltaExposure: -96105074.1251669,
            grossDeltaExposure: 96105074.1251669,
            valueAtRisk95: 1011677.4188076105,
            contributionToPortfolioValueAtRisk95: -297836.8265080715,
            absolutePercentageOfTotalRisk: 0.449426571448669
        },
        {
            rowId: "20240613_66-100%_Long",
            group: ["66-100%", "Long"],
            date: "2024-06-13",
            contributionGroup: "66-100%",
            groupName: "66-100% (Long)",
            percentageOfTotalRisk: 1.2306672911232852,
            percentageOfFactorRisk: 2.8593971243369274,
            percentageOfSpecificRisk: 0.602090895727598,
            percentageOfGross: 0.36404956756181944,
            percentageOfNet: 0.36404956756181944,
            mctr: 0.05886928167466449,
            mcfr: 0.07217825009938729,
            longDeltaExposure: 133704056.95302045,
            shortDeltaExposure: 0,
            netDeltaExposure: 133704056.95302045,
            grossDeltaExposure: 133704056.95302045,
            valueAtRisk95: 1409067.4528106966,
            contributionToPortfolioValueAtRisk95: 815568.2457624965,
            absolutePercentageOfTotalRisk: 1.2306672911232852
        },
        {
            rowId: "20240613_Unknown_Short",
            group: ["Unknown", "Short"],
            date: "2024-06-13",
            contributionGroup: "Unknown",
            groupName: "Unknown (Short)",
            percentageOfTotalRisk: -0.000008174963799767238,
            percentageOfFactorRisk: -0.000029359372197873353,
            percentageOfSpecificRisk: 7.439191722019509e-10,
            percentageOfGross: 0.002302818934892557,
            percentageOfNet: -0.002302818934892557,
            mctr: -0.00006182080337230511,
            mcfr: -0.00011716000055628043,
            longDeltaExposure: 0,
            shortDeltaExposure: 845753.6046134275,
            netDeltaExposure: -845753.6046134275,
            grossDeltaExposure: 845753.6046134275,
            valueAtRisk95: 22.43948367000129,
            contributionToPortfolioValueAtRisk95: -5.417581935782651,
            absolutePercentageOfTotalRisk: 0.000008174963799767238
        },
        {
            rowId: "20240613_Unknown_Long",
            group: ["Unknown", "Long"],
            date: "2024-06-13",
            contributionGroup: "Unknown",
            groupName: "Unknown (Long)",
            percentageOfTotalRisk: 7.915656413882264e-7,
            percentageOfFactorRisk: 0.000002842810176752952,
            percentageOfSpecificRisk: -7.203222804507125e-11,
            percentageOfGross: 0.0001759667931199112,
            percentageOfNet: 0.0001759667931199112,
            mctr: 0.00007833661723066349,
            mcfr: 0.00014846002668469444,
            longDeltaExposure: 64627.117364037964,
            shortDeltaExposure: 0,
            netDeltaExposure: 64627.117364037964,
            grossDeltaExposure: 64627.117364037964,
            valueAtRisk95: 2.1727710016490764,
            contributionToPortfolioValueAtRisk95: 0.524573787090429,
            absolutePercentageOfTotalRisk: 7.915656413882264e-7
        }
    ],
    schema: [
        {
            columnId: 0,
            contentType: 9,
            schema: [],
            name: "rowId",
            metaData: {
                hidden: true,
                displayName: "Row Id",
                properties: {}
            }
        },
        {
            columnId: 1,
            contentType: 13,
            schema: [],
            name: "group",
            metaData: {
                hidden: true,
                displayName: "Group",
                properties: {}
            }
        },
        {
            columnId: 2,
            contentType: 17,
            schema: [],
            name: "date",
            metaData: {
                contentTypeForDisplay: "DateTimeISO",
                displayName: "Date",
                properties: {
                    order: 1
                }
            }
        },
        {
            columnId: 3,
            contentType: 9,
            schema: [],
            name: "contributionGroup",
            metaData: {
                displayName: "Contribution Type",
                properties: {
                    order: 1
                }
            }
        },
        {
            columnId: 4,
            contentType: 9,
            schema: [],
            name: "groupName",
            metaData: {
                displayName: "Name",
                properties: {
                    order: 2
                }
            }
        },
        {
            columnId: 5,
            contentType: 8,
            schema: [],
            name: "percentageOfTotalRisk",
            metaData: {
                contentTypeForDisplay: "Standard0dp",
                displayName: "% Total Risk",
                properties: {
                    order: 3,
                    chartTooltipPropsFormatterType: "percent",
                    chartTooltipPropsValueFormat: "percent2dp"
                }
            }
        },
        {
            columnId: 6,
            contentType: 8,
            schema: [],
            name: "percentageOfFactorRisk",
            metaData: {
                contentTypeForDisplay: "Standard0dp",
                displayName: "% Factor Risk",
                properties: {
                    order: 4
                }
            }
        },
        {
            columnId: 7,
            contentType: 8,
            schema: [],
            name: "percentageOfSpecificRisk",
            metaData: {
                contentTypeForDisplay: "Standard0dp",
                displayName: "% Specific Risk",
                properties: {
                    order: 5,
                    chartTooltipPropsFormatterType: "percent",
                    chartTooltipPropsValueFormat: "percent2dp"
                }
            }
        },
        {
            columnId: 8,
            contentType: 8,
            schema: [],
            name: "percentageOfGross",
            metaData: {
                contentTypeForDisplay: "Standard0dp",
                displayName: "% Gross",
                properties: {
                    order: 6
                }
            }
        },
        {
            columnId: 9,
            contentType: 8,
            schema: [],
            name: "percentageOfNet",
            metaData: {
                contentTypeForDisplay: "Standard0dp",
                displayName: "% Net",
                properties: {
                    order: 7
                }
            }
        },
        {
            columnId: 10,
            contentType: 8,
            schema: [],
            name: "mctr",
            metaData: {
                contentTypeForDisplay: "Standard2dp",
                displayName: "MCTR",
                properties: {
                    order: 8
                }
            }
        },
        {
            columnId: 11,
            contentType: 8,
            schema: [],
            name: "mcfr",
            metaData: {
                contentTypeForDisplay: "Standard2dp",
                displayName: "MCFR",
                properties: {
                    order: 9
                }
            }
        },
        {
            columnId: 12,
            contentType: 8,
            schema: [],
            name: "longDeltaExposure",
            metaData: {
                contentTypeForDisplay: "DollarValue",
                displayName: "Long Delta Exposure",
                properties: {
                    order: 10
                }
            }
        },
        {
            columnId: 13,
            contentType: 8,
            schema: [],
            name: "shortDeltaExposure",
            metaData: {
                contentTypeForDisplay: "DollarValue",
                displayName: "Short Delta Exposure",
                properties: {
                    order: 11
                }
            }
        },
        {
            columnId: 14,
            contentType: 8,
            schema: [],
            name: "netDeltaExposure",
            metaData: {
                contentTypeForDisplay: "DollarValue",
                displayName: "Net Delta Exposure",
                properties: {
                    order: 12,
                    adapters: [
                        "@addPositiveNegativeFillToDatum(netDeltaExposure)",
                        "@addPropertyToDatum(stroke, var(--color__background--container))",
                        "@toData(mp(assoc('percentageOfTotalRiskAbsolute', compose(abs, prop('percentageOfTotalRisk')))))",
                        "@toData(mp(assoc('percentageOfSpecificRiskAbsolute', compose(abs, prop('percentageOfSpecificRisk')))))"
                    ],
                    stampComponentName: "Components.ChartStamp",
                    stampPropsTitle: "Risk Bubbles",
                    stampPropsModalContentComponentName: "Layouts.LayoutRiskContribution",
                    chartSeriesPropsIsAnimationActive: "@calc[not(#context(ungroupTickers))]",
                    chartZoomAndPanPropsEnabled: false,
                    chartType: "bubbleseries",
                    chartPropsChartType: "scatter",
                    chartLabelsPropsShow: false,
                    chartLabelsPropsValueKey: "netDeltaExposure",
                    chartLabelsPropsLabelKey: "percentageOfTotalRisk",
                    chartLabelsPropsFormatterType: "number",
                    chartLabelsPropsValueFormat: "number2dp",
                    chartLabelsPropsSortKey: "percentageOfTotalRiskAbsolute",
                    chartLabelsPropsMaxColliding: "@calc[#context(ungroupTickers) ? 1 : 2]",
                    chartLabelsPropsStrategies:
                        "@calc[#context(ungroupTickers) ? array('bottom', 'pivot-left-right', 'left', 'right') : array('pivot-left-right', 'bottom', 'left', 'right')]",
                    chartPropsMargin: {
                        top: 10,
                        bottom: 10,
                        left: -45
                    },
                    chartCartesianGridPropsShow: false,
                    chartXAxisPropsDataKey: "netDeltaExposure",
                    chartXAxisPropsName: "Net Exposure $",
                    chartXAxisPropsFormatterType: "bigValue",
                    chartXAxisPropsValueFormat: "$.0f",
                    chartXAxisPropsPadding: {
                        left: 40,
                        right: 40
                    },
                    chartXAxisPropsShow: true,
                    chartXAxisLabelPropsValue: "Net Exposure $",
                    chartXAxisLabelPropsPosition: "bottom",
                    chartXAxisLabelPropsOffset: -5,
                    chartXAxisLabelPropsFontSize: "10px",
                    chartYAxisLeftPropsDataKey: "contributionGroup",
                    chartYAxisLeftPropsType: "category",
                    chartYAxisLeftPropsScale: "auto",
                    chartYAxisLeftPropsAllowDuplicatedCategory: "false",
                    chartYAxisLeftPropsName: "Trading Range",
                    chartYAxisLeftPropsWidth: 65,
                    chartYAxisLeftPropsShow: true,
                    chartYAxisLeftPropsPadding: {
                        top: 15,
                        bottom: 15
                    },
                    chartYAxisLeftPropsTick: "null",
                    chartYAxisLeftLabelPropsValue: "12M Trading Range",
                    chartYAxisLeftLabelPropsPosition: "insideLeft",
                    chartYAxisLeftLabelPropsClassName: "anchor-middle",
                    chartYAxisLeftLabelPropsOffset: 55,
                    chartYAxisLeftLabelPropsFontSize: "10px",
                    chartZAxisPropsDataKey: "percentageOfSpecificRiskAbsolute",
                    chartZAxisPropsRange: "[80, 300]",
                    chartZAxisPropsShow: true,
                    chartReferenceLinePropsShow: true,
                    chartReferenceLinePropsList: [
                        {
                            x: 0,
                            ifOverflow: "extendDomain"
                        }
                    ],
                    chartTooltipPropsTitle: "#ref(groupName)",
                    chartTooltipPropsSortBy:
                        "contributionGroup,netDeltaExposure,valueAtRisk95,percentageOfTotalRisk",
                    chartTooltipPropsName: "Net Exposure $",
                    chartTooltipPropsFormatterType: "bigValue",
                    chartTooltipPropsValueFormat: "$.1f"
                }
            }
        },
        {
            columnId: 15,
            contentType: 8,
            schema: [],
            name: "grossDeltaExposure",
            metaData: {
                contentTypeForDisplay: "DollarValue",
                displayName: "Gross Delta Exposure",
                properties: {
                    order: 13
                }
            }
        },
        {
            columnId: 16,
            contentType: 8,
            schema: [],
            name: "valueAtRisk95",
            metaData: {
                contentTypeForDisplay: "Standard2dp",
                displayName: "VAR 95",
                properties: {
                    order: 14,
                    chartTooltipPropsName: "Value at Risk",
                    chartTooltipPropsFormatterType: "bigValue",
                    chartTooltipPropsValueFormat: "$.1f"
                }
            }
        },
        {
            columnId: 17,
            contentType: 8,
            schema: [],
            name: "contributionToPortfolioValueAtRisk95",
            metaData: {
                contentTypeForDisplay: "Standard2dp",
                displayName: "Contr to portf VAR 95",
                properties: {
                    order: 15
                }
            }
        },
        {
            columnId: 18,
            contentType: 8,
            schema: [],
            name: "absolutePercentageOfTotalRisk",
            metaData: {
                contentTypeForDisplay: "Standard2dp",
                displayName: "Absolute percentage of total risk",
                properties: {
                    order: 16
                }
            }
        }
    ] as Schema
};

export const ChartStampStory = () => <ChartStamp {...PROPS} />;
