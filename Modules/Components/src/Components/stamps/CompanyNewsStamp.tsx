import React, { ReactNode } from "react";

import { Flex, type FlexProps, Typography, LayoutGrid } from "PrimitiveComponents/index";

import { StampContainer } from "./Shared";

export type CompanyNewsStampProps = FlexProps & {
    title?: ReactNode;
    min?: number;
    max?: number;
    data?: string[];
};

export const CompanyNewsStamp = ({ title, data, ...rest }: CompanyNewsStampProps) => {
    console.log(`[e-log] [CompanyNewsStamp]`, { title, data, rest });
    return (
        <StampContainer
            disabled={!data}
            title={title}
            {...rest}
        >
            <Flex
                direction="column"
                w="100%"
                pv="x-large"
            >
                <LayoutGrid
                    columns={data.length > 0 ? 2 : 1}
                    w="100%"
                    gap
                >
                    {data.length > 0 ? (
                        data.map((news, index) => {
                            const key = `${news}-${index}`;
                            return (
                                <Typography.Value
                                    key={key}
                                    align="center"
                                    lineHeight="small"
                                    type="negative"
                                >
                                    <Flex
                                        w="100%"
                                        justify="center"
                                        items="center"
                                    >
                                        {news}
                                    </Flex>
                                </Typography.Value>
                            );
                        })
                    ) : (
                        <Flex
                            w="100%"
                            justify="center"
                            items="center"
                        >
                            N/A
                        </Flex>
                    )}
                </LayoutGrid>
            </Flex>
        </StampContainer>
    );
};
