import React, { ReactNode } from "react";

import { Flex, type FlexProps, Typography, CheckMark } from "PrimitiveComponents/index";
import { Chart } from "Charting/index";
import { isDefined } from "Utilities/index";
import type { Schema } from "Contracts/index";

import { StampContainer } from "./Shared";

const CHART_MAX_SIZE = 90;

export type EarningsChartStampProps = FlexProps & {
    title?: ReactNode;
    data?: {
        daysSinceString: string;
        earningsInflexionScore: number;
        trending: string;
        first: { data: {}[]; schema: Schema };
        second: { data: {}[]; schema: Schema };
        third: { data: {}[]; schema: Schema };
        fourth: { data: {}[]; schema: Schema };
    };
    schema?: Schema;
};

export const EarningsChartStamp = ({ title, data, schema, ...rest }: EarningsChartStampProps) => {
    const { daysSinceString, earningsInflexionScore, trending, first, second, third, fourth } =
        data ?? {};
    return (
        <StampContainer
            disabled={!data}
            title={title}
            {...rest}
        >
            {data && (
                <Flex
                    flex="1"
                    w="100%"
                    direction="column"
                    gap
                    pt
                >
                    <Flex
                        justify="center"
                        items="center"
                        gap="x-large"
                    >
                        {isDefined(daysSinceString) && (
                            <Flex title="Days Since">
                                <Typography.Text
                                    fontWeight="header"
                                    fontSize="x-large"
                                >
                                    {daysSinceString}
                                </Typography.Text>
                            </Flex>
                        )}

                        {isDefined(earningsInflexionScore) && earningsInflexionScore !== 0 && (
                            <Flex>
                                <CheckMark
                                    value={earningsInflexionScore > 0}
                                    type="uTurn"
                                    size={20}
                                    highlighted
                                    title={`Earnings Inflexion: ${earningsInflexionScore}`}
                                />
                            </Flex>
                        )}

                        {isDefined(trending) && (
                            <Flex>
                                <CheckMark
                                    value={trending === "With Trend"}
                                    type="chart"
                                    size={20}
                                    highlighted
                                    title={`Trending: ${trending}`}
                                />
                            </Flex>
                        )}
                    </Flex>
                    <Flex
                        flex="1"
                        gap="x-large"
                    >
                        <Flex
                            flex="1"
                            direction="column"
                            gap="x-small"
                            items="center"
                        >
                            <Flex justify="center">
                                <Typography.Text fontSize="x-small">EPS Change</Typography.Text>
                            </Flex>
                            <Flex
                                flex="1"
                                maxw={CHART_MAX_SIZE}
                                w="100%"
                            >
                                <Chart
                                    {...first}
                                    compactWidth={0}
                                    compactHeight={0}
                                />
                            </Flex>
                        </Flex>
                        <Flex
                            flex="1"
                            direction="column"
                            gap="x-small"
                            items="center"
                        >
                            <Flex justify="center">
                                <Typography.Text fontSize="x-small">DPS Change</Typography.Text>
                            </Flex>
                            <Flex
                                flex="1"
                                maxw={CHART_MAX_SIZE}
                                w="100%"
                            >
                                <Chart
                                    {...second}
                                    compactWidth={0}
                                    compactHeight={0}
                                />
                            </Flex>
                        </Flex>
                    </Flex>
                    <Flex
                        flex="1"
                        gap="x-large"
                    >
                        <Flex
                            flex="1"
                            direction="column"
                            gap="x-small"
                            items="center"
                        >
                            <Flex justify="center">
                                <Typography.Text fontSize="x-small">EPS Shock</Typography.Text>
                            </Flex>
                            <Flex
                                flex="1"
                                maxw={CHART_MAX_SIZE}
                                w="100%"
                            >
                                <Chart
                                    {...third}
                                    compactWidth={0}
                                    compactHeight={0}
                                />
                            </Flex>
                        </Flex>
                        <Flex
                            flex="1"
                            direction="column"
                            gap="x-small"
                            items="center"
                        >
                            <Flex justify="center">
                                <Typography.Text fontSize="x-small">
                                    EPS Revisions (3M)
                                </Typography.Text>
                            </Flex>
                            <Flex
                                flex="1"
                                maxw={CHART_MAX_SIZE}
                                w="100%"
                            >
                                <Chart
                                    {...fourth}
                                    compactWidth={0}
                                    compactHeight={0}
                                />
                            </Flex>
                        </Flex>
                    </Flex>
                </Flex>
            )}
        </StampContainer>
    );
};
