import React from "react";
import { Flex, ChangedValueBar, Change, ChangeProps } from "PrimitiveComponents/index";
import type { Schema } from "Contracts/index";
import { StampContainer, StampContainerProps, StampTickerWithLabel } from "./Shared";

export type TickersWithBarsStampProps = {
    title: string;
    tickerValueKey: string;
    tickerLabelKey: string;
    barValueKey: string;
    schema: Schema;
    data: Record<string, unknown>[];
} & Omit<StampContainerProps, "children">;

export const TickersWithBarsStamp = ({
    data,
    schema,
    title,
    tickerValueKey,
    tickerLabelKey,
    barValueKey,
    ...rest
}: TickersWithBarsStampProps) => (
    <StampContainer
        title={title}
        {...rest}
    >
        <Flex
            flex="1"
            direction="column"
            justify="space-evenly"
            gap="small"
        >
            {data.map(d => {
                const { properties: valueProperties } =
                    schema?.find(({ name }) => name === barValueKey)?.metaData ?? {};
                const value = d[barValueKey] as number;
                const absValue = Math.abs(value as number);
                const tickerValue = d[tickerValueKey] as string;

                return (
                    <Flex
                        key={tickerValue}
                        justify="space-around"
                        items="center"
                        gap="large"
                    >
                        <Flex
                            justify="center"
                            w="60%"
                        >
                            <StampTickerWithLabel bbgTicker={tickerValue} />
                        </Flex>

                        <Flex
                            w="40%"
                            justify="center"
                            items="center"
                            gap="medium"
                        >
                            <Flex
                                h={12}
                                flex="1"
                            >
                                <ChangedValueBar
                                    min={-absValue}
                                    max={absValue}
                                    value={value}
                                />
                            </Flex>
                            <Change
                                value={value}
                                {...(valueProperties as unknown as ChangeProps)}
                            />
                        </Flex>
                    </Flex>
                );
            })}
        </Flex>
    </StampContainer>
);
