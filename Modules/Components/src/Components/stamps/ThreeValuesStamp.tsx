import React, { ReactNode } from "react";

import { Flex } from "PrimitiveComponents/index";
import type { Schema } from "Contracts/index";

import { StampContainer, StampContainerProps, StampValueWithLabel } from "./Shared";

export type ThreeValuesStampProps = {
    title?: ReactNode;
    firstLabelKey?: string;
    firstValueKey?: string;
    secondLabelKey?: string;
    secondValueKey?: string;
    thirdLabelKey?: string;
    thirdValueKey?: string;
    schema: Schema;
    data: Record<string, unknown>;
} & Omit<StampContainerProps, "children">;

export const ThreeValuesStamp = ({
    data,
    schema,
    title,
    firstLabelKey,
    firstValue<PERSON>ey,
    secondLabel<PERSON>ey,
    secondValueKey,
    thirdLabelKey,
    thirdValueKey,
    ...rest
}: ThreeValuesStampProps) => {
    const { properties: firstValueProperties, displayName: firstDisplayName } =
        schema?.find(({ name }) => name === firstValueKey)?.metaData ?? {};
    const { properties: secondValueProperties, displayName: secondDisplayName } =
        schema?.find(({ name }) => name === secondValueKey)?.metaData ?? {};
    const { properties: thirdValueProperties, displayName: thirdDisplayName } =
        schema?.find(({ name }) => name === thirdValueKey)?.metaData ?? {};

    const firstLabel = (data[firstLabelKey] ?? firstDisplayName) as string;
    const firstValue = data[firstValueKey] as number;
    const secondLabel = (data[secondLabelKey] ?? secondDisplayName) as string;
    const secondValue = data[secondValueKey] as number;
    const thirdLabel = (data[thirdLabelKey] ?? thirdDisplayName) as string;
    const thirdValue = data[thirdValueKey] as number;

    return (
        <StampContainer
            title={title}
            {...rest}
        >
            <Flex
                flex="1"
                direction="column"
                justify="space-evenly"
                gap="small"
            >
                <StampValueWithLabel
                    value={firstValue}
                    label={firstLabel}
                    {...firstValueProperties}
                />
                <Flex
                    justify="space-evenly"
                    gap="large"
                >
                    <StampValueWithLabel
                        value={secondValue}
                        label={secondLabel}
                        {...secondValueProperties}
                    />
                    <StampValueWithLabel
                        value={thirdValue}
                        label={thirdLabel}
                        {...thirdValueProperties}
                    />
                </Flex>
            </Flex>
        </StampContainer>
    );
};
