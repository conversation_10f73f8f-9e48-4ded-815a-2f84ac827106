import React, { ReactNode } from "react";

import { Flex, type FlexProps, Typography, Dial, CheckMark } from "PrimitiveComponents/index";
import { Chart } from "Charting/index";
import type { Schema } from "Contracts/index";
import { SchemaValue } from "Schema/index";
import { AppIcon } from "Theming/index";
import { isNumberType, useColourScale } from "Utilities/index";

import { StampContainer } from "./Shared";

export type PositioningData = {
    inCrowdedBasket?: string;
};

export type BrokerChangesData = {
    countUpgrades?: number;
    countDowngrades?: number;
};

export type DivergenceData = {
    divergence?: number;
};

export type ScoreData = {
    investorCrowdedScore?: number;
    barColourChange?: number;
    positioningCheck?: number;
};

type PositioningAndSentimentStampProps = FlexProps & {
    title?: ReactNode;
    format?: string;
    data?: PositioningData;
    schema?: Schema;
    divergenceData?: DivergenceData;
    scoreSchema?: Schema;
    scoreData?: ScoreData[];
    divergenceSchema?: Schema;
    brokerChangesData?: BrokerChangesData;
    brokerChangesSchema?: Schema;
};

const CHART_MAX_SIZE = 90;

export const PositioningAndSentimentStamp = ({
    title,
    data,
    schema,
    divergenceData,
    scoreData,
    scoreSchema,
    brokerChangesData,
    brokerChangesSchema,
    ...rest
}: PositioningAndSentimentStampProps) => {
    const hasData = data;

    const { investorCrowdedScore } = scoreData?.[0] ?? {};
    const { divergence } = divergenceData ?? {};

    const divergenceColor = useColourScale(divergence, { highlighted: true })?.toString() ?? "";

    const renderBrokerChanges = (prop: keyof BrokerChangesData, checkMarkValue: boolean) => (
        <SchemaValue
            key={prop}
            name={prop}
            data={brokerChangesData}
            schema={brokerChangesSchema}
        >
            {(children, displayName, column, schema, dataValue) =>
                isNumberType(dataValue) &&
                dataValue !== 0 && (
                    <Flex
                        gap
                        items="center"
                    >
                        <CheckMark
                            value={checkMarkValue}
                            type="arrowUpDown"
                            size={15}
                            highlighted
                        />
                        <Typography.Text
                            fontWeight="header"
                            highlightedInversed
                        >
                            {children}
                        </Typography.Text>
                        <span>{displayName}</span>
                    </Flex>
                )
            }
        </SchemaValue>
    );

    return (
        <StampContainer
            disabled={!hasData}
            title={title}
            {...rest}
        >
            {hasData && (
                <Flex
                    flex="1"
                    direction="column"
                    justify="space-evenly"
                >
                    <Flex>
                        <Flex
                            flex="1"
                            items="center"
                            justify="center"
                            gap="small"
                            direction="column"
                        >
                            {divergence && (
                                <Flex
                                    items="center"
                                    justify="center"
                                    gap
                                >
                                    <AppIcon
                                        type="flipVertical"
                                        color={divergenceColor}
                                        title={divergence?.toString()}
                                    />
                                    <span>Divergence</span>
                                </Flex>
                            )}

                            <SchemaValue
                                name="inCrowdedBasket"
                                data={data}
                                schema={schema}
                            >
                                {children => (
                                    <Flex>
                                        <Flex
                                            items="center"
                                            justify="center"
                                            gap="small"
                                        >
                                            {children === "Y" && (
                                                <Typography.Value type="negative">
                                                    <Flex h={20}>
                                                        <AppIcon
                                                            title="In Crowded Basket"
                                                            type="basket"
                                                            responsive
                                                        />
                                                    </Flex>
                                                </Typography.Value>
                                            )}
                                        </Flex>
                                    </Flex>
                                )}
                            </SchemaValue>
                        </Flex>
                        <Flex
                            flex="1"
                            direction="column"
                            justify="space-evenly"
                        >
                            <div>{renderBrokerChanges("countUpgrades", true)}</div>
                            <div>{renderBrokerChanges("countDowngrades", false)}</div>
                        </Flex>
                    </Flex>
                    <Flex
                        direction="column"
                        gap="small"
                    >
                        <Flex
                            justify="space-evenly"
                            gap
                        >
                            <Flex
                                flex="1"
                                direction="column"
                            >
                                <Flex justify="center">Investors</Flex>
                            </Flex>
                            <Flex
                                flex="1"
                                items="center"
                                justify="center"
                                gap
                            >
                                <Flex justify="center">Analysts</Flex>
                            </Flex>
                        </Flex>
                        <Flex
                            justify="space-evenly"
                            gap
                        >
                            <Flex
                                flex="1"
                                justify="center"
                                items="center"
                                w="100%"
                            >
                                <Flex
                                    h={CHART_MAX_SIZE}
                                    maxw={CHART_MAX_SIZE}
                                    w="100%"
                                >
                                    <Dial
                                        value={investorCrowdedScore}
                                        defaultValue="N/A"
                                        min={0}
                                        max={100}
                                        maxSize={CHART_MAX_SIZE}
                                        scale="actual"
                                        colorVariant={
                                            isNumberType(investorCrowdedScore) &&
                                            investorCrowdedScore < 0
                                                ? "negative"
                                                : false
                                        }
                                    />
                                </Flex>
                            </Flex>
                            <Flex
                                flex="1"
                                items="center"
                                direction="column"
                            >
                                <Flex
                                    h={CHART_MAX_SIZE}
                                    maxw={CHART_MAX_SIZE}
                                    w="100%"
                                >
                                    <Chart
                                        data={scoreData}
                                        schema={scoreSchema}
                                        compactWidth={0}
                                        compactHeight={0}
                                    />
                                </Flex>
                            </Flex>
                        </Flex>
                    </Flex>
                </Flex>
            )}
        </StampContainer>
    );
};
