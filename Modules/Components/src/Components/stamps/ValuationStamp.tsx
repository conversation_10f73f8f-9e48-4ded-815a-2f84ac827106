import React, { ReactNode } from "react";
import { clamp } from "lodash";

import { Flex, type FlexProps, Typography, Dial } from "PrimitiveComponents/index";
import { isNumberType, useColourScale, getFormattedValue } from "Utilities/index";

import { <PERSON>amp<PERSON>ontainer, StampValueWithLabel } from "./Shared";

type ValuationData = {
    zScore2Y: number;
    zScore2Y1mChg: number;
};

type DialBlockProps = {
    title: ReactNode;
    min: number;
    max: number;
    data?: ValuationData;
};
const DialBlock = ({ title, min, max, data }: DialBlockProps) => {
    const { zScore2Y1mChg, zScore2Y } = data ?? {};
    const value = isNumberType(zScore2Y) ? clamp(zScore2Y, min, max) : undefined;
    const color =
        useColourScale(value, { min: max, max: min, highlighted: true })?.toString() ?? "";
    return (
        <Flex
            flex="1"
            direction="column"
            items="center"
            justify="center"
            pt="large"
        >
            <Flex>
                <Typography.Text
                    fontWeight="header"
                    fontSize="large"
                >
                    {title}
                </Typography.Text>
            </Flex>
            <Dial
                value={value}
                min={min}
                max={max}
                maxSize={90}
                type="gauge"
                scale="actual"
                defaultValue="N/A"
                fontSize="var(--font__size--x-small)"
                valueLabel={
                    isNumberType(value) ? (
                        <span style={{ color }}>
                            {getFormattedValue(value, {
                                showPositiveIndicator: false,
                                valueFormat: "number1dp",
                                suffix: " sd"
                            })}
                        </span>
                    ) : undefined
                }
            />

            <StampValueWithLabel
                direction="column-reverse"
                label="T-1m"
                value={zScore2Y1mChg}
                defaultValue="N/A"
                valueFontSize="medium"
                gap="xx-small"
                valueFormat="number1dp"
                colorValue={0}
                suffix=" sd"
            />
        </Flex>
    );
};

export type ValuationStampProps = FlexProps & {
    title?: ReactNode;
    min?: number;
    max?: number;
    peRelData?: ValuationData;
    peSectorData?: ValuationData;
};

export const ValuationStamp = ({
    title,
    peRelData,
    peSectorData,
    min = -2,
    max = 2,
    ...rest
}: ValuationStampProps) => {
    const hasData = peRelData || peSectorData;

    return (
        <StampContainer
            disabled={!hasData}
            title={title}
            {...rest}
        >
            <Flex
                gap="large"
                w="100%"
                pb="large"
                flex="1"
            >
                <DialBlock
                    title="P/E Rel Mrk"
                    min={min}
                    max={max}
                    data={peRelData}
                />

                <DialBlock
                    title="P/E Rel Sec"
                    min={min}
                    max={max}
                    data={peSectorData}
                />
            </Flex>
        </StampContainer>
    );
};
