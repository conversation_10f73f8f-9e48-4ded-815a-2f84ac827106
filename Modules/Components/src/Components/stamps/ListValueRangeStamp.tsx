import React, { ReactNode } from "react";

import { Flex, type FlexProps, ValueRange } from "PrimitiveComponents/index";

import { StampContainer, StampValueWithLabel } from "./Shared";

export type ListValueRangeStampData = {
    label?: string;
    value?: ReactNode;
    list: {
        current: number;
        low: number;
        high: number;
        sd: number;
        id?: number;
    }[];
};

export type ListValueRangeStampProps = FlexProps & {
    title?: ReactNode;
    data?: ListValueRangeStampData;
};

export const ListValueRangeStamp = ({ title, data, ...rest }: ListValueRangeStampProps) => {
    const { value, label = "", list } = data ?? {};
    const hasData =
        list?.map(({ current, low, high, sd }) => current || low || high || sd).filter(Boolean)
            .length > 0;
    return (
        <StampContainer
            disabled={!hasData}
            title={title}
            {...rest}
        >
            {hasData && (
                <Flex
                    flex="1"
                    direction="column"
                    justify="space-evenly"
                >
                    {value && (
                        <StampValueWithLabel
                            label={label}
                            value={value}
                            direction="column-reverse"
                            formatterType="number"
                            valueFormat="number2dp"
                            defaultValue="N/A"
                            gap="small"
                            colorValue={0}
                            showPositiveIndicator={false}
                        />
                    )}

                    <Flex
                        gap="large"
                        direction="column"
                    >
                        {list?.map(({ current, low, high, sd, id }, i) => (
                            <Flex key={id ?? i}>
                                <ValueRange
                                    current={current}
                                    low={low}
                                    high={high}
                                    sd={sd}
                                />
                            </Flex>
                        ))}
                    </Flex>
                </Flex>
            )}
        </StampContainer>
    );
};
