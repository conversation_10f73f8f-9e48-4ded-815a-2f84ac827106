import React, { ReactNode } from "react";

import type { ApiGatewayService } from "Contracts/index";
import { Flex, type FlexProps, Number, Typography } from "PrimitiveComponents/index";

import { StampMainValue, StampContainer, StampValueWithLabel } from "./Shared";

export type EarningsStampProps = FlexProps & {
    title?: ReactNode;
    format?: string;
    data?: ApiGatewayService.DecisionSupportPage.EarningsResponse;
};

export const EarningsStamp = ({
    title = "Earnings",
    data,
    format = "+.0f",
    ...rest
}: EarningsStampProps) => {
    const { daysToNextEarnings, optionImpliedEarningsMove, averageMoveOnNumbers } = data ?? {};

    return (
        <StampContainer
            title={title}
            {...rest}
        >
            <Typography.Text align="center">
                <Flex
                    flex="1"
                    direction="column"
                    justify="space-evenly"
                    gap="small"
                >
                    <StampMainValue>
                        <Number
                            value={daysToNextEarnings}
                            f={format}
                        />
                    </StampMainValue>

                    <StampValueWithLabel
                        label="Normally Moves"
                        value={averageMoveOnNumbers}
                        direction="column-reverse"
                        valueFontSize="medium"
                        defaultValue="N/A"
                        labelMuted={false}
                    />

                    <StampValueWithLabel
                        label="Option Implied Move"
                        value={optionImpliedEarningsMove}
                        direction="column-reverse"
                        valueFontSize="medium"
                        defaultValue="N/A"
                        labelMuted={false}
                    />
                </Flex>
            </Typography.Text>
        </StampContainer>
    );
};
