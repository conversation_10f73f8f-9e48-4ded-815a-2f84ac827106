import React from "react";

import { LayoutGrid, BreakpointPropPairs } from "PrimitiveComponents/index";
import { PMBio, PMBioProps } from "./PMBio";

const BREAKPOINTS: BreakpointPropPairs<typeof LayoutGrid> = [
    [700, { gridTemplateColumns: "minmax(20em, 1fr) 3fr" }]
];

export const PMBioOverview = (props: PMBioProps) => (
    <LayoutGrid
        columns={1}
        gap="xxx-large"
        breakpointPropsPairs={BREAKPOINTS}
        alignItems="flex-start"
    >
        <PMBio
            descriptionLines={8}
            {...props}
        />
    </LayoutGrid>
);
