import React, { ReactNode } from "react";
import styled from "styled-components";

import {
    LayoutHorizontalGroup,
    LayoutVertical,
    LayoutContainer,
    SkeletonImage,
    MetaData,
    MetaDataItem,
    Clamp,
    SkeletonText
} from "PrimitiveComponents/index";
import type { Schema } from "Contracts/index";
import { SchemaValue } from "Schema/index";

import { PMBiography } from "./types";
import { PMBioSummaryTooltip } from "./PMBioSummaryTooltip";

const Avatar = styled(SkeletonImage)`
    height: 100px;
    width: 100px;
`;

export type PMBioProps = {
    biography?: PMBiography;
    schema?: Schema;
    descriptionLines?: number;
    children?: ReactNode;
};

export const PMBio = ({ biography, schema, descriptionLines = 4, children }: PMBioProps) => (
    <>
        <LayoutVertical spacing="x-large">
            <LayoutHorizontalGroup
                spacing="x-large"
                alignItems="center"
            >
                <Avatar
                    type="avatar"
                    imageUrl={biography?.profileImageUrl}
                />
                <LayoutVertical spacing="medium">
                    <MetaData type="title">
                        <SchemaValue
                            name="name"
                            schema={schema}
                            data={biography}
                            defaultValue={<SkeletonText />}
                        >
                            {value => (
                                <MetaDataItem
                                    title={
                                        <SchemaValue
                                            name="role"
                                            schema={schema}
                                            data={biography}
                                            defaultValue={<SkeletonText />}
                                        />
                                    }
                                >
                                    {value}
                                </MetaDataItem>
                            )}
                        </SchemaValue>
                    </MetaData>
                    <MetaData type="secondary">
                        <SchemaValue
                            name="fundGroups"
                            schema={schema}
                            data={biography}
                            defaultValue={<SkeletonText />}
                        >
                            {(value, displayName) => (
                                <MetaDataItem title={displayName}>
                                    {Array.isArray(value) ? value.join(", ") : value}
                                </MetaDataItem>
                            )}
                        </SchemaValue>
                    </MetaData>
                </LayoutVertical>
            </LayoutHorizontalGroup>
            {children}
        </LayoutVertical>

        <LayoutContainer flex={false}>
            <Clamp
                lines={descriptionLines}
                canExpand={false}
                clamped={
                    <PMBioSummaryTooltip
                        biography={biography}
                        schema={schema}
                        descriptionLines={descriptionLines}
                    />
                }
            >
                <SchemaValue
                    name="description"
                    schema={schema}
                    data={biography}
                    defaultValue={<SkeletonText rows={descriptionLines} />}
                >
                    {children => <>{children}</>}
                </SchemaValue>
            </Clamp>
        </LayoutContainer>
    </>
);
