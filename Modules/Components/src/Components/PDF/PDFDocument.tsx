import React, { useState } from "react";
import { pdfjs, Document, Page } from "react-pdf";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import type { PDFDocumentProxy } from "pdfjs-dist";

import { LayoutContainer, LayoutHeader, withScrollable } from "PrimitiveComponents/index";
import { Zoom } from "./Zoom";

type PDFProps = {
    src: string;
};

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
    "pdfjs-dist/build/pdf.worker.min.js",
    import.meta.url
).toString();

const options = {
    cMapUrl: "/cmaps/",
    standardFontDataUrl: "/standard_fonts/"
};

const DocumentContainer = withScrollable(LayoutContainer);

export const PDFDocument = ({ src }: PDFProps) => {
    const [numPages, setNumPages] = useState<number>();
    const [scale, setScale] = useState(1);

    const onDocumentLoadSuccess = ({ numPages: nextNumPages }: PDFDocumentProxy) => {
        setNumPages(nextNumPages);
    };

    return (
        <LayoutContainer
            flex
            background="container-level-2"
        >
            <LayoutHeader style={{ marginBottom: 0 }}>
                <Zoom
                    value={scale}
                    onChange={setScale}
                    label="Zoom"
                    labelOrientation="left-inline"
                />
            </LayoutHeader>

            <DocumentContainer
                flex
                scrollPadding="none"
            >
                <Document
                    file={src}
                    onLoadSuccess={onDocumentLoadSuccess}
                    options={options}
                >
                    {Array.from(new Array(numPages), (el, index) => (
                        <Page
                            scale={scale}
                            key={`page_${index + 1}`}
                            pageNumber={index + 1}
                        />
                    ))}
                </Document>
            </DocumentContainer>
        </LayoutContainer>
    );
};
