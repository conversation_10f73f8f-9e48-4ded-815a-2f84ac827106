import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import { ThemeIterator, LayoutVertical, LayoutHorizontal, LayoutContainer, ThemeContainer, Typography } from "PrimitiveComponents/index";

import { ChecklistCompletenessIndicator } from "../ChecklistCompletenessIndicator";

<Meta
    title="Components/ChecklistCompletenessIndicator"
    component={ChecklistCompletenessIndicator}
/>

export const Template = args => (
    <LayoutVertical spacing="large">
        <ThemeIterator>
            {() => (
                <LayoutHorizontal spacing="large">
                    <LayoutVertical spacing="xxx-large">
                        <ThemeContainer>
                            <ChecklistCompletenessIndicator status="PassingValidation" />
                        </ThemeContainer>
                        <ThemeContainer>
                            <ChecklistCompletenessIndicator status="Incomplete" />
                        </ThemeContainer>
                        <ThemeContainer>
                            <ChecklistCompletenessIndicator {...args} />
                        </ThemeContainer>
                    </LayoutVertical>
                  </LayoutHorizontal>
            )}
        </ThemeIterator>
    </LayoutVertical>
);

# ChecklistCompletenessIndicator

<Description of={ChecklistCompletenessIndicator} />

<Canvas>
    <Story
        name="ChecklistCompletenessIndicator"
        args={{
            status: "some text"
        }}        
        argTypes={{
            status: {
                control: {
                    type: "text"
                }
            }
        }}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes
    of={ChecklistCompletenessIndicator}
    sort="requiredFirst"
/>
