import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import { ThemeIterator, LayoutVertical, ThemeContainer } from "PrimitiveComponents/index";

import { Avatar } from "../Avatar";

<Meta
    title="Components/Avatar"
    component={Avatar}
/>

import { avatarImageUser, avatarImageImpersonatedUser } from "./avatar.fixture";

export const Template = args => (
    <LayoutVertical spacing="large">
        <ThemeIterator>
            {() => (
                <ThemeContainer>
                    <div style={{ minHeight: "8em", minWidth: "8em", display: "flex", flexDirection: "column" }}>
                        <Avatar
                            {...args}
                            user={args.hasUserDetails && {
                                userId: 1,
                                name: "Test User",
                                initials: "TU",
                                avatarUrl: args.hasAvatarImage ? avatarImageUser : undefined,
                                impersonatedUser: args.impersonate && {
                                    userId: 2,
                                    name: "Impersonate Use 2r",
                                    initials: "IU",
                                    avatarUrl: args.hasAvatarImage ? avatarImageImpersonatedUser : undefined,
                                }
                            }}
                        />
                    </div>
                </ThemeContainer>
            )}
        </ThemeIterator>
    </LayoutVertical>
);

# Avatar

<Description of={Avatar} />

<Canvas>
    <Story
        name="Avatar"
        args={{
            hasAvatarImage: true,
            hasUserDetails: true,
            impersonate: false
        }}
        argTypes={{
            hasAvatarImage: {
                control: {
                    type: "boolean"
                }
            },
            hasUserDetails: {
                control: {
                    type: "boolean"
                }
            },
            impersonate: {
                control: {
                    type: "boolean"
                }
            },
        }}
        parameters={{
            controls: {
                disable: false,
                exclude: ["user"]
            }
        }}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes
    of={Avatar}
    sort="requiredFirst"
/>
