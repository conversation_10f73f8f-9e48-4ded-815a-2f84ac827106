import React from "react";

import { LayoutContainer } from "PrimitiveComponents/index";

import { ComponentLayout, type LayoutComponent } from "../Components";
import { Overlay } from "../Overlay";

type CockpitProps = {
    item?: LayoutComponent | LayoutComponent[];
};

export const Cockpit = ({ item }: CockpitProps) => (
    <Overlay
        size="small"
        indent="medium"
        filterPadding="small"
    >
        <LayoutContainer
            paddingV="medium"
            paddingH="large"
        >
            <ComponentLayout
                item={item}
                size="small"
            />
        </LayoutContainer>
    </Overlay>
);
