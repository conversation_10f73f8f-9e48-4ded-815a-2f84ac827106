import React from "react";
import { AppIcon } from "Theming/index";
import styled from "styled-components";

type Props = {
    value?: "Single" | "Pair/Group";
};

const IconContainer = styled.div`
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
`;

export const GamePlanType = ({ value }: Props) =>
    value !== "Pair/Group" ? null : (
        <IconContainer>
            <AppIcon type="link" />
        </IconContainer>
    );
