import type { ComponentsConfigFactory } from "../types";

export const TradeTicket: ComponentsConfigFactory = {
    name: "Trade Ticket",
    factory: () => ({
        componentName: "PrimitiveComponents.IconButton",
        componentProps: {
            icon: "ticket",
            responsive: true
        },
        componentDefaults: {
            label: "Trade Ticket",
            action: {
                actionType: "Window.Open",
                label: "Trade Ticket",
                actionParams: {
                    containerProps: {
                        id: "trade-impact",
                        target: "TradeTicket"
                    },
                    type: "url",
                    url: "/v2?traderId=#ref(traderId)&fasttrade=[t:#ref(bbgTicker)]#/trade-ticket"
                }
            }
        }
    })
};
