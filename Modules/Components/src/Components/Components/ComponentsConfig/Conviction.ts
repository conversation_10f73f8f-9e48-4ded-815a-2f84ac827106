import type { ComponentsConfigFactory } from "../types";

export const Conviction: ComponentsConfigFactory = {
    name: "Conviction",
    factory: () => ({
        componentName: "ConnectedComponents.ConvictionStampViewServer",
        componentProps: {
            stampType: "simple"
        },
        componentDefaults: {
            label: "Conviction",
            hints: {
                widthSpan: 1,
                heightSpan: 1,
                responsive: true
            }
        }
    })
};
