import type { ComponentsConfigFactory } from "../types";

export const PositionPNLAndStats: ComponentsConfigFactory = {
    name: "Position PNL and Stats",
    factory: () => ({
        componentName: "PrimitiveComponents.IconButton",
        componentProps: {
            icon: "chart",
            responsive: true
        },
        componentDefaults: {
            label: "Position PNL and Stats",
            action: {
                actionType: "Window.Open",
                label: "Position PNL and Stats",
                actionParams: {
                    componentName: "Layouts.SectionPositionPNLAndStats",
                    containerProps: {
                        width: "95vw",
                        title: "Position PNL and Stats",
                        id: "Layouts.SectionPositionPNLAndStats",
                        hasPadding: false
                    }
                }
            }
        }
    })
};
