import type { ComponentsConfigFactory } from "../types";

export const CorrelationsExtended: ComponentsConfigFactory = {
    name: "Correlations (Extended)",
    factory: () => ({
        componentName: "ConnectedComponents.CorrelationsExtendedViewServer",
        componentProps: {
            stampType: "simple"
        },
        componentDefaults: {
            label: "Correlations",
            hints: {
                widthSpan: 3,
                heightSpan: 3,
                responsive: true
            }
        }
    })
};
