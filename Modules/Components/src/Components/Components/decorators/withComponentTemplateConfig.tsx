import React, { type ComponentType, useCallback, useMemo, useState } from "react";
import styled from "styled-components";
import { isEqual } from "lodash";

import {
    withPopover,
    ContextMenu,
    MenuItem,
    Flex,
    Typography,
    Button,
    HOC,
    withDeferred
} from "PrimitiveComponents/index";
import { SchemaForm } from "Schema/index";
import { isComponentFactorySpec } from "Contracts/index";
import { AppIcon } from "Theming/index";

import { useConfig } from "../../../decorators";

import type { ComponentTemplateType } from "../ComponentTemplate/types";
import { ComponentTemplateSchema } from "../ComponentTemplate/ComponentTemplateSchema";

const ConfigContainer = styled(ContextMenu)`
    position: relative;
`;

const ConfigOption = styled(withPopover(styled.div``))`
    position: absolute;
    top: 0;
    right: 0;
`;

const DeferredButton = withDeferred(<PERSON>ton, 1000);

type ComponentTemplateConfigProps = {};

export const withComponentTemplateConfig = <P,>(Component: ComponentType<P>) => {
    const WrappedComponent = (props: P & ComponentTemplateConfigProps) => {
        const { updateConfig, setConfig, template, config } = useConfig<ComponentTemplateType>();
        const [configVisible, setConfigVisible] = useState(false);

        const title = config?.name || "";
        const hasTemplate = typeof template === "object" && template !== null;
        const hasTemplateChanged = useMemo(
            () => (hasTemplate ? !isEqual(template, config) : false),
            [config, hasTemplate, template]
        );

        const componentConfig = useMemo(() => {
            const schemaConfig = updateConfig && ComponentTemplateSchema;

            if (schemaConfig) {
                // Extract the top level json schema title
                return {
                    title: `${title} Template Config`,
                    config: schemaConfig
                };
            }

            return undefined;
        }, [title, updateConfig]);

        const updateComponentConfig = useCallback(
            (update: ComponentTemplateType) => {
                setConfig?.(update);
            },
            [setConfig]
        );

        const contextMenuItems = useMemo(
            () =>
                componentConfig &&
                [
                    !hasTemplate && (
                        <MenuItem
                            key="config"
                            icon="template"
                            onSelect={() => setConfigVisible(true)}
                        >
                            Configure {title && `'${title}'`} template…
                        </MenuItem>
                    ),
                    hasTemplate && hasTemplateChanged && config?.template?.type === "layout" && (
                        <MenuItem
                            key="config"
                            icon="template"
                            onSelect={() => updateComponentConfig(undefined)}
                            confirmMessage={
                                <>
                                    <p>Are you sure you want to reset this template?</p>
                                    <p>
                                        <strong>You will loose all your changes.</strong>
                                    </p>
                                </>
                            }
                        >
                            Reset {title && `'${title}'`} template…
                        </MenuItem>
                    )
                ].filter(item => item),
            [
                componentConfig,
                config?.template?.type,
                hasTemplate,
                hasTemplateChanged,
                title,
                updateComponentConfig
            ]
        );

        if (componentConfig) {
            const hasConfig =
                (config?.template?.type === "component" &&
                    (isComponentFactorySpec(config.template.component)
                        ? config.template.component.componentType
                        : config.template.component.component)) ||
                (config?.template?.type === "layout" && config.template.components?.length > 0);

            return (
                <ConfigContainer menuItems={contextMenuItems}>
                    <div>
                        <ConfigOption
                            popoverTitle={componentConfig.title}
                            popoverContent={
                                <SchemaForm
                                    value={config}
                                    onChange={updateComponentConfig}
                                    jsonSchema={componentConfig.config}
                                    autoSave
                                />
                            }
                            popoverVisible={configVisible}
                            popoverAutoScrollIntoView
                            onHide={() => setConfigVisible(false)}
                        />
                    </div>

                    {hasConfig ? (
                        <Component {...props} />
                    ) : (
                        <Flex
                            minHeight="5em"
                            justifyContent="center"
                            alignItems="center"
                        >
                            <DeferredButton
                                type="ghost"
                                size="x-large"
                                onClick={() => setConfigVisible(true)}
                            >
                                <Typography.Text
                                    fontSize="inherit"
                                    fontWeight="main"
                                    muted
                                >
                                    <Flex
                                        gap="large"
                                        alignItems="center"
                                    >
                                        <AppIcon type="template" />
                                        Setup new Template
                                    </Flex>
                                </Typography.Text>
                            </DeferredButton>
                        </Flex>
                    )}
                </ConfigContainer>
            );
        }

        return <Component {...props} />;
    };

    return HOC("withComponentTemplateConfig", WrappedComponent, Component);
};
