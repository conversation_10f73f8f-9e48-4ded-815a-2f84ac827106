import type { LayoutComponent } from "../../../types";

const fixture: LayoutComponent[] = [
    {
        componentId: "1",
        component: "PrimitiveComponents.SkeletonText",
        label: "Test CP",
        componentProps: {
            rows: 6
        }
    },
    {
        componentId: "2",
        component: "PrimitiveComponents.SkeletonText",
        componentProps: {
            rows: 6
        },
        label: "Test 2",
        children: {
            items: [
                {
                    componentId: "2.1",
                    component: "PrimitiveComponents.SkeletonText",
                    componentProps: {
                        rows: 6
                    },
                    label: "Test 2.1",
                    children: {
                        items: [
                            {
                                componentId: "2.1.1",
                                component: "PrimitiveComponents.SkeletonText",
                                componentProps: {
                                    rows: 6
                                },
                                label: "Test 2.1.1"
                            }
                        ]
                    }
                },
                {
                    componentId: "2.2",
                    component: "PrimitiveComponents.SkeletonText",
                    componentProps: {
                        rows: 6
                    },
                    label: "Test 2.2",
                    children: {
                        items: [
                            {
                                componentId: "2.2.1",
                                component: "PrimitiveComponents.SkeletonText",
                                componentProps: {
                                    rows: 6
                                },
                                label: "Test 2.2.1"
                            }
                        ]
                    }
                }
            ]
        }
    },
    {
        componentId: "3",
        component: "PrimitiveComponents.SkeletonText",
        componentProps: {
            rows: 6
        },
        label: "Test 3"
    },
    {
        componentId: "4",
        component: "PrimitiveComponents.SkeletonText",
        componentProps: {
            rows: 6
        },
        label: "Test 4",
        children: {
            items: [
                {
                    componentId: "4.1",
                    component: "PrimitiveComponents.SkeletonText",
                    componentProps: {
                        rows: 6
                    },
                    label: "Test 4.1"
                }
            ]
        }
    }
];

export default fixture;
