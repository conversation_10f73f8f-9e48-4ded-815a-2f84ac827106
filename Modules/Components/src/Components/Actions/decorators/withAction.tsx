import React, { ComponentType, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react";

import { HOC } from "PrimitiveComponents/index";

import { withActionFactory } from "./withActionFactory";

type ActonProps = {
    onClick?: MouseEventHandler;
};

export const withAction = <P extends ActonProps>(Component: ComponentType<P>) => {
    const WrappedComponent = withActionFactory<P>(({ action, actionApi: { invoke }, ...rest }) => {
        if (!action || action?.visible === false) {
            return <Component {...(rest as P)} />;
        }

        return (
            <Component
                {...(rest as P)}
                onClick={e => {
                    e.preventDefault();
                    e.stopPropagation();
                    invoke();
                }}
            />
        );
    });

    return HOC("withAction", WrappedComponent, Component);
};
