import React from "react";

import {
    LayoutVertical,
    MetaData,
    MetaDataItem,
    LayoutColumns,
    LayoutHorizontal,
    LayoutContainer,
    // SimpleTable,
    Typography,
    ValuePair
} from "PrimitiveComponents/index";
import { SchemaValue } from "Schema/index";

import type { PMStrategyDescriptionProps } from "../PMStrategyDescription";

// Temporarily disabled because the backend is being removed. It can be enabled again once the backend is back up. (TPT-1004)
// const YTDGrid = ({ strategy, schema }: PMStrategyDescriptionProps) => (
//     <LayoutContainer>
//         <LayoutHorizontal>
//             <SimpleTable>
//                 <thead>
//                     <tr>
//                         <td aria-label="Description" />
//                         <td>Objective</td>
//                         <td>YTD</td>
//                     </tr>
//                 </thead>
//                 <tbody>
//                     <tr>
//                         <td>Return</td>
//                         <td>
//                             <SchemaValue
//                                 name="objectiveReturn"
//                                 schema={schema}
//                                 data={strategy}
//                             />
//                         </td>
//                         <td>
//                             <SchemaValue
//                                 name="ytdReturn"
//                                 schema={schema}
//                                 data={strategy}
//                             />
//                         </td>
//                     </tr>
//                     <tr>
//                         <td>Vol Range</td>
//                         <td>
//                             <SchemaValue
//                                 name="objectiveVolatility"
//                                 schema={schema}
//                                 data={strategy}
//                             />
//                         </td>
//                         <td>
//                             <SchemaValue
//                                 name="ytdVolatility"
//                                 schema={schema}
//                                 data={strategy}
//                             />
//                         </td>
//                     </tr>
//                     <tr>
//                         <td>Sharpe</td>
//                         <td>
//                             <SchemaValue
//                                 name="objectiveSharpeRatio"
//                                 schema={schema}
//                                 data={strategy}
//                             />
//                         </td>
//                         <td>
//                             <SchemaValue
//                                 name="ytdSharpeRatio"
//                                 schema={schema}
//                                 data={strategy}
//                             />
//                         </td>
//                     </tr>
//                     <tr>
//                         <td>Capital Deployed</td>
//                         <td>
//                             <SchemaValue
//                                 name="objectiveCapitalDeployed"
//                                 schema={schema}
//                                 data={strategy}
//                             />
//                         </td>
//                         <td>
//                             <SchemaValue
//                                 name="ytdCapitalDeployed"
//                                 schema={schema}
//                                 data={strategy}
//                             />
//                         </td>
//                     </tr>
//                 </tbody>
//             </SimpleTable>
//         </LayoutHorizontal>
//     </LayoutContainer>
// );

export const LayoutFull = ({ strategy, schema }: PMStrategyDescriptionProps) => (
    <LayoutColumns
        gap="xxx-large"
        containerBreakpointMin="small-auto"
    >
        <LayoutContainer flexBasis="30%">
            <LayoutVertical spacing="xx-large">
                <MetaData type="primary">
                    <SchemaValue
                        name="capitalAllocation"
                        schema={schema}
                        data={strategy}
                    >
                        {(children, displayName) => (
                            <MetaDataItem title={displayName}>{children}</MetaDataItem>
                        )}
                    </SchemaValue>
                </MetaData>
                <MetaData type="primary">
                    <SchemaValue
                        name="strategyType"
                        schema={schema}
                        data={strategy}
                    >
                        {(children, displayName) => (
                            <MetaDataItem title={displayName}>{children}</MetaDataItem>
                        )}
                    </SchemaValue>
                </MetaData>
                <MetaData type="primary">
                    <SchemaValue
                        name="strategySummary"
                        schema={schema}
                        data={strategy}
                    >
                        {(children, displayName) => (
                            <MetaDataItem title={displayName}>{children}</MetaDataItem>
                        )}
                    </SchemaValue>
                </MetaData>
                <MetaData type="primary">
                    <SchemaValue
                        name="marketCap"
                        schema={schema}
                        data={strategy}
                    >
                        {(children, displayName) => (
                            <MetaDataItem title={displayName}>{children}</MetaDataItem>
                        )}
                    </SchemaValue>
                </MetaData>
                <MetaData type="primary">
                    <SchemaValue
                        name="universe"
                        schema={schema}
                        data={strategy}
                    >
                        {(children, displayName) => (
                            <MetaDataItem title={displayName}>{children}</MetaDataItem>
                        )}
                    </SchemaValue>
                </MetaData>
            </LayoutVertical>
        </LayoutContainer>
        <LayoutContainer flexBasis="70%">
            <LayoutVertical spacing="xx-large">
                <LayoutColumns>
                    <MetaData type="primary">
                        <SchemaValue
                            name="strategyName"
                            schema={schema}
                            data={strategy}
                        >
                            {(children, displayName) => (
                                <MetaDataItem title={displayName}>{children}</MetaDataItem>
                            )}
                        </SchemaValue>
                    </MetaData>
                    <MetaData type="primary">
                        <SchemaValue
                            name="strategyStartDate"
                            schema={schema}
                            data={strategy}
                        >
                            {(startDate, displayName) => (
                                <SchemaValue
                                    name="strategyEndDate"
                                    schema={schema}
                                    data={strategy}
                                >
                                    {endDate => (
                                        <MetaDataItem title={displayName}>
                                            <LayoutHorizontal
                                                spacing="none"
                                                gap="xx-large"
                                            >
                                                <ValuePair title="Start">{startDate}</ValuePair>
                                                <ValuePair title="End">{endDate}</ValuePair>
                                            </LayoutHorizontal>
                                        </MetaDataItem>
                                    )}
                                </SchemaValue>
                            )}
                        </SchemaValue>
                    </MetaData>
                </LayoutColumns>
                <MetaData type="primary">
                    <SchemaValue
                        name="experienceTotalInYears"
                        schema={schema}
                        data={strategy}
                    >
                        {(totalYears, displayName) => (
                            <SchemaValue
                                name="experienceMbamInYears"
                                schema={schema}
                                data={strategy}
                            >
                                {mbamYears => (
                                    <SchemaValue
                                        name="experiencePriorExperienceInYears"
                                        schema={schema}
                                        data={strategy}
                                    >
                                        {priorYears => (
                                            <MetaDataItem
                                                title={displayName}
                                                direction="horizontal"
                                            >
                                                <Typography.Text>{totalYears}</Typography.Text>
                                                &nbsp;
                                                <Typography.Text muted>
                                                    ({priorYears}, {mbamYears})
                                                </Typography.Text>
                                            </MetaDataItem>
                                        )}
                                    </SchemaValue>
                                )}
                            </SchemaValue>
                        )}
                    </SchemaValue>
                </MetaData>

                <MetaData type="primary">
                    <SchemaValue
                        name="description"
                        schema={schema}
                        data={strategy}
                    >
                        {(children, displayName) => (
                            <MetaDataItem title={displayName}>{children}</MetaDataItem>
                        )}
                    </SchemaValue>
                </MetaData>

                {/* <YTDGrid strategy={strategy} schema={schema} /> */}
            </LayoutVertical>
        </LayoutContainer>
    </LayoutColumns>
);
