import React, { ReactNode, ReactElement } from "react";
import { I<PERSON><PERSON><PERSON><PERSON> } from "PrimitiveComponents/index";

const STREET_ACCOUNT_ICON = `data:image/png;base64,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`;

type ChildrenProps = {
    url: string;
};
export type StreetAccountLinkProps = {
    sedol: string;
    size?: number;
    title?: string;
    className?: string;
    openInNewWindow?: boolean;
    children?: ReactNode | ((props: StreetAccountLinkProps & ChildrenProps) => ReactElement);
};
export const StreetAccountLink = (props: StreetAccountLinkProps) => {
    const {
        sedol,
        title = "Street Account",
        size = 14,
        className,
        openInNewWindow = true,
        children
    } = props;

    const url =
        sedol &&
        `https://www.streetaccount.com/search.aspx?searchtype=ticker_ue&searchterm=${sedol}`;

    if (typeof children === "function") {
        return children({
            ...props,
            url
        });
    }

    return (
        (url && (
            <IconButton
                className={className}
                title={title}
                padding="none"
                responsive
                invert
                iconComponent={
                    <a
                        href={url}
                        target={(openInNewWindow && "_blank") || ""}
                        onClick={e => e.stopPropagation()}
                    >
                        <img
                            className="icon"
                            alt={title}
                            style={{ height: size, width: size }}
                            src={STREET_ACCOUNT_ICON}
                        />
                    </a>
                }
            />
        )) ||
        null
    );
};
