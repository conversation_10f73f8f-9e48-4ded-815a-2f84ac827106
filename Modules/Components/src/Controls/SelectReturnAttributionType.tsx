import React, { ComponentProps } from "react";

import { Radio, Option, ControlProps } from "PrimitiveComponents/index";
import { ReturnAttributionType } from "Contracts/index";

const OPTIONS: Option<ReturnAttributionType>[] = [
    {
        value: ReturnAttributionType.PercentageOfAllocated,
        label: "Allocation"
    },
    {
        value: ReturnAttributionType.PercentageOfInvested,
        label: "Invested"
    }
];

export const SelectReturnAttributionType = (
    props: ControlProps<ReturnAttributionType, Omit<ComponentProps<typeof Radio>, "options">>
) => (
    <Radio
        options={OPTIONS}
        {...props}
    />
);
