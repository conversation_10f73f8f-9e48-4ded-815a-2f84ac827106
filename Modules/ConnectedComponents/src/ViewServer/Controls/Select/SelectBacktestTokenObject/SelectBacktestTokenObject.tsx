import React, { useState, use<PERSON>emo, useCallback, useEffect } from "react";
import { startCase, memoize } from "lodash";
import styled from "styled-components";
import {
    Flex,
    Tabs,
    TabPane,
    withPopover,
    with<PERSON>abel,
    withSelectObjectOptions,
    with<PERSON><PERSON><PERSON><PERSON>,
    Button,
    Modal,
    ToggleOption,
    useComponentsPersistState,
} from "PrimitiveComponents/index";
import { Grid, GridProps } from "Grid/index";
import { groupBy, isStringType } from "Utilities/index";
import type { ApiGatewayService, Workflow, Schema } from "Contracts/index";
import {
    createConfig,
    IReport,
    reportSubscriptionSpecFactory,
    unstringifyReport,
    withCommandState,
    withConnectedSkeleton,
} from "TransportGateway/index";
import { AppIcon } from "Theming/index";
import { CreateTokenForm, type CustomToken } from "./CreateTokenForm";

interface IReportWithData extends IReport {
    data: Workflow.Token.Token<unknown>;
    label: string;
    value: string;
}

const DEFAULT_SCHEMA = [
    {
        columnId: 0,
        name: "rowId",
        contentType: 9,
        metaData: { hidden: true },
    },
    {
        columnId: 1,
        name: "name",
        contentType: 9,
        metaData: {
            displayName: "Name",
            searchable: true,
            sortable: true,
            properties: {
                order: 1,
                flex: 2.5,
                sort: "asc",
            },
        },
    },
    {
        columnId: 2,
        name: "category",
        contentType: 9,
        metaData: {
            displayName: "Category",
            searchable: true,
            sortable: true,
            properties: {
                order: 2,
                flex: 1,
            },
        },
    },
    {
        columnId: 3,
        name: "subCategory",
        contentType: 9,
        metaData: {
            displayName: "Sub Category",
            searchable: true,
            sortable: true,
            properties: {
                order: 3,
                flex: 1,
            },
        },
    },
    {
        columnId: 4,
        name: "lastUpdated",
        contentType: 10,
        metaData: {
            displayName: "Last Updated",
            searchable: false,
            sortable: true,
            contentTypeForDisplay: "DateTime",
            properties: {
                order: 4,
                flex: 1,
                parseFormat: "YYYY-MM-DDTHH:mm:ss.SSSZ",
                valueFormat: "DD MMM YY",
            },
        },
    },
] as Schema;

const DeferredTabs = withDeferred(Tabs, 10);

const sortTabs = (a, b) => {
    const keyA = a[0];
    const keyB = b[0];
    if (keyA === "All") return -1;
    if (keyB === "All") return 1;
    return keyA.localeCompare(keyB);
};

type TokenLibraryProps = {
    data?: IReportWithData[];
    schema?: Schema;
    onChange?: GridProps["onChange"];
    onCreateSubmit?: (token: CustomToken) => void;
    disableCreateButton?: boolean;
};
export const TokenLibrary = ({
    data: dataProp,
    schema = DEFAULT_SCHEMA,
    onChange,
    onCreateSubmit,
    disableCreateButton,
}: TokenLibraryProps) => {
    const { entriesAll, entriesHN } = useMemo(() => {
        const localData = dataProp?.map(d => d.data) ?? [];

        const groupedData = {
            All: localData,
            ...groupBy((d: Workflow.Token.Token<unknown>) => d.category, localData),
        };

        const { HN = [] } = groupedData as any;

        const groupedDataHN = {
            All: HN,
            ...groupBy((d: Workflow.Token.Token<unknown>) => d.subCategory, HN),
        };

        return {
            entriesAll: Object.entries(groupedData).sort(sortTabs),
            entriesHN: Object.entries(groupedDataHN).sort(sortTabs),
        };
    }, [dataProp]);

    const [visible, setVisible] = useState(false);
    const [showHN, setShowHN] = useComponentsPersistState("token-library.show-hn", true);

    return (
        <>
            <div className="flex flex-col flex-1 gap overflow-hidden">
                <div className="flex flex-row items-center justify-between">
                    <ToggleOption
                        labelOn="HN"
                        labelOff="All"
                        value={showHN}
                        onMouseDownVanilla={(e, v) => {
                            e.preventDefault();
                            e.stopPropagation();
                            setShowHN(v);
                        }}
                    />

                    {disableCreateButton ? (
                        <div />
                    ) : (
                        <Button
                            onClick={e => {
                                e.preventDefault();
                                e.stopPropagation();
                                setVisible(true);
                            }}
                        >
                            Create Custom Token
                        </Button>
                    )}
                </div>
                <DeferredTabs layout="default">
                    {(showHN ? entriesHN : entriesAll)?.map(([key, entryData]) => (
                        <TabPane
                            tab={key}
                            key={key}
                        >
                            <Grid
                                data={entryData}
                                schema={schema}
                                onChange={onChange}
                            />
                        </TabPane>
                    ))}
                </DeferredTabs>
            </div>
            <Modal
                title="Create Custon Token"
                visible={visible}
                onClose={() => {
                    onChange(undefined);
                    setVisible(false);
                }}
                width={1200}
            >
                <CreateTokenForm onSubmit={onCreateSubmit} />
            </Modal>
        </>
    );
};

const StyledInput = styled.input`
    text-overflow: ellipsis;
    caret-color: transparent;
`;

const Popover = withPopover(Flex);

// @TODO: implement real saveCustomTokenToBackend
const fakeSaveCustomTokenToBackend = async token => {
    // the backend should return an object similar to what we receive from reportSubscriptionSpecFactory
    /*
    {
        "reportKey": "xADV",
        "parentReportKey": "Volume",
        "name": "xADV",
        "parameters": "[{\"name\":\"dateFrame\",\"defaultValue\":120,\"isRequired\":false,\"isHidden\":true,\"contentType\":\"1\",\"config\":null},{\"name\":\"universe\",\"defaultValue\":\"CompanyPage\",\"isRequired\":true,\"isHidden\":true,\"contentType\":\"7\",\"config\":{\"props\":{\"options\":[{\"label\":\"Developed Europe\",\"value\":\"DevelopedEurope\"},{\"label\":\"Pan Asian Markets\",\"value\":\"PanAsianMarkets\"},{\"label\":\"North America\",\"value\":\"NorthAmerica\"},{\"label\":\"United Kingdom\",\"value\":\"UnitedKingdom\"},{\"label\":\"Company Page Universe\",\"value\":\"CompanyPage\"},{\"label\":\"Australia Universe\",\"value\":\"Australia\"}]}}}]",
        "source": "BacktestReportingNode",
        "owner": "",
        "status": "",
        "category": "",
        "statusMessage": "",
        "lastUpdated": "2024-10-17T16:52:34.166Z",
        "lastUpdatedBy": "",
        "persistent": false,
        "abstract": false,
        "outputOperator": "dataRailOperator",
        "rowId": 435
    }
    */
    return {
        reportKey: token.name,
        name: token.name,
        category: token.category,
        subCategory: token.subCategory,
        lastUpdated: new Date().toISOString(),
    };
};

type SelectBacktestTokenProps = {
    options?: IReportWithData[];
    onChange?: GridProps["onChange"];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    value?: any; // @TODO: Fix this type. Possible an issue in the withSelectObjectOptions type
    mode?: "select" | "plus";
    disableCreate?: boolean;
    width?: string;
};
export const SelectBacktestTokenObjectBase = withLabel((props: SelectBacktestTokenProps) => {
    const { options, onChange: onChangeProp, value, mode = "select", width, disableCreate } = props;
    const [selected, setSelected] = useState<string>();

    const [popoverVisible, setPopoverVisible] = useState(false);

    useEffect(() => {
        setSelected(options?.find(o => o.value === value)?.label ?? value);
    }, [value, options]);

    const onChange = useCallback(
        (v: string) => {
            onChangeProp?.(v);
            setPopoverVisible(false);
        },
        [onChangeProp],
    );

    const onCreateSubmit = useCallback(
        async (token: CustomToken) => {
            const response = await fakeSaveCustomTokenToBackend(token);

            onChangeProp?.(response, { newOption: true });

            setPopoverVisible(false);
        },
        [onChangeProp],
    );

    const showPopover = useCallback(() => {
        setPopoverVisible(true);
    }, []);

    const formattedSelected = startCase(selected);

    return (
        <Popover
            flex="1"
            alignItems="center"
            direction="row"
            gap="medium"
            popoverVisible={popoverVisible}
            onClick={showPopover}
            popoverContent={
                <Flex
                    w={700}
                    h={300}
                    tabIndex={-1} // This should be part of this controls focus
                >
                    <TokenLibrary
                        data={options}
                        onChange={onChange}
                        disableCreateButton={disableCreate}
                        onCreateSubmit={onCreateSubmit}
                    />
                </Flex>
            }
            popoverPlacement="bottom-start"
            style={{ overflow: "hidden" }}
        >
            {mode === "select" ? (
                <Flex
                    flex="1"
                    items="center"
                    pr="x-small"
                    style={{ overflow: "hidden", width }}
                >
                    <Flex
                        flex="1"
                        style={{ overflow: "hidden" }}
                    >
                        <StyledInput
                            title={formattedSelected}
                            type="text"
                            placeholder="Select…"
                            value={formattedSelected}
                            onMouseDown={e => e.preventDefault()} // Prevent control blur propagation - popover should now have focus
                            onFocus={showPopover}
                        />
                    </Flex>

                    <Flex items="center">
                        <Flex
                            h="1.5em"
                            style={{ position: "relative", top: -1 }}
                        >
                            <AppIcon
                                type="downChevronSelect"
                                color="var(--input__color__indicator)"
                                responsive
                            />
                        </Flex>
                    </Flex>
                </Flex>
            ) : (
                <Button
                    style={{
                        height: 28,
                        width: 28,
                        minWidth: 28,
                        padding: 0,
                        borderColor: "var(--input__border-color)",
                        background: "var(--input__background-color)",
                    }}
                >
                    <AppIcon type="add" />
                </Button>
            )}
        </Popover>
    );
});

const extractPropsFrom = (name = "") => {
    const parts = name?.split("_") || [];
    if (parts.length !== 3) {
        return {
            name,
        };
    }
    return {
        category: parts[0],
        subCategory: parts[1],
        name: parts[2],
    };
};

const extractPropsFromMap = x => {
    const { name, ...rest } = extractPropsFrom(x.name);
    return {
        ...x,
        id: x.reportKey,
        value: x.reportKey,
        label: startCase(name),
        ...rest,
    };
};

const DecoratedSelectBacktestTokens = withSelectObjectOptions<
    Workflow.Token.Token<unknown>,
    ApiGatewayService.Filters.FiltersResponse & Workflow.Token.Token<unknown> & IReport
>({
    keyName: "id",
    optionDecorator: optionBase => {
        const option = extractPropsFromMap(unstringifyReport(optionBase));
        return {
            ...option,
            data: {
                rowId: option.id?.toString(),
                id: option.id?.toString(),
                valueFormat: option.valueFormat,
                name: option.label,
                category:
                    isStringType(option.category) && option.category.length > 0
                        ? startCase(option.category)
                        : "Misc",
                subCategory:
                    isStringType(option.subCategory) && option.subCategory.length > 0
                        ? startCase(option.subCategory)
                        : "Misc",
                paramsDefinition: option.parameters
                    .filter(parameter => !parameter.IsHidden)
                    .map(parameter => ({
                        name: extractPropsFrom(parameter.Name).name,
                        contentType: parameter.ContentType,
                        default: parameter.DefaultValue,
                        required: parameter.IsRequired,
                        config: parameter.Config,
                    })),
                lastUpdated: option.lastUpdated,
            },
        };
    },
})(SelectBacktestTokenObjectBase);

export const SelectBacktestTokenObject = withConnectedSkeleton(
    withCommandState(
        DecoratedSelectBacktestTokens,
        createConfig({
            props: reportSubscriptionSpecFactory({
                dataMapperRaw: memoize((data, _props, schema) => ({
                    // @WARNING: Avoid mapping data here.
                    //  Instead, use the optionDecorator above, as it also handles mapping for new custom tokens.
                    options: data.filter(d => d.parentReportKey === "BacktestTokens"),
                    schema,
                })),
            }),
        }),
    ),
);
