import React, { useMemo, ComponentProps } from "react";

import { withSchemaOverrides } from "@mocks/api-gateway";
import { Stamp, StampTitleWithScore } from "Components/index";
import {
    createConfig,
    withCommandState,
    withConnectedSkeleton,
    reportSubscriptionCommandSpec
} from "TransportGateway/index";
import { withSkeletonComponentDefinition, SkeletonRect } from "PrimitiveComponents/index";
import type { Schema } from "Contracts/index";

import { schemaOverride } from "./schemaOverride";

type SignalData = {
    signalScore: number;
};

export type SignalScoreStampViewServerBaseProps = {
    data?: SignalData;
    schema?: Schema;
    dateFrame?: string;
    signal?: string;
} & ComponentProps<typeof Stamp>;
const SignalScoreStampViewServerBase = withSkeletonComponentDefinition(
    ({
        data,
        schema: schemaProp,
        title: titleProp,
        ...rest
    }: SignalScoreStampViewServerBaseProps) => {
        const schema = useMemo(() => withSchemaOverrides(schemaProp, schemaOverride), [schemaProp]);

        const title = useMemo(
            () => (
                <StampTitleWithScore
                    title={titleProp}
                    score={data?.[0]?.signalScore}
                />
            ),
            [data, titleProp]
        );

        return (
            <Stamp
                {...rest}
                data={data}
                schema={schema}
                title={title}
            />
        );
    }
)(SkeletonRect);

export const SignalScoreStampViewServer = withConnectedSkeleton(
    withCommandState(
        SignalScoreStampViewServerBase,
        createConfig({
            props: reportSubscriptionCommandSpec(
                "DSPage_Cockpit_Signals_v2",
                ({ signal, dateFrame }) => (signal ? { signal, dateFrame } : undefined),
                (data, props, schema) => ({ data, schema }),
                "ProjectionNodeRecentWidget"
            )
        })
    )
);
