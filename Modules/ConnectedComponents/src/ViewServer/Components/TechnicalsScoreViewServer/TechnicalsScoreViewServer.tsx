import React from "react";

import { withFilterValues } from "Components/index";
import { withSkeletonComponentDefinition, SkeletonRect } from "PrimitiveComponents/index";
import { useColourScale, strategyLongShortToDirection } from "Utilities/index";
import type { Schema } from "Contracts/index";
import { Grid } from "Grid/index";
import {
    createConfig,
    withCommandState,
    withConnectedSkeleton,
    reportSubscriptionCommandSpec
} from "TransportGateway/index";

import { createGrid } from "./schemaOverride";

export type TechnicalsScoreData = {
    // initial score - trend
    absTrendScoreAdjusted: number;
    trendScoreWeight: number;
    absTrendScoreWeightAdjusted: number;
    // initial score - specific
    specificSlope3mScoreAdjusted: number;
    specificSlope3mScoreWeight: number;
    specificSlope3mScoreWeightAdjusted: number;
    // initial score - volume
    technical_VolumeAndIndependenceScoreAdjusted: number;
    technical_VolumeAndIndependenceScoreWeight: number;
    technical_VolumeAndIndependenceScoreWeightAdjusted: number;
    // initial score - candle
    todayCandleScoreAdjusted: number;
    todayCandleScoreWeight: number;
    todayCandleScoreWeightAdjusted: number;
    // initial score - timing
    daysToNumbersScoreAdjusted: number;
    daysToNumbersScoreWeight: number;
    daysToNumbersScoreWeightAdjusted: number;
    // initial score
    technicalScorePreExtension: number;
    // discount - extension
    extensionScoreAdjusted: number;
    extensionScoreWeight: number;
    extensionScoreWeightAdjusted: number;
    // discount - 1d chg
    dailyNmoveScoreAdjusted: number;
    dailyNmoveScoreWeight: number;
    dailyNmoveScoreWeightAdjusted: number;
    // discount
    technicalScorePenaltyScore: number;
    // total
    overallTechnicalScore: number;
    // not used
    generic_chgToday: number;
    hN_technical_score_withTrend: number;
    identifier: string;
    quadrant: string;
    absTrendScore: number;
    specificSlope3mScore: number;
    technical_VolumeAndIndependenceScore: number;
    todayCandleScore: number;
    daysToNumbersScore: number;
    extensionScore: number;
    dailyNmoveScore: number;
};

export type TechnicalsScoreViewServerBaseProps = {
    data: TechnicalsScoreData;
    schema: Schema;
};

const USE_COLOUR_SCALE_OPTIONS = { min: -1, max: 1, dark: true };

const TechnicalsScoreViewServerBase = withSkeletonComponentDefinition(
    ({ data }: TechnicalsScoreViewServerBaseProps) => {
        const initialScoreBg = useColourScale(0.1, USE_COLOUR_SCALE_OPTIONS)?.toString() ?? "";
        const discountScoreBg = useColourScale(-0.1, USE_COLOUR_SCALE_OPTIONS)?.toString() ?? "";
        const overallScoreBg = useColourScale(0.3, USE_COLOUR_SCALE_OPTIONS)?.toString() ?? "";
        const { gridData, gridSchema } = createGrid({
            data,
            initialScoreBg,
            discountScoreBg,
            overallScoreBg
        });
        return (
            <Grid
                data={gridData}
                schema={gridSchema}
                domLayout="autoHeight"
                layout="xx-full"
                hasTransparentBackground
                hasAllSolidBorders
                hasNoFirstCell
            />
        );
    }
)(SkeletonRect);

export const TechnicalsScoreViewServer = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            TechnicalsScoreViewServerBase,
            createConfig({
                props: reportSubscriptionCommandSpec(
                    "HN_cockpit_technicalScore_New",
                    ({ bbgTicker, longShort }) =>
                        bbgTicker
                            ? {
                                  bbgTicker,
                                  direction: strategyLongShortToDirection(longShort)
                              }
                            : undefined,
                    (data, props, schema) => ({ data: data?.[0], schema }),
                    "ProjectionNodeSpecificStrength"
                )
            })
        )
    ),
    ["bbgTicker", "longShort"],
    {
        bbgTicker: {
            required: true
        }
    }
);
