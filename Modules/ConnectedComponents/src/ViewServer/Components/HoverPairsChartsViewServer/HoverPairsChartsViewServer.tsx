import React from "react";

import {
    createConfig,
    withCommandState,
    withConnectedSkeleton,
    reportSubscriptionCommandSpec,
} from "TransportGateway/index";
import { withComponentProps } from "PrimitiveComponents/index";
import { StackedAndDriversCharts } from "../StackedChartViewServer";

export const HoverPairsCharts = ({ data, dateFrame, buyTicker, sellTicker, riskModel }) => (
    <div className="grid grid-cols-3 gap-md">
        <div className="flex flex-col gap-md">
            <div className="h-[20px]">
                Name:{" "}
                <span className="text-highlighted-inversed font-semibold">
                    {data?.buyTicker_name ?? "N/A"}
                </span>
            </div>
            <StackedAndDriversCharts
                bbgTicker={buyTicker}
                dateFrame={dateFrame}
                riskModel={riskModel}
                className="h-[600px]"
            />
        </div>

        <div className="flex flex-col gap-md">
            <div className="h-[20px]">
                Name:{" "}
                <span className="text-highlighted-inversed font-semibold">
                    {data?.sellTicker_name ?? "N/A"}
                </span>
            </div>
            <StackedAndDriversCharts
                bbgTicker={sellTicker}
                dateFrame={dateFrame}
                riskModel={riskModel}
                className="h-[600px]"
            />
        </div>

        <div className="flex flex-col gap-md">
            <div className="h-[20px]" />
            <StackedAndDriversCharts
                bbgTicker={buyTicker}
                relativeBbgTicker={sellTicker}
                dateFrame={dateFrame}
                riskModel={riskModel}
                className="h-[600px]"
            />
        </div>
    </div>
);

export const HoverPairsChartsViewServer = withComponentProps(
    withConnectedSkeleton(
        withCommandState(
            HoverPairsCharts,
            createConfig({
                props: reportSubscriptionCommandSpec(
                    "Hover_Pairs",
                    ({
                        buyTicker,
                        sellTicker,
                        dateFrame,
                        riskModel,
                    }: {
                        buyTicker?: string;
                        sellTicker?: string;
                        dateFrame?: number;
                        riskModel?: string;
                    }) =>
                        buyTicker &&
                        sellTicker && { ticker: buyTicker, sellTicker, dateFrame, riskModel },
                    (data, props, schema) => ({ data: data?.[0], schema }),
                    "ProjectionNodeOut",
                ),
            }),
        ),
    ),
    ({ buyTicker, dateFrame = 6, riskModel = "WW4AxiomaMH" }) => ({
        identifier: buyTicker,
        dateFrame,
        riskModel,
    }),
);
