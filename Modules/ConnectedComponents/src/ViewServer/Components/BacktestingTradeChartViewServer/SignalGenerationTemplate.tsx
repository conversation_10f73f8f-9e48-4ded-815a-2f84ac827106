import React from "react";
import { JSONSchema, SchemaForm } from "Schema/index";

const SignalGenerationTemplateSchema: JSONSchema = {
    $schema: "https://json-schema.org/draft/2019-09/schema",
    title: "Signal Generation Template*",
    type: "object",
    readOnly: false,
    extension: {
        actions: {
            reset: {
                disabled: false,
                props: {
                    title: "Cancel",
                },
            },
            submit: {
                disabled: false,
                props: {
                    title: "Save",
                },
            },
        },
        layout: "grid",
        gridTemplateColumns: "1fr 14em 2fr",
        displayOrder: ["positions", "thesis", "checklist"],
    },
    required: ["positions"],
    properties: {
        signalIdea: {
            type: "string",
            title: "Signal Idea",
            extension: {
                layout: "inline",
                targetColumn: "2/",
                rows: 2,
            },
        },
        hypothesis: {
            type: "string",
            title: "What are we testing and why",
            extension: {
                layout: "inline",
                targetColumn: "2/",
                rows: 2,
            },
        },
        buildingBlocks: {
            type: "string",
            title: "Key areas Within The Signal",
            extension: {
                layout: "inline",
                targetColumn: "2/",
                rows: 2,
            },
        },
        convictionLevels: {
            type: "string",
            title: "Levels to test",
            extension: {
                layout: "inline",
                targetColumn: "2/",
                rows: 2,
            },
        },
        entryLevel: {
            type: "string",
            title: "Key catalysts for entry",
            extension: {
                layout: "inline",
                targetColumn: "2/",
                rows: 2,
            },
        },
        exit: {
            type: "string",
            title: "What are the ways to Exit",
            extension: {
                layout: "inline",
                targetColumn: "2/",
                rows: 2,
            },
        },
    },
};

export const SignalGenerationIdeaTemplate = (props: any) => {
    const { value, onChange } = props;
    return (
        <SchemaForm
            jsonSchema={SignalGenerationTemplateSchema}
            value={value}
            onChange={onChange}
            autoSave
        />
    );
};
