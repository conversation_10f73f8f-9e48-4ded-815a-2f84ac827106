/* eslint-disable max-lines */
import { Workflow } from "Contracts/index";
import {
    BackTestCriteriaType,
    BacktestingIterationsData,
    FACTOR_NAMES,
    FACTOR_NAMES_AND_SPECIFIC,
} from "Components/index";
import moment from "moment";
import { cleanse } from "./ConnectedBacktestingSignalReturn";

export type OverviewAnalysisParamsData = {
    holdingPeriod: number;
    stopLoss: number;
    takeProfit: number;
    reentryTime: number;
};

export type OverviewParamsData = OverviewAnalysisParamsData & {
    swingAllocation: number;
    reentryTime: number;
    lowerBounds: string;
    upperBounds: string;
};

export type VersionedCriteria = [
    number,
    number,
    BackTestCriteriaType,
    OverviewParamsData,
    AdditionalCriteria[],
];
export type AdditionalCriteria = [BackTestCriteriaType, OverviewAnalysisParamsData, number];

function getTokenId(
    tokenId: string,
    parameterValues: Workflow.Token.TokenParamsValues,
    universe: string,
) {
    const universePrefix = !universe || universe.startsWith("@") ? "" : `${universe}_`;
    return `${universePrefix}${tokenId}${toVariableName(parameterValues)}`;
}

function createReportReferenceNodeForToken(
    condition: Workflow.Condition.Condition,
    universe: string,
) {
    if (!condition?.token) {
        return [];
    }
    const results = [
        {
            name: getTokenId(condition?.token?.id, condition.tokenParams, universe),
            operatorType: "report_reference",
            reportKey: condition?.token?.id,
            nodeType: "Persistent",
            parameterValues: { ...condition.tokenParams, universe },
        },
    ];
    if (condition.type === "token") {
        if (condition.tokenValue) {
            results.push({
                name: getTokenId(condition.tokenValue.id, condition.tokenValueParams, universe),
                operatorType: "report_reference",
                reportKey: condition.tokenValue.id,
                nodeType: "Persistent",
                parameterValues: { ...condition.tokenValueParams, universe },
            });
        }
    }
    return results;
}

function toVariableName(obj: Record<string, unknown>): string {
    if (!obj || typeof obj !== "object") {
        return "";
    }

    // Combine keys and values into a single string
    let result = Object.entries(obj)
        .map(([key, value]) => {
            // Process the key
            const processedKey = key.replace(/[^a-zA-Z0-9_]/g, "x"); // Replace invalid characters with "x"

            // Process the value (convert to string and sanitize)
            const processedValue = String(value).replace(/[^a-zA-Z0-9_]/g, "x"); // Replace invalid characters with "x"

            return `${processedKey}x${processedValue}`; // Combine key and value
        })
        .join("x");

    // Ensure the result starts with a valid Rust identifier character
    if (!/^[a-zA-Z_]/.test(result)) {
        result = `x${result}`;
    }

    return result;
}

export function extractNameFromTokenId(
    tokenId: string,
    tokenValueParams: Workflow.Token.TokenParamsValues,
) {
    if (!tokenId) {
        return tokenId;
    }
    const parts = tokenId?.split("_");
    return parts[parts.length - 1] + toVariableName(tokenValueParams);
}
function createJoinElementsFromToken(condition: Workflow.Condition.Condition, universe: string) {
    if (!condition.token) {
        return [];
    }
    const results = [
        {
            columnPrefix: extractNameFromTokenId(condition.token.id, condition.tokenParams),
            joinColumnName: "DateTimeAndIdentifier",
            name: getTokenId(condition.token.id, condition.tokenParams, universe),
        },
    ];
    if (condition.type === "token") {
        if (condition.tokenValue) {
            results.push({
                columnPrefix: extractNameFromTokenId(
                    condition.tokenValue.id,
                    condition.tokenValueParams,
                ),
                joinColumnName: "DateTimeAndIdentifier",
                name: getTokenId(condition.tokenValue.id, condition.tokenValueParams, universe),
            });
        }
    }

    return results;
}

function getElementNamesFromCondition(condition: Workflow.Condition.Condition, universe: string) {
    if (!condition.token) {
        return [];
    }
    const results = [getTokenId(condition.token.id, condition.tokenParams, universe)];
    if (condition.type === "token") {
        if (condition.tokenValue) {
            results.push(getTokenId(condition.tokenValue.id, condition.tokenValueParams, universe));
        }
    }

    return results;
}

function createLastPriceJoinElement() {
    return {
        joinColumnName: "DateTimeAndIdentifier",
        name: "General_Price_Close",
    };
}

function createSpecificJoinElement() {
    return {
        joinColumnName: "DateTimeAndIdentifier",
        columnPrefix: "SpecificToday",
        name: "General_Price_SpecificToday",
    };
}

function createRelativePriceJoinElement() {
    return {
        joinColumnName: "DateTimeAndIdentifier",
        columnPrefix: "RelativeClose",
        name: "General_Price_RelativeClose",
    };
}

function createVolScalerJoinElement() {
    return {
        columnPrefix: "VolScaler",
        joinColumnName: "DateTimeAndIdentifier",
        name: "Technicals_Volatility_VolScaler",
    };
}

function createStockRiskAttributionJoinElement() {
    return {
        columnPrefix: "RiskAttribution",
        joinColumnName: "DateTimeAndIdentifier",
        name: "StockRiskAttribution",
    };
}

function createJoinNodeFromTokens(
    conditions: Workflow.Condition.Condition[],
    version: number,
    universe: string,
    additionalCriteria: AdditionalCriteria[],
) {
    const universePrefix = !universe || universe.startsWith("@") ? "" : `${universe}_`;
    const joinNode = {
        name: undefined,
        operatorType: "dimensionJoin",
        nodeType: "Persistent",
        joinColAlias: "DateTimeAndIdentifier",
        joinDimensionName: "DateTimeAndIdentifier",
        joinElements: [],
        connections: {
            General_Price_SpecificToday: [`${universePrefix}General_Price_SpecificToday`, "out"],
            General_Price_Close: [`${universePrefix}General_Price_Close`, "out"],
            StockRiskAttribution: [`${universePrefix}StockRiskAttribution`, "out"],
            General_Price_RelativeClose: [`${universePrefix}General_Price_RelativeClose`, "out"],
            Technicals_Volatility_VolScaler: [
                `${universePrefix}Technicals_Volatility_VolScaler`,
                "out",
            ],
        },
    };

    const joinElementsByName = {};

    const addCondition = condition => {
        const elements = createJoinElementsFromToken(condition, universe);
        elements
            .filter(
                c =>
                    c.name !== "General_Price_Close" &&
                    c.name !== "General_Price_SpecificToday" &&
                    c.name !== "General_Price_RelativeClose" &&
                    c.name !== "StockRiskAttribution" &&
                    c.name !== "Technicals_Volatility_VolScaler",
            )
            .forEach(el => {
                joinElementsByName[el.name] = el;
                joinNode.connections[el.name] = [el.name, "out"];
            });
    };

    conditions.forEach(condition => {
        addCondition(condition);
    });

    additionalCriteria?.forEach(crit => {
        crit[0].tokens.forEach(condition => {
            addCondition(condition);
        });
    });

    joinNode.joinElements = Object.values(joinElementsByName);
    joinNode.joinElements.push(createLastPriceJoinElement());
    joinNode.joinElements.push(createSpecificJoinElement());
    joinNode.joinElements.push(createRelativePriceJoinElement());
    joinNode.joinElements.push(createVolScalerJoinElement());
    joinNode.joinElements.push(createStockRiskAttributionJoinElement());
    joinNode.name = `joinNode${padNumber(version)}`;

    return joinNode;
}

export function getJoinElementNames(
    conditions: Workflow.Condition.Condition[],
    universe: string,
    additionalCriteria: AdditionalCriteria[],
) {
    const joinElementNames = [];
    if (conditions) {
        conditions.forEach(condition => {
            const elements = getElementNamesFromCondition(condition, universe);
            joinElementNames.push(...elements);
        });
    }
    additionalCriteria?.forEach(crit => {
        crit[0].tokens.forEach(condition => {
            const elements = getElementNamesFromCondition(condition, universe);
            joinElementNames.push(...elements);
        });
    });

    return new Set(joinElementNames);
}

function createLastPriceNode(universe: string) {
    const universePrefix = !universe || universe.startsWith("@") ? "" : `${universe}_`;
    return {
        name: `${universePrefix}General_Price_Close`,
        operatorType: "report_reference",
        reportKey: "General_Price_CloseUsd",
        nodeType: "Persistent",
        parameterValues: {
            universe,
        },
    };
}

function createSpecificNode(universe: string) {
    const universePrefix = !universe || universe.startsWith("@") ? "" : `${universe}_`;
    return {
        name: `${universePrefix}General_Price_SpecificToday`,
        operatorType: "report_reference",
        reportKey: "General_Price_SpecificToday",
        nodeType: "Persistent",
        parameterValues: {
            universe,
        },
    };
}

function createRelativePriceNode(universe: string) {
    const universePrefix = !universe || universe.startsWith("@") ? "" : `${universe}_`;
    return {
        name: `${universePrefix}General_Price_RelativeClose`,
        operatorType: "report_reference",
        reportKey: "General_Price_RelativeClose",
        nodeType: "Persistent",
        parameterValues: {
            universe,
        },
    };
}

function createVolScalerNode(universe: string) {
    const universePrefix = !universe || universe.startsWith("@") ? "" : `${universe}_`;
    return {
        name: `${universePrefix}Technicals_Volatility_VolScaler`,
        operatorType: "report_reference",
        reportKey: "Technicals_Volatility_VolScaler",
        nodeType: "Persistent",
        parameterValues: {
            universe,
        },
    };
}

function createStockRiskAttribution(universe: string) {
    const universePrefix = !universe || universe.startsWith("@") ? "" : `${universe}_`;
    return {
        name: `${universePrefix}StockRiskAttribution`,
        operatorType: "report_reference",
        reportKey: "StockRiskAttribution",
        nodeType: "Persistent",
        parameterValues: {
            universe,
        },
    };
}

function createExcludeInvalidColsNode(joinNodeName: string, version: number) {
    return {
        name: `excludeInvalidCols${padNumber(version)}`,
        operatorType: "projection",
        nodeType: "Persistent",
        columns: ["*_Identifier", "*_DateTime", "*IdentifierType"],
        mode: "exclude",
        connections: {
            in: [joinNodeName, "out"],
        },
    };
}

function createRenameValueColumnsNode(version: number) {
    return {
        name: `renameValueColumns${padNumber(version)}`,
        operatorType: "projection",
        nodeType: "Persistent",
        columns: [
            {
                inboundName: "RelativeClose_Value",
                outboundNames: ["relative_price_original"],
            },
            {
                inboundName: "SpecificToday_Value",
                outboundNames: ["specific"],
            },
            {
                inboundName: "*_Value",
                outboundNames: ["*"],
            },
            {
                inboundName: "Value",
                outboundNames: ["price_original"],
            },
            ...FACTOR_NAMES.map(([fn]) => ({
                inboundName: `RiskAttribution_${fn}_ExposureTimesReturn`,
                outboundNames: [fn],
            })),
        ],
        mode: "project",
        connections: {
            in: [`excludeInvalidCols${padNumber(version)}`, "out"],
        },
    };
}

function createFilterInvalidRecordsNode(version: number) {
    return {
        name: `filterInvalidRecords${padNumber(version)}`,
        operatorType: "filter",
        nodeType: "Persistent",
        expression:
            "is_some(row.DateTime) && is_some(row.VolScaler)  && is_some(row.price_original) && is_some(row.relative_price_original)",
        rustOptimized: true,
        connections: {
            in: [`renameValueColumns${padNumber(version)}`, "out"],
        },
    };
}

export function createFilterHitsNode(version: number) {
    return {
        name: `filterHits${padNumber(version)}`,
        operatorType: "filter",
        nodeType: "Persistent",
        expression:
            "row.IsActive && is_within_date_frame(row.DateTime,dimension_cache, @dateForFilterFrame)",
        rustOptimized: true,
        connections: {
            in: [`calcActiveTrade${padNumber(version)}`, "out"],
        },
    };
}

export function createHitOutputProjectionNode(
    version: number,
    signalName: string,
    signalReportName: string,
) {
    return {
        name: `finalOutput${padNumber(version)}`,
        operatorType: "report_reference",
        reportKey: "SIGNAL_HITS_ABSTRACT_DO_NOT_REMOVE",
        nodeType: "Persistent",
        parameterValues: {
            signalReportName,
            signalName,
            dateForFilterFrame: "@dateForFilterFrame",
        },
        connections: {
            in: [`filterHits${padNumber(version)}`, "out"],
        },
    };
}

function createCalcInitiationParamsNode(
    swingAllocation: number,
    joinNodeVersion: number,
    analysisNodeVersion: number,
) {
    const node = {
        name: `calcInitiationParams${padNumber(analysisNodeVersion)}`,
        operatorType: "calc",
        nodeType: "Persistent",
        columnConfigs: [],
        connections: {
            in: [`filterInvalidRecords${padNumber(joinNodeVersion)}`, "out"],
        },
    };
    node.columnConfigs.push(createCalcConfigforSwingAllocation(swingAllocation));
    return node;
}

function formatToOneDecimal(input) {
    // Convert the input to a number
    const number = typeof input === "string" ? parseFloat(input) : input;

    // Check if the conversion is valid
    if (!Number.isNaN(number)) {
        return number.toFixed(1); // Format to 1 decimal place and return as string
    }

    // Return a fallback for invalid inputs
    return "Invalid input";
}

export type StringMap = {
    [key: string]: string | number;
};

function createCalcConfigforSwingAllocation(swingAllocation: number) {
    return {
        dataType: "Float64",
        expression: `(${formatToOneDecimal(swingAllocation)} / row.VolScaler) / row.price_original`,
        name: `noShares`,
    };
}

function getRustConditionFromCondition(
    condition: Workflow.Condition.Condition,
): string | number | undefined {
    const conditionText =
        "numberCondition" in condition ? condition.numberCondition : condition.stringCondition;

    return getRustCondition(conditionText);
}

export function getRustCondition(conditionText: string): string | number | undefined {
    switch (conditionText) {
        case "=":
            return "equal";
        case "<":
            // Logic for less than
            return "lessThan";
        case ">":
            // Logic for greater than
            return "greaterThan";
        case ">=":
            // Logic for greater than or equal
            return "greaterThanOrEqual";
        case "<=":
            // Logic for less than or equal
            return "lessThanOrEqual";
        case "<>":
            // Logic for not equal
            return "notEqual";
        default:
            throw Error(`Cannot create rust condition from condition text ${conditionText}`);
    }
}

export function getConditionLabel(conditionLabel: string): string | undefined {
    switch (conditionLabel) {
        case "equal":
            return "=";
        case "lessThan":
            return "<";
        case "greaterThan":
            return ">";
        case "greaterThanOrEqual":
            return ">=";
        case "lessThanOrEqual":
            return "<=";
        case "notEqual":
            return "<>";
        default:
            throw Error(`Cannot create condition text from Rust condition label ${conditionLabel}`);
    }
}

function createStatementFromCondition(condition: Workflow.Condition.Condition) {
    if (!condition.token) {
        return undefined;
    }
    const lhsName = extractNameFromTokenId(condition.token.id, condition.tokenParams);
    const rustConditionName = getRustConditionFromCondition(condition);
    let rhs;
    if (condition.type === "value") {
        const constant = "stringValue" in condition ? condition.stringValue : condition.numberValue;
        rhs = {
            Constant: {
                Float64: constant,
            },
        };
    } else if (condition.type === "token" && condition?.tokenValue) {
        const rhsName = `${extractNameFromTokenId(
            condition?.tokenValue?.id,
            condition?.tokenValueParams,
        )}`;
        rhs = {
            Column: rhsName,
        };
    }
    return {
        lhs: {
            Column: lhsName,
        },
        operator: rustConditionName,
        rhs,
    };
}
function createFieldsFromCondition(condition: Workflow.Condition.Condition) {
    const fields = [];
    if (condition?.token) {
        const lhsName = extractNameFromTokenId(condition.token.id, condition.tokenParams);
        fields.push(lhsName);
        if (condition.type === "token" && condition?.tokenValue) {
            const rhsName = `${extractNameFromTokenId(
                condition?.tokenValue?.id,
                condition?.tokenValueParams,
            )}`;
            fields.push(rhsName);
        }
    }
    const uniqueFields = [...new Set(fields)];
    return uniqueFields.map(fld => `${fld}~Float64`);
}

export function createCriteriaStatements(criteria: BackTestCriteriaType) {
    const result = [];
    criteria.tokens
        .filter(c => c.token?.id)
        .forEach(tk => result.push({ column: createStatementFromCondition(tk) }));
    return {
        group: {
            group: {
                and: result,
            },
        },
    };
}

export function createFieldsForStatements(criteria: BackTestCriteriaType) {
    const result = [];
    criteria.tokens.forEach(tk => result.push(...createFieldsFromCondition(tk)));
    return result;
}

export function createAdditonalCriteriaFromSelectedStudies(
    iterations: BacktestingIterationsData[] = [],
) {
    const result: AdditionalCriteria[] = [];
    iterations.forEach((it: BacktestingIterationsData) => {
        result.push([
            JSON.parse(it.criteria) as any as BackTestCriteriaType,
            {
                holdingPeriod: it.holdingPeriod,
                takeProfit: it.takeProfitPercent,
                stopLoss: it.stopLossPercent,
                reentryTime: it.reEntryTime,
            },
            it.rowId,
        ]);
    });
    return result;
}

function createCriteriaTuple(
    criteria: BackTestCriteriaType,
    overviewData: OverviewAnalysisParamsData,
) {
    return [
        {
            columnCriteria: {
                reEntryTime: overviewData.reentryTime,
                criteria: createCriteriaStatements(criteria),
            },
        },
        {
            stopLossAndTakeProfit: {
                priceColumn: "price",
                initiationPriceColumn: "initiation_price_percentages",
                stopLoss: overviewData.stopLoss,
                takeProfit: overviewData.takeProfit,
                holdingPeriod: overviewData.holdingPeriod,
            },
        },
    ];
}

function createUniqueCombinations(
    criteria: BackTestCriteriaType,
    overviewData: OverviewAnalysisParamsData,
    additionalCriteria: AdditionalCriteria[],
) {
    const result = [];
    result.push(createCriteriaTuple(criteria, overviewData));
    additionalCriteria?.forEach(([crit, ov]) => {
        result.push(createCriteriaTuple(crit, ov));
    });
    return result;
}

function createTradeModelFromCriteria(
    criteria: BackTestCriteriaType,
    takeProfit: number,
    stopLoss: number,
    holdingPeriod: number,
    reentryTime: number,
    additionalCriteria: AdditionalCriteria[],
    initialAllocation: number,
) {
    return {
        instrumentFieldName: "Identifier",
        dateFieldName: "DateTime",
        tradeIdFieldName: "DateTimeAndIdentifier",
        currentPriceFieldName: "price",
        currentScalingFactorFieldName: "noShares",
        uniqueCombinations: createUniqueCombinations(
            criteria,
            {
                takeProfit,
                stopLoss,
                holdingPeriod,
                reentryTime,
            },
            additionalCriteria,
        ),
        fieldsToSnapshot: [
            "relative_price~Float64",
            "noShares~Float64",
            "price~Float64",
            ...createRiskAttributionFieldsToSnapshot(),
            ...createFieldsForStatements(criteria),
        ],
        noThreads: 1,
        returnStrategies: [
            ["relative_price", "absperc"],
            ...createRiskAttributionPriceFields(),
            ["specific_filled", "retperc"],
            ["price", "absperc"],
            ["price", "absabs"],
        ],
        initialAllocation,
    };
}
export function createRiskAttributionFieldsToSnapshot() {
    return FACTOR_NAMES_AND_SPECIFIC.map(([fn]) => `${fn}_filled~Float64`);
}

export function createRiskAttributionPriceFields() {
    return FACTOR_NAMES.map(([fn]) => [`${fn}_filled`, "retperc100"]);
}
export function formatDate(dt) {
    return dt.startOf("day").format("YYYY-MM-DD[T]HH:mm:ss.SSS");
}
export function parseDate(dateString) {
    return moment(dateString, "YYYY-MM-DD[T]HH:mm:ss.SSS", true);
}

export function calculateDateRangeFromOverviewData(overviewData: OverviewParamsData) {
    const lower = parseDate(overviewData.lowerBounds);
    const upper = parseDate(overviewData.upperBounds);
    return upper.diff(lower, "years");
}

export function padNumber(num: number): string {
    return `${num}`.padStart(4, "0");
}

export function padNumberTo(num: number, noDigits: number): string {
    return `${num}`.padStart(noDigits, "0");
}

function createSignalAnalysisNode(
    stopLoss: number,
    takeProfit: number,
    holdingPeriod: number,
    swingAllocation: number,
    reentryTime: number,
    criteria: BackTestCriteriaType,
    analysisOperatorName: string,
    identifier: string,
    joinVersion: number,
    analysisNodeVersion: number,
    iteration: number,
    lowerBounds: string,
    upperBounds: string,
    correlationWindowSize: number,
    correlationIdentifier: string,
    correlationOutputOperatorName: string,
    additionalCriteria: AdditionalCriteria[],
    initialAllocation: number,
) {
    return {
        name: `signalAnalysis${padNumber(analysisNodeVersion)}`,
        operatorType: "report_reference",
        reportKey: "Signal_Analysis_Dynamic",
        nodeType: "Persistent",
        parameterValues: {
            tradeModelString: createTradeModelFromCriteria(
                criteria,
                takeProfit,
                stopLoss,
                holdingPeriod,
                reentryTime,
                additionalCriteria,
                initialAllocation,
            ),
            lowerBounds,
            upperBounds,
            iteration,
            identifier,
            correlationWindowSize,
            correlationIdentifier,
            modelConstructor: "new_flat_list",
            correlationOutputOperatorName,

            modelType: "Basic",
        },
        outputOperatorName: analysisOperatorName,
        connections: {
            in: [`calcInitiationParams${padNumber(analysisNodeVersion)}`, "out"],
        },
    };
}

export function createReportNodesFromBacktestCriteriaBeforeTranspose(
    versionedCriteria: VersionedCriteria,
) {
    const [
        joinVersion,
        analysisVersion,
        criteria,
        overviewData = {} as any,
        additionalCriteria = [],
    ] = versionedCriteria;
    const { swingAllocation } = overviewData;
    const resultNodes: { name: string }[] = [];
    const remainingTokens = criteria?.tokens?.filter(c => c.token);

    if (additionalCriteria) {
        additionalCriteria.forEach(crit => {
            const tokens = crit[0]?.tokens?.filter(c => c.token);
            tokens.forEach(tk => remainingTokens.push(tk));
        });
    }

    if (remainingTokens) {
        remainingTokens.forEach(token =>
            resultNodes.push(...createReportReferenceNodeForToken(token, criteria.universe)),
        );
        resultNodes.push(createLastPriceNode(criteria.universe));
        resultNodes.push(createSpecificNode(criteria.universe));
        resultNodes.push(createRelativePriceNode(criteria.universe));
        resultNodes.push(createVolScalerNode(criteria.universe));
        resultNodes.push(createStockRiskAttribution(criteria.universe));
        const joinNode = createJoinNodeFromTokens(
            remainingTokens,
            joinVersion,
            criteria.universe,
            additionalCriteria,
        );
        resultNodes.push(joinNode);
        resultNodes.push(createExcludeInvalidColsNode(joinNode.name, joinVersion));
        resultNodes.push(createRenameValueColumnsNode(joinVersion));
        resultNodes.push(createFilterInvalidRecordsNode(joinVersion));
        resultNodes.push(
            createCalcInitiationParamsNode(swingAllocation, joinVersion, analysisVersion),
        );
    }
    return deduplicateByName(resultNodes);
}

function getCondition(condition: Workflow.Condition.Condition): string | number | undefined {
    const conditionText =
        "numberCondition" in condition ? condition.numberCondition : condition.stringCondition;
    if (conditionText === "=") {
        return "==";
    }
    return conditionText;
}

function getConditionValue(condition: Workflow.Condition.Condition): string | number | undefined {
    if (condition.type === "value") {
        const constant = "stringValue" in condition ? condition.stringValue : condition.numberValue;
        // const parameterName = `${condition.token.id}`;
        // eslint-disable-next-line no-param-reassign
        // parameterValues[parameterName] = constant;
        // return `@${parameterName}`;
        return `${constant}`;
    }
    if (condition.type === "token" && condition?.tokenValue) {
        return `row.${extractNameFromTokenId(
            condition?.tokenValue?.id,
            condition?.tokenValueParams,
        )}`;
    }
    return undefined;
}

function getColumnPrefixFromCondition(
    condition: Workflow.Condition.Condition,
): string | number | undefined {
    const conditionText =
        "numberCondition" in condition ? condition.numberCondition : condition.stringCondition;

    return getColumnPrefix(conditionText);
}

export function getColumnPrefix(conditionText: string): string | number | undefined {
    switch (conditionText) {
        case "=":
            return "";
        case "<":
            // Logic for less than
            return "Max";
        case ">":
            // Logic for greater than
            return "Min";
        case ">=":
            // Logic for greater than or equal
            return "Min";
        case "<=":
            // Logic for less than or equal
            return "Max";
        case "<>":
            // Logic for not equal
            return "Not";
        default:
            throw Error(`Cannot create column prefix from ${conditionText}`);
    }
}

function getParameterNameFromCondition(condition: Workflow.Condition.Condition) {
    return `${getColumnPrefixFromCondition(condition)}${extractNameFromTokenId(
        condition.token.id,
        condition.tokenParams,
    )}`;
}

function getConditionValueWithParams(
    condition: Workflow.Condition.Condition,
    parameterValues: StringMap,
): string | number | undefined {
    if (condition.type === "value") {
        const constant = "stringValue" in condition ? condition.stringValue : condition.numberValue;
        const parameterName = getParameterNameFromCondition(condition);
        // eslint-disable-next-line no-param-reassign
        parameterValues[parameterName] = constant;
        return `@${parameterName}`;
    }
    if (condition.type === "token" && condition?.tokenValue) {
        return `row.${extractNameFromTokenId(
            condition?.tokenValue?.id,
            condition?.tokenValueParams,
        )}`;
    }
    return undefined;
}

export function applyParameterValuesToCriteria(
    parameterValues: any,
    criteria: BackTestCriteriaType,
) {
    const clonedCritieria = JSON.parse(JSON.stringify(criteria));
    clonedCritieria.tokens.forEach(condition => {
        if (condition.type === "value") {
            const parameterName = getParameterNameFromCondition(condition);
            const valueForParameterName = parameterValues[parameterName];
            if (valueForParameterName) {
                if ("stringValue" in condition) {
                    // eslint-disable-next-line no-param-reassign
                    condition.stringValue = `${valueForParameterName}`;
                }
                if ("numberValue" in condition) {
                    // eslint-disable-next-line no-param-reassign
                    condition.numberValue = Number(valueForParameterName);
                }
            }
        }
    });
    return clonedCritieria;
}

function createCalcConfigFromToken(
    condition: Workflow.Condition.Condition,
    parameterValues: StringMap,
) {
    return {
        dataType: "Boolean",
        expression: `row.${extractNameFromTokenId(
            condition.token.id,
            condition.tokenParams,
        )} ${getCondition(condition)} ${getConditionValueWithParams(condition, parameterValues)}`,
        name: `Is_${cleanse(condition.id)}`,
    };
}
function createIsActiveCalcColumnConfig(conditions: Workflow.Condition.Condition[]) {
    return {
        dataType: "Boolean",
        expression: conditions.map(condition => ` row.Is_${cleanse(condition.id)} `).join("&&"),
        name: `IsActive`,
    };
}

export function createCalcActiveTradeNodeFromNodes(
    versionedCriteria: VersionedCriteria,
    parameterValues: StringMap,
) {
    const [joinVersion, , criteria] = versionedCriteria;
    const remainingTokens = criteria?.tokens?.filter(c => c.token);

    const node = {
        name: `calcActiveTrade${padNumber(joinVersion)}`,
        operatorType: "calc",
        nodeType: "Persistent",
        columnConfigs: [],
        connections: {
            in: [`filterInvalidRecords${padNumber(joinVersion)}`, "out"],
        },
    };
    remainingTokens.forEach(token =>
        node.columnConfigs.push(createCalcConfigFromToken(token, parameterValues)),
    );
    node.columnConfigs.push(createIsActiveCalcColumnConfig(remainingTokens));
    return node;
}

export function createReportNodesFromBacktestCriteria(
    versionedCriteria: VersionedCriteria,
    analysisOperatorName: string,
    identifier: string,
    iteration: number,
    correlationWindowSize: number,
    correlationIdentifier: string,
    correlationOutputOperatorName: string,
) {
    const resultNodes = createReportNodesFromBacktestCriteriaBeforeTranspose(versionedCriteria);

    const [
        joinVersion,
        analysisNodeVersion,
        criteria,
        overviewData = {} as any,
        additionalCriteria,
    ] = versionedCriteria;
    const {
        holdingPeriod,
        takeProfit,
        stopLoss,
        swingAllocation,
        reentryTime,
        lowerBounds,
        upperBounds,
        initialAllocation,
    } = overviewData;

    const remainingTokens = criteria?.tokens?.filter(c => c.token);
    if (remainingTokens) {
        resultNodes.push(
            createSignalAnalysisNode(
                stopLoss,
                takeProfit,
                holdingPeriod,
                swingAllocation,
                reentryTime,
                criteria,
                analysisOperatorName,
                identifier,
                joinVersion,
                analysisNodeVersion,
                iteration,
                lowerBounds,
                upperBounds,
                correlationWindowSize,
                correlationIdentifier,
                correlationOutputOperatorName,
                additionalCriteria,
                initialAllocation || 100000000,
            ),
        );
    }
    return deduplicateByName(resultNodes as any);
}

function deduplicateByName(nodes: { name: string }[]) {
    const nodesByName = {};
    nodes.forEach(nd => {
        nodesByName[nd.name] = nd;
    });
    return Object.values(nodesByName) as { name: string }[];
}
