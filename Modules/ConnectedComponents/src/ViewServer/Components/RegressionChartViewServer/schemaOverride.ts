import { isNumberType } from "Utilities/index";

export const regressionSchemaOverride = {
    dateTime: {
        displayName: "Date",
        properties: {
            chartCartesianGridPropsShow: false,
            chartReferenceLinePropsShow: true,
            chartLegendPropsShow: false,
            chartPropsMargin: { right: 50, left: -30 },

            chartXAxisPropsShow: "#context(showXAxis)",
            chartXAxisPropsFormatterType: "date",
            chartXAxisPropsTicksMode: "default",
            chartXAxisPropsInterval: "preserveEnd",
            chartXAxisPropsHeight: 25,
            chartXAxisPropsMinTickGap: 20,

            chartYAxisLeftPropsShow: true,
            chartYAxisLeftPropsPadding: { top: 5, bottom: 5 },
            chartYAxisLeftPropsDomain: ["auto", "auto"],
            chartYAxisLeftPropsTick: "null",
            chartYAxisLeftPropsClassName: "hide-line",
            chartYAxisLeftLabelPropsValue: "#context(yLabel)",
            chartYAxisLeftLabelPropsPosition: "insideLeft",
            chartYAxisLeftLabelPropsClassName: "anchor-middle",
            chartYAxisLeftLabelPropsOffset: 40,
            chartYAxisLeftLabelPropsFontSize: "12px",

            chartTooltipPropsValueColumnName: "dateTime",
            chartTooltipPropsLabelFormatterType: "date",
            chartTooltipPropsLabelValueFormat: "ddd, Do MMM YYYY",
            chartTooltipPropsSortBy:
                "relative, open, high, low, close, mavg50d, mavg100d, mavg200d",
            chartTooltipPropsPosition: '{ "y": "50%" }',
        },
    },
    relative: {
        displayName: "Relative",
        properties: {
            order: 2,
            chartType: "line",
            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStrokeWidth: "2px",
            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "significant3dp",
            chartSeriesPropsStroke: "var(--charts__series__line__1)",

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "relative",
            chartPriceMarkerPropsValueFormat: "significant3dp",
            chartPriceMarkerPropsPadding: 0,
            chartPriceMarkerPropsBorderWidth: 0,
        },
    },
    close: {
        displayName: "Close",
        properties: {
            order: 2,

            chartType: "line",

            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStrokeWidth: "2px",
            chartSeriesPropsStroke: "var(--charts__series__line__1)",

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "significant3dp",
            chartTooltipPropsDisableColor: true,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "close",
            chartPriceMarkerPropsValueFormat: "significant3dp",
            chartPriceMarkerPropsPadding: 0,
            chartPriceMarkerPropsBorderWidth: 0,
        },
    },
    open: {
        displayName: "Open",
        properties: {
            order: 2,

            chartType: "line",

            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStrokeWidth: "0px",
            chartSeriesPropsStroke: "var(--charts__series__line__1)",

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "significant3dp",
            chartTooltipPropsDisableColor: true,
        },
    },
    low: {
        displayName: "Low",
        properties: {
            order: 2,

            chartType: "line",

            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStrokeWidth: "0px",
            chartSeriesPropsStroke: "var(--charts__series__line__1)",

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "significant3dp",
            chartTooltipPropsDisableColor: true,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "low",
            chartPriceMarkerPropsValueFormat: "significant3dp",
            chartPriceMarkerPropsTextColor: "var(--charts__label--color_muted)",
            chartPriceMarkerPropsPadding: 0,
            chartPriceMarkerPropsBorderWidth: 0,
        },
    },
    high: {
        displayName: "High",
        properties: {
            order: 2,

            chartType: "line",

            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStrokeWidth: "0px",
            chartSeriesPropsStroke: "var(--charts__series__line__1)",

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "significant3dp",
            chartTooltipPropsDisableColor: true,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "high",
            chartPriceMarkerPropsValueFormat: "significant3dp",
            chartPriceMarkerPropsTextColor: "var(--charts__label--color_muted)",
            chartPriceMarkerPropsPadding: 0,
            chartPriceMarkerPropsBorderWidth: 0,
        },
    },
    mavg50d: {
        displayName: "MA 50 day",
        properties: {
            order: 3,

            chartType: "line",

            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStroke: "var(--charts__series__line__4)",

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "significant3dp",
            chartTooltipPropsDisableColor: true,
        },
    },
    mavg100d: {
        displayName: "MA 100 day",
        properties: {
            order: 4,

            chartType: "line",

            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStroke: "var(--charts__series__line__5)",

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "significant3dp",
            chartTooltipPropsDisableColor: true,
        },
    },
    mavg200d: {
        displayName: "MA 200 day",
        properties: {
            order: 5,

            chartType: "line",

            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStroke: "var(--charts__series__line__12)",

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "significant3dp",
            chartTooltipPropsDisableColor: true,
        },
    },
};

export const rsiSchemaOverride = ({ isPair }) => ({
    dateTime: {
        displayName: "Date",
        properties: {
            ...regressionSchemaOverride.dateTime.properties,
            chartYAxisLeftLabelPropsValue: "RSI",
            chartYAxisLeftPropsPadding: { top: 0, bottom: 0 },
            chartYAxisLeftPropsDomain: [0, 100],
            chartTooltipPropsSortBy: `${isPair ? "relRsi" : "absRsi"}`,
        },
    },

    absRsi: {
        displayName: "RSI (Absolute)",
        hidden: isPair,
        properties: {
            order: 11,

            chartType: "line",

            chartReferenceLinePropsShow: true,
            chartReferenceLinePropsList: [
                { y: 30, stroke: "var(--charts__series__line__12)" },
                { y: 70, stroke: "var(--charts__series__line__4)" },
            ],

            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStroke: "var(--charts__series__line__1)",

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "number1dp",
            chartTooltipPropsDisableColor: true,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "absRsi",
            chartPriceMarkerPropsValueFormat: "number1dp",
        },
    },
    relRsi: {
        displayName: "RSI (Relative)",
        hidden: !isPair,
        properties: {
            order: 11,

            chartType: "line",

            chartReferenceLinePropsShow: true,
            chartReferenceLinePropsList: [
                { y: 30, stroke: "var(--charts__series__line__12)" },
                { y: 70, stroke: "var(--charts__series__line__4)" },
            ],

            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStroke: "var(--charts__series__line__1)",

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "number1dp",
            chartTooltipPropsDisableColor: true,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "relRsi",
            chartPriceMarkerPropsValueFormat: "number1dp",
        },
    },
});

export const correlationSchemaOverride = {
    dateTime: {
        displayName: "Date",
        properties: {
            ...regressionSchemaOverride.dateTime.properties,
            chartYAxisLeftLabelPropsValue: "Corr",
            chartTooltipPropsSortBy: `corr3m, corr6m`,
            chartYAxisLeftPropsDomain: ["auto", "auto"],
            chartYAxisLeftPropsPadding: { top: 5, bottom: 5 },
        },
    },
    corr3m: {
        displayName: "3M Correlation",
        properties: {
            chartType: "line",

            chartSeriesPropsStroke: "var(--charts__series__line__2)",

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "corr3m",
            chartPriceMarkerPropsValueFormat: "number2dp",
            chartPriceMarkerPropsTextColor: "var(--charts__series__line__2)",
        },
    },
    corr6m: {
        displayName: "6M Correlation",
        properties: {
            chartType: "line",

            chartSeriesPropsStroke: "var(--charts__series__line__2__50)",

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "corr6m",
            chartPriceMarkerPropsValueFormat: "number2dp",
            chartPriceMarkerPropsTextColor: "var(--charts__series__line__2__50)",
        },
    },
};

export const exposureSchemaOverride = ({ data }) => {
    const absMinMax = Math.max(...data.map(d => (isNumberType(d.value) ? Math.abs(d.value) : 0)));
    return {
        dateTime: {
            displayName: "Date",
            properties: {
                ...regressionSchemaOverride.dateTime.properties,
                chartYAxisLeftLabelPropsValue: "Exposure",
                chartTooltipPropsSortBy: `value`,
                chartYAxisLeftPropsPadding: { top: 5, bottom: 5 },
                chartYAxisLeftPropsDomain: `[-${absMinMax}, ${absMinMax}]`,
            },
        },
        value: {
            displayName: "Exposure",
            properties: {
                chartType: "area",

                // chartPriceMarkerPropsShow: true,
                // chartPriceMarkerPropsTextColor: "minmax",
                // chartPriceMarkerPropsDataKey: "value",
                // chartPriceMarkerPropsFormatterType: "bigValue",
                // chartPriceMarkerPropsValueFormat: ".2f",
                // chartPriceMarkerPropsZeroValue: "0",

                chartTooltipPropsFormatterType: "bigValue",
                chartTooltipPropsValueFormat: ".2f",

                chartSeriesPropsConnectNulls: true,
                chartSeriesPropsGradientType: "minmax",
            },
        },
    };
};

export const headerSchemaOverride = {
    perf: {
        contentTypeForDisplay: "ChangeValue",
        properties: {
            formatterType: "percent",
            valueFormat: "percent1dp",
        },
    },
    perfTimeRange: {
        contentTypeForDisplay: "ChangeValue",
        properties: {
            formatterType: "percent",
            valueFormat: "percent1dp",
        },
    },
};
