import React, { ComponentType, useMemo, Fragment } from "react";
import { v4 } from "uuid";

import { withSchemaOverrides } from "@mocks/api-gateway";
import { Chart, ChartProps } from "Charting/index";
import { sanitiseTicker, toObjectByProp, cn } from "Utilities/index";
import {
    createConfig,
    reportSubscriptionCommandSpec,
    withCommandStateFactory,
} from "TransportGateway/index";
import { SchemaValue } from "Schema/index";
import { TickerWithTooltip } from "PrimitiveComponents/index";
import { DriversChart } from "ComponentLibrary/index";

import {
    regressionSchemaOverride,
    rsiSchemaOverride,
    correlationSchemaOverride,
    exposureSchemaOverride,
    headerSchemaOverride,
} from "./schemaOverride";

const RegressionChart = Chart as ComponentType<
    ChartProps & {
        type?: "price" | "rsi";
        isPair: boolean;
        yLabel?: string;
        showXAxis?: boolean;
    }
>;

const addSchema = (schema, columnName, dashArray, strokeLineColor) => [
    ...schema,
    {
        name: columnName,
        metaData: {
            properties: {
                order: 3,
                chartType: "line",
                chartSeriesPropsConnectNulls: true,
                chartSeriesPropsStroke: `var(--charts__series__line__${strokeLineColor})`,
                chartTooltipPropsFormatterType: "number",
                chartTooltipPropsValueFormat: "number2dp",
                ...(dashArray && { chartSeriesPropsStrokeDasharray: dashArray }),
            },
        },
    },
];

const addRegressionLines = (data, schema, regression, propName, strokeLineColor) => {
    if (data?.length > 0 && schema?.length > 0 && regression) {
        const { regressCurrent, regressSlope, regressLength, regressStdErr } = regression;
        let count = Math.min(regressLength, data.length);
        const first = data.length - count;
        const newData = data.map((item, i) => {
            if (i >= first) {
                const regress = regressCurrent - regressSlope * count;
                const result = {
                    ...item,
                    [propName]: regress,
                    [`${propName}StdErr1`]: regress + regressStdErr,
                    [`${propName}StdErr2`]: regress + regressStdErr * 2,
                    [`${propName}StdErr3`]: regress - regressStdErr,
                    [`${propName}StdErr4`]: regress - regressStdErr * 2,
                };
                count -= 1;
                return result;
            }
            return item;
        });

        const newSchema = [
            propName,
            `${propName}StdErr1`,
            `${propName}StdErr2`,
            `${propName}StdErr3`,
            `${propName}StdErr4`,
        ].reduce(
            (acc, columnName, i) =>
                addSchema(acc, columnName, i === 0 ? undefined : "6 6", strokeLineColor),
            schema,
        );

        return { data: newData, schema: newSchema };
    }
    return { data, schema };
};

const RegressionChartViewServerBase = ({
    syncId: syncIdProp,
    series,
    regression,
    regression2,
    correlation,
    exposure,
    header,
    ...rest
}) => {
    const { bbgTicker, relativeTicker, dateFrame } = rest;
    const isPair = Boolean(relativeTicker);

    const syncId = useMemo(
        () => syncIdProp ?? `${bbgTicker}-${relativeTicker}-${v4()}`,
        [syncIdProp, bbgTicker, relativeTicker],
    );

    const { data, schema } = useMemo(
        () =>
            [
                ["regress", regression?.data, 1],
                ["regress2", regression2?.data, 2],
            ].reduce(
                (acc, [propName, regData, strokeLineColor]) =>
                    addRegressionLines(acc.data, acc.schema, regData, propName, strokeLineColor),
                {
                    data: series?.data,
                    schema: withSchemaOverrides(series?.schema, regressionSchemaOverride),
                },
            ),
        [series, regression, regression2],
    );

    const rsiSchema = useMemo(
        () => withSchemaOverrides(series?.schema, rsiSchemaOverride({ isPair })),
        [series, isPair],
    );

    const correlationSchema = useMemo(
        () => withSchemaOverrides(correlation?.schema, correlationSchemaOverride),
        [correlation?.schema],
    );

    const exposureData = useMemo(() => {
        if (!data?.length || !exposure?.data?.length) return [];
        const exposureMap = toObjectByProp("dateTime", exposure?.data);
        return data?.map(({ dateTime, rowId }) => ({
            dateTime,
            rowId,
            value: exposureMap[dateTime]?.value || undefined,
        }));
    }, [data, exposure?.data]);

    const exposureSchema = useMemo(
        () => withSchemaOverrides(exposure?.schema, exposureSchemaOverride({ data: exposureData })),
        [exposureData, exposure?.schema],
    );

    const headerSchema = useMemo(
        () => withSchemaOverrides(header?.schema, headerSchemaOverride),
        [header?.schema],
    );

    const renderHeader = ({ name, bbgTickers }: { name: string; bbgTickers?: string[] }) =>
        header?.data && (
            <SchemaValue
                name={name}
                data={header?.data}
                schema={headerSchema}
            >
                {children => (
                    <div className="flex items-center gap-sm">
                        <div className="flex items-center gap-sm">
                            {bbgTickers
                                ? bbgTickers.map((ticker, index) => (
                                      <Fragment key={ticker}>
                                          {index > 0 && <span> vs </span>}
                                          <TickerWithTooltip
                                              bbgTicker={ticker}
                                              disabled={false}
                                          />
                                      </Fragment>
                                  ))
                                : `${dateFrame}M`}
                        </div>
                        <span>{children}</span>
                    </div>
                )}
            </SchemaValue>
        );

    return (
        <div className="flex flex-1 flex-col">
            <div className="flex items-center gap-2xl h-[24px] pb-lg">
                {renderHeader({
                    name: "perf",
                    bbgTickers: [bbgTicker, relativeTicker].filter(Boolean),
                })}
                {renderHeader({ name: "perfTimeRange" })}
            </div>
            <div className="flex flex-1 flex-col">
                <RegressionChart
                    data={data}
                    schema={schema}
                    syncId={syncId}
                    type="price"
                    yLabel={
                        isPair
                            ? `${sanitiseTicker(bbgTicker)} vs ${sanitiseTicker(relativeTicker)}`
                            : sanitiseTicker(bbgTicker)
                    }
                    isPair={isPair}
                    compactWidth={0}
                    compactHeight={0}
                    order={3}
                    {...rest}
                />
            </div>
            <div className="flex flex-col h-[75px]">
                <RegressionChart
                    data={data}
                    schema={rsiSchema}
                    syncId={syncId}
                    yLabel="RSI"
                    type="rsi"
                    isPair={isPair}
                    compactWidth={0}
                    compactHeight={0}
                    order={2}
                    {...rest}
                />
            </div>
            <div className="flex flex-col h-[75px]">
                {isPair ? (
                    <RegressionChart
                        data={correlation?.data}
                        schema={correlationSchema}
                        syncId={syncId}
                        isPair={isPair}
                        showXAxis
                        compactWidth={0}
                        compactHeight={0}
                        order={1}
                        {...rest}
                    />
                ) : (
                    <RegressionChart
                        data={exposureData}
                        schema={exposureSchema}
                        syncId={syncId}
                        isPair={isPair}
                        showXAxis
                        compactWidth={0}
                        compactHeight={0}
                        order={1}
                        {...rest}
                    />
                )}
            </div>
        </div>
    );
};

export const RegressionChartViewServer = withCommandStateFactory(
    RegressionChartViewServerBase,
    ({ relativeTicker }) => {
        const isPair = Boolean(relativeTicker);
        const reportKey = isPair
            ? "RegressionChartRequestHandler_StockVsRelative"
            : "RegressionChartRequestHandler_Stock";
        return createConfig({
            header: reportSubscriptionCommandSpec(
                reportKey,
                ({ bbgTicker, relativeTicker, dateFrame = 0 }) =>
                    bbgTicker ? { bbgTicker, relativeTicker, period: dateFrame } : undefined,
                (data, props, schema) => ({ data: data?.[0], schema }),
                "Header",
            ),
            series: reportSubscriptionCommandSpec(
                reportKey,
                ({ bbgTicker, relativeTicker, dateFrame }) =>
                    bbgTicker ? { bbgTicker, relativeTicker, dateFrame } : undefined,
                (data, props, schema) => ({ data, schema }),
                "Series",
            ),
            regression: reportSubscriptionCommandSpec(
                reportKey,
                ({
                    withRegressionLines = true,
                    bbgTicker,
                    relativeTicker,
                    regressionDateFrame = 0,
                }) =>
                    withRegressionLines && bbgTicker
                        ? { bbgTicker, relativeTicker, dateFrame: regressionDateFrame }
                        : undefined,
                (data, props, schema) => ({ data: data?.[0], schema }),
                "Lines",
            ),
            regression2: reportSubscriptionCommandSpec(
                reportKey,
                ({
                    withRegressionLines = true,
                    withRegressionLine2 = false,
                    bbgTicker,
                    relativeTicker,
                    regressionDateFrame = 0,
                    maxRegressionLookback = 90,
                    minRegressionLookback = 30,
                }) =>
                    withRegressionLines && withRegressionLine2 && bbgTicker
                        ? {
                              bbgTicker,
                              relativeTicker,
                              dateFrame: regressionDateFrame,
                              maxRegressionLookback,
                              minRegressionLookback,
                          }
                        : undefined,
                (data, props, schema) => ({ data: data?.[0], schema }),
                "Lines",
            ),
            correlation: reportSubscriptionCommandSpec(
                "PairsPage_CorrelationCalc",
                ({ bbgTicker, relativeTicker, dateFrame }) =>
                    isPair && bbgTicker
                        ? { ticker: bbgTicker, sellTicker: relativeTicker, dateFrame }
                        : undefined,
                (data, props, schema) => ({ data, schema }),
                "ProjectionNode",
            ),
            exposure: reportSubscriptionCommandSpec(
                "SummaryChartRequestHandler_Exposure",
                ({ bbgTicker, dateFrame, traderId = 350 }) =>
                    !isPair && bbgTicker
                        ? {
                              bbgTicker,
                              dateFrame,
                              traderId,
                              isDivAdjusted: false,
                              includeWorkingOrders: false,
                          }
                        : undefined,
                (data, props, schema) => ({ data, schema }),
                "SummaryChartRequestHandler_Exposure",
            ),
        });
    },
);

export const RegressionDriversCharts = ({
    bbgTicker,
    relativeBbgTicker,
    className,
    dateFrame = 6,
    riskModel = "WW4AxiomaMH",
}: {
    bbgTicker: string;
    relativeBbgTicker?: string;
    className?: string;
    dateFrame?: number;
    riskModel?: string;
}) => {
    return (
        <div className={cn("flex flex-col gap-xs", className)}>
            <div className="flex flex-col bg-container p-lg flex-1">
                <RegressionChartViewServer
                    bbgTicker={bbgTicker}
                    relativeTicker={relativeBbgTicker}
                    dateFrame={dateFrame}
                />
            </div>
            <div className="flex flex-col bg-container p-lg flex-1">
                <DriversChart
                    identifier={bbgTicker}
                    hedgeTicker={relativeBbgTicker}
                    showRollingSpecificPerf
                    params={{ DateFrame: dateFrame, RiskModel: riskModel }}
                />
            </div>
        </div>
    );
};
