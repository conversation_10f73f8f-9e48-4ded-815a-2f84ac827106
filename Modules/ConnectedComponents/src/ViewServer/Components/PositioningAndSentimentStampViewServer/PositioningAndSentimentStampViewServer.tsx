import React, { ComponentProps, useMemo, type ComponentType } from "react";

import { withSchemaOverrides } from "@mocks/api-gateway";
import type { Schema } from "Contracts/index";
import {
    withFilterValues,
    Stamp,
    type StampProps,
    StampTitleWithMark,
    type BrokerChangesData,
    type PositioningData,
    type DivergenceData,
    type ScoreData,
} from "Components/index";
import {
    createConfig,
    withCommandState,
    withConnectedSkeleton,
    reportSubscriptionCommandSpec,
} from "TransportGateway/index";
import { withSkeletonComponentDefinition, SkeletonRect } from "PrimitiveComponents/index";

import { schemaOverride, schemaOverrideScore, schemaOverrideBrokerChanges } from "./schemaOverride";
import { useFeature } from "../../../ApiGateway";

const PositioningAndSentimentStamp = Stamp as ComponentType<
    StampProps & {
        scoreData?: ScoreData[];
        scoreSchema?: Schema;
        brokerChangesData?: BrokerChangesData;
        brokerChangesSchema?: Schema;
        divergenceData?: DivergenceData;
        divergenceSchema?: Schema;
    }
>;

type PositioningAndSentimentStampViewServerBaseProps = {
    positioning?: {
        data?: PositioningData;
        schema?: Schema;
    };
    brokerChanges?: {
        data?: BrokerChangesData;
        schema?: Schema;
    };
    divergence?: {
        data?: DivergenceData;
        schema?: Schema;
    };
    score?: {
        data?: ScoreData[];
        schema?: Schema;
    };
    title?: string;
} & ComponentProps<typeof Stamp>;

const PositioningAndSentimentStampViewServerBase = withSkeletonComponentDefinition(
    ({
        title: titleProp,
        positioning,
        score,
        brokerChanges,
        divergence,
        ...rest
    }: PositioningAndSentimentStampViewServerBaseProps) => {
        const { hasFeature } = useFeature();
        const isDSPHNVersion = hasFeature("dsp.hn-version");

        const title = useMemo(
            () =>
                isDSPHNVersion ? (
                    <StampTitleWithMark
                        title={titleProp}
                        score={score?.data?.[0]?.positioningCheck}
                    />
                ) : (
                    titleProp
                ),
            [titleProp, score, isDSPHNVersion],
        );

        const schema = useMemo(
            () => withSchemaOverrides(positioning?.schema ?? [], schemaOverride),
            [positioning],
        );

        const scoreSchema = useMemo(
            () => withSchemaOverrides(score?.schema ?? [], schemaOverrideScore(score?.data?.[0])),
            [score],
        );

        const brokerChangesSchema = useMemo(
            () => withSchemaOverrides(brokerChanges?.schema ?? [], schemaOverrideBrokerChanges),
            [brokerChanges],
        );

        return (
            <PositioningAndSentimentStamp
                {...rest}
                title={title}
                data={positioning?.data}
                schema={schema}
                scoreData={score?.data}
                scoreSchema={scoreSchema}
                brokerChangesData={brokerChanges?.data}
                brokerChangesSchema={brokerChangesSchema}
                divergenceData={divergence?.data}
                divergenceSchema={divergence?.schema}
            />
        );
    },
)(SkeletonRect);

export const PositioningAndSentimentStampViewServer = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            PositioningAndSentimentStampViewServerBase,
            createConfig({
                positioning: reportSubscriptionCommandSpec(
                    "HN_Cockpit_Positioning_CrowdedScore_SingleTicker",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data: data?.[0], schema }),
                    "CalcNode",
                ),
                score: reportSubscriptionCommandSpec(
                    "HN_Cockpit_Positioning_CrowdedScore_SingleTicker",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data, schema }),
                    "ProjectionNodeWidget",
                ),
                brokerChanges: reportSubscriptionCommandSpec(
                    "HN_Cockpit_Positioning_BrokerChangesSinceFigs",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data: data?.[0], schema }),
                    "GroupByNode",
                ),
                divergence: reportSubscriptionCommandSpec(
                    "HN_Cockpit_Positioning_Divergence",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data: data?.[0], schema }),
                    "ProjectionNode",
                ),
            }),
        ),
    ),
    ["bbgTicker"],
    {
        bbgTicker: {
            required: true,
        },
    },
);
