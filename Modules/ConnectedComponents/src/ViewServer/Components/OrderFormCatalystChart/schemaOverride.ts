export const schemaOverride = {
    overrides: {
        dateTime: {
            properties: {
                chartCartesianGridPropsShow: true,
                chartReferenceLinePropsShow: true,
                chartLegendPropsShow: false,
                chartPropsMargin: { left: -10, right: 50 },

                chartXAxisPropsShow: true,
                chartXAxisPropsFormatterType: "date",
                chartXAxisPropsValueFormat: "DD MMM YY",
                chartXAxisPropsTicksMode: "default",
                chartXAxisPropsInterval: "preserveEnd",
                chartXAxisPropsMinTickGap: 40,
                chartXAxisPropsHeight: 25,
                chartXAxisPropsPadding: { left: 8, right: 0 },

                chartYAxisLeftPropsShow: true,
                chartYAxisLeftPropsFormatterType: "number",
                chartYAxisLeftPropsValueFormat: "numberMax2dp",
                chartYAxisLeftPropsPadding: { top: 30, bottom: 10 },
                chartYAxisLeftPropsDomain: "['dataMin', 'dataMax']",

                chartTooltipPropsShow: true,
                chartTooltipPropsValueColumnName: "dateTime",
                chartTooltipPropsLabelFormatterType: "date",
                chartTooltipPropsLabelValueFormat: "ddd, Do MMM YYYY",
            },
        },
        close: {
            properties: {
                chartType: "line",
                chartSeriesPropsStroke: "var(--charts__series__line__1)",

                chartTooltipPropsDataKey: "close",
                chartTooltipPropsFormatterType: "number",
                chartTooltipPropsValueFormat: "numberMax2dp",

                chartPricePinPropsShow: true,
                chartPricePinPropsComponent: "OrderFormCatalystPin",

                chartBrushPropsShow: true,

                chartPriceMarkerPropsShow: true,
                chartPriceMarkerPropsDataKey: "close",
                chartPriceMarkerPropsDataKeyFallback: "closeFutureLine",
                chartPriceMarkerPropsFormatterType: "number",
                chartPriceMarkerPropsValueFormat: "numberMax2dp",
                chartPriceMarkerPropsMarginLeft: 6,
            },
        },

        closeFutureLine: {
            properties: {
                order: 100,
                chartType: "line",
                chartSeriesPropsStroke: "var(--charts__series__line__1)",
                chartSeriesPropsStrokeDasharray: "4 4",
            },
        },
    },

    schema: [{ columnId: 100, name: "closeFutureLine" }],
};
