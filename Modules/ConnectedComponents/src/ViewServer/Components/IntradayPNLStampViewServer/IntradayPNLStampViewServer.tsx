import React, { useMemo, ComponentProps } from "react";
import moment from "moment";

import { withSchemaOverrides } from "@mocks/api-gateway";
import { Stamp } from "Components/index";
import {
    createConfig,
    withCommandState,
    withConnectedSkeleton,
    reportSubscriptionCommandSpec,
} from "TransportGateway/index";
import { withSkeletonComponentDefinition, SkeletonRect } from "PrimitiveComponents/index";

import { schemaOverride } from "./schemaOverride";

export type IntradayPNLStampViewServerBaseProps = {
    data: Record<string, unknown>[];
    schema: unknown;
} & ComponentProps<typeof Stamp>;
const IntradayPNLStampViewServerBase = withSkeletonComponentDefinition(
    ({ data: dataProp, schema: schemaProp, ...rest }: IntradayPNLStampViewServerBaseProps) => {
        const schema = useMemo(() => withSchemaOverrides(schemaProp, schemaOverride), [schemaProp]);
        const data = useMemo(
            () =>
                dataProp?.map(({ dateTime, ...row }) => {
                    const d = moment(dateTime, "DD/MM/YYYY HH:mm:ss").toString();
                    return { dateTime: d, ...row };
                }),
            [dataProp],
        );

        return (
            <Stamp
                {...rest}
                data={data}
                schema={schema}
            />
        );
    },
)(SkeletonRect);

export const IntradayPNLStampViewServer = withConnectedSkeleton(
    withCommandState(
        IntradayPNLStampViewServerBase,
        createConfig({
            props: reportSubscriptionCommandSpec(
                "Intraday_PNL",
                () => ({ TraderId: "350" }),
                (data, props, schema) => ({ data, schema }),
                "GetPositionSnapshotRequestNode",
            ),
        }),
    ),
);
