export const schemaOverride = {
    ticker: {
        properties: {
            stampComponentName: "Components.IndexDetailsStamp"
        }
    }
};

export const schemaOverrideGrid = {
    ticker: {
        width: "#unset",
        searchable: false,
        properties: {
            flex: 1,
            multiLineHeader: "false"
        }
    },
    weight: {
        width: "#unset",
        searchable: false,
        properties: {
            flex: 1,
            multiLineHeader: "false"
        }
    },
    dTE: {
        width: "#unset",
        searchable: false,
        properties: {
            flex: 1,
            multiLineHeader: "false"
        }
    }
};
