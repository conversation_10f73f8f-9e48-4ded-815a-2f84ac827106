import React, { useMemo } from "react";

import { withSchemaOverrides } from "@mocks/api-gateway";
import {
    createConfig,
    withCommandState,
    withConnectedSkeleton,
    reportSubscriptionCommandSpec
} from "TransportGateway/index";
import { withSkeletonComponentDefinition, SkeletonRect } from "PrimitiveComponents/index";
import { Grid } from "Grid/index";
import type { Schema } from "Contracts/index";
import { withFilterValues } from "Components/index";

import { schemaOverride } from "./schemaOverride";

type CorrelationsExtendedViewServerBaseProps = {
    data?: Record<string, unknown>[];
    schema?: Schema;
};

const CorrelationsExtendedViewServerBase = withSkeletonComponentDefinition(
    ({ data, schema: schemaProp, ...rest }: CorrelationsExtendedViewServerBaseProps) => {
        const schema = useMemo(() => withSchemaOverrides(schemaProp, schemaOverride), [schemaProp]);

        return (
            <Grid
                data={data}
                schema={schema}
                {...rest}
            />
        );
    }
)(SkeletonRect);

export const CorrelationsExtendedViewServer = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            CorrelationsExtendedViewServerBase,
            createConfig({
                props: reportSubscriptionCommandSpec(
                    "hn_cockpit_macro_correlations",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data, schema }),
                    "ProjectionNodePopOut"
                )
            })
        )
    ),
    ["bbgTicker"],
    {
        bbgTicker: {
            required: true
        }
    }
);
