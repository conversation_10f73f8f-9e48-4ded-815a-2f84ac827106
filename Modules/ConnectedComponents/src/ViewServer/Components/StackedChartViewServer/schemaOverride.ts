import { isNumberType } from "Utilities/index";

export const regressionSchemaOverride = {
    dateTime: {
        displayName: "Date",
        properties: {
            chartCartesianGridPropsShow: false,
            chartReferenceLinePropsShow: false,
            chartLegendPropsShow: false,
            chartPropsMargin: { right: 50, left: -36 /*-40*/ },

            chartXAxisPropsShow: "#context(showXAxis)",
            chartXAxisPropsFormatterType: "date",
            chartXAxisPropsTicksMode: "default",
            chartXAxisPropsInterval: "preserveEnd",
            chartXAxisPropsMinTickGap: 0 /*25*/,
            chartXAxisPropsHeight: 40 /*20*/,

            chartYAxisLeftPropsShow: true,
            chartYAxisLeftPropsPadding: { top: 5, bottom: 5 },
            chartYAxisLeftPropsDomain: ["auto", "auto"],
            chartYAxisLeftPropsTick: "null",
            chartYAxisLeftPropsClassName: "hide-line",
            chartYAxisLeftLabelPropsValue: "#context(yLabel)",
            chartYAxisLeftLabelPropsPosition: "insideLeft",
            chartYAxisLeftLabelPropsClassName: "anchor-middle",
            chartYAxisLeftLabelPropsOffset: 43 /*45*/,
            chartYAxisLeftLabelPropsFontSize: "11px",

            chartTooltipPropsValueColumnName: "dateTime",
            chartTooltipPropsLabelFormatterType: "date",
            chartTooltipPropsLabelValueFormat: "ddd, Do MMM YYYY",
            chartTooltipPropsSortBy: "relative, open, high, low, close",
            chartTooltipPropsPosition: '{ "y": "50%" }',
        },
    },
    relative: {
        displayName: "Relative",
        properties: {
            order: 2,
            chartType: "line",
            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStrokeWidth: "2px",
            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "significant3dp",
            chartSeriesPropsStroke: "var(--charts__series__line__1)",

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "relative",
            chartPriceMarkerPropsValueFormat: "significant3dp",
            chartPriceMarkerPropsPadding: 0,
            chartPriceMarkerPropsBorderWidth: 0,
        },
    },
    close: {
        displayName: "Close",
        properties: {
            order: 2,

            chartType: "line",

            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStrokeWidth: "2px",
            chartSeriesPropsStroke: "var(--charts__series__line__1)",

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "significant3dp",
            chartTooltipPropsDisableColor: true,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "close",
            chartPriceMarkerPropsValueFormat: "significant3dp",
            chartPriceMarkerPropsPadding: 0,
            chartPriceMarkerPropsBorderWidth: 0,
        },
    },
    open: {
        displayName: "Open",
        properties: {
            order: 2,

            chartType: "line",

            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStrokeWidth: "0px",
            chartSeriesPropsStroke: "var(--charts__series__line__1)",

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "significant3dp",
            chartTooltipPropsDisableColor: true,
        },
    },
    low: {
        displayName: "Low",
        properties: {
            order: 2,

            chartType: "line",

            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStrokeWidth: "0px",
            chartSeriesPropsStroke: "var(--charts__series__line__1)",

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "significant3dp",
            chartTooltipPropsDisableColor: true,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "low",
            chartPriceMarkerPropsValueFormat: "significant3dp",
            chartPriceMarkerPropsTextColor: "var(--charts__label--color_muted)",
            chartPriceMarkerPropsPadding: 0,
            chartPriceMarkerPropsBorderWidth: 0,
        },
    },
    high: {
        displayName: "High",
        properties: {
            order: 2,

            chartType: "line",

            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStrokeWidth: "0px",
            chartSeriesPropsStroke: "var(--charts__series__line__1)",

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "significant3dp",
            chartTooltipPropsDisableColor: true,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "high",
            chartPriceMarkerPropsValueFormat: "significant3dp",
            chartPriceMarkerPropsTextColor: "var(--charts__label--color_muted)",
            chartPriceMarkerPropsPadding: 0,
            chartPriceMarkerPropsBorderWidth: 0,
        },
    },
    mavg50d: {
        displayName: "MA 50 day",
        properties: {
            order: 3,

            chartType: "line",

            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStroke: "var(--charts__series__line__4)",

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "significant3dp",
            chartTooltipPropsDisableColor: true,
        },
    },
    mavg100d: {
        displayName: "MA 100 day",
        properties: {
            order: 4,

            chartType: "line",

            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStroke: "var(--charts__series__line__5)",

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "significant3dp",
            chartTooltipPropsDisableColor: true,
        },
    },
    mavg200d: {
        displayName: "MA 200 day",
        properties: {
            order: 5,

            chartType: "line",

            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStroke: "var(--charts__series__line__12)",

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "significant3dp",
            chartTooltipPropsDisableColor: true,
        },
    },
};

export const rsiSchemaOverride = ({ isPair }) => ({
    dateTime: {
        displayName: "Date",
        properties: {
            ...regressionSchemaOverride.dateTime.properties,
            chartYAxisLeftLabelPropsValue: "RSI",
            chartYAxisLeftPropsPadding: { top: 0, bottom: 0 },
            chartYAxisLeftPropsDomain: [0, 100],
            chartTooltipPropsSortBy: `${isPair ? "relRsi" : "absRsi"}`,
        },
    },

    absRsi: {
        displayName: "RSI (Absolute)",
        hidden: isPair,
        properties: {
            order: 11,

            chartType: "line",

            chartReferenceLinePropsShow: true,
            chartReferenceLinePropsList: [
                { y: 30, stroke: "var(--charts__series__line__12)" },
                { y: 70, stroke: "var(--charts__series__line__4)" },
            ],

            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStroke: "var(--charts__series__line__1)",

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "number1dp",
            chartTooltipPropsDisableColor: true,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "absRsi",
            chartPriceMarkerPropsValueFormat: "number1dp",
        },
    },
    relRsi: {
        displayName: "RSI (Relative)",
        hidden: !isPair,
        properties: {
            order: 11,

            chartType: "line",

            chartReferenceLinePropsShow: true,
            chartReferenceLinePropsList: [
                { y: 30, stroke: "var(--charts__series__line__12)" },
                { y: 70, stroke: "var(--charts__series__line__4)" },
            ],

            chartSeriesPropsConnectNulls: true,
            chartSeriesPropsStroke: "var(--charts__series__line__1)",

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "number1dp",
            chartTooltipPropsDisableColor: true,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "relRsi",
            chartPriceMarkerPropsValueFormat: "number1dp",
        },
    },
});

export const correlationSchemaOverride = {
    dateTime: {
        displayName: "Date",
        properties: {
            ...regressionSchemaOverride.dateTime.properties,
            chartYAxisLeftLabelPropsValue: "Corr",
            chartTooltipPropsSortBy: `corr3m, corr6m`,
            chartYAxisLeftPropsDomain: ["auto", "auto"],
            chartYAxisLeftPropsPadding: { top: 5, bottom: 5 },
        },
    },
    corr3m: {
        displayName: "3M Correlation",
        properties: {
            chartType: "line",

            chartSeriesPropsStroke: "var(--charts__series__line__2)",
            chartSeriesPropsConnectNulls: true,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "corr3m",
            chartPriceMarkerPropsValueFormat: "number2dp",
            chartPriceMarkerPropsTextColor: "var(--charts__series__line__2)",
        },
    },
    corr6m: {
        displayName: "6M Correlation",
        properties: {
            chartType: "line",

            chartSeriesPropsStroke: "var(--charts__series__line__2__50)",
            chartSeriesPropsConnectNulls: true,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "corr6m",
            chartPriceMarkerPropsValueFormat: "number2dp",
            chartPriceMarkerPropsTextColor: "var(--charts__series__line__2__50)",
        },
    },
};

export const exposureSchemaOverride = ({ data }) => {
    const absMinMax = Math.max(...data.map(d => (isNumberType(d.value) ? Math.abs(d.value) : 0)));
    return {
        dateTime: {
            displayName: "Date",
            properties: {
                ...regressionSchemaOverride.dateTime.properties,
                chartYAxisLeftLabelPropsValue: "Exposure",
                chartTooltipPropsSortBy: `value`,
                chartYAxisLeftPropsPadding: { top: 5, bottom: 5 },
                chartYAxisLeftPropsDomain: `[-${absMinMax}, ${absMinMax}]`,
            },
        },
        value: {
            displayName: "Exposure",
            properties: {
                chartType: "area",

                chartTooltipPropsFormatterType: "bigValue",
                chartTooltipPropsValueFormat: ".2f",

                chartSeriesPropsConnectNulls: false,
                chartSeriesPropsGradientType: "minmax",
            },
        },
    };
};

export const headerSchemaOverride = {
    perf: {
        contentTypeForDisplay: "ChangeValue",
        properties: {
            formatterType: "percent",
            valueFormat: "percent1dp",
        },
    },
    perfTimeRange: {
        contentTypeForDisplay: "ChangeValue",
        properties: {
            formatterType: "percent",
            valueFormat: "percent1dp",
        },
    },
};

export const relativeSchemaOverride = {
    dateTime: {
        displayName: "Date",
        properties: {
            ...regressionSchemaOverride.dateTime.properties,
            chartYAxisLeftLabelPropsValue: "Rel",
            chartTooltipPropsSortBy: `value`,
            chartYAxisLeftPropsDomain: ["auto", "auto"],
            chartYAxisLeftPropsPadding: { top: 5, bottom: 5 },
        },
    },
    value: {
        displayName: "Relative",
        properties: {
            chartType: "line",

            chartSeriesPropsStroke: "var(--charts__series__line__2)",
            chartSeriesPropsConnectNulls: true,

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "significant3dp",
            chartTooltipPropsDisableColor: true,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "value",
            chartPriceMarkerPropsFormatterType: "number",
            chartPriceMarkerPropsValueFormat: "significant3dp",
            chartPriceMarkerPropsTextColor: "var(--charts__series__line__2)",
        },
    },
};

export const specificSchemaOverride = {
    overrides: {
        date: {
            displayName: "Date",
            properties: {
                ...regressionSchemaOverride.dateTime.properties,
                chartTooltipPropsValueColumnName: "date",
                chartYAxisLeftLabelPropsValue: "Spec",
                chartTooltipPropsSortBy: `specificReturnCumulative`,
                chartYAxisLeftPropsDomain: ["auto", "auto"],
                chartYAxisLeftPropsPadding: { top: 5, bottom: 5 },
            },
        },
        specificReturnCumulative: {
            displayName: "Specific Return",
            properties: {
                chartType: "line",

                chartSeriesPropsStroke: "var(--charts__series__line__5)",
                chartSeriesPropsConnectNulls: true,

                chartTooltipPropsFormatterType: "number",
                chartTooltipPropsValueFormat: "percent2dp",
                chartTooltipPropsDisableColor: true,

                chartPriceMarkerPropsShow: true,
                chartPriceMarkerPropsDataKey: "specificReturnCumulative",
                chartPriceMarkerPropsValueFormat: "percent2dp",
                chartPriceMarkerPropsTextColor: "var(--charts__series__line__5)",
            },
        },
    },
    schema: [{ columnId: 100, name: "specificReturnCumulative" }],
};

export const crowdingSchemaOverride = {
    date: {
        displayName: "Date",
        properties: {
            ...regressionSchemaOverride.dateTime.properties,

            chartYAxisLeftLabelPropsOffset: 45,
            chartPropsMargin: {
                right: 50,
                left: -40,
            },

            chartTooltipPropsValueColumnName: "date",
            chartYAxisLeftLabelPropsValue: "Crowd Score",
            chartTooltipPropsSortBy: "netScoreAdjusted, percentile",
        },
    },

    netScoreAdjusted: {
        displayName: "Crowded Score",
        properties: {
            chartType: "line",
            chartSeriesPropsStroke: "var(--charts__series__line__2)",
            chartSeriesPropsConnectNulls: true,

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "number0dp",
            chartTooltipPropsDisableColor: false,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "netScoreAdjusted",
            chartPriceMarkerPropsFormatterType: "number",
            chartPriceMarkerPropsValueFormat: "number0dp",
            chartPriceMarkerPropsTextColor: "var(--charts__series__line__2)",
        },
    },

    percentile: {
        displayName: "Crowded Percentile",
        properties: {
            chartType: "line",
            chartSeriesPropsStroke: "var(--charts__series__line__1)",
            chartSeriesPropsConnectNulls: true,

            chartTooltipPropsFormatterType: "percent",
            chartTooltipPropsValueFormat: "percent2dp1m",
            chartTooltipPropsDisableColor: false,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "percentile",
            chartPriceMarkerPropsFormatterType: "percent",
            chartPriceMarkerPropsValueFormat: "percent2dp1m",
            chartPriceMarkerPropsTextColor: "var(--charts__series__line__1)",

            chartYAxisRightPropsShow: true,
            chartYAxisRightPropsHide: true,
            chartYAxisRightPropsPadding: { top: 5, bottom: 5 },
            chartYAxisRightPropsDataKey: "percentile",
            chartYAxisRightPropsYAxisId: "right-axis",
            chartSeriesPropsYAxisId: "right-axis",
        },
    },
};

export const technicalSchemaOverride = {
    dateTime: {
        displayName: "Date",
        properties: {
            ...regressionSchemaOverride.dateTime.properties,
            chartYAxisLeftLabelPropsValue: "#context(yLabel)",
            chartTooltipPropsSortBy:
                "@calc[eq('#context(type)', 'technicalDistFrom50d') ? 'distFrom50d' : eq('#context(type)', 'technicalRel1mReturn') ? 'rel1mReturn' : 'rel3mReturn']",
            chartYAxisLeftPropsDomain: ["auto", "auto"],
            chartYAxisLeftPropsPadding: { top: 5, bottom: 5 },
            chartYAxisLeftLabelPropsOffset: 45,
            chartPropsMargin: {
                right: 50,
                left: -40,
            },
        },
    },
    distFrom50d: {
        displayName: "Distance from 50d MA in N moves",
        hidden: "@calc[no(eq('#context(type)', 'technicalDistFrom50d'))]",
        properties: {
            chartType: "area",

            chartSeriesPropsGradientType: "minmax",
            chartSeriesPropsConnectNulls: true,

            chartTooltipPropsFormatterType: "number",
            chartTooltipPropsValueFormat: "number2dp",
            chartTooltipPropsDisableColor: false,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "distFrom50d",
            chartPriceMarkerPropsFormatterType: "number",
            chartPriceMarkerPropsValueFormat: "number2dp",
            chartPriceMarkerPropsTextColor: "minmax",
        },
    },
    rel1mReturn: {
        displayName: "1M Rolling Rel Performance",
        hidden: "@calc[no(eq('#context(type)', 'technicalRel1mReturn'))]",
        properties: {
            chartType: "area",

            chartSeriesPropsGradientType: "minmax",
            chartSeriesPropsConnectNulls: true,

            chartTooltipPropsFormatterType: "percent",
            chartTooltipPropsValueFormat: "percent2dp",
            chartTooltipPropsDisableColor: false,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "rel1mReturn",
            chartPriceMarkerPropsFormatterType: "percent",
            chartPriceMarkerPropsValueFormat: "percent2dp",
            chartPriceMarkerPropsTextColor: "minmax",
        },
    },
    rel3mReturn: {
        displayName: "3M Rolling Rel Performance",
        hidden: "@calc[no(eq('#context(type)', 'technicalRel3mReturn'))]",
        properties: {
            chartType: "area",

            chartSeriesPropsGradientType: "minmax",
            chartSeriesPropsConnectNulls: true,

            chartTooltipPropsFormatterType: "percent",
            chartTooltipPropsValueFormat: "percent2dp",
            chartTooltipPropsDisableColor: false,

            chartPriceMarkerPropsShow: true,
            chartPriceMarkerPropsDataKey: "rel3mReturn",
            chartPriceMarkerPropsFormatterType: "percent",
            chartPriceMarkerPropsValueFormat: "percent2dp",
            chartPriceMarkerPropsTextColor: "minmax",
        },
    },
};

export const daysToCoverSchemaOverride = {
    overrides: {
        dateTime: {
            properties: {
                ...regressionSchemaOverride.dateTime.properties,
                chartYAxisLeftPropsDomain: undefined,

                chartYAxisLeftLabelPropsValue: "Days To Cover",
                chartTooltipPropsSortBy: "daysToCover, shortUtil",
                chartYAxisLeftPropsShow: true,
                chartYAxisLeftPropsHide: false,
                chartYAxisLeftLabelPropsOffset: 60,
                chartPropsMargin: {
                    right: 50,
                    left: -40,
                },
            },
        },
        daysToCover: {
            properties: {
                chartSeriesPropsStroke: "var(--charts__series__line__3)",

                chartPriceMarkerPropsShow: true,
                chartPriceMarkerPropsDataKey: "daysToCover",
                chartPriceMarkerPropsFormatterType: "number",
                chartPriceMarkerPropsValueFormat: "number2dp",
                chartPriceMarkerPropsTextColor: "var(--charts__series__line__3)",
            },
        },
        shortUtil: {
            properties: {
                chartYAxisRightPropsHide: true,

                chartSeriesPropsStroke: "var(--charts__series__line__4)",

                chartPriceMarkerPropsShow: true,
                chartPriceMarkerPropsDataKey: "shortUtil",
                chartPriceMarkerPropsFormatterType: "number",
                chartPriceMarkerPropsValueFormat: "number2dp",
                chartPriceMarkerPropsTextColor: "var(--charts__series__line__4)",
            },
        },
    },
};

export const addSchema = (schema, columnName, dashArray, strokeLineColor) => [
    ...schema,
    {
        name: columnName,
        metaData: {
            properties: {
                order: 3,
                chartType: "line",
                chartSeriesPropsConnectNulls: true,
                chartSeriesPropsStroke: `var(--charts__series__line__${strokeLineColor})`,
                chartTooltipPropsFormatterType: "number",
                chartTooltipPropsValueFormat: "number2dp",
                ...(dashArray && { chartSeriesPropsStrokeDasharray: dashArray }),
            },
        },
    },
];

export const addRegressionLines = (data, schema, regression, propName, strokeLineColor) => {
    if (data?.length > 0 && schema?.length > 0 && regression) {
        const { regressCurrent, regressSlope, regressLength, regressStdErr } = regression;
        let count = Math.min(regressLength, data.length);
        const first = data.length - count;
        const newData = data.map((item, i) => {
            if (i >= first) {
                const regress = regressCurrent - regressSlope * count;
                const result = {
                    ...item,
                    [propName]: regress,
                    [`${propName}StdErr1`]: regress + regressStdErr,
                    [`${propName}StdErr2`]: regress + regressStdErr * 2,
                    [`${propName}StdErr3`]: regress - regressStdErr,
                    [`${propName}StdErr4`]: regress - regressStdErr * 2,
                };
                count -= 1;
                return result;
            }
            return item;
        });

        const newSchema = [
            propName,
            `${propName}StdErr1`,
            `${propName}StdErr2`,
            `${propName}StdErr3`,
            `${propName}StdErr4`,
        ].reduce(
            (acc, columnName, i) =>
                addSchema(acc, columnName, i === 0 ? undefined : "6 6", strokeLineColor),
            schema,
        );

        return { data: newData, schema: newSchema };
    }
    return { data, schema };
};
