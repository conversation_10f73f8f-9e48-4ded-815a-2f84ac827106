import React, { ComponentType, useMemo, useCallback, Fragment } from "react";
import { v4 } from "uuid";
import moment from "moment";

import { withSchemaOverrides } from "@mocks/api-gateway";
import { Chart, ChartProps } from "Charting/index";
import { sanitiseTicker, cn, toObjectByProp, isIndex } from "Utilities/index";
import {
    createConfig,
    reportSubscriptionCommandSpec,
    withCommandState,
    withCommandStateFactory,
    apiGatewaySpec,
} from "TransportGateway/index";
import { SchemaValue } from "Schema/index";
import { TickerWithTooltip } from "PrimitiveComponents/index";
import { DriversChart } from "ComponentLibrary/index";
import type { Schema, ApiGatewayService } from "Contracts/index";
import { withFilterValues } from "Components/index";

import {
    regressionSchemaOverride,
    rsiSchemaOverride,
    correlationSchemaOverride,
    exposureSchemaOverride,
    headerSchemaOverride,
    relativeSchemaOverride,
    specificSchemaOverride,
    addRegressionLines,
    crowdingSchemaOverride,
    technicalSchemaOverride,
    daysToCoverSchemaOverride,
} from "./schemaOverride";

const StackedRawChart = Chart as ComponentType<
    ChartProps & {
        type?: "price" | "rsi";
        isPair?: boolean;
        yLabel?: string;
        showXAxis?: boolean;
    }
>;

/** ~~~ utils
 */
export const cumulativeProduct = (initialValue: number) => {
    let product = initialValue;
    return (value: number | null) => {
        if (value === null) return null;
        product *= value;
        return product;
    };
};

export const toCumilativeSeries = <K extends string, T extends Record<K, number | null>>(
    data: T[],
    dataKey: K,
) => {
    const cumProd = data.map(d => d[dataKey]).map(cumulativeProduct(1));
    return cumProd.map((res, i) => ({
        ...data[i],
        [`${dataKey}Cumulative`]: res !== null ? res - 1 : res,
    })) as Array<T & Record<`${K}Cumulative`, number | null>>;
};

export const filterDataByDates = <K extends string, T extends Record<K, string>>(
    dateKey: K,
    datesFilter: string[],
    data: T[],
): T[] => {
    const riskAttributionDataMap: Record<string, T> = toObjectByProp(dateKey, data);
    return datesFilter.map(date => riskAttributionDataMap[date] ?? ({ [dateKey]: date } as T));
};

/** ~~~ useExposureChart
 */
const useExposureChart = ({ syncId, rest, exposure, chartTypes, datesFilter }) => {
    const hasExposure = chartTypes.includes("exposure");
    const isLast = chartTypes[chartTypes.length - 1] === "exposure";

    const exposureData = useMemo(() => {
        if (!hasExposure) return [];
        if (!exposure?.data?.length || !datesFilter?.length) return [];
        return filterDataByDates(
            "dateTime",
            datesFilter,
            exposure.data as { dateTime: string; value: number }[],
        ).map(d => (d.value === 0 ? { ...d, value: undefined } : d));
    }, [exposure?.data, hasExposure, datesFilter]);

    const exposureSchema = useMemo(() => {
        if (!hasExposure) return [];
        return withSchemaOverrides(
            exposure?.schema,
            exposureSchemaOverride({ data: exposureData }),
        );
    }, [exposureData, exposure?.schema, hasExposure]);

    const exposureChart = useMemo(() => {
        if (!hasExposure) return null;
        return (
            <StackedRawChart
                data={exposureData}
                schema={exposureSchema}
                syncId={syncId}
                showXAxis={isLast}
                compactWidth={0}
                compactHeight={0}
                order={chartTypes.length - chartTypes.indexOf("exposure")}
                {...rest}
            />
        );
    }, [exposureData, exposureSchema, hasExposure, syncId, rest, isLast]);

    return { exposureChart };
};

/** ~~~ useCrowdingChart
 */
const useCrowdingChart = ({ crowding, chartTypes, syncId, rest, datesFilter }) => {
    const hasCrowding = chartTypes.includes("crowding");
    const isLast = chartTypes[chartTypes.length - 1] === "crowding";

    const crowdingData = useMemo(() => {
        if (!hasCrowding) return [];
        if (!crowding?.data?.length || !datesFilter?.length) return [];
        return filterDataByDates(
            "date",
            datesFilter,
            crowding.data.map(d => ({
                ...d,
                date: moment(d.date, "YYYY-MM-DD-HH.mm.ss").format("YYYY-MM-DD"),
            })),
        );
    }, [crowding?.data, hasCrowding, datesFilter]);

    const crowdingSchema = useMemo(() => {
        if (!hasCrowding) return [];
        return withSchemaOverrides(crowding?.schema, crowdingSchemaOverride);
    }, [crowding?.schema, hasCrowding]);

    const crowdingChart = useMemo(() => {
        if (!hasCrowding) return null;
        return (
            <StackedRawChart
                data={crowdingData}
                schema={crowdingSchema}
                syncId={syncId}
                showXAxis={isLast}
                compactWidth={0}
                compactHeight={0}
                order={chartTypes.length - chartTypes.indexOf("crowding")}
                {...rest}
            />
        );
    }, [crowdingData, crowdingSchema, hasCrowding, syncId, rest, isLast]);

    return { crowdingChart };
};

/** ~~~ useDaysToCoverChart
 */
const useTechnicalChart = ({ technical, chartTypes, syncId, rest }) => {
    const hasTechnical =
        chartTypes.includes("technicalDistFrom50d") ||
        chartTypes.includes("technicalRel1mReturn") ||
        chartTypes.includes("technicalRel3mReturn");

    const technicalData = useMemo(() => {
        if (!hasTechnical) return [];
        if (!technical?.data?.length) return [];
        return technical?.data;
    }, [technical?.data, hasTechnical]);

    const technicalSchema = useMemo(() => {
        if (!hasTechnical) return [];
        return withSchemaOverrides(technical?.schema, technicalSchemaOverride);
    }, [technical?.schema, hasTechnical]);

    const technicalDistFrom50dChart = useMemo(() => {
        const hasTechnicalDistFrom50d = chartTypes.includes("technicalDistFrom50d");
        if (!hasTechnicalDistFrom50d) return null;
        const isLast = chartTypes[chartTypes.length - 1] === "technicalDistFrom50d";
        return (
            <StackedRawChart
                data={technicalData}
                schema={technicalSchema}
                syncId={syncId}
                showXAxis={isLast}
                compactWidth={0}
                compactHeight={0}
                order={chartTypes.length - chartTypes.indexOf("technicalDistFrom50d")}
                yLabel="Dist 50d"
                type="technicalDistFrom50d"
                {...rest}
            />
        );
    }, [technicalData, technicalSchema, hasTechnical, syncId, rest, chartTypes]);

    const technicalRel1mReturnChart = useMemo(() => {
        const hasTechnicalRel1mReturn = chartTypes.includes("technicalRel1mReturn");
        if (!hasTechnicalRel1mReturn) return null;
        const isLast = chartTypes[chartTypes.length - 1] === "technicalRel1mReturn";
        return (
            <StackedRawChart
                data={technicalData}
                schema={technicalSchema}
                syncId={syncId}
                showXAxis={isLast}
                compactWidth={0}
                compactHeight={0}
                order={chartTypes.length - chartTypes.indexOf("technicalRel1mReturn")}
                yLabel="1M Return"
                type="technicalRel1mReturn"
                {...rest}
            />
        );
    }, [technicalData, technicalSchema, hasTechnical, syncId, rest, chartTypes]);

    const technicalRel3mReturnChart = useMemo(() => {
        const hasTechnicalRel3mReturn = chartTypes.includes("technicalRel3mReturn");
        if (!hasTechnicalRel3mReturn) return null;
        const isLast = chartTypes[chartTypes.length - 1] === "technicalRel3mReturn";
        return (
            <StackedRawChart
                data={technicalData}
                schema={technicalSchema}
                syncId={syncId}
                showXAxis={isLast}
                compactWidth={0}
                compactHeight={0}
                order={chartTypes.length - chartTypes.indexOf("technicalRel3mReturn")}
                yLabel="3M Return"
                type="technicalRel3mReturn"
                {...rest}
            />
        );
    }, [technicalData, technicalSchema, hasTechnical, syncId, rest, chartTypes]);

    return { technicalDistFrom50dChart, technicalRel1mReturnChart, technicalRel3mReturnChart };
};

/** ~~~ useDaysToCoverChart
 */
const useDaysToCoverChart = ({ daysToCover, chartTypes, syncId, rest }) => {
    const hasDaysToCover = chartTypes.includes("daysToCover");
    const isLast = chartTypes[chartTypes.length - 1] === "daysToCover";

    const daysToCoverData = useMemo(() => {
        if (!hasDaysToCover) return [];
        if (!daysToCover?.data?.length /*|| !datesFilter?.length*/) return [];
        // return filterDataByDates("dateTime", datesFilter, daysToCover.data);
        return daysToCover?.data;
    }, [daysToCover?.data, hasDaysToCover /*, datesFilter*/]);

    const daysToCoverSchema = useMemo(() => {
        if (!hasDaysToCover) return [];
        return withSchemaOverrides(daysToCover?.schema, daysToCoverSchemaOverride);
    }, [daysToCover?.schema, hasDaysToCover]);

    const daysToCoverChart = useMemo(() => {
        if (!hasDaysToCover) return null;
        return (
            <StackedRawChart
                data={daysToCoverData}
                schema={daysToCoverSchema}
                syncId={syncId}
                showXAxis={isLast}
                compactWidth={0}
                compactHeight={0}
                order={chartTypes.length - chartTypes.indexOf("daysToCover")}
                {...rest}
            />
        );
    }, [daysToCoverData, daysToCoverSchema, hasDaysToCover, syncId, rest, isLast]);

    const daysToCoverDatesFilter = useMemo(() => {
        if (!daysToCover?.data?.length) return [];
        return daysToCover.data.map(d => d.dateTime);
    }, [daysToCover?.data]);

    return { daysToCoverChart, daysToCoverDatesFilter };
};

/** ~~~ useSpecificChart
 */
const useSpecificChart = ({ riskAttribution, chartTypes, syncId, rest, datesFilter }) => {
    const hasSpecific = chartTypes.includes("specific");
    const isLast = chartTypes[chartTypes.length - 1] === "specific";

    const specificData = useMemo(() => {
        if (!hasSpecific) return [];
        if (!riskAttribution?.data?.length || !datesFilter?.length) return [];

        const dataWithCumulative = toCumilativeSeries(
            riskAttribution?.data as {
                date: string;
                specificReturn: number;
            }[],
            "specificReturn",
        );

        return filterDataByDates("date", datesFilter, dataWithCumulative);
    }, [riskAttribution?.data, hasSpecific, datesFilter]);

    const specificSchema = useMemo(() => {
        if (!hasSpecific) return [];
        return withSchemaOverrides(riskAttribution?.schema, specificSchemaOverride);
    }, [riskAttribution?.schema, hasSpecific]);

    const specificChart = useMemo(() => {
        if (!hasSpecific) return null;
        return (
            <StackedRawChart
                data={specificData}
                schema={specificSchema}
                syncId={syncId}
                showXAxis={isLast}
                compactWidth={0}
                compactHeight={0}
                order={chartTypes.length - chartTypes.indexOf("specific")}
                {...rest}
            />
        );
    }, [specificSchema, hasSpecific, syncId, rest, isLast, specificData]);

    return { specificChart };
};

/** ~~~ useRsiChart
 */
const useRsiChart = ({ syncId, rest, data, series, isPair, chartTypes }) => {
    const hasRsi = chartTypes.includes("rsi");
    const isLast = chartTypes[chartTypes.length - 1] === "rsi";

    const rsiSchema = useMemo(() => {
        if (!hasRsi) return [];
        return withSchemaOverrides(series?.schema, rsiSchemaOverride({ isPair }));
    }, [series?.schema, isPair, hasRsi]);

    const rsiChart = useMemo(() => {
        if (!hasRsi) return null;
        if (!data?.length) return null;

        return (
            <StackedRawChart
                data={data}
                schema={rsiSchema}
                syncId={syncId}
                yLabel="RSI"
                type="rsi"
                isPair={isPair}
                showXAxis={isLast}
                compactWidth={0}
                compactHeight={0}
                order={chartTypes.length - chartTypes.indexOf("rsi")}
                {...rest}
            />
        );
    }, [data, rsiSchema, isPair, hasRsi, syncId, rest, isLast]);

    return { rsiChart };
};

/** ~~~ usePriceChart
 */
const usePriceChart = ({ syncId, rest, series, isPair, chartTypes, regression, regression2 }) => {
    const hasPriceOrRsi = chartTypes.includes("price") || chartTypes.includes("rsi");
    const isLast = chartTypes[chartTypes.length - 1] === "price";

    const { data, schema } = useMemo(
        () =>
            [
                ["regress", regression?.data, "1__50"],
                ["regress2", regression2?.data, "2__50"],
            ].reduce(
                (acc, [propName, regData, strokeLineColor]) =>
                    addRegressionLines(acc.data, acc.schema, regData, propName, strokeLineColor),
                {
                    data: series?.data,
                    schema: withSchemaOverrides(series?.schema, regressionSchemaOverride),
                },
            ),
        [series, regression, regression2],
    );

    const priceChart = useMemo(() => {
        if (!hasPriceOrRsi) return null;
        if (!data?.length) return null;

        const { bbgTicker, relativeTicker } = rest;

        return (
            <StackedRawChart
                data={data}
                schema={schema}
                syncId={syncId}
                type="price"
                yLabel={
                    isPair
                        ? `${sanitiseTicker(bbgTicker)} vs ${sanitiseTicker(relativeTicker)}`
                        : sanitiseTicker(bbgTicker)
                }
                isPair={isPair}
                showXAxis={isLast}
                compactWidth={0}
                compactHeight={0}
                order={chartTypes.length - chartTypes.indexOf("price")}
                {...rest}
            />
        );
    }, [data, schema, isPair, hasPriceOrRsi, syncId, rest, isLast]);

    const priceDatesFilter = useMemo(() => {
        if (!series?.data?.length) return [];
        return series.data.map(d => d.dateTime);
    }, [series?.data]);

    return { priceChart, data, priceDatesFilter };
};

/** ~~~ useCorrelationHeader
 */
const useCorrelationHeader = ({ correlationHeader, headerTypes, rest }) => {
    const hasCorrelation = headerTypes?.includes("correlation");

    const { dateFrame } = rest;

    const headerSchema = useMemo(() => {
        if (!hasCorrelation) return [];
        return withSchemaOverrides(correlationHeader?.schema, headerSchemaOverride);
    }, [correlationHeader?.schema]);

    const renderHeader = useCallback(
        ({ name, bbgTickers }: { name: string; bbgTickers?: string[] }) =>
            correlationHeader?.data && (
                <SchemaValue
                    name={name}
                    data={correlationHeader?.data}
                    schema={headerSchema}
                >
                    {children => (
                        <div className="flex items-center gap-sm">
                            <div className="flex items-center gap-sm">
                                {bbgTickers
                                    ? bbgTickers.map((ticker, index) => (
                                          <Fragment key={ticker}>
                                              {index > 0 && <span> vs </span>}
                                              <TickerWithTooltip
                                                  bbgTicker={ticker}
                                                  disabled={false}
                                              />
                                          </Fragment>
                                      ))
                                    : `${dateFrame}M`}
                            </div>
                            <span>{children}</span>
                        </div>
                    )}
                </SchemaValue>
            ),
        [correlationHeader?.data, dateFrame],
    );

    const correlationHeaderEl = useMemo(() => {
        if (!hasCorrelation) return null;
        const { bbgTicker, relativeTicker } = rest;
        return (
            <div className="flex items-center gap-2xl h-[24px]">
                {renderHeader({
                    name: "perf",
                    bbgTickers: [bbgTicker, relativeTicker].filter(Boolean),
                })}
                {renderHeader({ name: "perfTimeRange" })}
            </div>
        );
    }, [renderHeader, rest]);

    return { correlationHeaderEl };
};

/** ~~~ CorrelationChartViewServer
 */
const CorrelationChart = ({ correlation, datesFilter, ...rest }) => {
    const data = useMemo(() => {
        if (!correlation?.data?.length || !datesFilter?.length) return [];
        return filterDataByDates("dateTime", datesFilter, correlation.data);
    }, [correlation?.data, datesFilter]);

    const schema = useMemo(
        () => withSchemaOverrides(correlation?.schema, correlationSchemaOverride),
        [correlation?.schema],
    );

    return (
        <StackedRawChart
            data={data}
            schema={schema}
            compactWidth={0}
            compactHeight={0}
            {...rest}
        />
    );
};
const CorrelationChartViewServer = withCommandStateFactory(CorrelationChart, () =>
    createConfig({
        correlation: reportSubscriptionCommandSpec(
            "PairsPage_CorrelationCalc",
            ({ bbgTicker, relativeTicker, dateFrame }) =>
                bbgTicker
                    ? { ticker: bbgTicker, sellTicker: relativeTicker, dateFrame }
                    : undefined,
            (data, props, schema) => ({ data, schema }),
            "ProjectionNode",
        ),
    }),
);

/** ~~~ RelativeChartViewServer
 */
const RelativeChart = ({ relative, datesFilter, ...rest }) => {
    const data = useMemo(() => {
        if (!relative?.data?.length || !datesFilter?.length) return [];
        return filterDataByDates("dateTime", datesFilter, relative.data);
    }, [relative?.data, datesFilter]);

    const schema = useMemo(
        () => withSchemaOverrides(relative?.schema ?? [], relativeSchemaOverride),
        [relative?.schema],
    );

    return (
        <StackedRawChart
            data={data}
            schema={schema}
            compactWidth={0}
            compactHeight={0}
            {...rest}
        />
    );
};
const RelativeChartViewServer = withCommandStateFactory(RelativeChart, () =>
    createConfig({
        relative: reportSubscriptionCommandSpec(
            "SummaryChartRequestHandler_Relative",
            ({ bbgTicker, dateFrame = 6 }) =>
                bbgTicker && {
                    bbgTicker,
                    dateFrame,
                },
            (data, _props, schema) => ({ data, schema }),
        ),
    }),
);

/** ~~~ StackedChartViewServer
 */
type ChartTypes =
    | "price"
    | "rsi"
    | "correlation"
    | "exposure"
    | "relative"
    | "specific"
    | "crowding"
    | "daysToCover"
    | "technicalDistFrom50d"
    | "technicalRel1mReturn"
    | "technicalRel3mReturn";
type HeaderTypes = "correlation";

type StackedChartViewServerBaseProps = {
    syncId?: string;

    chartTypes: ChartTypes[];
    headerTypes: HeaderTypes[];

    correlationHeader?: {
        data?: Record<string, unknown>;
        schema?: Schema;
    };

    series?: {
        data?: Record<string, unknown>[];
        schema?: Schema;
    };
    regression?: {
        data?: Record<string, unknown>[];
        schema?: Schema;
    };
    regression2?: {
        data?: Record<string, unknown>[];
        schema?: Schema;
    };

    correlation?: {
        data?: Record<string, unknown>;
        schema?: Schema;
    };
    exposure?: {
        data?: Record<string, unknown>[];
        schema?: Schema;
    };
    riskAttribution?: {
        data?: Record<string, unknown>[];
        schema?: Schema;
    };
    crowding?: {
        data?: Record<string, unknown>[];
        schema?: Schema;
    };
    daysToCover?: {
        data?: Record<string, unknown>[];
        schema?: Schema;
    };
    technical?: {
        data?: Record<string, unknown>[];
        schema?: Schema;
    };

    bbgTicker: string;
    relativeTicker?: string;
    dateFrame?: number;
    primaryCharts?: ChartTypes[];
    primaryChartHeightMultiplier?: number;
};
const StackedChartViewServerBase = ({
    syncId: syncIdProp,

    chartTypes,
    headerTypes,

    correlationHeader,

    series,
    regression,
    regression2,

    correlation,
    exposure,
    riskAttribution,
    crowding,
    daysToCover,
    technical,

    primaryCharts = ["price"],
    primaryChartHeightMultiplier = 1.75,

    ...rest
}: StackedChartViewServerBaseProps) => {
    console.log(`[e-log] [StackedChartViewServerBase]`, {
        correlationHeader,

        series,
        regression,
        regression2,

        correlation,
        exposure,
        riskAttribution,
        crowding,
        daysToCover,
        technical,
    });

    const { bbgTicker, relativeTicker } = rest;
    const isPair = Boolean(relativeTicker);

    const syncId = useMemo(
        () => syncIdProp ?? `${bbgTicker}-${relativeTicker}-${v4()}`,
        [syncIdProp, bbgTicker, relativeTicker],
    );

    const { correlationHeaderEl } = useCorrelationHeader({ correlationHeader, headerTypes, rest });

    const { priceChart, priceDatesFilter, data } = usePriceChart({
        isPair,
        chartTypes,
        syncId,
        rest,
        series,
        regression,
        regression2,
    });

    const { rsiChart } = useRsiChart({
        data,
        isPair,
        chartTypes,
        syncId,
        rest,
        series,
    });
    const { exposureChart } = useExposureChart({
        exposure,
        chartTypes,
        syncId,
        rest,
        datesFilter: priceDatesFilter,
    });
    const { specificChart } = useSpecificChart({
        riskAttribution,
        chartTypes,
        syncId,
        rest,
        datesFilter: priceDatesFilter,
    });

    const { daysToCoverChart, daysToCoverDatesFilter } = useDaysToCoverChart({
        daysToCover,
        chartTypes,
        syncId,
        rest,
    });
    const { crowdingChart } = useCrowdingChart({
        crowding,
        chartTypes,
        syncId,
        rest,
        datesFilter: daysToCoverDatesFilter,
    });

    const { technicalDistFrom50dChart, technicalRel1mReturnChart, technicalRel3mReturnChart } =
        useTechnicalChart({
            technical,
            chartTypes,
            syncId,
            rest,
        });

    const numberOfParts = chartTypes.reduce(
        (acc, chartType) =>
            acc + (primaryCharts.includes(chartType) ? primaryChartHeightMultiplier : 1),
        0,
    );

    return (
        <div className="flex flex-1 flex-col">
            <div>
                {headerTypes?.map(h => {
                    if (h === "correlation") {
                        return (
                            <div
                                key={h}
                                className="content"
                            >
                                {correlationHeaderEl}
                            </div>
                        );
                    }
                })}
            </div>

            <div className="flex flex-col flex-1">
                {chartTypes.map((chartType, index) => {
                    const order = chartTypes.length - index;

                    const isLast = index === chartTypes.length - 1;
                    const lastModifier = isLast
                        ? `+ ${(40 / chartTypes.length) * (chartTypes.length - 1)}`
                        : `- ${40 / chartTypes.length}`;

                    const isPrimaryChart = primaryCharts.includes(chartType);
                    const primaryChartModifier =
                        (100 / numberOfParts) * (isPrimaryChart ? primaryChartHeightMultiplier : 1);

                    const style = { height: `calc(${primaryChartModifier}% ${lastModifier}px)` };

                    const className = "flex flex-col";

                    switch (chartType) {
                        case "price":
                            return (
                                <div
                                    key={chartType}
                                    className={className}
                                    style={style}
                                >
                                    {priceChart}
                                </div>
                            );
                        case "rsi":
                            return (
                                <div
                                    key={chartType}
                                    className={className}
                                    style={style}
                                >
                                    {rsiChart}
                                </div>
                            );
                        case "correlation":
                            return (
                                <div
                                    key={chartType}
                                    className={className}
                                    style={style}
                                >
                                    <CorrelationChartViewServer
                                        syncId={syncId}
                                        showXAxis={isLast}
                                        datesFilter={priceDatesFilter}
                                        order={order}
                                        {...rest}
                                    />
                                </div>
                            );
                        case "exposure":
                            return (
                                <div
                                    key={chartType}
                                    className={className}
                                    style={style}
                                >
                                    {exposureChart}
                                </div>
                            );
                        case "relative":
                            return (
                                <div
                                    key={chartType}
                                    className={className}
                                    style={style}
                                >
                                    <RelativeChartViewServer
                                        syncId={syncId}
                                        showXAxis={isLast}
                                        datesFilter={priceDatesFilter}
                                        order={order}
                                        {...rest}
                                    />
                                </div>
                            );
                        case "specific":
                            return (
                                <div
                                    key={chartType}
                                    className={className}
                                    style={style}
                                >
                                    {specificChart}
                                </div>
                            );
                        case "crowding":
                            return (
                                <div
                                    key={chartType}
                                    className={className}
                                    style={style}
                                >
                                    {crowdingChart}
                                </div>
                            );
                        case "daysToCover":
                            return (
                                <div
                                    key={chartType}
                                    className={className}
                                    style={style}
                                >
                                    {daysToCoverChart}
                                </div>
                            );
                        case "technicalDistFrom50d":
                            return (
                                <div
                                    key={chartType}
                                    className={className}
                                    style={style}
                                >
                                    {technicalDistFrom50dChart}
                                </div>
                            );
                        case "technicalRel1mReturn":
                            return (
                                <div
                                    key={chartType}
                                    className={className}
                                    style={style}
                                >
                                    {technicalRel1mReturnChart}
                                </div>
                            );
                        case "technicalRel3mReturn":
                            return (
                                <div
                                    key={chartType}
                                    className={className}
                                    style={style}
                                >
                                    {technicalRel3mReturnChart}
                                </div>
                            );
                        default:
                            throw new Error(
                                `[StackedChartViewServer] Chart type "${chartType}" does not exist`,
                            );
                    }
                })}
            </div>
        </div>
    );
};

type StackedChartViewServerProps = {
    bbgTicker?: string;
    relativeTicker?: string;
    dateFrame?: number;
    chartTypes?: ChartTypes[];
    headerTypes?: HeaderTypes[];
};
export const StackedChartViewServer = withFilterValues(
    withCommandStateFactory(
        StackedChartViewServerBase,
        ({
            bbgTicker,
            relativeTicker,
            chartTypes,
            headerTypes,
            dateFrame = 6,
        }: StackedChartViewServerProps) => {
            const isPair = Boolean(relativeTicker);
            const reportKey = isPair
                ? "RegressionChartRequestHandler_StockVsRelative"
                : "RegressionChartRequestHandler_Stock";
            const hasPriceOrRsi = chartTypes.includes("price") || chartTypes.includes("rsi");
            const hasExposure = chartTypes.includes("exposure");
            const hasSpecific = chartTypes.includes("specific");
            const hasCrowding = chartTypes.includes("crowding");
            const hasDaysToCover = chartTypes.includes("daysToCover");
            const hasTechnical =
                chartTypes.includes("technicalDistFrom50d") ||
                chartTypes.includes("technicalRel1mReturn") ||
                chartTypes.includes("technicalRel3mReturn");

            const hasCorrelationHeader = headerTypes.includes("correlation");

            return createConfig({
                correlationHeader: reportSubscriptionCommandSpec(
                    reportKey,
                    () =>
                        hasCorrelationHeader && bbgTicker
                            ? { bbgTicker, relativeTicker, dateFrame }
                            : undefined,
                    (data, props, schema) => ({ data: data?.[0], schema }),
                    "Header",
                ),
                series: reportSubscriptionCommandSpec(
                    reportKey,
                    () =>
                        hasPriceOrRsi && bbgTicker
                            ? { bbgTicker, relativeTicker, dateFrame }
                            : undefined,
                    (data, props, schema) => ({ data, schema }),
                    "Series",
                ),
                regression: reportSubscriptionCommandSpec(
                    reportKey,
                    ({ withRegressionLines = true, regressionDateFrame = 0 }) =>
                        hasPriceOrRsi && withRegressionLines && bbgTicker
                            ? { bbgTicker, relativeTicker, dateFrame: regressionDateFrame }
                            : undefined,
                    (data, props, schema) => ({ data: data?.[0], schema }),
                    "Lines",
                ),
                regression2: reportSubscriptionCommandSpec(
                    reportKey,
                    ({
                        withRegressionLines = true,
                        withRegressionLine2 = false,
                        regressionDateFrame = 0,
                        maxRegressionLookback = 90,
                        minRegressionLookback = 30,
                    }) =>
                        hasPriceOrRsi && withRegressionLines && withRegressionLine2 && bbgTicker
                            ? {
                                  bbgTicker,
                                  relativeTicker,
                                  dateFrame: regressionDateFrame,
                                  maxRegressionLookback,
                                  minRegressionLookback,
                              }
                            : undefined,
                    (data, props, schema) => ({ data: data?.[0], schema }),
                    "Lines",
                ),
                exposure: reportSubscriptionCommandSpec(
                    "SummaryChartRequestHandler_Exposure",
                    ({ traderId = 350 }) =>
                        hasExposure && bbgTicker
                            ? {
                                  bbgTicker,
                                  dateFrame,
                                  traderId,
                                  isDivAdjusted: false,
                                  includeWorkingOrders: false,
                              }
                            : undefined,
                    (data, props, schema) => ({ data, schema }),
                    "SummaryChartRequestHandler_Exposure",
                ),
                riskAttribution: apiGatewaySpec<
                    Partial<ApiGatewayService.CompanyPage.RiskAttributionRequest>,
                    ApiGatewayService.CompanyPage.RiskAttributionResponse
                >(
                    "companypage.risk-attribution",
                    ({ riskModel = "EU4AxiomaMH" }) =>
                        hasSpecific && {
                            bbgTicker,
                            dateFrame,
                            riskModel,
                        },
                    (data, props, schema) => ({ data, schema }),
                ),

                crowding: reportSubscriptionCommandSpec(
                    "JPM_Single_Stock_Crowding_SingleStock",
                    () =>
                        hasCrowding && bbgTicker
                            ? { BBGTicker: bbgTicker, dateFrame: 126 }
                            : undefined,
                    (data, props, schema) => ({ data, schema }),
                    "ProjectionNode2",
                ),

                daysToCover: reportSubscriptionCommandSpec(
                    isIndex(bbgTicker)
                        ? "Index_DaysToCoverQueryEngineRequest_IFundamentalSchema"
                        : "DaysToCoverRequestHandler",
                    ({ bbgTicker, dateFrame }) =>
                        hasDaysToCover && bbgTicker
                            ? { BBGTicker: bbgTicker, dateFrame }
                            : undefined,
                    (data, _, schema) => ({ data, schema }),
                    isIndex(bbgTicker) ? "ProjectionNode" : "DaysToCoverVSShortUtil",
                ),

                // specialDates: specialDates(),

                technical: reportSubscriptionCommandSpec(
                    "TechnicalChartData",
                    ({ bbgTicker, dateFrame }) =>
                        hasTechnical && bbgTicker
                            ? {
                                  BbgTicker: bbgTicker,
                                  DateFrame: dateFrame,
                              }
                            : undefined,
                    (data, props, schema) => ({ data, schema }),
                    "FetchData",
                ),
            });
        },
    ),
    ["bbgTicker"],
    {
        bbgTicker: {
            required: true,
        },
    },
);

/** ~~~ StackedAndDriversCharts
 */
export const StackedAndDriversCharts = ({
    bbgTicker,
    relativeBbgTicker,
    className,
    dateFrame = 6,
    riskModel = "WW4AxiomaMH",
}: {
    bbgTicker: string;
    relativeBbgTicker?: string;
    className?: string;
    dateFrame?: number;
    riskModel?: string;
}) => {
    const isPair = Boolean(relativeBbgTicker);
    return (
        <div className={cn("flex flex-col gap-xs", className)}>
            <div className="flex flex-col bg-container p-lg flex-1">
                <StackedChartViewServer
                    bbgTicker={bbgTicker}
                    relativeTicker={relativeBbgTicker}
                    dateFrame={dateFrame}
                    chartTypes={["price", "rsi", isPair ? "correlation" : "exposure"]}
                    headerTypes={["correlation"]}
                />
            </div>
            <div className="flex flex-col bg-container p-lg flex-1">
                <DriversChart
                    identifier={bbgTicker}
                    hedgeTicker={relativeBbgTicker}
                    showRollingSpecificPerf
                    params={{ DateFrame: dateFrame, RiskModel: riskModel }}
                />
            </div>
        </div>
    );
};
