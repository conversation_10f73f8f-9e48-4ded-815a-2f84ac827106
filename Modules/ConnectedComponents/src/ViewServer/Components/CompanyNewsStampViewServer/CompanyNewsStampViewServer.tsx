import React, { ComponentProps, useMemo } from "react";

import { withSchemaOverrides } from "@mocks/api-gateway";
import type { Schema } from "Contracts/index";
import { withFilterValues, Stamp, StampTitleWithMark } from "Components/index";
import {
    createConfig,
    withCommandState,
    withConnectedSkeleton,
    reportSubscriptionCommandSpec,
} from "TransportGateway/index";
import { withSkeletonComponentDefinition, SkeletonRect } from "PrimitiveComponents/index";
import { isNullish } from "Utilities/index";

import { schemaOverride } from "./schemaOverride";
import { useFeature } from "../../../ApiGateway";

type CompanyNewsStampViewServerBaseProps = {
    data?: {
        newsType?: string;
        newsData?: string;
    }[];
    schema?: Schema;
    title?: string;
    score?: number;
} & ComponentProps<typeof Stamp>;

const CompanyNewsStampViewServerBase = withSkeletonComponentDefinition(
    ({
        data: dataProp,
        schema: schemaProp,
        title: titleProp,
        ...rest
    }: CompanyNewsStampViewServerBaseProps) => {
        const { hasFeature } = useFeature();
        const isDSPHNVersion = hasFeature("dsp.hn-version");

        const schema = useMemo(() => withSchemaOverrides(schemaProp, schemaOverride), [schemaProp]);

        const data = useMemo(
            () =>
                dataProp?.length > 0
                    ? dataProp
                          .filter(
                              ({ newsType, newsData }) =>
                                  !(isNullish(newsType) && isNullish(newsData)),
                          )
                          .flatMap(({ newsType, newsData }) => [newsType, newsData])
                    : dataProp,
            [dataProp],
        );

        const title = useMemo(
            () =>
                isDSPHNVersion ? (
                    <StampTitleWithMark
                        title={titleProp}
                        score={data && (data.length === 0 ? 0 : -2)}
                    />
                ) : (
                    titleProp
                ),
            [titleProp, data, isDSPHNVersion],
        );

        return (
            <Stamp
                {...rest}
                title={title}
                data={data}
                schema={schema}
            />
        );
    },
)(SkeletonRect);

export const CompanyNewsStampViewServer = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            CompanyNewsStampViewServerBase,
            createConfig({
                props: reportSubscriptionCommandSpec(
                    "hn_cockpit_topnews_widget",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data, schema }),
                    "ProjectionNode",
                ),
            }),
        ),
    ),
    ["bbgTicker"],
    {
        bbgTicker: {
            required: true,
        },
    },
);
