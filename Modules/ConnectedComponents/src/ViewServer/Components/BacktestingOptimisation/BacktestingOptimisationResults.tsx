import React, { useMemo } from "react";

import { createConfig, subscriptionCommandSpec, withCommandState } from "TransportGateway/index";
import { Grid } from "Grid/index";
import type { BackTestCriteriaType } from "Components/index";
import type { FormatterType } from "Contracts/index";
import { getFormattedValue } from "Utilities/index";

import {
    VersionedCriteria,
    createCriteriaStatements,
    createFieldsForStatements,
    createReportNodesFromBacktestCriteriaBeforeTranspose,
    createRiskAttributionFieldsToSnapshot,
    createRiskAttributionPriceFields,
    getConditionLabel,
    padNumber,
} from "../BacktestingTradeChartViewServer/utils";

import type { Optimisation } from "./BacktestingOptimisation";
import { BacktestingOptimisationResultsChart } from "./BacktestingOptimisationResultsChart";

type CriteriaOperator =
    | "equal"
    | "notEqual"
    | "greaterThan"
    | "greaterThanOrEqual"
    | "lessThan"
    | "lessThanOrEqual";

type ColumnCondition = {
    column: {
        lhs: { Column: string };
        operator: CriteriaOperator;
        rhs: any;
    };
};

type CriteriaGroupSpec = {
    group: {
        group: AndGroup | OrGroup;
    };
};

type AndGroup = {
    and: CriteriaSpec[];
};

type OrGroup = {
    or: CriteriaSpec[];
};

type CriteriaSpec = ColumnCondition | CriteriaGroupSpec;

type EntranceStrategy = {
    columnCriteria: {
        reEntryTime: number;
        criteria: CriteriaSpec;
        name?: string;
    };
};

type ExitStrategy = {
    stopLossAndTakeProfit: {
        name?: string;
        priceColumn: string;
        initiationPriceColumn: string;
        stopLoss: number;
        takeProfit: number;
        holdingPeriod: number;
    };
};

//   group: {
//             group: {
//                 and: result,
//             },
//         },

const entranceStrategyVariations = (
    entranceStrategy: EntranceStrategy,
    optimisation: Optimisation,
) => {
    if (optimisation.lowerBounds >= optimisation.upperBounds) {
        throw new Error(
            `Invalid lower bounds ${optimisation.lowerBounds} and upper bounds ${optimisation.upperBounds} for optimisation`,
        );
    }
    if (optimisation.type === "entry") {
        const strategies = [];
        const step =
            (optimisation.upperBounds - optimisation.lowerBounds) / optimisation.numberSteps;
        // step = step < 1 ? 1 : step;
        let previousStep;
        for (let i = optimisation.lowerBounds; i <= optimisation.upperBounds; i += step) {
            const strategyClone = JSON.parse(
                JSON.stringify(entranceStrategy),
            ) as EntranceStrategy as any;
            const isFirstStep = typeof previousStep === "undefined";

            if (!("group" in strategyClone.columnCriteria.criteria)) {
                throw new Error("Expecting criteria that we're optimising to be of type group");
            }

            const criteriaForOptimisation =
                strategyClone.columnCriteria.criteria.group.group.and.find(c => {
                    if ("column" in c && "lhs" in c.column && "Column" in c.column.lhs) {
                        return c.column.lhs.Column === optimisation.fieldName;
                    }
                    return false;
                }) as any;
            if (!criteriaForOptimisation) {
                throw new Error(
                    `Cannot find criteria for the select optimisation ${optimisation.fieldName}`,
                );
            }

            // Up until here I have fiexed

            if (optimisation.buckets) {
                if (isFirstStep) {
                    if (optimisation.outOfRange) {
                        criteriaForOptimisation.column.operator = "lessThan";
                        strategyClone.columnCriteria.name = `${optimisation.fieldName} = < ${i}`;
                        criteriaForOptimisation.column.rhs.Constant.Float64 = i;
                        strategies.push(strategyClone);
                    }
                } else {
                    criteriaForOptimisation.column.operator = "lessThan";
                    strategyClone.columnCriteria.name = `${optimisation.fieldName} = ${previousStep} to <${i}`;
                    criteriaForOptimisation.column.rhs.Constant.Float64 = i;
                    const criteriaForOptimisationClone = JSON.parse(
                        JSON.stringify(criteriaForOptimisation),
                    );
                    criteriaForOptimisationClone.column.operator = "greaterThanOrEqual";
                    criteriaForOptimisationClone.column.rhs.Constant.Float64 = previousStep;
                    strategyClone.columnCriteria.criteria.group.and.push(
                        criteriaForOptimisationClone,
                    );
                    strategies.push(strategyClone);
                }
            } else {
                strategyClone.columnCriteria.name = `${
                    optimisation.fieldName
                } = ${getConditionLabel(criteriaForOptimisation.column.operator)} ${i}`;
                criteriaForOptimisation.column.rhs.Constant.Float64 = i;
                strategies.push(strategyClone);
            }
            previousStep = i;
        }

        if (optimisation.buckets && optimisation.outOfRange) {
            const strategyClone = JSON.parse(JSON.stringify(entranceStrategy)) as EntranceStrategy;
            if (!("group" in strategyClone.columnCriteria.criteria)) {
                throw new Error("Expecting criteria that we're optimising to be of type group");
            }

            if (!("and" in strategyClone.columnCriteria.criteria.group)) {
                throw new Error("Expecting criteria that we're optimising to be of type and group");
            }

            strategyClone.columnCriteria.name = `${optimisation.fieldName} = > ${previousStep}`;
            const criteriaForOptimisation =
                "and" in strategyClone.columnCriteria.criteria.group.group &&
                strategyClone.columnCriteria.criteria.group.group.and.find(c => {
                    if ("column" in c && "lhs" in c.column && "Column" in c.column.lhs) {
                        return c.column.lhs.Column === optimisation.fieldName;
                    }
                    return false;
                });
            if (
                !(
                    "column" in criteriaForOptimisation &&
                    "rhs" in criteriaForOptimisation.column &&
                    "Constant" in criteriaForOptimisation.column.rhs
                )
            ) {
                throw new Error(
                    "Expecting criteria for optimisation to be a column that has a column on the left and a constant on the right ",
                );
            }

            criteriaForOptimisation.column.operator = "greaterThan";
            criteriaForOptimisation.column.rhs.Constant.Float64 = previousStep;
            strategies.push(strategyClone);
        }

        return strategies;
    }
    if (optimisation.type === "reentry") {
        const strategies = [];
        const step = Math.max(
            1,
            Math.floor(
                (optimisation.upperBounds - optimisation.lowerBounds) / optimisation.numberSteps,
            ),
        );
        for (let i = optimisation.lowerBounds; i <= optimisation.upperBounds; i += step) {
            const strategyClone = JSON.parse(JSON.stringify(entranceStrategy)) as EntranceStrategy;
            strategyClone.columnCriteria.name = `Re-Entry = ${i}`;
            strategyClone.columnCriteria.reEntryTime = i;
            strategies.push(strategyClone);
        }
        return strategies;
    }
    return [entranceStrategy];
};

const exitStrategyVariations = (exitStrategy: ExitStrategy, optimisation: Optimisation) => {
    if (optimisation.type === "exit") {
        const strategies = [];
        const step =
            optimisation.fieldName === "holdingPeriod"
                ? Math.max(
                      1,
                      Math.floor(
                          (optimisation.upperBounds - optimisation.lowerBounds) /
                              optimisation.numberSteps,
                      ),
                  )
                : (optimisation.upperBounds - optimisation.lowerBounds) / optimisation.numberSteps;

        if (optimisation.lowerBounds >= optimisation.upperBounds) {
            throw new Error(
                `Invalid lower bounds ${optimisation.lowerBounds} and upper bounds ${optimisation.upperBounds} for optimisation`,
            );
        }
        // step = step < 1 ? 1 : step;
        for (let i = optimisation.lowerBounds; i <= optimisation.upperBounds; i += step) {
            const strategyClone = JSON.parse(JSON.stringify(exitStrategy)) as ExitStrategy;
            strategyClone.stopLossAndTakeProfit[optimisation.fieldName] = i;
            strategyClone.stopLossAndTakeProfit.name = `${optimisation.fieldName} = ${i}`;
            strategies.push(strategyClone);
        }
        return strategies;
    }
    return [exitStrategy];
};

function createTradeModelFromCriteria(
    criteria: BackTestCriteriaType,
    takeProfit: number,
    stopLoss: number,
    holdingPeriod: number,
    reentryTime: number,
    optimisation: Optimisation,
    initialAllocation: number,
) {
    return {
        initialAllocation: initialAllocation || 100000000,
        instrumentFieldName: "Identifier",
        dateFieldName: "DateTime",
        tradeIdFieldName: "DateTimeAndIdentifier",
        currentPriceFieldName: "price",
        currentScalingFactorFieldName: "noShares",
        entranceStrategies: entranceStrategyVariations(
            {
                columnCriteria: {
                    reEntryTime: reentryTime,
                    criteria: createCriteriaStatements(criteria),
                },
            },
            optimisation,
        ),
        exitStrategies: exitStrategyVariations(
            {
                stopLossAndTakeProfit: {
                    priceColumn: "price",
                    initiationPriceColumn: "initiation_price",
                    stopLoss,
                    takeProfit,
                    holdingPeriod,
                },
            },
            optimisation,
        ),
        fieldsToSnapshot: [
            "relative_price~Float64",
            "noShares~Float64",
            "price~Float64",
            ...createRiskAttributionFieldsToSnapshot(),
            ...createFieldsForStatements(criteria),
        ],
        returnStrategies: [
            ["relative_price", "absperc"],
            ...createRiskAttributionPriceFields(),
            ["specific_filled", "retperc"],
            ["price", "absperc"],
            ["price", "absabs"],
        ],
    };
}

function createOptimisationSignalAnalysisNode(
    stopLoss: number,
    takeProfit: number,
    holdingPeriod: number,
    reentryTime: number,
    criteria: BackTestCriteriaType,
    optimisation: Optimisation,
    analysisOperatorName: string,
    joinVersion: number,
    analysisNodeVersion: number,
    lowerBounds: string,
    upperBounds: string,
    initialAllocation: number,
) {
    return {
        name: `optimisationOfSignal${padNumber(optimisation.version)}`,
        operatorType: "report_reference",
        reportKey: "Signal_Analysis_Dynamic",
        nodeType: "Persistent",
        parameterValues: {
            tradeModelString: createTradeModelFromCriteria(
                criteria,
                takeProfit,
                stopLoss,
                holdingPeriod,
                reentryTime,
                optimisation,
                initialAllocation,
            ),
            modelType: "Enhanced",
            lowerBounds,
            upperBounds,
        },
        outputOperatorName: analysisOperatorName,
        connections: {
            in: [`calcInitiationParams${padNumber(analysisNodeVersion)}`, "out"],
        },
    };
}

function createAdhocSubscriptionRequest(
    versionedCriteria: VersionedCriteria,
    optimisation: Optimisation,
    analysisOperatorName: string,
) {
    const [
        joinVersion /* parentRetportAnalysisNodeVersion */,
        analysisNodeVersion,
        criteria,
        overviewData = {} as any,
    ] = versionedCriteria;

    const {
        holdingPeriod,
        takeProfit,
        stopLoss,
        reentryTime,
        lowerBounds,
        upperBounds,
        initialAllocation,
    } = overviewData;
    const resultNodes = createReportNodesFromBacktestCriteriaBeforeTranspose(versionedCriteria);
    const remainingTokens = criteria?.tokens?.filter(c => c.token);
    if (remainingTokens) {
        resultNodes.push(
            createOptimisationSignalAnalysisNode(
                stopLoss,
                takeProfit,
                holdingPeriod,
                reentryTime,
                criteria,
                optimisation,
                analysisOperatorName,
                joinVersion,
                analysisNodeVersion,
                lowerBounds,
                upperBounds,
                initialAllocation,
            ),
        );
    }

    console.log("Optimisation nodes are", resultNodes);

    return {
        outputOperatorName: `optimisationOfSignal${padNumber(optimisation.version)}`,
        nodeTypeOverride: "User",
        // executionContextOverride: cleanse((criteria as any).name),
        nodes: resultNodes,
    };
}

const adhocReportBasedOnOptimisationAndCriteriaSubscriptionCommandSpec = (
    outputOperator: string,
    dataMapper: (data, props, schema) => any,
) =>
    subscriptionCommandSpec(
        "SubscribeToRemote",
        ({
            criteria,
            optimisation,
        }: {
            criteria: VersionedCriteria;
            optimisation: Optimisation;
        }) => {
            return (
                criteria &&
                criteria[2] &&
                criteria[2].tokens &&
                criteria[2].tokens.length && {
                    ServerUri: "BacktestReportingNode",
                    RemoteCommandName: "SubscribeToAdhocReport",
                    RemoteCommandPayload: JSON.stringify(
                        createAdhocSubscriptionRequest(criteria, optimisation, outputOperator),
                    ),
                }
            );
        },
        dataMapper,
    );

// @TODO: Would be better to get this from a schema
const guessFormatting = (fieldName: string) => {
    if (
        fieldName.toLowerCase().includes("cap") ||
        fieldName.toLowerCase().includes("usd") ||
        fieldName.toLowerCase().includes("price")
    ) {
        return { formatterType: "bigValue" as FormatterType, valueFormat: "$.1f" };
    }

    return {
        formatterType: "number" as FormatterType,
        valueFormat: "number2dp",
    };
};

export type BacktestOptimisationRowData = {
    cumulativeReturnPriceInitiationSwingpriceconviction: number;
    totalWonPrice: number;
    cumulativeReturnPrice: number;
    sharpePrice: number;
    iterationName: string;
    annualReturnPrice: number;
    noTrades: number;
    noHits: number;
    drawDownPrice: number;
    volatilityPrice: number;
    cumulativeReturnRelativePrice: number;
    drawDownPriceInitiationSwingpriceconviction: number;
    annualReturnPriceInitiationSwingpriceconviction: number;
    hitRatePrice: number;
    totalLostPrice: number;
    iteration: number;
    sluggingRatioPrice: number;
    rowId: number;
};

type BacktestingOptimisationResutsProps = {
    data?: BacktestOptimisationRowData[];
    schema: any;
    onRowSelect?: (indexRow: number, row: BacktestOptimisationRowData) => void;
};

export const BacktestingOptimisationResultsBase = ({
    data: dataProp,
    schema: schemaProp,
    onRowSelect,
}: BacktestingOptimisationResutsProps) => {
    const [data, schema] = useMemo(() => {
        const data = dataProp
            ?.sort((a, b) => a.iteration - b.iteration)
            ?.map((it, i) => {
                const { iterationName } = it;
                const nameParts = iterationName?.split("=");
                return {
                    ...it,
                    iterationName: nameParts ? nameParts[nameParts.length - 1] : undefined,
                    variableName: nameParts && nameParts.length > 1 ? nameParts[0] : undefined,
                };
            });

        const schemaClone = schemaProp ? JSON.parse(JSON.stringify(schemaProp)) : schemaProp;
        const columnForIteration = schemaClone?.find(c => c.name === "iterationName");
        if (columnForIteration) {
            columnForIteration.metaData.displayName = data[0]?.variableName;
        } else {
            console.error("No iteration name column found");
        }

        return [data, schemaClone];
    }, [dataProp, schemaProp]);

    return (
        <div className="flex flex-1 gap-xs">
            <div className="flex w-[800px] bg-container flex-col p-lg gap-md">
                <BacktestingOptimisationResultsChart
                    data={data}
                    schema={schema}
                />
            </div>

            <div className="flex flex-1 bg-container p-lg">
                <Grid
                    data={data}
                    onChange={onRowSelect}
                    schema={schema}
                    layout="compact"
                />
            </div>
        </div>
    );
};

export const BacktestingOptimisationResults = withCommandState(
    BacktestingOptimisationResultsBase,
    createConfig({
        props: adhocReportBasedOnOptimisationAndCriteriaSubscriptionCommandSpec(
            "projectIterations",
            (data, props, schema) => ({ data, schema }),
        ),
    }),
);
