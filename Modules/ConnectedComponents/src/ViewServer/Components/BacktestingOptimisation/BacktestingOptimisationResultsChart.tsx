import React, { useMemo, useState, useEffect } from "react";

import { Chart } from "Charting/index";
import { SelectWithLabel } from "PrimitiveComponents/index";
import { isUndefined } from "Utilities/index";
import { withSchemaOverrides } from "@mocks/api-gateway";

export const BacktestingOptimisationResultsChart = ({ data, schema: schemaProp }) => {
    const options = useMemo(
        () =>
            schemaProp
                ?.filter(c => c.name !== "iteration" && c.name !== "iterationName")
                ?.sort((a, b) => {
                    if (isUndefined(a?.metaData?.order) || isUndefined(b?.metaData?.order)) {
                        return 0;
                    }
                    return a.metaData.order - b.metaData.order;
                })
                ?.map(c => ({ label: c.metaData?.displayName || c.name, value: c.name })),
        [schemaProp],
    );

    const [selected, setSelected] = useState<string>();

    // pre-select the first option
    useEffect(() => {
        if (options?.length > 0 && isUndefined(selected)) {
            setSelected(options?.[0]?.value);
        }
    }, [options, selected]);

    const schema = useMemo(() => {
        const { formatterType, valueFormat } = (() => {
            switch (selected) {
                case "cumulativeReturnPrice":
                case "annualReturnPrice":
                case "cumulativeReturnRelativePrice":
                case "drawDownPrice":
                case "volatilityPrice":
                case "hitRatePrice":
                    return {
                        formatterType: "percent",
                        valueFormat: "percent2dp",
                    };
                case "annualReturnPriceInitiationSwingpriceconviction":
                case "cumulativeReturnPriceInitiationSwingpriceconviction":
                case "drawDownPriceInitiationSwingpriceconviction":
                    return {
                        formatterType: "bigValue",
                        valueFormat: "$.1f",
                    };
                case "totalWonPrice":
                case "totalLostPrice":
                case "noTrades":
                case "noHits":
                default:
                    return {
                        formatterType: "number",
                        valueFormat: "number0dp",
                    };
            }
        })();

        return withSchemaOverrides(schemaProp ?? [], {
            iteration: {
                properties: {
                    chartPropsMargin: { bottom: 0, left: 20, right: 0, top: 0 },
                    chartCartesianGridPropsShow: true,

                    chartXAxisPropsShow: true,
                    chartXAxisPropsDataKey: "iterationName",
                    chartXAxisPropsType: "category",
                    chartXAxisPropsTicksMode: "default",
                    chartXAxisPropsPadding: { left: 0, right: 0 },

                    chartYAxisLeftPropsShow: true,
                    chartYAxisLeftPropsWidth: 40,
                    chartYAxisLeftPropsPadding: { top: 20, bottom: 20 },
                    chartYAxisLeftPropsFormatterType: formatterType,
                    chartYAxisLeftPropsValueFormat: valueFormat,
                    chartYAxisLeftPropsDomain: "['dataMin', 'dataMax']",

                    chartTooltipPropsFormatterType: formatterType,
                    chartTooltipPropsValueFormat: valueFormat,
                },
            },
            [selected]: {
                properties: {
                    chartType: "line",
                },
            },
        });
    }, [schemaProp, selected]);

    return (
        <>
            <div>
                <SelectWithLabel
                    label="Field"
                    options={options}
                    required
                    width="150px"
                    onChange={setSelected}
                    value={selected}
                />
            </div>
            <Chart
                data={data}
                schema={schema}
            />
        </>
    );
};
