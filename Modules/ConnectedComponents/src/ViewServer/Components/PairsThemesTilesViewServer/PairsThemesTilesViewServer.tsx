import React, { useMemo } from "react";
import { times } from "lodash";

import { isArray, isNonEmptyArray, isObject, isNonEmptyObject } from "Utilities/index";
import { withSchemaOverrides } from "@mocks/api-gateway";
import type { Schema } from "Contracts/index";
import { SchemaValue } from "Schema/index";
import {
    createConfig,
    withCommandState,
    withConnectedSkeleton,
    reportSubscriptionCommandSpec,
} from "TransportGateway/index";
import { Tile } from "PrimitiveComponents/index";

import { schemaOverride } from "./schemaOverride";

type PairsThemesTilesViewServerBaseProps = {
    data?: {
        coreThemes: string;
        tickerInThematic: number;
        hedgeTickerInThematic: number;
        tickerMinusHedgeThematicExposure: number;
    }[];
    schema?: Schema;
};
const PairsThemesTilesViewServerBase = ({
    data: dataProp,
    schema: schemaProp,
}: PairsThemesTilesViewServerBaseProps) => {
    const schema = useMemo(
        () => withSchemaOverrides(schemaProp ?? [], schemaOverride),
        [schemaProp],
    );

    const data = useMemo(
        () =>
            dataProp
                ?.map(d => ({
                    ...d,
                    absTickerMinusHedgeThematicExposure: Math.abs(
                        d.tickerMinusHedgeThematicExposure,
                    ),
                }))
                ?.sort(
                    (a, b) =>
                        b.absTickerMinusHedgeThematicExposure -
                        a.absTickerMinusHedgeThematicExposure,
                )
                ?.slice(0, 3),
        [dataProp],
    );

    const renderKeyValue = (name, d) => (
        <SchemaValue
            name={name}
            schema={schema}
            data={d}
        >
            {(children, displayName) => (
                <div className="flex items-center gap-md">
                    <div>{displayName}:</div>
                    <div className="text-highlighted-inversed">{children}</div>
                </div>
            )}
        </SchemaValue>
    );

    return (
        <div className="grid grid-cols-3 gap-xs h-full">
            {(isArray(data) && isNonEmptyArray(data) ? data : times(3)).map((d, index) => {
                const key = d?.coreThemes ?? index;
                return (
                    <Tile
                        key={key}
                        title={d?.coreThemes}
                        fontSize="medium"
                    >
                        {isObject(d) && isNonEmptyObject(d) && (
                            <div className="flex flex-1 flex-col justify-center items-center">
                                <div>
                                    {renderKeyValue("tickerInThematic", d)}
                                    {renderKeyValue("hedgeTickerInThematic", d)}
                                    {renderKeyValue("tickerMinusHedgeThematicExposure", d)}
                                </div>
                            </div>
                        )}
                    </Tile>
                );
            })}
        </div>
    );
};

export const PairsThemesTilesViewServer = withConnectedSkeleton(
    withCommandState(
        PairsThemesTilesViewServerBase,
        createConfig({
            props: reportSubscriptionCommandSpec(
                "Pairs_ThematicDrivers_HedgeBreakout",
                ({ buyTicker, indexTicker }: { buyTicker?: string; indexTicker?: string }) =>
                    buyTicker && indexTicker ? { buyTicker, indexTicker } : undefined,
                (data, props, schema) => ({ data, schema }),
                "ProjectionNode",
            ),
        }),
    ),
);
