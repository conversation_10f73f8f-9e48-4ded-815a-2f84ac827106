import React, { useMemo } from "react";
import moment from "moment/moment";

import { withSchemaOverrides } from "@mocks/api-gateway";
import {
    createConfig,
    withCommandState,
    withConnectedSkeleton,
    reportSubscriptionCommandSpec,
} from "TransportGateway/index";
import { withFilterValues } from "Components/index";
import { withSkeletonComponentDefinition, SkeletonRect } from "PrimitiveComponents/index";
import type { Schema } from "Contracts/index";
import { Chart } from "Charting/index";

import { chartSchemaOverride, type ChartSchemaOverride } from "./schemaOverride";

type CrowdingChartViewServerBaseProps = {
    data?: Record<string, unknown>[];
    schema?: Schema;
} & ChartSchemaOverride;

const CrowdingChartViewServerBase = withSkeletonComponentDefinition(
    ({ data: dataProp, schema: schemaProp, mode, ...rest }: CrowdingChartViewServerBaseProps) => {
        const schema = useMemo(
            () => withSchemaOverrides(schemaProp ?? [], chartSchemaOverride({ mode })),
            [schemaProp, mode],
        );

        const data = useMemo(
            () =>
                dataProp?.map(d => ({
                    ...d,
                    date: moment(d.date, "YYYY-MM-DD-HH.mm.ss").toISOString(),
                })),
            [dataProp],
        );

        return (
            <Chart
                data={data}
                schema={schema}
                compactWidth={0}
                compactHeight={0}
                {...rest}
            />
        );
    },
)(SkeletonRect);

export const CrowdingChartViewServer = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            CrowdingChartViewServerBase,
            createConfig({
                props: reportSubscriptionCommandSpec(
                    "JPM_Single_Stock_Crowding_SingleStock",
                    ({ bbgTicker }: { bbgTicker?: string }) =>
                        bbgTicker ? { BBGTicker: bbgTicker } : undefined,
                    (data, props, schema) => ({ data, schema }),
                    "ProjectionNode2",
                ),
            }),
        ),
    ),
    ["bbgTicker"],
    {
        bbgTicker: {
            required: true,
        },
    },
);
