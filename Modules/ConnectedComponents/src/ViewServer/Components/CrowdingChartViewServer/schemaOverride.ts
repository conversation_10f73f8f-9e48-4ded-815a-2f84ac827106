export type ChartSchemaOverride = {
    mode?: "default" | "compact";
};
export const chartSchemaOverride = ({ mode = "default" }: ChartSchemaOverride) => {
    if (mode === "compact") {
        return {
            date: {
                displayName: "Date",
                properties: {
                    chartPropsMargin: { bottom: 0, left: 0, right: 35, top: 0 },

                    chartYAxisLeftPropsShow: true,
                    chartYAxisLeftPropsHide: true,
                    chartYAxisLeftPropsDomain: "['dataMin', 'dataMax']",
                    chartYAxisLeftPropsPadding: { top: 5, bottom: 5 },

                    chartTooltipPropsLabelFormatterType: "date",
                    chartTooltipPropsLabelValueFormat: "ddd, Do MMM YYYY",
                    chartTooltipPropsValueColumnName: "date",
                    chartTooltipPropsSortBy: "netScoreAdjusted, percentile",
                },
            },
            netScoreAdjusted: {
                displayName: "Crowded Score",
                properties: {
                    chartType: "line",
                    chartSeriesPropsStroke: "var(--charts__series__line__2)",

                    chartPriceMarkerPropsShow: true,
                    chartPriceMarkerPropsDataKey: "netScoreAdjusted",
                    chartPriceMarkerPropsFormatterType: "number",
                    chartPriceMarkerPropsValueFormat: "number0dp",
                    chartPriceMarkerPropsFontSize: "9px",

                    chartTooltipPropsFormatterType: "number",
                    chartTooltipPropsValueFormat: "number0dp",
                },
            },
            percentile: {
                properties: {
                    chartTooltipPropsFormatterType: "percent",
                    chartTooltipPropsValueFormat: "percent2dp1m",
                },
            },
        };
    }

    return {
        date: {
            displayName: "Date",
            properties: {
                chartPropsMargin: { bottom: 0, left: 20, right: 50, top: 0 },
                chartCartesianGridPropsShow: true,

                chartXAxisPropsShow: true,
                chartXAxisPropsFormatterType: "date",
                chartXAxisPropsHeight: 25,
                chartXAxisPropsInterval: "preserveEnd",
                chartXAxisPropsMinTickGap: 60,
                chartXAxisPropsTicksMode: "default",
                chartXAxisPropsValueFormat: "MMM YY",

                chartYAxisLeftPropsShow: true,
                chartYAxisLeftPropsFormatterType: "number",
                chartYAxisLeftPropsPadding: { top: 10, bottom: 10 },
                chartYAxisLeftPropsValueFormat: "number0dp",
                chartYAxisLeftPropsWidth: 45,

                chartYAxisLeftLabelPropsClassName: "anchor-middle",
                chartYAxisLeftLabelPropsFontSize: "14px",
                chartYAxisLeftLabelPropsOffset: 5,
                chartYAxisLeftLabelPropsPosition: "insideLeft",
                chartYAxisLeftLabelPropsValue: "Crowded Score",

                chartTooltipPropsLabelFormatterType: "date",
                chartTooltipPropsLabelValueFormat: "ddd, Do MMM YYYY",
                chartTooltipPropsValueColumnName: "date",
                chartTooltipPropsSortBy: "netScoreAdjusted, percentile",
            },
        },

        netScoreAdjusted: {
            displayName: "Crowded Score",
            properties: {
                chartType: "line",
                chartSeriesPropsStroke: "var(--charts__series__line__2)",

                chartPriceMarkerPropsShow: true,
                chartPriceMarkerPropsDataKey: "netScoreAdjusted",
                chartPriceMarkerPropsFormatterType: "number",
                chartPriceMarkerPropsValueFormat: "number0dp",
                chartPriceMarkerPropsTextColor: "var(--charts__series__line__2)",

                chartTooltipPropsFormatterType: "number",
                chartTooltipPropsValueFormat: "number0dp",
            },
        },

        percentile: {
            properties: {
                order: 2,

                chartType: "line",

                chartPriceMarkerPropsShow: true,
                chartPriceMarkerPropsDataKey: "percentile",
                chartPriceMarkerPropsFormatterType: "percent",
                chartPriceMarkerPropsValueFormat: "percent2dp1m",

                chartYAxisRightPropsShow: true,
                chartYAxisRightPropsHide: true,

                chartYAxisRightPropsPadding: { top: 10, bottom: 10 },
                chartYAxisRightPropsDataKey: "percentile",
                chartYAxisRightPropsYAxisId: "right-axis",

                chartSeriesPropsYAxisId: "right-axis",

                chartTooltipPropsFormatterType: "percent",
                chartTooltipPropsValueFormat: "percent2dp1m",
            },
        },
    };
};
