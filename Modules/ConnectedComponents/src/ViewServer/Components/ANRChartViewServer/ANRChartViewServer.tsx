import React, { ComponentProps, useMemo } from "react";

import { withSchemaOverrides } from "@mocks/api-gateway";
import { withFilterValues } from "Components/index";
import {
    createConfig,
    withCommandState,
    withConnectedSkeleton,
    reportSubscriptionCommandSpec,
} from "TransportGateway/index";
import { withSkeletonComponentDefinition, SkeletonRect } from "PrimitiveComponents/index";
import type { Schema } from "Contracts/index";
import { tryParseDate } from "Utilities/index";
import { Chart } from "Charting/index";

import { schemaOverride, type SchemaOverride } from "./schemaOverride";

type ANRChartViewServerBaseProps = {
    data?: {
        snapshotDate: string;
    }[];
    schema?: Schema;
} & SchemaOverride &
    ComponentProps<typeof Chart>;

const ANRChartViewServerBase = withSkeletonComponentDefinition(
    ({ data: dataProp, schema: schemaProp, mode, hideLegend }: ANRChartViewServerBaseProps) => {
        const schema = useMemo(
            () => withSchemaOverrides(schemaProp, schemaOverride({ mode, hideLegend })),
            [schemaProp, mode],
        );
        const data = useMemo(
            () =>
                dataProp
                    ?.map(({ snapshotDate, ...rest2 }) => {
                        const date = tryParseDate(snapshotDate);
                        return {
                            snapshotDate: date.format("YYYY-MM-DD"),
                            dateVal: date.toDate().valueOf(),
                            ...rest2,
                        };
                    })
                    .sort((a, z) => a.dateVal - z.dateVal),
            [dataProp],
        );

        return (
            <Chart
                data={data}
                schema={schema}
                compactWidth={0}
                compactHeight={0}
            />
        );
    },
)(SkeletonRect);

export const ANRChartViewServer = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            ANRChartViewServerBase,
            createConfig({
                props: reportSubscriptionCommandSpec(
                    "BBGConsensusFetchHistoryPrice",
                    ({ bbgTicker }: { bbgTicker?: string }) =>
                        bbgTicker ? { BBGTicker: bbgTicker } : undefined,
                    (data, props, schema) => ({
                        data,
                        schema,
                    }),
                    "DataRailLiveLookupRequestNode",
                ),
            }),
        ),
    ),
    ["bbgTicker"],
    {
        bbgTicker: {
            required: true,
        },
    },
);
