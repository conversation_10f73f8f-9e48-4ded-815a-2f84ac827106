import React, { type ComponentProps, type ComponentType, useMemo } from "react";

import { withSchemaOverrides } from "@mocks/api-gateway";
import type { Schema } from "Contracts/index";
import { withFilterValues, Stamp, StampTitleWithMark, type StampProps } from "Components/index";
import {
    createConfig,
    withCommandState,
    withConnectedSkeleton,
    reportSubscriptionCommandSpec,
} from "TransportGateway/index";
import { withSkeletonComponentDefinition, SkeletonRect } from "PrimitiveComponents/index";

import { peRelSchemaOverride, peSectorSchemaOverride, schemaOverride } from "./schemaOverride";
import { useFeature } from "../../../ApiGateway";

type ValuationData = {
    valuationScore: number;
};

type PeData = {
    zScore2Y: number;
    zScore2Y1mChg: number;
};

const ValuationStamp = Stamp as ComponentType<
    StampProps & {
        peRelData?: Record<string, unknown>[];
        peRelSchema?: Schema;
        peSectorData?: Record<string, unknown>[];
        peSectorSchema?: Schema;
    }
>;

type ValuationStampViewServerBaseProps = {
    data?: ValuationData;
    schema?: Schema;
    title?: string;
    score?: number;
    peRel?: {
        data?: PeData[];
        schema?: Schema;
    };
    peSector?: {
        data?: PeData[];
        schema?: Schema;
    };
} & ComponentProps<typeof Stamp>;

const ValuationStampViewServerBase = withSkeletonComponentDefinition(
    ({
        data,
        schema: schemaProp,
        title: titleProp,
        peRel,
        peSector,
        ...rest
    }: ValuationStampViewServerBaseProps) => {
        const { hasFeature } = useFeature();
        const isDSPHNVersion = hasFeature("dsp.hn-version");

        const schema = useMemo(() => withSchemaOverrides(schemaProp, schemaOverride), [schemaProp]);
        const peRelSchema = useMemo(
            () => withSchemaOverrides(peRel?.schema ?? [], peRelSchemaOverride),
            [peRel?.schema],
        );
        const peSectorSchema = useMemo(
            () => withSchemaOverrides(peSector?.schema ?? [], peSectorSchemaOverride),
            [peSector?.schema],
        );

        const title = useMemo(
            () =>
                isDSPHNVersion ? (
                    <StampTitleWithMark
                        title={titleProp}
                        score={data?.valuationScore}
                    />
                ) : (
                    titleProp
                ),
            [titleProp, data, isDSPHNVersion],
        );

        return (
            <ValuationStamp
                {...rest}
                title={title}
                data={data}
                schema={schema}
                peRelData={peRel?.data}
                peRelSchema={peRelSchema}
                peSectorData={peSector?.data}
                peSectorSchema={peSectorSchema}
            />
        );
    },
)(SkeletonRect);

export const ValuationStampViewServer = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            ValuationStampViewServerBase,
            createConfig({
                props: reportSubscriptionCommandSpec(
                    "hn_cockpit_valuationWidget",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data: data?.[0], schema }),
                    "ProjectionNode",
                ),
                peRel: reportSubscriptionCommandSpec(
                    "PERelQueryEngineRequest_IFundamentalSchema",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data: data?.[0], schema }),
                    "PERelQueryEngineRequest_IFundamentalSchema",
                ),
                peSector: reportSubscriptionCommandSpec(
                    "PESectorQueryEngineRequest_IFundamentalSchema",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data: data?.[0], schema }),
                    "PESectorQueryEngineRequest_IFundamentalSchema",
                ),
            }),
        ),
    ),
    ["bbgTicker"],
    {
        bbgTicker: {
            required: true,
        },
    },
);
