export const schemaOverride = {
    name: {
        displayName: "Driver",
        width: 180,
        properties: {
            order: 1,
            cellRenderer: "HighlightedCell",
            cellRendererPropsColorValue: "#ref(percentageOfTotalRisk)",
        },
    },
    oneSDMove: {
        width: "#unset",
        properties: {
            order: 2,
            flex: 1,
        },
    },
    exposure: {
        width: "#unset",
        contentTypeForDisplay: "ChangeValue",
        properties: {
            order: 3,
            flex: 1,
            formatterType: "bigValue",
            valueFormat: "$.1f",
            colorValue: 0,
        },
    },
    percentageOfTotalRisk: {
        contentTypeForDisplay: "ChangeValue",
        width: "#unset",
        properties: {
            order: 4,
            flex: 1,
            formatterType: "percent",
            valueFormat: "percent2dp",
        },
    },
    factorType: {
        hidden: true,
    },
};
