export const schemaOverride = {
    rank: {
        properties: { flex: 1 },
    },
    previous: {
        properties: { flex: 1 },
    },
    identifier: {
        properties: { flex: 1 },
    },
    guidance: {
        properties: { flex: 1 },
    },
    scoreCappedBuyBack: {
        properties: { flex: 1 },
    },
    dividendAdj: {
        properties: { flex: 1 },
    },
    ctsAdj: {
        properties: { flex: 1 },
    },
    cTSscore: {
        properties: { flex: 1 },
    },
    turnoverUSD: {
        properties: { flex: 1 },
    },
    generic_mktCap: {
        properties: { flex: 1 },
    },
    chgToday: {
        properties: { flex: 1 },
    },
    hN_technical_score_withTrend: {
        properties: { flex: 1 },
    },
    epsChangeScore: {
        properties: { flex: 1 },
    },
    dpsChangeScore: {
        properties: { flex: 1 },
    },
    epsShockAdj: {
        properties: { flex: 1 },
    },
    brokerRevisionsScore: {
        properties: { flex: 1 },
    },
    earningsScore: {
        properties: { flex: 1 },
    },
    earningsScoreAdj: {
        properties: { flex: 1 },
    },
    direction: {
        properties: { flex: 1 },
    },
    overallScore: {
        properties: { flex: 1 },
    },
    daysSinceScore: {
        properties: { flex: 1 },
    },
    daysToScore: {
        properties: { flex: 1 },
    },
    percentileScore: {
        properties: { flex: 1 },
    },
    rsiScore: {
        properties: { flex: 1 },
    },
    timeSpentExtendedScore: {
        properties: { flex: 1 },
    },
    rolling1mPerfScore: {
        properties: { flex: 1 },
    },
    distFrom50dScore: {
        properties: { flex: 1 },
    },
    sdOnRegressionScore: {
        properties: { flex: 1 },
    },
    nMoveScore: {
        properties: { flex: 1 },
    },
    shootingStarScore: {
        properties: { flex: 1 },
    },
    hangingManScore: {
        properties: { flex: 1 },
    },
    intradayReversal: {
        properties: { flex: 1 },
    },
    xadvScore: {
        properties: { flex: 1 },
    },
    gapUpScore: {
        properties: { flex: 1 },
    },
    jpmCrowdedScore: {
        properties: { flex: 1 },
    },
    aNRscore: {
        properties: { flex: 1 },
    },
    shortInterestScore: {
        properties: { flex: 1 },
    },
    shortInterestChg: {
        properties: { flex: 1 },
    },
    revGrowthScore: {
        properties: { flex: 1 },
    },
    peRelzScore: {
        properties: { flex: 1 },
    },
    peSectorRelzScore: {
        properties: { flex: 1 },
    },
    peSlopeScore: {
        properties: { flex: 1 },
    },
    gradientScore: {
        properties: { flex: 1 },
    },
    gradientScoreRel: {
        properties: { flex: 1 },
    },
    gradientScoreSpec: {
        properties: { flex: 1 },
    },
    gradientScoreST: {
        properties: { flex: 1 },
    },
    gradientScoreRelST: {
        properties: { flex: 1 },
    },
    gradientScoreSpecST: {
        properties: { flex: 1 },
    },
    catalystScore: {
        properties: { flex: 1 },
    },
    gradientScoreCombined: {
        properties: { flex: 1 },
    },
    gradientScoreSTCombined: {
        properties: { flex: 1 },
    },
    extensionScoreCombined: {
        properties: { flex: 1 },
    },
    priceAndVolume: {
        properties: { flex: 1 },
    },
    gradientKinkScore: {
        properties: { flex: 1 },
    },
    pEGratioScore: {
        properties: { flex: 1 },
    },
    fundamentalScore: {
        properties: { flex: 1 },
    },
};
