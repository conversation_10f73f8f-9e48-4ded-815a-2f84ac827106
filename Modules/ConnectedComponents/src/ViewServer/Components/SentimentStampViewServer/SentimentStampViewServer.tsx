import React, { ComponentProps, useMemo } from "react";

import { withSchemaOverrides } from "@mocks/api-gateway";
import type { Schema } from "Contracts/index";
import { withFilterValues, Stamp, StampTitleWithMark } from "Components/index";
import {
    createConfig,
    withCommandStateFactory,
    withConnectedSkeleton,
    reportSubscriptionCommandSpec,
} from "TransportGateway/index";
import { withSkeletonComponentDefinition, SkeletonRect } from "PrimitiveComponents/index";

import { schemaOverride } from "./schemaOverride";
import { useFeature } from "../../../ApiGateway";

type SentimentData = {
    sentimentCheck: number;
};

type SentimentStampViewServerBaseProps = {
    data?: SentimentData;
    schema?: Schema;
    title?: string;
    score?: number;
} & ComponentProps<typeof Stamp>;

const SentimentStampViewServerBase = withSkeletonComponentDefinition(
    ({
        data,
        schema: schemaProp,
        title: titleProp,
        ...rest
    }: SentimentStampViewServerBaseProps) => {
        const { hasFeature } = useFeature();
        const isDSPHNVersion = hasFeature("dsp.hn-version");

        const schema = useMemo(() => withSchemaOverrides(schemaProp, schemaOverride), [schemaProp]);

        const title = useMemo(
            () =>
                isDSPHNVersion ? (
                    <StampTitleWithMark
                        title={titleProp}
                        score={data?.sentimentCheck}
                    />
                ) : (
                    titleProp
                ),
            [titleProp, data, isDSPHNVersion],
        );

        return (
            <Stamp
                {...rest}
                title={title}
                data={data}
                schema={schema}
            />
        );
    },
)(SkeletonRect);

const EQUITY_REPORT = "HN_Cockpit_Positioning_JPM";
const INDEX_REPORT = "HN_SectorCockpit_Positioning_JPM";

type SentimentStampViewServerProps = {
    isIndex?: boolean;
};
export const SentimentStampViewServer = withFilterValues(
    withConnectedSkeleton(
        withCommandStateFactory(
            SentimentStampViewServerBase,
            ({ isIndex }: SentimentStampViewServerProps) =>
                createConfig({
                    props: reportSubscriptionCommandSpec(
                        isIndex ? INDEX_REPORT : EQUITY_REPORT,
                        ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                        (data, props, schema) => ({ data: data?.[0], schema }),
                        "ProjectionNode",
                    ),
                }),
        ),
    ),
    ["bbgTicker"],
    {
        bbgTicker: {
            required: true,
        },
    },
);
