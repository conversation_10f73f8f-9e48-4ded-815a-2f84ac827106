import React, { useMemo } from "react";
import moment from "moment/moment";

import { withSchemaOverrides } from "@mocks/api-gateway";
import {
    createConfig,
    withCommandState,
    withConnectedSkeleton,
    reportSubscriptionCommandSpec,
} from "TransportGateway/index";
import { withSkeletonComponentDefinition, SkeletonRect } from "PrimitiveComponents/index";
import type { Schema } from "Contracts/index";
import { withFilterValues, type DivergenceData } from "Components/index";

import {
    PositioningExtended,
    type OwnershipData,
    type PutCallData,
    type ScoreData,
} from "./PositioningExtended";
import type { BrokerChangesData, PositioningData } from "../ANRViewServer";
import { putCallSchemaOverride, crowdingSchemaOverride } from "./schemaOverride";
import { positioningSchemaOverride } from "../ANRViewServer/schemaOverride";

type PositioningExtendedViewServerBaseProps = {
    positioning?: {
        data?: PositioningData;
        schema?: Schema;
    };
    score?: {
        data?: ScoreData[];
        schema?: Schema;
    };
    ownership?: {
        data?: OwnershipData[];
        schema?: Schema;
    };
    brokerDowngrades?: {
        data?: BrokerChangesData[];
        schema?: Schema;
    };
    brokerUpgrades?: {
        data?: BrokerChangesData[];
        schema?: Schema;
    };
    putCall?: {
        data?: PutCallData;
        schema?: Schema;
    };
    divergence?: {
        data?: DivergenceData;
        schema?: Schema;
    };
    crowding?: {
        data?: Record<string, unknown>[];
        schema?: Schema;
    };
};

const PositioningExtendedViewServerBase = withSkeletonComponentDefinition(
    ({
        positioning,
        ownership,
        brokerDowngrades,
        brokerUpgrades,
        putCall,
        divergence,
        score,
        crowding,
        ...rest
    }: PositioningExtendedViewServerBaseProps) => {
        const positioningSchema = useMemo(
            () => withSchemaOverrides(positioning?.schema ?? [], positioningSchemaOverride),
            [positioning],
        );
        const putCallSchema = useMemo(
            () => withSchemaOverrides(putCall?.schema ?? [], putCallSchemaOverride),
            [putCall],
        );
        const crowdingSchema = useMemo(
            () => withSchemaOverrides(crowding?.schema ?? [], crowdingSchemaOverride),
            [crowding],
        );

        return (
            <PositioningExtended
                {...rest}
                positioningData={positioning?.data}
                positioningSchema={positioningSchema}
                ownershipData={ownership?.data}
                ownershipSchema={ownership?.schema}
                brokerDowngradesData={brokerDowngrades?.data}
                brokerDowngradesSchema={brokerDowngrades?.schema}
                brokerUpgradesData={brokerUpgrades?.data}
                brokerUpgradesSchema={brokerUpgrades?.schema}
                putCallData={putCall?.data}
                putCallSchema={putCallSchema}
                divergenceData={divergence?.data}
                divergenceSchema={divergence?.schema}
                scoreData={score?.data}
                scoreSchema={score?.schema}
                crowdingData={crowding?.data}
                crowdingSchema={crowdingSchema}
            />
        );
    },
)(SkeletonRect);

export const PositioningExtendedViewServer = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            PositioningExtendedViewServerBase,
            createConfig({
                positioning: reportSubscriptionCommandSpec(
                    "HN_Cockpit_Positioning_CrowdedScore_SingleTicker",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data: data?.[0], schema }),
                    "CalcNode",
                ),
                score: reportSubscriptionCommandSpec(
                    "HN_Cockpit_Positioning_CrowdedScore_SingleTicker",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data, schema }),
                    "ProjectionNodeWidget",
                ),
                ownership: reportSubscriptionCommandSpec(
                    "HN_cockpit_ownership",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data, schema }),
                    "ProjectionNode",
                ),
                brokerDowngrades: reportSubscriptionCommandSpec(
                    "HN_Cockpit_Positioning_BrokerChangesSinceFigs",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data, schema }),
                    "ProjectionNodeDowngrades",
                ),
                brokerUpgrades: reportSubscriptionCommandSpec(
                    "HN_Cockpit_Positioning_BrokerChangesSinceFigs",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data, schema }),
                    "ProjectionNodeUpgrades",
                ),
                putCall: reportSubscriptionCommandSpec(
                    "HN_Cockpit_PutCallSpread",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data: data?.[0], schema }),
                    "FilterLast",
                ),
                divergence: reportSubscriptionCommandSpec(
                    "HN_Cockpit_Positioning_Divergence",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data: data?.[0], schema }),
                    "ProjectionNode",
                ),
                crowding: reportSubscriptionCommandSpec(
                    "JPM_Single_Stock_Crowding_SingleStock",
                    ({ bbgTicker }) => (bbgTicker ? { BBGTicker: bbgTicker } : undefined),
                    (data, props, schema) => ({ data, schema }),
                    "ProjectionNode",
                ),
            }),
        ),
    ),
    ["bbgTicker"],
    {
        bbgTicker: {
            required: true,
        },
    },
);
