import React, { useMemo } from "react";

import { withSchemaOverrides } from "@mocks/api-gateway";
import {
    createConfig,
    withCommandState,
    withConnectedSkeleton,
    reportSubscriptionCommandSpec
} from "TransportGateway/index";
import { withSkeletonComponentDefinition, SkeletonRect } from "PrimitiveComponents/index";
import type { Schema } from "Contracts/index";
import { Grid } from "Grid/index";
import { monthNumberToMonthAcronym } from "Utilities/index";
import { withFilterValues } from "Components/index";

import { schemaOverride } from "./schemaOverride";

type SeasonalityViewServerBaseProps = {
    data?: {
        month: number;
    }[];
    schema?: Schema;
};

const SeasonalityViewServerBase = withSkeletonComponentDefinition(
    ({ data: dataProp, schema: schemaProp, ...rest }: SeasonalityViewServerBaseProps) => {
        const schema = useMemo(() => withSchemaOverrides(schemaProp, schemaOverride), [schemaProp]);
        const data = useMemo(
            () =>
                dataProp?.map(d => ({
                    ...d,
                    month: monthNumberToMonthAcronym(d.month)
                })),
            [dataProp]
        );

        return (
            <Grid
                data={data}
                schema={schema}
                domLayout="autoHeight"
                layout="xx-full"
                hasTransparentBackground
                hasAllSolidBorders
                hasNoFirstCell
                {...rest}
            />
        );
    }
)(SkeletonRect);

export const SeasonalityViewServer = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            SeasonalityViewServerBase,
            createConfig({
                props: reportSubscriptionCommandSpec(
                    "HN_Cockpit_Seasonaility",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data, schema }),
                    "ProjectionNode"
                )
            })
        )
    ),
    ["bbgTicker"],
    {
        bbgTicker: {
            required: true
        }
    }
);
