import React, { ComponentType, useEffect, useMemo, useState, ReactNode } from "react";

import { withCommandState, createConfig, subscriptionCommandSpec } from "TransportGateway/index";
import { withConfirm, Button, Modal, HOC } from "PrimitiveComponents/index";
import { AppIcon, cn } from "Theming/index";
import { componentFactory } from "Utilities/ComponentFactory";

import {
    useChatGPTOverrides,
    type GPTOverridesRequest,
    getTickerAndDateAndContextKey,
} from "../../hooks";

const ChatGPTOverrideBase = ({ children, override }) => children({ override });

const ChatGPTOverride = withCommandState(
    ChatGPTOverrideBase,
    createConfig({
        override: subscriptionCommandSpec(
            "SubscribeToRemote",
            ({
                ticker,
                earningDate,
                contextKey,
            }: {
                ticker: string;
                earningDate: string;
                contextKey: string;
            }) => {
                const tickerAndDateAndContextKey = getTickerAndDateAndContextKey({
                    ticker,
                    earningDate,
                    contextKey,
                });
                return {
                    ServerUri: "CompanyPage",
                    RemoteCommandName: "SubscribeToOperator",
                    RemoteCommandPayload: JSON.stringify({
                        OperatorPath: "/Reports/ChatGPTOverrides/ChatGPTOverride",
                    }),
                    pagination: {
                        FilterExpression: `row.TickerAndDateAndContextKey == "${tickerAndDateAndContextKey}"`,
                    },
                };
            },
            data => data?.[0],
        ),
    }),
);

const DeleteWithConfirm = withConfirm(Button);

/**
 * Manages a delayed show state with override support.
 * The state becomes true after a delay, or immediately if overridden.
 */
const useDelayedShow = (isOverrode: boolean): boolean => {
    const [show, setShow] = useState(false);

    useEffect(() => {
        if (show) return;
        const timer = setTimeout(() => setShow(true), 300);
        return () => clearTimeout(timer);
    }, [show]);

    useEffect(() => {
        if (show || !isOverrode) return;
        setShow(true);
    }, [isOverrode, show]);

    return show;
};

const ChatGPTOverrideInnerItem = ({
    value: originalValue,
    contextKey,
    componentName,
    componentProps,
    earningDate,
    ticker,
    userName,
    override,
}) => {
    const isOverrode = Boolean(override);
    const value = isOverrode ? override.override : originalValue;

    const { remove, create } = useChatGPTOverrides();
    const [modalValue, setModalValue] = useState(value);

    const tickerAndDateAndContextKey = getTickerAndDateAndContextKey({
        ticker,
        earningDate,
        contextKey,
    });

    useEffect(() => {
        setModalValue(value);
    }, [value]);

    const onChange = (v: string) => {
        setModalValue(v);
    };

    const onSave = () => {
        create([
            {
                ticker,
                editedBy: userName,
                override: modalValue,
                contextKey,
                earningDate,
            },
        ]);
    };

    const onDelete = () => {
        remove([tickerAndDateAndContextKey]);
    };

    const EditComponent = useMemo<
        ComponentType<{
            value: string;
            onChange: (v: string) => void;
        }>
    >(() => componentFactory(componentName), [componentName]);

    const show = useDelayedShow(isOverrode);
    if (!show) return null;

    return (
        <div className="flex flex-col gap-md p-lg bg-container-level-2">
            <div className="flex gap-md">
                <div className="w-[100px]">Context Key:</div>
                <div className="flex-1 text-highlighted-inversed">{contextKey}</div>
            </div>
            {isOverrode && (
                <>
                    <div className="flex gap-md">
                        <div className="w-[100px]">Edited By:</div>
                        <div className="flex-1 text-highlighted-inversed">
                            {override?.editedBy ?? "N/A"}
                        </div>
                    </div>
                    <div className="flex gap-md">
                        <div className="w-[100px]">Edited On:</div>
                        <div className="flex-1 text-highlighted-inversed">
                            {override?.editedOn
                                ? new Date(override.editedOn).toLocaleString()
                                : "N/A"}
                        </div>
                    </div>
                    <div className="flex gap-md">
                        <div className="w-[100px]">Original Value:</div>
                        <div className="flex-1 text-highlighted-inversed">
                            {isOverrode ? originalValue : "N/A"}
                        </div>
                    </div>
                </>
            )}
            <EditComponent
                {...componentProps}
                value={modalValue}
                onChange={onChange}
            />
            <div className="flex justify-end gap-lg">
                {isOverrode && (
                    <span>
                        <DeleteWithConfirm
                            onClick={onDelete}
                            type="danger"
                        >
                            Delete Override
                        </DeleteWithConfirm>
                    </span>
                )}

                <Button
                    onClick={onSave}
                    type="primary"
                    disabled={modalValue === value}
                >
                    Save
                </Button>
            </div>
        </div>
    );
};

type ChatGPTOverrideInnerProps = {
    Component: ComponentType<{ value: string; children: ReactNode }>;
    contextKey: string;
    componentName: string;
    componentProps: Record<string, unknown>;
    override: GPTOverridesRequest;
    tickerAndDateAndContextKey: string;
    children: ReactNode;
    overridable: boolean;
    earningDate: string;
    ticker: string;
    userName: string;
    value: string;

    additionalOverrides?: ChatGPTOverrideInnerProps[];
};

const ChatGPTOverrideInner = ({
    Component,
    contextKey,
    componentName,
    componentProps,
    override,
    children,
    value: originalValue,
    overridable,
    earningDate,
    ticker,
    userName,

    additionalOverrides,
}: ChatGPTOverrideInnerProps) => {
    const [showEdit, setShowEdit] = useState(false);

    const isOverrode = Boolean(override);
    const value = isOverrode ? override.override : originalValue;

    const show = useDelayedShow(isOverrode);
    if (!show) return null;

    if (!overridable) return <Component value={value}>{children}</Component>;

    return (
        <div className="flex w-full gap-lg">
            <div className="flex-1">
                <Component value={value}>{children}</Component>
            </div>

            <AppIcon
                type="edit"
                onClick={() => setShowEdit(true)}
                title="Override value"
                className={cn({
                    "text-highlighted/70 hover:text-highlighted": isOverrode,
                })}
            />

            <Modal
                title="Overrides"
                visible={showEdit}
                onClose={() => setShowEdit(false)}
                width="700px"
            >
                <div className="flex flex-col gap-lg">
                    <ChatGPTOverrideInnerItem
                        value={originalValue}
                        contextKey={contextKey}
                        componentName={componentName}
                        componentProps={componentProps}
                        earningDate={earningDate}
                        ticker={ticker}
                        userName={userName}
                        override={override}
                    />

                    {additionalOverrides?.map(additionalOverrideProps => (
                        <ChatGPTOverride
                            key={additionalOverrideProps.contextKey}
                            {...additionalOverrideProps}
                        >
                            {({ override }) => (
                                <ChatGPTOverrideInnerItem
                                    override={override}
                                    {...additionalOverrideProps}
                                />
                            )}
                        </ChatGPTOverride>
                    ))}
                </div>
            </Modal>
        </div>
    );
};

type WithChatGPTOverridesProps = {
    overridable: boolean;
    ticker: string;
    earningDate: string;
    userName: string;
    value: string;
    children: ReactNode;
    contextKey: string;
    componentName: string;
    componentProps?: Record<string, unknown>;
};

export const withChatGPTOverrides = <P extends WithChatGPTOverridesProps>(
    Component: ComponentType<P>,
) => {
    const WrappedComponent = (props: P) => {
        const { ticker, earningDate, contextKey } = props;

        return (
            <ChatGPTOverride
                ticker={ticker}
                earningDate={earningDate}
                contextKey={contextKey}
            >
                {({ override }) => (
                    <ChatGPTOverrideInner
                        Component={Component}
                        override={override}
                        {...(props as unknown as ChatGPTOverrideInnerProps)}
                    />
                )}
            </ChatGPTOverride>
        );
    };

    return HOC("withChatGPTOverrides", WrappedComponent, Component);
};
