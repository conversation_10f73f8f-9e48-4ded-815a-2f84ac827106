import React, { ComponentProps, useMemo } from "react";

import { withSchemaOverrides } from "@mocks/api-gateway";
import type { Schema } from "Contracts/index";
import { withFilterValues, Stamp, StampTitleWithMark } from "Components/index";
import {
    createConfig,
    withCommandState,
    withConnectedSkeleton,
    reportSubscriptionCommandSpec,
} from "TransportGateway/index";
import { withSkeletonComponentDefinition, SkeletonRect } from "PrimitiveComponents/index";

import { schemaOverride } from "./schemaOverride";
import { useFeature } from "../../../ApiGateway";

type PeersData = {
    valuationScore: number;
};

type PeersStampViewServerBaseProps = {
    data?: PeersData;
    schema?: Schema;
    title?: string;
    score?: {
        redFlagCheck: number;
    };
} & ComponentProps<typeof Stamp>;

const PeersStampViewServerBase = withSkeletonComponentDefinition(
    ({
        data,
        schema: schemaProp,
        title: titleProp,
        score,
        ...rest
    }: PeersStampViewServerBaseProps) => {
        const { hasFeature } = useFeature();
        const isDSPHNVersion = hasFeature("dsp.hn-version");

        const schema = useMemo(() => withSchemaOverrides(schemaProp, schemaOverride), [schemaProp]);

        const title = useMemo(
            () =>
                isDSPHNVersion ? (
                    <StampTitleWithMark
                        title={titleProp}
                        score={score?.redFlagCheck}
                    />
                ) : (
                    titleProp
                ),
            [titleProp, score, isDSPHNVersion],
        );

        return (
            <Stamp
                {...rest}
                title={title}
                data={data}
                schema={schema}
            />
        );
    },
)(SkeletonRect);

export const PeersStampViewServer = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            PeersStampViewServerBase,
            createConfig({
                props: reportSubscriptionCommandSpec(
                    "HN_Cockpit_Peers",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    (data, props, schema) => ({ data, schema }),
                    "ProjectionNode",
                ),
                score: reportSubscriptionCommandSpec(
                    "HN_Cockpit_Peers",
                    ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                    data => data?.[0],
                    "ProjectionNodeScore",
                ),
            }),
        ),
    ),
    ["bbgTicker"],
    {
        bbgTicker: {
            required: true,
        },
    },
);
