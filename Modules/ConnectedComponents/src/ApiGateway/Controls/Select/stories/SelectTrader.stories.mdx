import { useState } from "react";
import { useArgs } from "@storybook/client-api";
import { Meta, Story, Canvas, Description, ArgTypes } from "@storybook/addon-docs";

import {
    ThemeIterator,
    LayoutVertical,
    ThemeContainer
} from "PrimitiveComponents/index";
import { withFixtures } from "TransportGateway/index";
import { BaseStyles } from "Theming/index";

import { SelectTrader } from "../";

export const SelectTraderWithFixtures = withFixtures(SelectTrader, {
    "filters.traders": {
        options: () => import("./fixtures/filters.traders.json")
    }
});

<Meta
    title="ConnectedComponents/Controls/SelectTrader"
    component={SelectTrader}
/>

export const Template = ({ traderId }) => {
    const [, updateArgs] = useArgs();
    return (
        <BaseStyles
            theme="dashboard-dark"
            global={false}
        >
            <ThemeContainer
                style={{
                    margin: 20,
                    maxWidth: "initial"
                }}
            >
                <SelectTraderWithFixtures label="Trader" value={traderId} onChange={id => updateArgs({ traderId: id })}/>
            </ThemeContainer>
        </BaseStyles>
    );
};

# Select Trader

<Description of={SelectTrader} />

<ArgTypes of={SelectTrader} sort="requiredFirst" />

<Canvas>
    <Story
        name="SelectTrader"
        parameters={{}}
        args={{
            traderId: 350
        }}
    >
        {Template.bind({})}
    </Story>
</Canvas>
