import { ComponentProps } from "react";

import type { ApiGatewayService } from "Contracts/index";
import { SelectWithLabel, withComponentProps } from "PrimitiveComponents/index";

import { withConnectedOptions } from "../../decorators";

export const SelectImpersonatableUser = withComponentProps(
    withConnectedOptions<
        ComponentProps<typeof SelectWithLabel<number>>,
        ApiGatewayService.Filters.TradersRequest,
        ApiGatewayService.Filters.TradersResponse
    >(SelectWithLabel, "filters.impersonatable-users", ["fundGroupId"]),
    { placeholder: "", label: "Trader", minWidth: "12em" },
);
