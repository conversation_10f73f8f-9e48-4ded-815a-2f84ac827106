import React, { ComponentType, useEffect, useState } from "react";

import { HOC } from "PrimitiveComponents/index";
import { safeJSONParse } from "Utilities/index";

type Config = Record<string, unknown>;

const CONFIG_KEY = "raid2-user-templates";

type ConfigProps<C = unknown> = {
    id?: string;
    config?: C;
    setConfig?: (config: C) => void;
};

// @TODO: update to use api gateway persist endpoint - for now just stick in local storage
export const withPersistConfig = <P,>(Component: ComponentType<P>) => {
    const WrappedComponent = ({
        id,
        config: outerConfig,
        setConfig: outerSetConfig,
        ...props
    }: P & ConfigProps) => {
        const isControlled = !!outerSetConfig; // Defer config management to parent

        const [config, setConfig] = useState(
            isControlled
                ? undefined
                : safeJSONParse<Config>(localStorage.getItem(CONFIG_KEY))?.[id] || outerConfig
        );

        useEffect(() => {
            if (!isControlled) {
                const current = safeJSONParse<Config>(localStorage.getItem(CONFIG_KEY)) || {};
                localStorage.setItem(
                    CONFIG_KEY,
                    JSON.stringify({
                        ...current,
                        [id]: config
                    })
                );
            }
        }, [config, id, isControlled]);

        return (
            <Component
                id={id}
                {...(props as P)}
                {...(isControlled
                    ? {
                          config: outerConfig,
                          setConfig: outerSetConfig
                      }
                    : {
                          config,
                          setConfig
                      })}
            />
        );
    };

    return HOC("withPersistConfig", WrappedComponent, Component);
};
