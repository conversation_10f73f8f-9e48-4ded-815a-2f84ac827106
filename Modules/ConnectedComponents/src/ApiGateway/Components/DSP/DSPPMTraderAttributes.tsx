import React, { useMemo } from "react";
import {
    withConnectedSkeleton,
    withCommandState,
    createConfig,
    apiGatewaySpec
} from "TransportGateway/index";
import { ApiGatewayService } from "Contracts/index";
import {
    withSkeletonComponentDefinition,
    MetaDataGroupsProps,
    SkeletonMetaDataGroups,
    withLabelGroup,
    MetaDataGroups as MetaDataGroupsBase
} from "PrimitiveComponents/index";

export const DSPMetaDataGroups = withSkeletonComponentDefinition(
    ({ metaData, ...rest }: MetaDataGroupsProps) => {
        const filteredMetaData = useMemo(
            () => metaData?.filter(d => d.name !== "Method"),
            [metaData]
        );
        return (
            <MetaDataGroupsBase
                metaData={filteredMetaData}
                {...rest}
            />
        );
    }
)(SkeletonMetaDataGroups);

export const DSPPMTraderAttributes = withConnectedSkeleton(
    withCommandState(
        withLabelGroup(DSPMetaDataGroups),
        createConfig({
            props: apiGatewaySpec<
                ApiGatewayService.PMFolder.TraderAttributesRequest,
                ApiGatewayService.PMFolder.TraderAttributesResponse
            >(
                "pm-folder.trader-attributes",
                ({ traderId, fundGroupId }) =>
                    traderId && {
                        traderId,
                        fundGroupId
                    },
                (data, _, schema) => ({ metaData: data, schema })
            )
        })
    )
);
