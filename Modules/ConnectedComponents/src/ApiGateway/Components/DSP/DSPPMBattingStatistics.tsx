import {
    withConnectedSkeleton,
    withCommandState,
    createConfig,
    apiGatewaySpec
} from "TransportGateway/index";
import { ApiGatewayService } from "Contracts/index";
import { withComponentProps, withLabelGroup } from "PrimitiveComponents/index";
import { PMBattingStatistics as PMBattingStatisticsBase } from "Components/index";

export const DSPPMBattingStatistics = withConnectedSkeleton(
    withComponentProps(
        withCommandState(
            withLabelGroup(PMBattingStatisticsBase),
            createConfig({
                propsStats: apiGatewaySpec<
                    ApiGatewayService.PMFolder.BattingStatisticsRequest,
                    ApiGatewayService.PMFolder.BattingStatisticsResponse
                >(
                    "pm-folder.batting-statistics",
                    ({ traderId, fundGroupId }) =>
                        traderId && {
                            traderId,
                            fundGroupId
                        },
                    (data, props, schema) => ({ statistics: data, statisticsSchema: schema })
                )
            })
        ),
        () => ({
            includeLatestStatistics: false
        })
    )
);
