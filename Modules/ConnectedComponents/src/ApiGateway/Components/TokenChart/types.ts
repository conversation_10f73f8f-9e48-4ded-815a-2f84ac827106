import { ReactNode } from "react";

import type { ChartSeriesType, GradientType, StrokeType } from "Charting/index";
import type { Workflow } from "Contracts/index";
import type { NumberFormatType } from "Utilities/index";

export type SeriesDomain = {
    min: {
        dataMin?: boolean;
        offset?: number;
    };
    max: {
        dataMax: boolean;
        offset?: number;
    };
};

export type SeriesDefinitionYAxis = {
    domain?: SeriesDomain;
    label?: ReactNode;
    valueFormat?: string;
};

export type SeriesDefinitionXAxis = {
    label?: ReactNode;
};

export type Series = Workflow.Series.SeriesType<
    {
        seriesType: ChartSeriesType;

        valueFormat?: string;

        stroke?: StrokeType;

        gradient?: GradientType;

        hasDot?: boolean;
        connectNulls?: boolean;

        yAxis?: {
            position?: "auto" | "left" | "right";
        };

        stackId?: number;
    },
    Workflow.Token.Token<unknown>
>;

export type ChartDefinition = Workflow.Series.SeriesDefinition<
    {
        showLegend?: boolean;
        showXAxis?: boolean;
        showYAxis?: boolean;
        showTooltip?: boolean;
        showCartesianGrid?: boolean;

        yAxis?: {
            left?: SeriesDefinitionYAxis;
            right?: SeriesDefinitionYAxis;
        };
        xAxis?: SeriesDefinitionXAxis;

        referenceLine?: {
            show?: boolean;
            yAxis?: "left" | "right";
        };

        yAxisLeftWidth?: number | "auto";
        yAxisRightWidth?: number | "auto";

        defaultValueFormat?: NumberFormatType;
    },
    Workflow.Token.Token<unknown>,
    Series["definition"]
>;
