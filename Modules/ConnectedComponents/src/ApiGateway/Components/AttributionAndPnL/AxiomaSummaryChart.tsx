import { Chart } from "Charting/index";
import { withFilterValues } from "Components/index";
import { withComponentProps } from "PrimitiveComponents/index";
import type { ApiGatewayService } from "Contracts/index";
import {
    apiGatewaySpec,
    createConfig,
    withCommandState,
    withConnectedSkeleton
} from "TransportGateway/index";

export const AxiomaSummaryChart = withFilterValues(
    withConnectedSkeleton(
        withComponentProps(
            withCommandState(
                Chart,
                createConfig({
                    props: apiGatewaySpec<
                        ApiGatewayService.RiskModel.AxiomaSummarySeriesRequest,
                        ApiGatewayService.RiskModel.AxiomaSummarySeriesResponse
                    >(
                        "risk-page.axioma-summary-series",
                        ({
                            fundGroupId,
                            riskModelName,
                            traderId,
                            datePreset,
                            returnAttributionType
                        }) =>
                            fundGroupId && {
                                riskModelName,
                                datePreset,
                                fundGroupId,
                                traderId,
                                returnAttributionType
                            },
                        (data, _props, schema) => ({
                            data,
                            schema
                        })
                    )
                })
            ),
            {
                labelAlign: "center" as const,
                datePreset: "6M" as const,
                riskModelName: "EU4AxiomaSH"
            }
        )
    ),
    ["fundGroupId", "traderId"],
    {
        fundGroupId: {
            required: true
        }
    }
);
