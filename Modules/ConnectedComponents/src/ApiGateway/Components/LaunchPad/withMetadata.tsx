import React, { ComponentType, ReactNode, useMemo } from "react";

import { ApiGatewayService } from "Contracts/index";
import { SectionsProps, NavigationProps } from "Components/index";
import { HOC, withBadge } from "PrimitiveComponents/index";

import { Avatar } from "../Avatar";

const AppIconWithBadge = withBadge(({ children }: { children: ReactNode }) => <>{children}</>);

const optionFactory =
    (
        metadata: ApiGatewayService.Notification.ActiveMessageCountResponse[]
    ): NavigationProps<
        Pick<ApiGatewayService.Notification.ActiveMessageCountResponse, "laneId">
    >["optionFactory"] =>
    ({ componentProps, id, icon }, el) => {
        const lane =
            componentProps?.laneId &&
            metadata?.find(({ laneId }) => laneId === componentProps.laneId);

        if (lane) {
            return (
                <AppIconWithBadge
                    badge={lane?.count}
                    badgeType={lane.isPrimary ? "primary" : "default"}
                >
                    {el}
                </AppIconWithBadge>
            );
        }

        if (id === "settings") {
            return (
                <Avatar
                    defaultIcon={icon}
                    outerPadding="medium"
                    hasConnectedState={false}
                />
            );
        }

        return undefined;
    };

type MetadataProps = {
    metadata?: ApiGatewayService.Notification.ActiveMessageCountResponse[];
};

export const withMetadata = <P extends SectionsProps & MetadataProps>(
    Component: ComponentType<P>
) => {
    const WrappedComponent = ({
        metadata,
        activeSection: activeSectionOuter,
        sections,
        ...props
    }: P) => {
        const launchpadOptionFactory = useMemo(() => optionFactory(metadata), [metadata]);

        const activeSection = useMemo(
            () =>
                activeSectionOuter ||
                sections?.find(section => section.type === "Option" && !section.isDisabled)?.id,
            [activeSectionOuter, sections]
        );

        return (
            <Component
                {...(props as P)}
                activeSection={activeSection}
                sections={sections}
                optionFactory={launchpadOptionFactory}
            />
        );
    };

    return HOC("withMetadata", WrappedComponent, Component);
};
