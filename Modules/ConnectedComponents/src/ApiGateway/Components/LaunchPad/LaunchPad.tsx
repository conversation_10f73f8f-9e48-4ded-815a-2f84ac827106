import React, { ReactNode, useMemo } from "react";
import styled from "styled-components";

import {
    apiGatewaySpec,
    apiGatewaySubscriptionSpec,
    createConfig,
    withCommandState,
    withConnectedComponentStatus,
} from "TransportGateway/index";
import type { ApiGatewayService } from "Contracts/index";
import {
    Sections,
    type SectionsProps,
    FiltersProvider,
    type AllFilters,
    type SectionType,
} from "Components/index";
import { withComponentProps, useComponentsPersistState } from "PrimitiveComponents/index";
import { withAnalyticsInteractionViewport, withAnalyticsData } from "Analytics/index";

import { useFeature, withSectionData } from "../../decorators";

import { withMetadata } from "./withMetadata";

const LaunchPadSections = styled(Sections)`
    background-color: var(--color__background--overlay-content);
`;

const SectionContainer = withAnalyticsInteractionViewport(
    ({ children }: { children: ReactNode }) => <>{children}</>,
);

const LaunchPadWithProviders = withAnalyticsData(
    ({ sections, ...props }: SectionsProps) => {
        const [filters, setFilters] = useComponentsPersistState<AllFilters>("launchpad.filters");
        const { features } = useFeature();

        /**
         * @deprecated @TODO: remove once settings is released
         */
        const decoratedSections = useMemo<SectionType[]>(() => {
            if (features["notification.advanced-settings"]) {
                return (
                    sections && [
                        ...sections.filter(option => option.id !== "settings"),
                        {
                            id: "settings",
                            type: "Option",
                            name: "Settings",
                            url: "menu",
                            icon: "user",
                            isDisabled: false,
                            componentName: "ClientApi.Settings",
                            action: {
                                actionType: "Popout.Open",
                                label: "Settings",
                                actionParams: {
                                    componentName: "ClientApi.Settings",
                                    componentProps: {
                                        v2: true,
                                    },
                                    containerProps: {
                                        id: "ClientApi.Settings",
                                        title: "Settings",
                                        hasPadding: false,
                                        popoutType: "overlay",
                                        minWidth: "74em",
                                        minHeight: "75vh",
                                        maxHeight: "74em",
                                    },
                                },
                            },
                            sections: [
                                // If user has permission to impersonate:
                                {
                                    id: "ImpersonateUser",
                                    parentRowId: "settings",
                                    type: "Option",
                                    name: "Impersonate User",
                                    icon: "impersonate",
                                    componentName: "ConnectedComponents.ImpersonateUser",
                                },
                            ],
                        },
                    ]
                );
            }

            return undefined;
        }, [features, sections]);

        return (
            <FiltersProvider
                defaultFilters={filters}
                onChange={setFilters}
            >
                <LaunchPadSections
                    {...props}
                    sections={decoratedSections || sections}
                    SectionContainer={SectionContainer}
                />
            </FiltersProvider>
        );
    },
    { type: "dimension", name: "component", value: "ConnectedComponents.LaunchPad" },
);

export const LaunchPad = withConnectedComponentStatus(
    withCommandState(
        withSectionData(
            withMetadata(
                withComponentProps(LaunchPadWithProviders, {
                    scrollable: true,
                    containerType: "menu",
                }),
            ),
        ),
        createConfig({
            sectionsData: apiGatewaySpec<never, ApiGatewayService.Dashboard.SectionsResponse>(
                "launch-pad.section-table",
                () => ({}),
            ),
            metadata: apiGatewaySubscriptionSpec<
                ApiGatewayService.Notification.ActiveMessageCountRequest,
                ApiGatewayService.Notification.ActiveMessageCountResponse
            >("notification.active-message-count", () => ({})),
        }),
    ),
);
