import React, { ComponentProps, ComponentType } from "react";

import { Tooltip, HOC } from "PrimitiveComponents/index";
import { useServiceConfig } from "TransportGateway/index";

import { AttachmentsMetadata } from "../AttachmentsMetadata";

type OuterProps = {
    multiple?: boolean;
    disableTooltip?: boolean;
} & ComponentProps<typeof AttachmentsMetadata>;

export const withMetadataTooltip = <P,>(Component: ComponentType<P>) => {
    const WrappedComponent = ({
        columns,
        value,
        commandName,
        disableTooltip,
        multiple,
        ...rest
    }: P & OuterProps) => {
        const { apiGatewayEndpoint } = useServiceConfig();

        return (
            <Tooltip
                maxHeight="25vh"
                content={
                    disableTooltip !== true &&
                    ((Array.isArray(value) && value?.length > 0) || value) && (
                        <AttachmentsMetadata
                            {...(rest as P)}
                            $transactionId={value}
                            value={value}
                            baseUrl={apiGatewayEndpoint}
                            commandName={commandName}
                            columns={columns || multiple ? 2 : 1}
                        />
                    )
                }
            >
                <Component
                    {...(rest as P)}
                    multiple={multiple}
                    value={value}
                    commandName={commandName}
                />
            </Tooltip>
        );
    };

    return HOC(
        "withMetadataTooltip",
        WrappedComponent as ComponentType<
            Omit<ComponentProps<typeof WrappedComponent>, "onDelete">
        >,
        Component
    );
};
