import { useState } from "react";
import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import { ThemeIterator, LayoutVertical, LayoutContainer, LayoutHorizontal, ThemeContainer } from "PrimitiveComponents/index";

import { Fixtures } from "./fixtures/GamePlanFixtures";

import { GamePlan } from "../GamePlan";

<Meta title="ConnectedComponents/Gameplan" component={GamePlan} />

export const Template = args => {
    const [model, setModel] = useState();
    return (
        <LayoutHorizontal>
            <LayoutContainer minHeight={300} minWidth={570} maxWidth={600}>
                <LayoutVertical spacing="xx-large">
                    <ThemeIterator>
                        {() => (
                            <ThemeContainer flex>
                                <LayoutContainer>
                                    <Fixtures>
                                        <LayoutContainer
                                            background="container-level-2"
                                            paddingV="medium"
                                            paddingH="large"
                                        >
                                            <GamePlan
                                                id={12345}
                                                onChangeTradePlan={setModel}
                                                {...args}
                                            />
                                        </LayoutContainer>
                                    </Fixtures>
                                </LayoutContainer>
                            </ThemeContainer>
                        )}
                    </ThemeIterator>
                </LayoutVertical>
            </LayoutContainer>
            <pre>{model && JSON.stringify(model,  null, 2)}</pre>
        </LayoutHorizontal>
    );
};

# GamePlan

<Description of={GamePlan} />

<Canvas>
    <Story
        name="GamePlan"
        parameters={{
            controls: {
                disable: false,
                include: [
                    "readOnly",
                    "editMode"
                ]
            }
        }}
        args={{
            readOnly: false,
            editMode: false
        }}
        argTypes={{
        }}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes of={GamePlan} sort="requiredFirst" />
