import type { JSONSchema } from "Schema/index";

export const GamePlanSchema: JSONSchema = {
    type: "object",
    title: "Idea Summary",
    extension: {
        displayOrder: ["ideaSummary", "thesis"],
        rowGap: "medium",
        flexHeight: true,
    },
    properties: {
        id: {
            type: "integer",
            writeOnly: true,
        },
        ideaSummary: {
            type: "object",
            title: "",
            required: ["edgeVariantPerception"],
            extension: {
                displayOrder: ["ideaDetails", "edgeVariantPerception"],
                rowGap: "small",
                outline: true,
            },
            properties: {
                ideaDetails: {
                    title: "",
                    type: "object",
                    required: ["name", "sourceOfIdea", "ideaTypeId"],
                    extension: {
                        displayOrder: ["name", "sourceOfIdea", "ideaTypeId"],
                        layoutColumns: 3,
                    },
                    properties: {
                        name: {
                            type: "string",
                            extension: {
                                boundTo: "/name",
                                view: '@calc[boolean(#ref(/isLocked)) ? string("readOnly") : undefined]',
                                targetColumn: "1",
                            },
                        },
                        ideaTypeId: {
                            title: "Idea Type",
                            type: "integer",
                            extension: {
                                boundTo: "/ideaTypeId",
                                view: '@calc[boolean(#ref(/isLocked)) ? string("readOnly") : undefined]',
                                schemaProperties: {
                                    component: "ConnectedComponents.Select",
                                    componentPropsCommandName:
                                        '@calc[boolean(#ref(traderId)) ? string("filters.gameplanForTrader.#ref(traderId).idea-types") : undefined]',
                                },
                            },
                        },
                        sourceOfIdea: {
                            title: "Idea Source",
                            type: "string",
                            extension: {
                                boundTo: "/sourceOfIdea",
                                view: '@calc[boolean(#ref(/isLocked)) ? string("readOnly") : undefined]',
                                schemaProperties: {
                                    component: "ConnectedComponents.Select",
                                    componentPropsCommandName:
                                        '@calc[boolean(#ref(traderId)) ? string("filters.gameplanForTrader.#ref(traderId).sourcesOfIdeas") : undefined]',
                                    componentPropsAllowInput: true,
                                },
                            },
                        },
                    },
                },
                edgeVariantPerception: {
                    type: "string",
                    title: "Edge / Variant Perception",
                    maxLength: 200,
                    extension: {
                        boundTo: "/edgeVariantPerception",
                        view: '@calc[boolean(#ref(/isLocked)) ? string("readOnly") : undefined]',
                        rows: 2,
                        maxDisplayRows: 4,
                    },
                },
            },
        },
        thesis: {
            type: "object",
            title: "Trade Thesis",
            extension: {
                rowHeight: "auto",
                displayOrder: ["additionalComponents@theses"],
                additionalComponents: {
                    theses: {
                        title: "",
                        extension: {
                            props: {
                                onAddThesis: "#context(/onAddThesis)",
                            },
                            schemaProperties: {
                                component: "ConnectedComponents.ThesisList",
                                componentPropsId: "#ref(/id)",
                                componentPropsAutoFocus: "#context(/form.autoFocus)",
                            },
                        },
                    },
                },
            },
            properties: {},
        },
    },
};
