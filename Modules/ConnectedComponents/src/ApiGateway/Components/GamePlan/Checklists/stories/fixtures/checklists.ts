import { ApiGatewayService } from "Contracts/index";

const sections: ApiGatewayService.GamePlan.GamePlanChecklistsResponse[] = [
    {
        rowId: "technical-checklist",
        name: "Technical",
        componentName: "ConnectedComponents.GamePlan.Checklists.Technical"
    },
    {
        rowId: "fundamental-checklist",
        name: "Fundamental",
        componentName: "ConnectedComponents.GamePlan.Checklists.Fundamental"
    },
    {
        rowId: "sentiment-checklist",
        name: "Sentiment",
        componentName: "ConnectedComponents.GamePlan.Checklists.Sentiment"
    },
    {
        rowId: "catalyst-checklist",
        name: "Catalysts",
        componentName: "ConnectedComponents.GamePlan.Checklists.Catalysts"
    },
    {
        rowId: "book-impact-checklist",
        name: "Book Impact",
        componentName: "ConnectedComponents.GamePlan.Checklists.BookImpact"
    },
    {
        rowId: "bespoke-checklist",
        name: "Bespoke",
        componentName: "ConnectedComponents.GamePlan.Checklists.Bespoke"
    }
];

export default sections;
