import React, { useMemo } from "react";
import {
    apiGatewaySpec,
    createConfig,
    withCommandState,
    withConnectedSkeleton,
    withCommandEvents,
} from "TransportGateway/index";
import { ApiGatewayService } from "Contracts/index";
import { Sections, SectionsProps, EditOptionProps, withEditOption } from "Components/index";
import {
    withComponentProps,
    withSkeletonComponentDefinition,
    SkeletonGrid,
    withLabelGroup,
} from "PrimitiveComponents/index";

import { withSectionData } from "../../../decorators";
import { SchemaFormProps } from "../../SchemaForm";

import { ChecklistProps } from "./types";

const ChecklistsBase = withSectionData(
    withEditOption(
        withLabelGroup(
            withComponentProps<
                ChecklistProps & SectionsProps<ChecklistProps & SchemaFormProps> & EditOptionProps
            >(
                withSkeletonComponentDefinition(Sections)(SkeletonGrid, {
                    minRows: 6,
                    cols: 2,
                    scale: 2,
                }),
                ({ gamePlanId, checklistId, ticker, traderId, readOnly }) => ({
                    hasLabels: true,
                    mode: "mountAll",
                    sectionProps: {
                        gamePlanId,
                        checklistId,
                        ticker,
                        traderId,
                        readOnly,
                    },
                }),
            ),
        ),
    ),
);

const ChecklistsInner = ({ sectionsData: sectionsDataProp, ...rest }) => {
    const sectionsData = useMemo(
        () =>
            sectionsDataProp?.sort((a, b) => {
                if (a.url === "bespoke-checklist") return -1;
                if (b.url === "bespoke-checklist") return 1;
                return 0;
            }),
        [sectionsDataProp],
    );
    return (
        <ChecklistsBase
            sectionsData={sectionsData}
            {...rest}
        />
    );
};

export const Checklists = withCommandEvents()(
    withComponentProps(
        withConnectedSkeleton(
            withCommandState(
                ChecklistsInner,
                createConfig({
                    sectionsData: apiGatewaySpec<
                        {},
                        ApiGatewayService.GamePlan.GamePlanChecklistsResponse,
                        ChecklistProps
                    >(
                        "gameplan/{gamePlanId}/positions/{gamePlanPositionId}/tabs/{traderId}",
                        ({ gamePlanId, checklistId, traderId }) =>
                            gamePlanId &&
                            checklistId && {
                                gamePlanId,
                                gamePlanPositionId: checklistId,
                                traderId,
                            },
                        undefined,
                        {
                            method: "GET",
                        },
                    ),
                }),
            ),
        ),
        { label: "Checklist", size: "small" },
    ),
);
