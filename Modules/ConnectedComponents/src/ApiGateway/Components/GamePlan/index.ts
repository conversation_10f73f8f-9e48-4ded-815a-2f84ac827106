import { GamePlan } from "./GamePlan";

import { Checklists } from "./Checklists";
import { Earnings } from "./Earnings";
import { TradePlan } from "./TradePlan";

export * from "./Create";
export * from "./GamePlanButton";
export * from "./GamePlanSchema";
export * from "./GamePlanSelector";
export * from "./Positions";
export * from "./ThesisEntryEdit";
export * from "./ThesisList";
export * from "./TradeRationale";
export * from "./Trades";
export * from "./GamePlanMovePositionButton";
export * from "./GamePlanTrades";
export * from "./GamePlanWithPositionsAndTrades";

type DecoratedGamePlanType = typeof GamePlan & {
    Checklists: typeof Checklists;
    Earnings: typeof Earnings;
    TradePlan: typeof TradePlan;
};

const DecoratedGamePlan = GamePlan as DecoratedGamePlanType;

DecoratedGamePlan.Checklists = Checklists;
DecoratedGamePlan.Earnings = Earnings;
DecoratedGamePlan.TradePlan = TradePlan;

export { DecoratedGamePlan as GamePlan };
