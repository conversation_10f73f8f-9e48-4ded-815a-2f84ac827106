import { withCommandStateFactory, createConfig, apiGatewaySpec } from "TransportGateway/index";
import { ApiGatewayService } from "Contracts/index";

import { Notification } from "Notifications/index";

type IdType = "breachId" | "naturalKey";
type Props = {
    id: string | number;
    idType?: IdType;
};

export const ComplianceNotification = withCommandStateFactory(
    Notification,
    ({ idType = "breachId" }: Props) =>
        createConfig({
            props: apiGatewaySpec<
                ApiGatewayService.Notification.ComplianceRuleBreachRequest,
                ApiGatewayService.Notification.ComplianceRuleBreachResponse
            >(
                idType === "naturalKey"
                    ? "compliance-rule-breach.by-breach-natural-key.{id}"
                    : "compliance-rule-breach.by-compliance-rule-breached-id.{id}",
                ({ id }) => ({ id }),
                data => ({
                    data: data?.[0],
                    type: "full"
                }),
                {
                    method: "GET"
                }
            )
        })
);
