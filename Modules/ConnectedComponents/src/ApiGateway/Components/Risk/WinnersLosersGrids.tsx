import React, { ComponentType } from "react";

import { Grid, type GridProps } from "Grid/index";
import {
    createConfig,
    withCommandState,
    apiGatewaySpec,
    withConnectedSkeleton
} from "TransportGateway/index";
import { ApiGatewayService } from "Contracts/index";
import { withFilterValues } from "Components/index";
import { withComponentProps, Flex, Typography } from "PrimitiveComponents/index";
import { isDefined } from "Utilities/index";

type PositionSummaryGridProps = {
    type: "winners-losers";
};

const WinnersLosersGrid = Grid as ComponentType<
    GridProps & {
        type?: "winners" | "losers";
    }
>;

const WinnersGridBase = withComponentProps(
    WinnersLosersGrid,
    {
        hasOutline: true,
        hasTransparentBackground: true
    },
    {
        type: "winners"
    }
);

const LosersGridBase = withComponentProps(
    WinnersLosersGrid,
    {
        hasOutline: true,
        hasTransparentBackground: true
    },
    {
        type: "losers"
    }
);

const WinnersLosersGridsBase = ({ data, schema }) => (
    <Flex
        direction="column"
        gap="large"
        flex="1"
    >
        <Flex
            direction="column"
            flex="1"
            gap="small"
        >
            <Flex>
                <Typography.Text fontWeight="header">Winners</Typography.Text>
            </Flex>
            <Flex flex="1">
                <WinnersGridBase
                    data={data}
                    schema={schema}
                />
            </Flex>
        </Flex>
        <Flex
            direction="column"
            flex="1"
            gap="small"
        >
            <Flex>
                <Typography.Text fontWeight="header">Losers</Typography.Text>
            </Flex>
            <Flex flex="1">
                <LosersGridBase
                    data={data}
                    schema={schema}
                />
            </Flex>
        </Flex>
    </Flex>
);

const createWinnersLosersComponent = Component =>
    withFilterValues(
        withConnectedSkeleton(
            withComponentProps(
                withCommandState(
                    Component,
                    createConfig({
                        props: apiGatewaySpec<
                            Partial<ApiGatewayService.RiskPage.RiskPositionSummaryRequest>,
                            ApiGatewayService.RiskPage.RiskPositionSummaryResponse,
                            PositionSummaryGridProps
                        >(
                            "risk-page.position-summary",
                            ({
                                riskModelName,
                                fundGroupId,
                                fundId,
                                traderId,
                                includeWorkingOrderType,
                                type,
                                useSpecificCovarianceMatrix
                            }) =>
                                isDefined(riskModelName) &&
                                (isDefined(fundGroupId) ||
                                    isDefined(fundId) ||
                                    isDefined(traderId)) && {
                                    traderId,
                                    fundGroupId,
                                    fundId,
                                    includeWorkingOrderType,
                                    riskModelName,
                                    type,
                                    useSpecificCovarianceMatrix
                                },
                            (data, _, schema) => ({
                                data,
                                schema
                            })
                        )
                    })
                ),
                {
                    type: "winners-losers"
                }
            )
        ),
        [
            "fundGroupId",
            "fundId",
            "riskModel",
            "traderId",
            "includeWorkingOrderType",
            "useSpecificCovarianceMatrix"
        ],
        {
            riskModel: {
                required: true
            },
            or: {
                fundGroupId: {
                    required: true
                },
                fundId: {
                    required: true
                },
                traderId: {
                    required: true
                }
            }
        },
        {
            riskModel: "riskModelName"
        }
    );

export const WinnersLosersGrids = createWinnersLosersComponent(WinnersLosersGridsBase);
export const WinnersGrid = createWinnersLosersComponent(WinnersGridBase);
export const LosersGrid = createWinnersLosersComponent(LosersGridBase);
