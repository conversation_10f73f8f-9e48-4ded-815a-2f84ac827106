import { ApiGatewayService } from "Contracts/index";
import { withFilterValues } from "Components/index";
import { Chart } from "Charting/index";

import {
    createConfig,
    withCommandState,
    apiGatewaySpec,
    withConnectedSkeleton
} from "TransportGateway/index";

export const BetRollingChart = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            Chart,
            createConfig({
                props: apiGatewaySpec<
                    ApiGatewayService.RiskPage.BetRollingPerformanceRequest,
                    ApiGatewayService.RiskPage.BetRollingPerformanceResponse
                >(
                    "risk-page.bet-rolling-performance",
                    ({ riskModelName, factorName, datePreset }) => ({
                        datePreset,
                        factorName,
                        riskModelName
                    }),
                    (data, props, schema) => ({ data, schema })
                )
            })
        )
    ),
    ["riskModel", "fundGroupId", "traderId"],
    {
        riskModel: {
            required: true
        },
        fundGroupId: {
            required: true
        }
    },
    {
        riskModel: "riskModelName"
    }
);
