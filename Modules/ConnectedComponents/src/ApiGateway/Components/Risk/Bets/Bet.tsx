import React, { ReactNode } from "react";
import { Flex, Typography } from "PrimitiveComponents/index";
import type { DatePreset as DatePresetType } from "Contracts/index";

import { BetCharts } from "./BetCharts";
import { BetFactorExposureHeatmapChart } from "./BetFactorExposureHeatmapChart";

export const TREE_MAP_CHART_HEIGHT = 180;

type BetWrapperProps = {
    title: ReactNode;
    titleDetail?: ReactNode;
    children: ReactNode;
};

export const BetWrapper = ({ children, title, titleDetail }: BetWrapperProps) => (
    <Flex
        flex="1"
        direction="column"
        gap="large"
        padding="large"
        background="container-level-2"
    >
        <Typography.Text
            fontWeight="header"
            fontSize="large"
        >
            <Flex gap="large">
                <Typography.Text highlighted>{title}</Typography.Text>
                {titleDetail}
            </Flex>
        </Typography.Text>

        {children}
    </Flex>
);

type BetProps = {
    betId: string;
    title: string;
    factor: string;
    datePreset: DatePresetType;
    withTradersTreemap: boolean;
};
export const Bet = ({ betId, title, factor, datePreset, withTradersTreemap }: BetProps) => (
    <BetWrapper title={title}>
        <BetCharts
            syncId={betId}
            factor={factor}
            datePreset={datePreset}
        />

        {withTradersTreemap && (
            <Flex
                direction="column"
                h={TREE_MAP_CHART_HEIGHT}
            >
                <Typography.Text highlightedInversed>Traders</Typography.Text>
                <BetFactorExposureHeatmapChart
                    heatmapType="Trader"
                    factorName={factor}
                />
            </Flex>
        )}

        <Flex
            direction="column"
            h={TREE_MAP_CHART_HEIGHT}
        >
            <Typography.Text highlightedInversed>Tickers</Typography.Text>
            <BetFactorExposureHeatmapChart
                heatmapType="Ticker"
                factorName={factor}
            />
        </Flex>
    </BetWrapper>
);
