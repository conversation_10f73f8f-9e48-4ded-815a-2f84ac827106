import React from "react";

import { ApiGatewayService } from "Contracts/index";
import { Flex } from "PrimitiveComponents/index";
import { withFilterValues } from "Components/index";
import { Chart } from "Charting/index";
import {
    createConfig,
    withCommandState,
    apiGatewaySpec,
    withConnectedSkeleton
} from "TransportGateway/index";
import { isStringType, isDefined } from "Utilities/index";

export const DriversBetFactorExposureHeatmapChart = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            Chart,
            createConfig({
                props: apiGatewaySpec<
                    ApiGatewayService.RiskPage.DriversBetFactorExposureHeatmapRequest,
                    ApiGatewayService.RiskPage.DriversBetFactorExposureHeatmapResponse,
                    ApiGatewayService.RiskPage.DriversBetFactorExposureHeatmapRequest & {
                        traderId?: string;
                        traderInitial?: string;
                        bbgTicker?: string;
                    }
                >(
                    "risk-page.drivers-bet-factor-exposure-heatmap",
                    ({
                        riskModelName,
                        factorDescription,
                        heatmapType,
                        fundGroupId,
                        traderId,
                        traderInitial,
                        bbgTicker
                    }) =>
                        (isDefined(fundGroupId) || isDefined(traderId)) && {
                            factorDescription,
                            heatmapType,
                            riskModelName,
                            ...(isDefined(fundGroupId) && {
                                fundGroupIds: [fundGroupId]
                            }),
                            ...(isDefined(traderId) && {
                                traderIds: [traderId]
                            }),
                            ...(isStringType(bbgTicker) && {
                                bbgTickers: [bbgTicker]
                            }),
                            ...(isStringType(traderInitial) && {
                                traderInitials: [traderInitial]
                            })
                        },
                    (data, props, schema) => ({ data, schema })
                )
            })
        )
    ),
    ["fundGroupId", "traderId"],
    {
        or: {
            fundGroupId: {
                required: true
            },
            traderId: {
                required: true
            }
        }
    }
);

export const DriversBetFactorExposureHeatmapChartModal = props => (
    <Flex
        direction="column"
        h="max(400px, 75vh)"
        w="100%"
    >
        <DriversBetFactorExposureHeatmapChart {...props} />
    </Flex>
);
