import React, { useState } from "react";
import { times } from "lodash";

import type { ApiGatewayService, DatePreset as DatePresetType } from "Contracts/index";
import { useFilters, withFilterValues } from "Components/index";
import { isDefined } from "Utilities/index";
import {
    Flex,
    LayoutGrid,
    SkeletonRect,
    DatePreset,
    DatePresetOption,
    withComponentProps
} from "PrimitiveComponents/index";
import {
    createConfig,
    withCommandState,
    apiGatewaySpec,
    withConnectedSkeleton
} from "TransportGateway/index";

import {
    FACTOR_CHART_HEIGHT_DEFAULT,
    RSI_CHART_HEIGHT_DEFAULT,
    EXTRA_CHART_HEIGHT_DEFAULT
} from "../Bets/BetCharts";
import { TREE_MAP_CHART_HEIGHT } from "../Bets/Bet";
import { DriversBet } from "./DriversBet";
import { BREAKPOINTS } from "../Bets/Bets";

type DriversBetsItem = {
    rowId: string;
    factorDescription: string;
    riskModelName: string;
};

const DATE_PRESET_OPTIONS: DatePresetOption[] = [
    { label: "3M", value: "3M" },
    { label: "6M", value: "6M" },
    { label: "12M", value: "12M" }
];
const DATE_PRESETS_DEFAULT = "12M" as DatePresetType;

const DRIVERS_BET_MIN_HEIGHT =
    FACTOR_CHART_HEIGHT_DEFAULT +
    EXTRA_CHART_HEIGHT_DEFAULT +
    RSI_CHART_HEIGHT_DEFAULT +
    TREE_MAP_CHART_HEIGHT;

type DriversBetsBaseProps = {
    maxBetsToReturn: number;
    data?: DriversBetsItem[];
};
const DriversBetsBase = ({ data, maxBetsToReturn }: DriversBetsBaseProps) => {
    const { filters } = useFilters();
    const { traderId } = filters;
    const [datePreset, setDatePreset] = useState(DATE_PRESETS_DEFAULT);

    return (
        <Flex
            direction="column"
            gap="large"
        >
            <Flex justify="end">
                <DatePreset
                    label="Date Range"
                    value={datePreset}
                    options={DATE_PRESET_OPTIONS}
                    onChange={(v: DatePresetType) => setDatePreset(v)}
                    selectType="select"
                    required
                />
            </Flex>

            <LayoutGrid
                gap="x-large"
                columns={1}
                breakpointPropsPairs={BREAKPOINTS}
            >
                {data
                    ? data.map(({ rowId, riskModelName, factorDescription }, i) => (
                          <DriversBet
                              key={rowId}
                              title={`Bet ${i + 1} - ${factorDescription}`}
                              factor={factorDescription}
                              datePreset={datePreset}
                              riskModelName={riskModelName}
                              traderId={traderId}
                          />
                      ))
                    : times(maxBetsToReturn).map(key => (
                          <Flex
                              key={key}
                              h={DRIVERS_BET_MIN_HEIGHT}
                          >
                              <SkeletonRect height="100%" />
                          </Flex>
                      ))}
            </LayoutGrid>
        </Flex>
    );
};

export const DriversBets = withFilterValues(
    withConnectedSkeleton(
        withComponentProps(
            withCommandState(
                DriversBetsBase,
                createConfig({
                    props: apiGatewaySpec<
                        ApiGatewayService.RiskPage.TopDriversBetsRequest,
                        ApiGatewayService.RiskPage.TopDriversBetsResponse
                    >(
                        "risk-page.top-drivers-bets",
                        ({ maxBetsToReturn, fundGroupId, traderId, includeWorkingOrderType }) =>
                            (isDefined(fundGroupId) || isDefined(traderId)) && {
                                maxBetsToReturn,
                                includeWorkingOrderType,
                                fundGroupId,
                                traderId
                            },
                        (data, props, schema) => ({ data, schema })
                    )
                })
            ),
            () => ({
                maxBetsToReturn: 6
            })
        )
    ),
    ["fundGroupId", "traderId", "includeWorkingOrderType"],
    {
        or: {
            fundGroupId: {
                required: true
            },
            traderId: {
                required: true
            }
        }
    }
);
