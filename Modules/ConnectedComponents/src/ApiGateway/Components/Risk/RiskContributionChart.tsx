import { castArray } from "lodash";
import {
    apiGatewaySpec,
    createConfig,
    withCommandState,
    withConnectedSkeleton
} from "TransportGateway/index";
import { withComponentProps } from "PrimitiveComponents/index";
import type { ApiGatewayService } from "Contracts/index";
import { Chart } from "Charting/index";
import { withFilterValues } from "Components/index";
import { isDefined } from "Utilities/index";

export type RiskContributionChartProps = {
    type: "positionInfo" | "riskContribution";
    pairTickers?: string[];
    isSpecificRisk?: boolean;
};

const ConnectedChart = withComponentProps(
    withConnectedSkeleton(
        withCommandState(
            Chart,
            createConfig({
                props: apiGatewaySpec<
                    ApiGatewayService.RiskModel.RiskContributionRequest &
                        RiskContributionChartProps,
                    ApiGatewayService.RiskModel.RiskContributionResponse
                >(
                    "riskmodel.risk-contribution-bubbles",
                    ({
                        daysAgo,
                        riskContributionType,
                        excludeInactivePositions,
                        ignoreCurrencyRisk,
                        fundGroupIds,
                        fundIds,
                        riskModelName,
                        traderIds,
                        type,
                        ungroupTickers,
                        includeWorkingOrderType,
                        useSpecificCovarianceMatrix
                    }) =>
                        isDefined(daysAgo) &&
                        isDefined(riskContributionType) &&
                        (isDefined(fundGroupIds) || isDefined(fundIds) || isDefined(traderIds)) && {
                            daysAgo,
                            riskContributionType,
                            ignoreCurrencyRisk,
                            excludeInactivePositions,
                            ...(isDefined(fundGroupIds) && {
                                fundGroupIds: castArray(fundGroupIds)
                            }),
                            ...(isDefined(fundIds) && { fundIds: castArray(fundIds) }),
                            ...(isDefined(traderIds) && { traderIds: castArray(traderIds) }),
                            riskModelName,
                            type,
                            ungroupTickers,
                            includeWorkingOrderType,
                            useSpecificCovarianceMatrix
                        },

                    (data, props, schema) => ({ data, schema })
                )
            })
        )
    ),
    {
        excludeInactivePositions: true,
        ignoreCurrencyRisk: true
    }
);

export const RiskContributionChart = withFilterValues(
    ConnectedChart,
    [
        "fundGroupId",
        "fundId",
        "riskModel",
        "traderId",
        "includeWorkingOrderType",
        // @WARNING:
        // This endpoint has a different set of acceptable values from the default "daysAgo" filter.
        // Until we have a better way to handle this, it needs to have a custom filter to pass the correct values: 0 1 2 3 4 5 7 30
        "daysAgo",
        "useSpecificCovarianceMatrix"
    ],
    {
        riskModel: {
            required: true
        },
        daysAgo: {
            required: true
        },
        or: {
            fundGroupId: {
                required: true
            },
            fundId: {
                required: true
            },
            traderId: {
                required: true
            }
        }
    },
    {
        riskModel: "riskModelName",
        fundGroupId: "fundGroupIds",
        fundId: "fundIds",
        traderId: "traderIds"
    }
);

export const PositionInfoChart = withFilterValues(ConnectedChart, undefined, undefined); // @TODO: why was this configured for ticker filter? does not seem to be used??
