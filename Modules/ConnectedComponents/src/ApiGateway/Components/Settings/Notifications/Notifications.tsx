import React, { useCallback } from "react";

import { SchemaForm } from "Schema/index";
import type { SettingsType } from "Contracts/index";
import {
    withScrollable,
    LayoutVertical,
    LayoutContainer,
    Description
} from "PrimitiveComponents/index";

import { useSettings } from "../Settings";

import { NotificationSettingsSchema } from "./NotificationsSettingsSchema";
import { V1NotificationSettingsSchema } from "./V1NotificationsSettingsSchema";

const SchemaFormWithScrollable = withScrollable(SchemaForm);

export type NotificationsProps = {
    /**
     * @deprecated Temp flag for settings v2
     */
    v2?: boolean;
};

export const Notifications = ({ v2 }: NotificationsProps) => {
    const { config, updateConfig } = useSettings();

    const updateNotificationsConfig = useCallback(
        (notificationsConfig: SettingsType["notifications"]) =>
            updateConfig("notifications")(notificationsConfig),
        [updateConfig]
    );

    return (
        <LayoutVertical spacing="x-large">
            {v2 && (
                <Description>
                    Customize your notification preferences by selecting a temperature level that
                    best suits your needs. Choose from Strict, Balanced, or Comprehensive to start
                    with a set of default notifications. Adjustments can be made at any time to
                    ensure you receive notifications in a way that works best for you.
                </Description>
            )}

            <LayoutContainer>
                <SchemaFormWithScrollable
                    jsonSchema={v2 ? NotificationSettingsSchema : V1NotificationSettingsSchema}
                    value={config?.notifications}
                    onChange={updateNotificationsConfig}
                    autoSave
                />
            </LayoutContainer>
        </LayoutVertical>
    );
};
