import React from "react";

import {
    LayoutVertical,
    withScrollable,
    LayoutContainer,
    LayoutHeader,
    Typography,
} from "PrimitiveComponents/index";

import { ImpersonateUser } from "../../ImpersonateUser";

const ScrollableContainer = withScrollable(LayoutContainer);

export const Advanced = () => (
    <LayoutContainer>
        <ScrollableContainer>
            <LayoutVertical>
                <LayoutHeader
                    compact
                    hasSeparator={false}
                    sticky={false}
                    indent="none"
                >
                    <Typography.Header type="main-subsection">Impersonate User</Typography.Header>
                </LayoutHeader>

                <LayoutContainer paddingV="medium">
                    <ImpersonateUser />
                </LayoutContainer>
            </LayoutVertical>
        </ScrollableContainer>
    </LayoutContainer>
);
