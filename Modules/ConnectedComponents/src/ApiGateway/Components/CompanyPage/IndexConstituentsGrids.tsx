import React, { ComponentType } from "react";
import { Grid, type GridProps } from "Grid/index";
import {
    createConfig,
    withCommandState,
    apiGatewaySpec,
    withConnectedSkeleton
} from "TransportGateway/index";
import type { ApiGatewayService, Schema } from "Contracts/index";
import { Flex } from "PrimitiveComponents/index";

const IndexConstituentsGrid = Grid as ComponentType<
    GridProps & {
        type: "leaders" | "laggards";
    }
>;

const IndexConstituentsGridsBase = ({
    data,
    schema
}: {
    data?: ApiGatewayService.CompanyPage.IndexConstituentsResponse[];
    schema?: Schema;
}) => (
    <Flex
        gap="medium"
        flex="1"
    >
        <Flex flex="1">
            <IndexConstituentsGrid
                data={data}
                schema={schema}
                hasTransparentBackground
                hasOutline
                type="leaders"
                layout="xxx-compact"
            />
        </Flex>
        <Flex flex="1">
            <IndexConstituentsGrid
                data={data}
                schema={schema}
                hasTransparentBackground
                hasOutline
                type="laggards"
                layout="xxx-compact"
            />
        </Flex>
    </Flex>
);

export const IndexConstituentsGrids = withConnectedSkeleton(
    withCommandState(
        IndexConstituentsGridsBase,
        createConfig({
            props: apiGatewaySpec<
                Partial<ApiGatewayService.CompanyPage.IndexConstituentsRequest>,
                ApiGatewayService.CompanyPage.IndexConstituentsResponse
            >(
                "companypage.index-constituents",
                ({ bbgTicker, leadersLaggardsRowCount }) =>
                    bbgTicker && { bbgTicker, leadersLaggardsRowCount },
                (data, _, schema) => ({ data, schema })
            )
        })
    )
);
