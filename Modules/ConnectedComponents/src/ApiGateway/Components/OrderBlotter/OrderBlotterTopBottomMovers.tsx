import React, { useMemo } from "react";
import { camelCase, pick } from "lodash/fp";

import {
    createConfig,
    withCommandState,
    apiGatewaySubscriptionSpec,
    withConnectedSkeleton,
} from "TransportGateway/index";
import type { ApiGatewayService, Schema } from "Contracts/index";
import {
    Flex,
    LayoutGrid,
    Typography,
    NoData,
    withSkeletonComponentDefinition,
    SkeletonChart,
    ProgressBar,
} from "PrimitiveComponents/index";
import { SchemaValue } from "Schema/index";
import { withFilterValues, type FilterComponentProps } from "Components/index";
import type { TokenGridProps } from "../TokenGrid/types";

type OrderBlotterTopBottomMoversBaseProps = {
    data?: ApiGatewayService.DataLibrary.DataResponse[];
    schema?: Schema;
    groupBy?: string;
    fundGroup?: string;
    bbgTicker?: string;
    type?: "top" | "bottom";
};

const OrderBlotterTopBottomMoversBase = withSkeletonComponentDefinition(
    ({
        data: dataProp,
        schema,
        groupBy: groupByProp,
        type,
    }: OrderBlotterTopBottomMoversBaseProps) => {
        const groupBy = camelCase(groupByProp);

        const data = useMemo(() => {
            if (!(dataProp?.length > 0)) return dataProp;
            return (
                type === "top"
                    ? dataProp?.filter(d => ["Booked", "Completed"].includes(d.status))
                    : dataProp
                          ?.filter(d => !["Booked", "Completed"].includes(d.status))
                          // $Value - $Filled (https://raidllp.atlassian.net/browse/TPT-1355)
                          ?.map(d => ({ ...d, dollarValue: d.dollarValue - d.dollarFilled }))
            )
                ?.sort((a, b) => b.dollarValue - a.dollarValue)
                ?.slice(0, 7);
        }, [dataProp, type]);

        const max = useMemo(
            () => data && Math.max(...data.map(d => Math.abs(d.dollarValue))),
            [data],
        );

        return (
            <Flex
                flex="1"
                direction="column"
                ph="large"
                justify="space-evenly"
                items="center"
            >
                {data?.length === 0 && <NoData />}
                {data?.map(datum => (
                    <LayoutGrid
                        key={datum.rowId}
                        gap
                        w="100%"
                        maxw={400}
                        gridTemplateColumns="110px 1fr 60px"
                    >
                        <Typography.Text
                            title={datum[groupBy] as string}
                            align="right"
                            ellipsis
                            block
                        >
                            {datum[groupBy]}
                        </Typography.Text>

                        <Flex items="center">
                            <ProgressBar
                                value={Math.abs(datum.dollarValue)}
                                min={0}
                                max={max}
                            />
                        </Flex>

                        <SchemaValue
                            name="dollarValue"
                            data={datum}
                            schema={schema}
                        >
                            {children => (
                                <Typography.Text
                                    ellipsis
                                    block
                                    title={String(datum[groupBy])}
                                >
                                    {children}
                                </Typography.Text>
                            )}
                        </SchemaValue>
                    </LayoutGrid>
                ))}
            </Flex>
        );
    },
)(SkeletonChart);

export const OrderBlotterTopBottomMovers = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            OrderBlotterTopBottomMoversBase,
            createConfig({
                props: apiGatewaySubscriptionSpec<
                    Omit<
                        ApiGatewayService.DataLibrary.DataRequest,
                        "groupBy" | "tokens" | "dataSource"
                    >,
                    ApiGatewayService.DataLibrary.DataResponse,
                    TokenGridProps & FilterComponentProps
                >(
                    "data-source.data",
                    ({ filters, componentFilters, groupBy = "Ticker", ...props }) => {
                        return {
                            dataSource: 4,
                            filters: {
                                ...pick(componentFilters, props),
                                ...filters,
                            },
                            groupBy: [groupBy],
                            tokens: [
                                "Sent",
                                "Direction",
                                "Status",
                                "Ticker",
                                "BS",
                                "Filled",
                                "Quantity",
                                "DollarValue",
                                "DollarFilled",
                                "PercentageFilled",
                                "PercentageFilled(Value)",
                                "AvgPrc",
                                "Limit",
                                "LtstPrice",
                                "MidOnEntry",
                                "Bid",
                                "Ask",
                                "PercentageChg",
                                "IntradayHigh",
                                "IntradayLow",
                                "StrategyDesc",
                                "InputBy",
                            ],

                            type: "order-blotter.top-bottom-movers",
                        };
                    },
                    (data, props, schema) => ({ data, schema }),
                ),
            }),
        ),
    ),
    ["bbgTicker", "traderId"],
    undefined,
);
