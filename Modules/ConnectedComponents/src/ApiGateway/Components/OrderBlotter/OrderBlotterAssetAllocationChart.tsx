import React, { useMemo } from "react";
import { camelCase, pick } from "lodash/fp";

import {
    createConfig,
    withCommandState,
    apiGatewaySubscriptionSpec,
    withConnectedSkeleton,
} from "TransportGateway/index";
import type { ApiGatewayService, Schema } from "Contracts/index";
import { Chart, Customized } from "Charting/index";
import {
    Flex,
    LayoutGrid,
    Typography,
    NoData,
    withSkeletonComponentDefinition,
    SkeletonChart,
} from "PrimitiveComponents/index";
import { SchemaValue } from "Schema/index";
import { partition } from "Utilities/index";
import { withFilterValues, type FilterComponentProps } from "Components/index";
import type { TokenGridProps } from "../TokenGrid/types";

type OrderBlotterAssetAllocationChartBaseProps = {
    data?: ApiGatewayService.DataLibrary.DataResponse[];
    schema?: Schema;
    groupBy?: string;
    fundGroup?: string;
    bbgTicker?: string;
};

const getColor = (status: ApiGatewayService.DataLibrary.DataResponse["status"]): string => {
    switch (status) {
        case "Cancelled":
        case "Rejected":
        case "Cancelled Post Booking":
            return "var(--charts__series__pie__12)";
        case "Booking Failed":
            return "var(--charts__series__pie__14)";
        case "Pending Booking":
        case "Sent":
            return "var(--charts__series__pie__16)";
        case "Part Filled":
            return "var(--charts__series__pie__17)";
        case "Booked":
            return "var(--charts__series__pie__18)";
        case "Completed":
            return "var(--charts__series__pie__1)";
        default:
            return "var(--charts__series__pie__9)";
    }
};

const OrderBlotterAssetAllocationChartBase = withSkeletonComponentDefinition(
    ({
        data: dataProp,
        schema,
        groupBy: groupByProp,
    }: OrderBlotterAssetAllocationChartBaseProps) => {
        const groupBy = camelCase(groupByProp);

        const totalDollarValue = dataProp?.reduce((acc, d) => acc + d.dollarValue, 0);
        const totalDollarFilled = dataProp?.reduce((acc, d) => acc + d.dollarValue, 0);

        const data = useMemo(() => {
            let newData = dataProp;

            if (dataProp?.length > 8) {
                const [top10, top10Rest] = partition((x, i) => i < 8, dataProp);
                const [unknown, other] = partition(x => x[groupBy] === "Unknown", top10Rest);

                const getCombined = () => {
                    if (other.length === 0) return [];
                    if (other.length === 1) return other;
                    return [
                        other.reduce(
                            (acc, curr) => ({
                                ...acc,
                                deltaExposure: acc.dollarValue + curr.dollarValue,
                            }),
                            {
                                rowId: "other",
                                [groupBy]: `Other (${other.length})`,
                                dollarValue: 0,
                            },
                        ),
                    ];
                };

                newData = [...top10, ...getCombined(), ...unknown];
            }

            return newData
                ?.map(d => ({
                    ...d,
                    fill: getColor(d.status),
                    dollarValuePercent: d.dollarValue / totalDollarValue,
                }))
                ?.sort((a, b) => b.dollarValuePercent - a.dollarValuePercent);
        }, [dataProp, groupBy, totalDollarValue]);

        return (
            <Flex
                flex="1"
                gap="large"
                justify="space-evenly"
            >
                {data?.length === 0 && <NoData />}
                {data?.length > 0 && (
                    <>
                        <Flex
                            flex="1"
                            maxw={200}
                            style={{ aspectRatio: "1/1" }}
                        >
                            <Chart
                                data={data}
                                schema={schema}
                                chartType="pie"
                                labelOrientation="bottom"
                                labelAlign="left"
                            >
                                <Customized
                                    component={
                                        <SchemaValue
                                            name="totalDollarFilled"
                                            context={{ totalDollarFilled }}
                                            schema={schema}
                                        >
                                            {children => <>{children}</>}
                                        </SchemaValue>
                                    }
                                />
                            </Chart>
                        </Flex>
                        <Flex
                            flex="1"
                            maxw={290}
                            direction="column"
                            pt="small"
                            style={{ fontSize: 12 }}
                        >
                            <LayoutGrid
                                gap="x-small"
                                gridTemplateColumns="1fr 90px 90px"
                            >
                                <span />

                                <Typography.Text
                                    align="right"
                                    tertiary
                                    fontSize="inherit"
                                >
                                    <div>
                                        <SchemaValue
                                            name="dollarValue"
                                            schema={schema}
                                        >
                                            {(_, displayValue) => <>{displayValue}</>}
                                        </SchemaValue>
                                    </div>
                                </Typography.Text>

                                <Typography.Text
                                    align="right"
                                    tertiary
                                    fontSize="inherit"
                                >
                                    <div>
                                        <SchemaValue
                                            name="dollarValuePercent"
                                            schema={schema}
                                        >
                                            {(_, displayValue) => <>{displayValue}</>}
                                        </SchemaValue>
                                    </div>
                                </Typography.Text>
                            </LayoutGrid>
                            {data?.map(datum => (
                                <LayoutGrid
                                    key={datum.rowId}
                                    gap="x-small"
                                    gridTemplateColumns="1fr 90px 90px"
                                >
                                    <Flex
                                        items="center"
                                        gap
                                    >
                                        <Flex
                                            style={{ backgroundColor: datum.fill }}
                                            w="10px"
                                            h="10px"
                                        />
                                        <Typography.Text
                                            lineHeight="smaller"
                                            fontSize="inherit"
                                        >
                                            {datum[groupBy]}
                                        </Typography.Text>
                                    </Flex>
                                    <Typography.Text
                                        align="right"
                                        fontSize="inherit"
                                    >
                                        <div>
                                            <SchemaValue
                                                name="dollarValue"
                                                data={datum}
                                                schema={schema}
                                            >
                                                {children => <>{children}</>}
                                            </SchemaValue>
                                        </div>
                                    </Typography.Text>

                                    <Typography.Text
                                        align="right"
                                        fontSize="inherit"
                                    >
                                        <div>
                                            <SchemaValue
                                                name="dollarValuePercent"
                                                data={datum}
                                                schema={schema}
                                            >
                                                {children => <>{children}</>}
                                            </SchemaValue>
                                        </div>
                                    </Typography.Text>
                                </LayoutGrid>
                            ))}
                        </Flex>
                    </>
                )}
            </Flex>
        );
    },
)(SkeletonChart);

export const OrderBlotterAssetAllocationChart = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            OrderBlotterAssetAllocationChartBase,
            createConfig({
                props: apiGatewaySubscriptionSpec<
                    Omit<
                        ApiGatewayService.DataLibrary.DataRequest,
                        "groupBy" | "tokens" | "dataSource"
                    >,
                    ApiGatewayService.DataLibrary.DataResponse,
                    TokenGridProps & FilterComponentProps
                >(
                    "data-source.data",
                    ({ filters, componentFilters, groupBy = "Status", ...props }) => {
                        return {
                            dataSource: 4,
                            filters: {
                                ...pick(componentFilters, props),
                                ...filters,
                            },
                            groupBy: [groupBy],
                            tokens: [
                                "Sent",
                                "Direction",
                                "Status",
                                "Ticker",
                                "BS",
                                "Filled",
                                "Quantity",
                                "DollarValue",
                                "DollarFilled",
                                "PercentageFilled",
                                "PercentageFilled(Value)",
                                "AvgPrc",
                                "Limit",
                                "LtstPrice",
                                "MidOnEntry",
                                "Bid",
                                "Ask",
                                "PercentageChg",
                                "IntradayHigh",
                                "IntradayLow",
                                "StrategyDesc",
                                "InputBy",
                            ],

                            type: "order-blotter.asset-allocation-chart",
                        };
                    },
                    (data, props, schema) => ({ data, schema }),
                ),
            }),
        ),
    ),
    ["bbgTicker", "traderId"],
    undefined,
);
