import {
    createConfig,
    withCommandState,
    apiGatewaySpec,
    withConnectedSkeleton
} from "TransportGateway/index";
import type { ApiGatewayService } from "Contracts/index";
import { withFilterValues } from "Components/index";
import { Chart } from "Charting/index";

/**
 * @deprecated Temporarily disabled because the backend is being removed. It can be enabled again once the backend is back up. (TPT-1004)
 */
export const PMConeChart = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            Chart,
            createConfig({
                props: apiGatewaySpec<
                    ApiGatewayService.PMFolder.HoganRequest,
                    ApiGatewayService.PMFolder.HoganResponse
                >(
                    "pm-folder.cone-chart",
                    ({ traderId, fundGroupId }) =>
                        traderId && {
                            traderId,
                            fundGroupId
                        },
                    (data, props, schema) => ({ data, schema })
                )
            })
        )
    ),
    ["traderId", "fundGroupId"],
    {
        traderId: {
            required: true
        },
        fundGroupId: {
            required: true
        }
    }
);
