import React, { ReactNode, useMemo } from "react";

import { withSkeletonComponentDefinition } from "PrimitiveComponents/index";
import {
    createConfig,
    withCommandState,
    apiGatewaySubscriptionSpec,
    withConnectedSkeleton
} from "TransportGateway/index";
import { ApiGatewayService, Schema } from "Contracts/index";
import {
    withFilterValues,
    PositionPolicies as PositionPoliciesBase,
    PositionPoliciesProps
} from "Components/index";

import { transformData } from "./transformData";

type PositionOverviewProps = {
    schema?: Schema;
    data?: ApiGatewayService.Policy.PoliciesResponse[];
    noDataContent?: ReactNode;
} & PositionPoliciesProps<ApiGatewayService.Policy.PoliciesResponse>;

export const PositionPoliciesWithMappedData = ({
    data,
    schema,
    noDataContent,
    ...rest
}: PositionOverviewProps) => {
    const policies = useMemo(() => transformData(data), [data]);

    if (policies?.length === 0) {
        return <>{noDataContent}</>;
    }

    return (
        <PositionPoliciesBase
            schema={schema}
            policies={policies}
            {...rest}
        />
    );
};

export const PositionPolicies = withFilterValues(
    withConnectedSkeleton(
        withCommandState(
            withSkeletonComponentDefinition(PositionPoliciesWithMappedData)(PositionPoliciesBase),
            createConfig({
                props: apiGatewaySubscriptionSpec<
                    ApiGatewayService.Policy.PoliciesRequest,
                    ApiGatewayService.Policy.PoliciesResponse,
                    PositionPoliciesProps<ApiGatewayService.Policy.PoliciesResponse>
                >(
                    "policy.policies",
                    ({ traderId }) => traderId && { traderId },
                    (data, _, schema) => ({ data, schema })
                )
            })
        )
    ),
    ["traderId"],
    {
        traderId: {
            required: true
        }
    }
);
