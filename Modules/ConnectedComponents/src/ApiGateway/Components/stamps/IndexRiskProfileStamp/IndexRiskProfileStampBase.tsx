import React from "react";
import styled from "styled-components";

import { BetaExposure } from "ComponentLibrary/index";
import { StampContainer } from "Components/index";
import { Flex } from "PrimitiveComponents/index";

const StyledBetaExposure = styled(BetaExposure)`
    height: 100%;
`;

export const IndexRiskProfileStampBase = ({ data, title, ...rest }) => {
    const hasData = !!data;
    return (
        <StampContainer
            disabled={!hasData}
            title={title}
            {...rest}
        >
            <Flex
                flex="1"
                direction="column"
                pt="large"
                pb
            >
                <StyledBetaExposure
                    data={data}
                    {...rest}
                />
            </Flex>
        </StampContainer>
    );
};
