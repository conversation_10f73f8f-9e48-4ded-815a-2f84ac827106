import { useCallback, useEffect, useState } from "react";
import { useInvokeApiGatewayCommand } from "TransportGateway/index";
import { isUndefined } from "Utilities/index";

// @TODO: use tanstack query?
const CACHE = {} as Record<string, number>;

type UseFetchInstrumentIdOptions = {
    enableCache?: boolean;
};

export const useFetchInstrumentId = (options?: UseFetchInstrumentIdOptions) => {
    const { enableCache } = options ?? {};
    const { invoke } = useInvokeApiGatewayCommand();

    const fetchInstrumentId = useCallback(
        async (bbgTicker?: string) => {
            if (isUndefined(bbgTicker)) return undefined;

            if (enableCache && CACHE[bbgTicker]) return CACHE[bbgTicker];

            const res = await invoke("reference-data.ticker-instrumentid", {
                bbgTicker,
            });

            const instrumentId = res.data?.[0]?.instrumentId as number;

            if (enableCache) CACHE[bbgTicker] = instrumentId;

            return instrumentId;
        },
        [invoke, enableCache],
    );

    return fetchInstrumentId;
};

export const useInstrumentId = (bbgTicker?: string, options?: UseFetchInstrumentIdOptions) => {
    const fetchInstrumentId = useFetchInstrumentId(options);
    const [instrumentId, setInstrumentId] = useState<number>();
    useEffect(() => {
        const fn = async () => {
            const id = await fetchInstrumentId(bbgTicker);
            setInstrumentId(id);
        };
        fn();
    }, [bbgTicker, fetchInstrumentId]);

    return instrumentId;
};
