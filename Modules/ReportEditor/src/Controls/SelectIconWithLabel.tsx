import React from "react";
import { Icon } from "PrimitiveComponents/index";
import styled from "styled-components";
import classNames from "classnames";
import { isTemplateIconAntd, ITemplateIcon, ITemplateIconAntd } from "TransportGateway/index";
import { ControlProps, withLabel } from "PrimitiveComponents/index";

type SelectIconProps = {} & ControlProps<ITemplateIcon, {}>;

export const SelectIcon = ({ value, onChange }: SelectIconProps) => (
    <Container>
        <ul>
            {AntdIcons.map(type => (
                <li
                    key={type}
                    className={classNames({
                        selected: isTemplateIconAntd(value) ? value.type === type : false
                    })}
                    onClick={() =>
                        onChange({
                            iconType: "antd",
                            type
                        } as ITemplateIconAntd)
                    }
                >
                    <Icon type={type} />
                </li>
            ))}
        </ul>
    </Container>
);

export const SelectIconWithLabel = withLabel(SelectIcon);

const Container = styled.div`
    &&&& {
        margin: 0;
        padding: var(--input__padding, 2px 8px);
        font-size: 1.5em;
        overflow: auto;

        > ul {
            list-style: none;
            display: flex;
            flex-wrap: wrap;
            margin: 0;
            padding: 0;

            > li {
                padding: 2px;
                width: 1.5em;
                height: 1.5em;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: var(--input__border-radius, 3px);
                margin: 2px;
                cursor: pointer;

                &:hover {
                    background-color: var(--element__color--highlight, #555);
                }

                &.selected {
                    background-color: var(--element__color--selected, #555);
                }
            }
        }
    }
`;

const AntdIcons = [
    "line-chart",
    "area-chart",
    "bar-chart",
    "dot-chart",
    "fall",
    "rise",
    "pie-chart",
    "radar-chart",
    "heat-map",
    "table",
    "project",
    "dashboard",
    "layout",
    "appstore",
    "stock",
    "box-plot",
    "fund",
    "sliders",
    "play-circle",
    "border-outer",
    "pic-center",
    "pic-left",
    "pic-right",
    "account-book",
    "alert",
    "notification",
    "bank",
    "bell",
    "book",
    "bulb",
    "calculator",
    "build",
    "calendar",
    "camera",
    "carry-out",
    "cloud",
    "code",
    "compass",
    "contacts",
    "container",
    "control",
    "credit-card",
    "crown",
    "database",
    "like",
    "dislike",
    "environment",
    "experiment",
    "eye-invisible",
    "eye",
    "filter",
    "fire",
    "hdd",
    "heart",
    "idcard",
    "insurance",
    "interaction",
    "lock",
    "mail",
    "message",
    "mobile",
    "tablet",
    "money-collect",
    "pay-circle",
    "picture",
    "profile",
    "pushpin",
    "read",
    "reconciliation",
    "rest",
    "rocket",
    "schedule",
    "setting",
    "sound",
    "star",
    "switcher",
    "tag",
    "tags",
    "tool",
    "thunderbolt",
    "trophy",
    "wallet",
    "apartment",
    "audit",
    "bars",
    "block",
    "border",
    "branches",
    "cloud-server",
    "cloud-sync",
    "cluster",
    "deployment-unit",
    "desktop",
    "dollar",
    "exception",
    "export",
    "fork",
    "gateway",
    "global",
    "gold",
    "inbox",
    "number",
    "pull-request",
    "qrcode",
    "robot",
    "scan",
    "search",
    "select",
    "share-alt",
    "team",
    "user",
    "wifi"
];
