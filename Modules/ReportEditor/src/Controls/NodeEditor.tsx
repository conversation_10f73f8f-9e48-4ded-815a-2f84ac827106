import React from "react";

import { IParameter, IReport, IDataSource } from "TransportGateway/index";
import { ControlProps } from "PrimitiveComponents/index";
import { IRunReportArgs } from "../Components/NodeEditor/types";
import {
    NodeEditor as NodeEditorBase,
    NodeEditorProps as NodeEditorBaseProps
} from "../Components";

type NodeEditorProps = {
    parameters: IParameter[];
    dataSource: IDataSource;
    selectedNodeName: string;
    onSelectNode: (node: IRunReportArgs) => void;
    className?: string;
} & ControlProps<IReport["nodes"], Pick<NodeEditorBaseProps, "onValidateNodes" | "disabled">>;

export const NodeEditor = ({
    value,
    onChange,
    dataSource,
    parameters,
    selectedNodeName,
    onSelectNode,
    className,
    onValidateNodes,
    disabled
}: NodeEditorProps) =>
    dataSource ? (
        <NodeEditorBase
            dataSource={dataSource}
            value={value}
            onChange={onChange}
            parameters={parameters}
            selectedNodeName={selectedNodeName}
            setSelectedNodeProp={onSelectNode}
            className={className}
            onValidateNodes={onValidateNodes}
            disabled={disabled}
        />
    ) : (
        <div style={{ padding: 10, margin: "auto", opacity: 0.5 }}>No Report Source</div>
    );
