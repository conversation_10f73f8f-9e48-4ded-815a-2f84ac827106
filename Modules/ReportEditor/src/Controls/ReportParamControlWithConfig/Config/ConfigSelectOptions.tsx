import React, { useMemo, useState } from "react";
import { debounce } from "lodash";

import {
    LayoutVertical,
    LayoutContainer,
    TextAreaWithLabel,
    Option,
    Note
} from "PrimitiveComponents/index";

import { ControlConfigProps } from "./types";

const OPTIONSTEXT_DIVIDER = "==";

const parseOptions = (options?: Option[]) =>
    (options || [])
        .map(
            ({ label, value }) =>
                `${value}${label === value ? "" : `${OPTIONSTEXT_DIVIDER}${label}`}`
        )
        .join("\r\n");

const parseOptionsText = (optionsText: string): Option[] =>
    optionsText
        ?.trimEnd()
        .split(/\r?\n/)
        .filter(value => value)
        .map((optionText, index) => {
            const [value, label] = optionText.split(OPTIONSTEXT_DIVIDER);
            return (
                (value || index === 0) && {
                    value: value?.trim() || "",
                    label: label?.trim() || value?.trim() || ""
                }
            );
        })
        .filter(option => option) || [];

type ConfigSelectOptionsProps = ControlConfigProps<
    {},
    {
        options: Option[];
    }
>;

export const ConfigSelectOptions = ({ value, onChange }: ConfigSelectOptionsProps) => {
    const [optionsText, setOptionsText] = useState(parseOptions(value?.props?.options));

    const onChangeDebounced = useMemo(
        () =>
            debounce(
                (optionsText: string) =>
                    onChange({
                        ...value,
                        props: {
                            ...value?.props,
                            options: parseOptionsText(optionsText)
                        }
                    }),
                200
            ),
        [onChange, value]
    );

    const handleChange = (optionsText: string) => {
        setOptionsText(optionsText);

        onChangeDebounced(optionsText);
    };

    return (
        <LayoutContainer>
            <LayoutVertical style={{ maxWidth: "35em" }}>
                <Note>
                    Enter a list of options below. Each new line will be a new option and each
                    option line must be in the following format:
                    <pre>
                        OptionValueOnly{"\n"}
                        OptionValue{OPTIONSTEXT_DIVIDER}Option With Label
                    </pre>
                </Note>
                <TextAreaWithLabel
                    style={{ resize: "none" }}
                    label="Options"
                    value={optionsText}
                    onChange={handleChange}
                    rows={8}
                    cols={30}
                    flex
                />
            </LayoutVertical>
        </LayoutContainer>
    );
};
