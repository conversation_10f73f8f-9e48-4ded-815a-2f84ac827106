import React from "react";

import { SelectWithLabel, Toggle, InputWithLabel } from "PrimitiveComponents/index";

import { ControlConfigProps } from "./types";

const DATE_FORMAT_MAP = {
    "ISO (e.g. 2025-01-25)": "yyyy-MM-dd",
    "ISO compact  (e.g. 20250125)": "yyyyMMdd",

    // UK formats
    "UK format slash (e.g. 25/01/2025)": "dd/MM/yyyy",
    "UK format hyphen (e.g. 25-01-2025)": "dd-MM-yyyy",
    "UK format dot (e.g. 25.01.2025)": "dd.MM.yyyy",
    "UK text short (e.g. 25 Jan 2025)": "d MMM yyyy",
    "UK text long (e.g. 25 January 2025)": "d MMMM yyyy",
    "UK weekday (e.g. Mon, 25 Jan 2025)": "EEE, d MMM yyyy",
    "UK full weekday (e.g. Monday, 25 January 2025)": "EEEE, d MMMM yyyy",

    // US formats
    "US format slash (e.g. 01/25/2025)": "MM/dd/yyyy",
    "US format hyphen (e.g. 01-25-2025)": "MM-dd-yyyy",
    "US format dot (e.g. 01.25.2025)": "MM.dd.yyyy",
    "US text short (e.g. Jan 25, 2025)": "MMM d, yyyy",
    "US text long (e.g. January 25, 2025)": "MMMM d, yyyy",
    "US weekday (e.g. Mon, Jan 25, 2025)": "EEE, MMM d, yyyy",
    "US full weekday (e.g. Saturday, January 25, 2025)": "EEEE, MMMM d, yyyy",
};
const OPTIONS = Object.entries(DATE_FORMAT_MAP).map(([label, value]) => ({ label, value }));

type ConfigDateProps = ControlConfigProps<
    {},
    {
        dateFormat?: string;
        preselectEnabled?: boolean;
        preselectAddedBusinessDays?: number;
    }
>;
export const ConfigDate = ({ value, onChange, ...rest }: ConfigDateProps) => {
    const {
        dateFormat = OPTIONS[0].value,
        preselectEnabled = false,
        preselectAddedBusinessDays = 0,
    } = value?.props ?? {};
    return (
        <div className="flex flex-col gap-lg py-md">
            <SelectWithLabel
                label="Type"
                value={dateFormat}
                onChange={(v: string) =>
                    onChange({ ...value, props: { ...value?.props, dateFormat: v } })
                }
                options={OPTIONS}
                required
                width="270px"
                {...rest}
            />

            <div className="flex items-center gap-xl p-lg border bg-container-level-2">
                <Toggle
                    label="With default"
                    value={preselectEnabled}
                    onChange={(v: boolean) =>
                        onChange({ ...value, props: { ...value?.props, preselectEnabled: v } })
                    }
                />

                <InputWithLabel
                    label="Today + Business Days"
                    value={String(preselectAddedBusinessDays)}
                    onChange={(v: string) =>
                        onChange({
                            ...value,
                            props: { ...value?.props, preselectAddedBusinessDays: parseInt(v, 10) },
                        })
                    }
                    type="number"
                    width="100px"
                />
            </div>
        </div>
    );
};
