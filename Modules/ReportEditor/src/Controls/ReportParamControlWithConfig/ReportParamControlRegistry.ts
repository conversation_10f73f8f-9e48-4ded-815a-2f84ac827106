import { ComponentType } from "react";

import { IParamContentType } from "TransportGateway/index";

import {
    ControlConfigProps,
    ConfigSelectOptions,
    ConfigDateFramePeriods,
    ConfigWidth,
    InlineControlConfigProps,
    ConfigSelect,
    ConfigRaidFilter,
    ConfigDate,
} from "./Config";

type ControlConfig = {
    ConfigComponent?: ComponentType<ControlConfigProps>[];
    InlineConfigComponent?: ComponentType<InlineControlConfigProps>[];
};

const ConfigReportParamControlRegistry = {
    [IParamContentType.DateFrame]: {
        ConfigComponent: [ConfigDateFramePeriods, ConfigRaidFilter],
    },
    SecurityList: {
        ConfigComponent: [ConfigRaidFilter],
        InlineConfigComponent: [ConfigWidth],
    },
    TradeSuggestionProcess: {
        ConfigComponent: [ConfigRaidFilter],
        InlineConfigComponent: [ConfigWidth],
    },
    [IParamContentType.CustomSelect]: {
        ConfigComponent: [ConfigSelectOptions, ConfigSelect, ConfigRaidFilter],
        InlineConfigComponent: [ConfigWidth],
    },
    [IParamContentType.Boolean]: {
        ConfigComponent: [ConfigRaidFilter],
    },
    [IParamContentType.Double]: {
        ConfigComponent: [ConfigRaidFilter],
        InlineConfigComponent: [ConfigWidth],
    },
    [IParamContentType.Integer]: {
        ConfigComponent: [ConfigRaidFilter],
        InlineConfigComponent: [ConfigWidth],
    },
    [IParamContentType.Long]: {
        ConfigComponent: [ConfigRaidFilter],
        InlineConfigComponent: [ConfigWidth],
    },
    [IParamContentType.Date]: {
        ConfigComponent: [ConfigDate, ConfigRaidFilter],
        InlineConfigComponent: [ConfigWidth],
    },
} as {
    [key: string]: ControlConfig;
};

export const getReportParamControl = (contentType: string): ControlConfig =>
    ConfigReportParamControlRegistry[contentType] || {
        ConfigComponent: [ConfigSelect, ConfigRaidFilter],
        InlineConfigComponent: [ConfigWidth],
    };
