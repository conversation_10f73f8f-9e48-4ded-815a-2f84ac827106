import React from "react";

import {
    Label,
    LayoutHorizontalGroup,
    LayoutVertical,
    CheckboxWithLabel,
    withStandardErrorBoundary,
    Note,
} from "PrimitiveComponents/index";

import { SelectTraderWithLabel } from "ComponentLibrary/index";
import { usePersistSettings } from "../../persistSettings";

export const ReportEditorOptions = withStandardErrorBoundary(() => {
    const { settings, updateSettings } = usePersistSettings();
    return (
        <Label
            groupContainer
            label="Settings"
            hasBackground={false}
        >
            <LayoutVertical
                spacing="large"
                margin="x-large"
            >
                <LayoutHorizontalGroup>
                    <SelectTraderWithLabel
                        label="Impersonate User"
                        placeholder="None"
                        value={settings?.user?.OriginatingUserId}
                        onChange={async value => {
                            await updateSettings(s => ({
                                ...s,
                                user: {
                                    ...s?.user,
                                    OriginatingUserId: value?.toString(),
                                },
                            }));
                            window.location.reload();
                        }}
                    />
                    <Note>This will reload the editor</Note>
                </LayoutHorizontalGroup>

                <CheckboxWithLabel
                    label="Show RAW report JSON tab"
                    value={settings?.editor?.debug?.showRaw}
                    onChange={value =>
                        updateSettings(s => ({
                            ...s,
                            editor: {
                                ...s?.editor,
                                debug: {
                                    ...s?.editor?.debug,
                                    showRaw: !!value,
                                },
                            },
                        }))
                    }
                />
            </LayoutVertical>
        </Label>
    );
});
