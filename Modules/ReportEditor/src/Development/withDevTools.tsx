import React, { ComponentType, createContext, useContext, useEffect, useState } from "react";
import styled from "styled-components";

import { useFeatures } from "ComponentLibrary/index";

const DevToolsContext = createContext<HTMLDivElement>(undefined);

export const useDevContainer = () => useContext(DevToolsContext);

export const withDevTools =
    <P extends {}>(Component: ComponentType<P>) =>
    (props: P) => {
        const [container, setContainer] = useState<HTMLDivElement>();

        return (
            <DevToolsContext.Provider value={container}>
                <Container>
                    <div className="app with-antd">
                        <Component {...props} />
                    </div>
                    <Wrapper
                        className="dev-tools with-antd"
                        ref={container => setContainer(container)}
                    >
                        <DevToolsOptions />
                    </Wrapper>
                </Container>
            </DevToolsContext.Provider>
        );
    };

const useRaidNewThemeStored = localStorage.getItem("debug:useRaidNewTheme");
const useRaidNewTheme =
    useRaidNewThemeStored !== null
        ? JSON.parse(useRaidNewThemeStored || "true")
        : document.getElementsByTagName("html")[0].classList.contains("new-raid");

const DevToolsOptions = () => {
    const [{ companyPageExperimental, companyPagePreRelease, viewServerRust }, setFeature] =
        useFeatures();
    const [raidTheme, setRaidTheme] = useState(useRaidNewTheme);

    const handleSetRaidTheme = (enabled: boolean) => {
        setRaidTheme(enabled);
        if (enabled) {
            document.getElementsByTagName("html")[0].classList.add("new-raid");
        } else {
            document.getElementsByTagName("html")[0].classList.remove("new-raid");
        }
    };

    useEffect(() => {
        localStorage.setItem("debug:useRaidNewTheme", JSON.stringify(raidTheme));
    }, [raidTheme]);

    useEffect(() => {
        handleSetRaidTheme(useRaidNewTheme);
    }, [useRaidNewTheme]);

    return (
        <>
            <div className="options">
                <h1>Features</h1>
                <p>
                    <label>
                        <span>Company Page Experimental</span>
                        <input
                            type="checkbox"
                            checked={companyPageExperimental}
                            onChange={() =>
                                setFeature(features => ({
                                    ...features,
                                    companyPageExperimental: !features.companyPageExperimental,
                                }))
                            }
                        />
                    </label>
                </p>
                <p>
                    <label>
                        <span>Company Page PreRelease</span>
                        <input
                            type="checkbox"
                            checked={companyPagePreRelease}
                            onChange={() =>
                                setFeature(features => ({
                                    ...features,
                                    companyPagePreRelease: !features.companyPagePreRelease,
                                }))
                            }
                        />
                    </label>
                </p>
                <p>
                    <label>
                        <span>ViewServer Rust</span>
                        <input
                            type="checkbox"
                            checked={viewServerRust}
                            onChange={() =>
                                setFeature(features => ({
                                    ...features,
                                    viewServerRust: !features.viewServerRust,
                                }))
                            }
                        />
                    </label>
                </p>
            </div>
            <div className="options">
                <h1>Theme</h1>
                <p>
                    <label>
                        <span>Use New Raid theme</span>
                        <input
                            type="checkbox"
                            checked={raidTheme}
                            onChange={e => handleSetRaidTheme(e.target.checked)}
                        />
                    </label>
                </p>
            </div>
        </>
    );
};

const Container = styled.div`
    display: flex;
    flex-direction: column;
    height: 100vh;

    .app {
        flex-grow: 1;
        flex-shrink: 0;
        overflow: auto;
        display: flex;
        background-color: inherit;
    }
`;

const Wrapper = styled.div`
    display: flex;
    padding: 10px;
    font-size: 12px;
    overflow-x: auto;
    border-top: 1px solid #ccc;
    max-height: min(220px, 25vh);
    overflow-y: auto;

    .overflow {
        max-height: 100%;
        overflow: auto;
    }

    > * {
        border: 1px solid #333;
        border-radius: 4px;
        padding: 0.5rem 1rem;
        background-color: #222;
        align-self: flex-start;
        min-height: 100px;
        overflow-y: auto;
        margin: 0 10px 0 0;
        font-size: 12px;
        flex-grow: 0 !important;
        flex-shrink: 0 !important;

        &:last-child {
            margin-right: 0;
        }

        h1,
        h2 {
            margin: 0 0 5px 0;
            padding: 0;
        }

        h1 {
            font-size: 14px;
        }

        h2 {
            font-size: 13px;
        }

        p {
            margin: 0 0 0.5rem 0;

            &:last-of-type {
                margin-bottom: 0;
            }
        }

        label {
            display: flex;
            align-items: baseline;

            span {
                margin-right: 1rem;
                flex-grow: 1;
            }
        }
    }
`;
