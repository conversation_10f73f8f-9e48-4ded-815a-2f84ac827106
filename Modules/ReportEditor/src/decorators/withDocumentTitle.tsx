import React, { ComponentType, useLayoutEffect } from "react";

import { useRaidSettings } from "ComponentLibrary/index";

type DocumentTitleProps = {
    useReportEngine: boolean;
};

export const withDocumentTitle = <P extends object>(Component: ComponentType<P>) => {
    const WrappedComponent = ({ useReportEngine, ...props }: P & DocumentTitleProps) => {
        const [raidSettings] = useRaidSettings() || [];

        useLayoutEffect(() => {
            try {
                document.title = `Report ${useReportEngine ? "Preview" : "Editor"}${
                    raidSettings?.useExperimentalViewServer ? " [Experimental]" : ""
                }`;
            } catch (ex) {
                console.error(ex);
            }
        }, [useReportEngine, raidSettings?.useExperimentalViewServer]);

        return <Component {...(props as P)} />;
    };

    WrappedComponent.displayName = `withDocumentTitle(${Component.displayName || Component.name})`;

    return WrappedComponent;
};
