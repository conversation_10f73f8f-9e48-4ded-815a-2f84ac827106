import { ActionSeverity, BuildReportActions } from "../types";

export const buildReportTemplateActions: BuildReportActions = report =>
    report?.templates?.flatMap(template =>
        template?.Regions?.flatMap(region => {
            const markupLineCount = region?.Markup?.split(/\r?\n/)?.length || 0;

            return [
                markupLineCount > 250 && {
                    category: "TEMPLATE",
                    severity: markupLineCount > 350 ? ActionSeverity.error : ActionSeverity.warning,
                    title: `Template "${template.Name}" is ${markupLineCount} lines`,
                    description: `Remove any unused code or consider using a referenced template.`
                }
            ];
        })
    );
