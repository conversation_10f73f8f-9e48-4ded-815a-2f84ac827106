import React, { Dispatch, SetStateAction } from "react";
import styled from "styled-components";

import { Label } from "PrimitiveComponents/index";

import { ReportTable } from "./ReportTable";
import { IRunReportArgs } from "../../../Components/NodeEditor/types";

type PreviewProps = {
    selectedNode: IRunReportArgs;
    setSelectedNode: Dispatch<SetStateAction<IRunReportArgs>>;
};

const CloseButton = styled.button`
    position: absolute;
    right: 0;
    top: 0px;
    border: 0;
    background: transparent;
    cursor: pointer;
    z-index: 1;
    opacity: 0.5;

    &:hover {
        opacity: 1;
    }
`;

export const Preview = ({ selectedNode, setSelectedNode }: PreviewProps) =>
    (selectedNode && (
        <Label
            label={`Preview "${selectedNode.operatorName}"`}
            groupContainer
        >
            <ReportTable selectedNode={selectedNode} />

            <CloseButton
                onClick={() => setSelectedNode(undefined)}
                title="Close Preview"
            >
                🗙
            </CloseButton>
        </Label>
    )) ||
    null;
