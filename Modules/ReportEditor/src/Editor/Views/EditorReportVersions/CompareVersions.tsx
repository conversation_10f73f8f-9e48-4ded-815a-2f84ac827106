import React, { ReactNode, useEffect, useMemo, useRef, useState } from "react";
import styled from "styled-components";
import { Dropdown, Icon, Menu } from "antd";
import { getIn, setIn } from "immutable";

import {
    CheckboxWithLabel,
    Header,
    LayoutHorizontal,
    LayoutHorizontalGroup,
    LayoutVertical,
    getContainer,
    Label
} from "PrimitiveComponents/index";
import { IReport } from "TransportGateway/index";

import { ResponsiveMonacoDiffEditor } from "../../../Components";
import { normalizeReport, normalizeReportNodes, parseReport, parseNodes } from "./utils";
import { ReportActionSave } from "../../Components";

type CompareModeModel = {
    label: ReactNode;
    language: string;
    path?: (number | string)[];
    formatter: (value: any) => string;
    parser: <T extends any>(value: string) => T;
};

const CompareModes = {
    report: {
        label: (
            <>
                Comparing <strong>Full Report</strong>
            </>
        ),
        language: "json",
        path: [],
        formatter: normalizeReport,
        parser: parseReport
    } as CompareModeModel,
    nodes: {
        label: (
            <>
                Comparing <strong>Report Nodes</strong>
            </>
        ),
        language: "json",
        path: ["nodes"],
        formatter: normalizeReportNodes,
        parser: parseNodes
    } as CompareModeModel
};

export type CompareModeType = keyof typeof CompareModes;

const EditorHeader = styled(Header)`
    &&&&&& {
        justify-content: flex-end;
        align-items: center;
        padding-top: 0;
        padding-bottom: 0;
        background-color: rgba(28, 28, 28, 0.75);
    }
`;

const CompareMenu = styled.div`
    cursor: pointer;
`;

const Footer = styled(LayoutHorizontalGroup)`
    &&&&&&&& {
        justify-content: flex-end;
    }
`;

type CompareVersionsProps = {
    originalReport: IReport;
    modifiedReport: IReport;
    readOnly?: boolean;
    reportKey: string;
    onReportKeyChange?: (reportKey: string, prevReportKey: string) => void;
    inlineCompare?: boolean;
    onChangeInlineCompare?: (inlineCompare: boolean) => void;
    compareMode?: CompareModeType;
    onChangeCompareMode?: (compareMode: CompareModeType) => void;
    onCompleteRestore?: () => void;
};

export const CompareVersions = ({
    inlineCompare = false,
    onChangeInlineCompare,
    originalReport,
    modifiedReport,
    readOnly = true,
    reportKey,
    onReportKeyChange,
    compareMode: compareModeType = "nodes",
    onChangeCompareMode,
    onCompleteRestore
}: CompareVersionsProps) => {
    const isMounted = useRef(true);

    const [isValid, setIsValid] = useState(true);
    const [busy, setBusy] = useState(false);
    const [modifiedVersion, setModifiedVersion] = useState(modifiedReport);

    const compareMode = CompareModes[compareModeType] || CompareModes.nodes;

    const [modified, setModified] = useState("");
    const original =
        (compareMode &&
            compareMode.formatter(getIn(originalReport, compareMode.path, undefined))) ||
        "";

    useEffect(() => {
        return () => {
            isMounted.current = false;
        };
    }, []);

    useEffect(() => {
        setModifiedVersion(modifiedReport);
    }, [modifiedReport, setModifiedVersion]);

    useEffect(() => {
        setModified(
            (compareMode &&
                compareMode.formatter(getIn(modifiedVersion, compareMode.path, undefined))) ||
                ""
        );
    }, [modifiedVersion, compareMode, setModified]);

    const menu = useMemo(
        () => (
            <Menu>
                <Menu.Item onClick={() => onChangeCompareMode && onChangeCompareMode("nodes")}>
                    Compare Report Nodes
                </Menu.Item>
                <Menu.Item onClick={() => onChangeCompareMode && onChangeCompareMode("report")}>
                    Compare Full Report
                </Menu.Item>
            </Menu>
        ),
        [onChangeCompareMode]
    );

    const handleChange = (value: string) => {
        setModified(value);

        if (compareMode.language === "json") {
            try {
                JSON.parse(value);
                setIsValid(true);
            } catch (ex) {
                setIsValid(false);
            }
        }

        setModifiedVersion(report => {
            try {
                return setIn(report, compareMode.path, compareMode.parser(value));
            } catch (ex) {
                console.error(ex);
            }
            return report;
        });
    };

    return (
        (compareMode && (
            <LayoutVertical>
                <Label
                    className="flex-grow"
                    groupContainer
                    error={
                        !isValid && (
                            <div className="message-compact message-inner-bottom-right">
                                Invalid nodes
                            </div>
                        )
                    }
                >
                    <EditorHeader>
                        <Dropdown
                            overlay={menu}
                            getPopupContainer={getContainer}
                        >
                            <CompareMenu>
                                {compareMode.label} <Icon type="down" />
                            </CompareMenu>
                        </Dropdown>
                        <CheckboxWithLabel
                            value={inlineCompare}
                            onChange={value => onChangeInlineCompare(!!value)}
                            label="View Inline"
                        />
                    </EditorHeader>
                    <ResponsiveMonacoDiffEditor
                        key={compareMode.path.join(".")}
                        language={compareMode.language}
                        options={{
                            fontSize: 11,
                            scrollbar: {
                                verticalScrollbarSize: 6,
                                horizontalScrollbarSize: 6
                            },
                            renderValidationDecorations: "off",
                            readOnly,
                            renderSideBySide: !inlineCompare,
                            enableSplitViewResizing: false
                        }}
                        value={modified}
                        original={original}
                        onChange={handleChange}
                    />
                </Label>
                <Footer compact={false}>
                    <LayoutHorizontal>
                        <ReportActionSave
                            report={modifiedVersion}
                            disabled={!isValid || busy}
                            reportKey={reportKey}
                            onBusy={busy => isMounted.current && setBusy(busy)}
                            onReportKeyChange={onReportKeyChange}
                            icon="rollback"
                            successMessage="Report restored"
                            onComplete={onCompleteRestore}
                        >
                            Restore this version
                        </ReportActionSave>
                    </LayoutHorizontal>
                </Footer>
            </LayoutVertical>
        )) ||
        null
    );
};
