import React, { useMemo, useState } from "react";
import { sortBy } from "lodash";
import { Button } from "antd";
import styled from "styled-components";

import { SelectClientCommandWithLabel } from "ComponentLibrary/index";
import {
    LayoutColumns,
    LayoutContainer,
    LayoutHorizontal,
    LayoutHorizontalGroup,
    LayoutVertical
} from "PrimitiveComponents/index";
import { IReport, CommandData } from "TransportGateway/index";

import { ReportVersionsWithLabel } from "../../../Controls";
import { usePersistSettings } from "../../../persistSettings";

import { mapCommitReport } from "./utils";
import { CompareModeType, CompareVersions } from "./CompareVersions";

const VersionsContainer = styled(LayoutHorizontalGroup)`
    &&&&&&&& {
        flex-grow: 1;
    }
`;

const StyledReportVersionsWithLabel = styled(ReportVersionsWithLabel)`
    &&&&&&&& {
        flex-grow: 1;
    }
`;

type EditorReportVersionsProps = {
    report: IReport;
    reportKey: string;
    onReportKeyChange: (reportKey: string, prevReportKey: string) => void;
};

const knownEnvironments = ["prod", "demo", "uat", "staging"];

export const EditorReportVersions = ({
    report,
    onReportKeyChange,
    reportKey
}: EditorReportVersionsProps) => {
    const { settings, updateSettings } = usePersistSettings();
    const [environment, setEnvironment] = useState<string>();
    const [versionModified, setVersionModified] = useState<string>();
    const [inlineCompare, setInlineCompare] = useState(settings?.editor?.versions?.inline ?? false);
    const [compareMode, setCompareMode] = useState<CompareModeType>(
        settings?.editor?.versions?.compareMode as CompareModeType
    );
    const [reloadVersions, setReloadVersion] = useState(0);

    const commandPayload = useMemo(
        () =>
            environment &&
            versionModified && {
                ReportKey: report.reportKey,
                PersistenceConfig: {
                    Properties: {
                        GitBranch: environment.toLowerCase()
                    }
                },
                Sha: versionModified
            },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [environment, versionModified]
    );

    const handleChangeInlineCompare = (compareInline: boolean) => {
        setInlineCompare(compareInline);
        updateSettings(s => ({
            ...s,
            editor: {
                ...s?.editor,
                versions: {
                    ...s?.editor?.versions,
                    inline: compareInline
                }
            }
        }));
    };

    const handleCompareMode = (cm: CompareModeType) => {
        setCompareMode(cm);
        updateSettings(s => ({
            ...s,
            editor: {
                ...s?.editor,
                versions: {
                    ...s?.editor?.versions,
                    compareMode: cm
                }
            }
        }));
    };

    return (
        <LayoutVertical>
            <LayoutHorizontal>
                <SelectClientCommandWithLabel
                    label="Source Environment"
                    placeholder="Select Environment…"
                    reportContentType="Branch"
                    defaultOptions
                    value={environment}
                    onChange={value => {
                        const selectedEnvironment = (
                            Array.isArray(value) ? value?.[0] ?? "" : value
                        ).toString();
                        if (selectedEnvironment !== environment) {
                            setEnvironment(selectedEnvironment);
                            setVersionModified(undefined);
                        }
                    }}
                    isGrouped
                    onSearchComplete={options =>
                        options &&
                        sortBy(options, option => {
                            const index = knownEnvironments.indexOf(option.value.toString());
                            return index !== -1 ? index : undefined;
                        }).map(option => ({
                            ...option,
                            group:
                                knownEnvironments.indexOf(option.value.toString()) === -1
                                    ? "Other"
                                    : "Environments"
                        }))
                    }
                />
            </LayoutHorizontal>
            {environment && (
                <LayoutColumns style={{ alignItems: "flex-end" }}>
                    <div>
                        {environment && versionModified && !inlineCompare && <>This version</>}
                    </div>

                    <VersionsContainer>
                        <StyledReportVersionsWithLabel
                            key={reloadVersions}
                            label="Source Version"
                            reportKey={report.reportKey}
                            environment={environment}
                            width="100%"
                            value={versionModified}
                            onChange={setVersionModified}
                            onSearchComplete={availableOptions =>
                                setVersionModified(
                                    versionModified =>
                                        versionModified ?? availableOptions?.[0]?.value?.toString()
                                )
                            }
                        />
                        <Button
                            size="small"
                            icon="redo"
                            onClick={() => setReloadVersion(value => value + 1)}
                            title="Refresh versions"
                        />
                    </VersionsContainer>
                </LayoutColumns>
            )}
            {environment && versionModified && (
                <LayoutContainer>
                    <CommandData
                        commandName="GetSpecificCommitFromSourceControl"
                        commandPayload={commandPayload}
                        dataMapper={mapCommitReport}
                    >
                        {versionedReport =>
                            versionedReport && (
                                <CompareVersions
                                    originalReport={report}
                                    modifiedReport={versionedReport}
                                    readOnly
                                    reportKey={reportKey}
                                    onReportKeyChange={onReportKeyChange}
                                    inlineCompare={inlineCompare}
                                    onChangeInlineCompare={handleChangeInlineCompare}
                                    compareMode={compareMode}
                                    onChangeCompareMode={handleCompareMode}
                                    onCompleteRestore={() => setReloadVersion(value => value + 1)}
                                />
                            )
                        }
                    </CommandData>
                </LayoutContainer>
            )}
        </LayoutVertical>
    );
};
