import { camelCaseObject } from "@tradinglabs/viewserver-core";
import { IReport, unstringifyReport } from "TransportGateway/index";

export const mapCommitReport = (payload: any) => unstringifyReport(camelCaseObject(payload, true));

const sortedPropertiesReplacer = (key: string, value: any) => {
    if (value === null) {
        return undefined;
    }
    if (value?.constructor != Object) {
        return value;
    }
    return Object.keys(value)
        .sort()
        .reduce((s, k) => {
            s[k] = value[k];
            return s;
        }, {});
};

export const normalizeReportNodes = (nodes: IReport["nodes"], indent = 2) => {
    try {
        return nodes && JSON.stringify(JSON.parse(nodes), sortedPropertiesReplacer, indent);
    } catch (error) {
        console.error(error);
    }
};

export const normalizeReport = (report: IReport, indent = 2) => {
    try {
        const {
            rowId, // Do not map
            historyJson, // Do not map
            ...sanitizedReport
        } = {
            ...report,
            nodes: report?.nodes && JSON.parse(report?.nodes)
        } as IReport & {
            rowId: number;
            historyJson: string;
        };

        return sanitizedReport && JSON.stringify(sanitizedReport, sortedPropertiesReplacer, indent);
    } catch (error) {
        console.error(error);
    }
};

export const parseNodes = (nodes: string): IReport["nodes"] => JSON.stringify(JSON.parse(nodes));

export const parseReport = (value: string): IReport => {
    const report = JSON.parse(value);
    return {
        ...report,
        nodes: report.nodes && JSON.stringify(report.nodes)
    };
};
