import React, { useEffect, useRef, useState } from "react";
import { Button } from "antd";
import styled from "styled-components";

import { EmptyReport, IReport } from "TransportGateway/index";
import { DraftEditApi, LayoutHorizontalGroup } from "PrimitiveComponents/index";

import { ReportActionSave } from "./ReportActionSave";
import { ReportActionDelete } from "./ReportActionDelete";

type ReportActionsProps = {
    report: IReport;
    reportKey: string;
    disabled?: boolean;
    isDirty?: boolean;
    onReportKeyChange?: (reportKey: string, prevReportKey: string) => void;
    prepareCommit?: DraftEditApi<IReport>["prepareCommit"];
    revokeChanges: DraftEditApi<IReport>["revokeChanges"];
    className?: string;
};

export const ReportActions = ({
    report,
    reportKey,
    disabled,
    isDirty,
    prepareCommit,
    revokeChanges,
    onReportKeyChange,
    className
}: ReportActionsProps) => {
    const isMounted = useRef(true);
    const [busy, setBusy] = useState(false);

    useEffect(() => {
        return () => {
            isMounted.current = false;
        };
    }, []);

    return (
        <Container className={className}>
            {reportKey !== EmptyReport.reportKey && (
                <ReportActionDelete
                    report={report}
                    reportKey={reportKey}
                    disabled={disabled || busy || !reportKey || !report?.reportKey}
                    onBusy={busy => isMounted.current && setBusy(busy)}
                    onReportKeyChange={onReportKeyChange}
                    className="option--delete"
                />
            )}
            <Button
                size="small"
                disabled={disabled || busy || !isDirty}
                onClick={revokeChanges}
                className="option--cancel"
            >
                Cancel
            </Button>
            <ReportActionSave
                report={report}
                reportKey={reportKey}
                disabled={disabled || busy || !isDirty}
                onBusy={busy => isMounted.current && setBusy(busy)}
                onReportKeyChange={onReportKeyChange}
                prepareCommit={prepareCommit}
                keyCode="KeyS"
                className="option--save"
                type="primary"
                icon="save"
            />
        </Container>
    );
};

const Container = styled(LayoutHorizontalGroup)`
    &&&&&&&& {
        .option--save {
            margin-left: 15px;
            min-width: 80px;
        }
    }
`;
