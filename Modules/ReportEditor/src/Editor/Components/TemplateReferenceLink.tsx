import React from "react";
import { Link } from "react-router-dom";
import styled from "styled-components";
import { Icon } from "PrimitiveComponents/index";

import { ITemplate } from "TransportGateway/index";

import { createReportLink } from "../../utils";

type TemplateReferenceLinkProps = {
    template: ITemplate;
    className?: string;
};
export const TemplateReferenceLink = ({ template, className }: TemplateReferenceLinkProps) =>
    (template?.TemplateReference?.reportKey && (
        <Links className={className}>
            <Icon
                type="export"
                className="icon"
            />
            <ul>
                <li>
                    <Link to={createReportLink(template?.TemplateReference?.reportKey)}>
                        {template.TemplateReference.reportKey}
                    </Link>
                </li>
                <li>
                    <Link
                        to={createReportLink(
                            template?.TemplateReference?.reportKey,
                            template.TemplateReference?.templateId
                        )}
                    >
                        {template.Name}
                    </Link>
                </li>
            </ul>
        </Links>
    )) ||
    null;

const Links = styled.span`
    &&&& {
        align-self: center;

        .icon {
            margin-right: 5px;
        }

        > ul {
            margin: 0;
            padding: 0;
            display: inline-flex;
            list-style: none;

            > li {
                padding: 0 0 0 5px;
                margin: 0;

                &:before {
                    content: " / ";
                }

                &:first-of-type {
                    padding-left: 0;

                    &:before {
                        content: unset;
                    }
                }
            }
        }
    }
`;
