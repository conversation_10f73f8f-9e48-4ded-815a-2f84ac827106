import React, { memo, useEffect, useMemo } from "react";
import styled from "styled-components";
import { Alert } from "antd";
import { v4 as uuidv4 } from "uuid";

import {
    DraftEditProvider,
    DraftEditApi,
    LayoutVertical,
    LayoutHorizontal,
    LayoutHorizontalGroup,
    Icon,
    TabPane,
    Tabs
} from "PrimitiveComponents/index";
import { EmptyReport, IReport, IDataSource } from "TransportGateway/index";

import { extensions } from "./DraftEdit";
import {
    EditorReportActions,
    EditorReportGraph,
    EditorReportTemplate,
    EditorReportVersions,
    Help
} from "./Views";
import {
    ReportUpdatedDetails,
    TemplateTab,
    Templates,
    ReportActions,
    ReportTab,
    EditorMenu
} from "./Components";
import { useReportActions } from "../Actions";
import { useReportHistory } from "../Components";
import { usePersistSettings } from "../persistSettings";
import { useAuthenticatedUser } from "../authenticatedUser";
import { isInvalidReport } from "../utils";

type EditorProps = {
    report?: IReport;
    reportKey?: string;
    sources?: IDataSource[];
    onReportViewChange?: (reportKey: string, viewId?: string) => void;
    viewId?: string;
    disabled?: boolean;
};

const VALID_TABS = ["EditGraph", "Actions", "Templates", "AddTemplate", "Versions", "Help", "json"];
const VALID_TABS_DISABLED = ["EditGraph", "Actions", "Templates", "Help", "json"];

const Container = styled.div`
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    padding: 6px 12px 0 12px;
    font-size: 12px;
    background-color: #222;
`;

const Footer = styled(LayoutHorizontalGroup)`
    border-top: 1px solid var(--border__color--muted, #333);
    align-items: flex-end;
    padding: 5px 0;
`;

const StyledReportActions = styled(ReportActions)`
    &&&&&&& {
        flex-grow: 1;
        justify-content: flex-end;
    }
`;

const ReadOnly = styled(LayoutHorizontal)`
    &&&&&&& {
        color: orange;
        font-size: 0.75em;
        opacity: 0.75;
        flex-grow: 1;
        justify-content: flex-end;
    }
`;

const StyledReportUpdatedDetails = styled(ReportUpdatedDetails)`
    font-size: 0.75em;
    opacity: 0.75;
`;

const Warning = styled.div`
    flex-grow: 1;
    align-items: center;
    align-self: center;
    display: flex;
    color: orangered;

    &&& {
        .ant-alert-info {
            background-color: #111932;
            border: 1px solid #061f70;
        }
    }
`;

type EditorLayoutProps = {
    api: DraftEditApi<IReport>;
} & EditorProps;

const EditorLayout = memo(
    ({ api, report, reportKey, sources, onReportViewChange, viewId }: EditorLayoutProps) => {
        const { settings } = usePersistSettings();
        const { reportActions, reRunReportActions, clearReportActions } = useReportActions(report);
        const { remove } = useReportHistory();

        const validTabs = useMemo(
            () =>
                (api.disabled ? VALID_TABS_DISABLED : VALID_TABS).filter(tab =>
                    reportActions?.actions?.length > 0 || reportActions?.keepAction
                        ? true
                        : tab !== "Actions"
                ),
            [api.disabled, reportActions?.actions?.length, reportActions?.keepAction]
        );
        const viewIdKey =
            ([...validTabs, ...(report?.templates?.map(({ Id }) => Id) || [])].some(
                key => key === viewId
            ) &&
                viewId) ||
            validTabs[0];

        const handleReportKeyChange = (rk: string, prevReportKey: string) => {
            api.revokeChanges();
            if (rk === EmptyReport.reportKey) {
                remove(prevReportKey);
            }
            onReportViewChange(rk);
        };

        return (
            <Container>
                <LayoutVertical>
                    <Tabs
                        animated={false}
                        activeKey={viewIdKey}
                        onChange={key => onReportViewChange(reportKey, key)}
                        className="flex-grow"
                        layout="default"
                        tabBarExtraContent={
                            <EditorMenu
                                templates={report?.templates}
                                onChange={id => onReportViewChange(reportKey, id)}
                                options={validTabs}
                                activeKey={viewIdKey}
                                showRaw={settings?.editor?.debug?.showRaw}
                                showVersions={report && reportKey !== EmptyReport.reportKey}
                            />
                        }
                    >
                        {validTabs.some(key => key === "EditGraph") && (
                            <TabPane
                                key="EditGraph"
                                tab={<ReportTab report={report} />}
                            >
                                <EditorReportGraph
                                    sources={sources}
                                    report={report}
                                    draftEditApi={api}
                                />
                            </TabPane>
                        )}

                        {validTabs.some(key => key === "Templates") &&
                            report?.templates?.map(template => (
                                <TabPane
                                    key={template.Id}
                                    tab={
                                        <TemplateTab
                                            template={template}
                                            onDeleteTemplate={t =>
                                                api.deleteArrayItem(["templates"], "Id", t.Id)
                                            }
                                            currentTemplateOrder={report?.templates?.map(
                                                ({ Id }) => Id
                                            )}
                                            onReorderTemplates={newOrder =>
                                                api.reorderArray(["templates"], "Id", newOrder)
                                            }
                                            disabled={api.disabled}
                                        />
                                    }
                                >
                                    <EditorReportTemplate
                                        report={report}
                                        template={template}
                                        getPath={name =>
                                            api.getPath("templates", "Id", template.Id, name)
                                        }
                                        disabled={api.disabled}
                                    />
                                </TabPane>
                            ))}

                        {validTabs.some(key => key === "AddTemplate") && (
                            <TabPane
                                key="AddTemplate"
                                tab={
                                    <Icon
                                        type="plus-circle"
                                        title="Add Template"
                                        style={{ margin: 0, lineHeight: "inherit" }}
                                    />
                                }
                            >
                                <Templates
                                    onSelect={({ Regions, ...rest }) => {
                                        const id = uuidv4();

                                        api.addArrayItem(["templates"], "Id", {
                                            ...rest,
                                            Id: id
                                        });

                                        // Add nested array items...
                                        Regions?.forEach(({ TemplatePropertyMappings, ...r }) => {
                                            const regionPath = api.getPath(
                                                "templates",
                                                "Id",
                                                id,
                                                "Regions"
                                            );

                                            api.addArrayItem(regionPath.split("."), "Id", {
                                                ...r
                                            });

                                            TemplatePropertyMappings?.forEach(
                                                TemplatePropertyMapping => {
                                                    api.addArrayItem(
                                                        api
                                                            .getPath(
                                                                regionPath,
                                                                "Id",
                                                                r.Id,
                                                                "TemplatePropertyMappings"
                                                            )
                                                            .split("."),
                                                        "Name",
                                                        {
                                                            ...TemplatePropertyMapping
                                                        }
                                                    );
                                                }
                                            );
                                        });
                                    }}
                                    templates={report?.templates}
                                    canSelectReferenceTemplate={report?.category !== "Template"}
                                    reportKey={reportKey}
                                />
                            </TabPane>
                        )}

                        {report &&
                            reportKey !== EmptyReport.reportKey &&
                            validTabs.some(key => key === "Versions") && (
                                <TabPane
                                    key="Versions"
                                    tab="Versions"
                                >
                                    {report && (
                                        <EditorReportVersions
                                            key={report.reportKey}
                                            report={report}
                                            reportKey={reportKey}
                                            onReportKeyChange={handleReportKeyChange}
                                        />
                                    )}
                                </TabPane>
                            )}

                        {validTabs.some(key => key === "Actions") && (
                            <TabPane
                                key="Actions"
                                tab={
                                    <span>
                                        <Icon type="warning" />
                                        <span>
                                            Actions{" "}
                                            <sup>({reportActions?.actions?.length || 0})</sup>
                                        </span>
                                    </span>
                                }
                            >
                                <EditorReportActions
                                    reportActions={reportActions?.actions}
                                    onUpdate={reRunReportActions}
                                />
                            </TabPane>
                        )}

                        {validTabs.some(key => key === "Help") && (
                            <TabPane
                                key="Help"
                                tab="Help"
                            >
                                <Help />
                            </TabPane>
                        )}

                        {settings?.editor?.debug?.showRaw && (
                            <TabPane
                                tab="RAW"
                                key="json"
                            >
                                <div style={{ overflow: "auto", flexGrow: 1, height: 0 }}>
                                    <pre
                                        style={{
                                            wordBreak: "break-all",
                                            whiteSpace: "break-spaces",
                                            margin: 0,
                                            fontSize: 10,
                                            lineHeight: 1.2
                                        }}
                                    >
                                        {JSON.stringify(report, undefined, 2)}
                                    </pre>
                                </div>
                            </TabPane>
                        )}
                    </Tabs>

                    {viewIdKey !== "Versions" && (
                        <Footer
                            type="footer"
                            compact={false}
                        >
                            <StyledReportUpdatedDetails
                                lastUpdated={report?.lastUpdated}
                                lastUpdatedBy={report?.lastUpdatedBy}
                            >
                                {/* {api.hasConflict() && <strong style={{ color: "orangered" }}> [CONFLICTS]</strong>} */}
                            </StyledReportUpdatedDetails>

                            {api.disabled ? (
                                <ReadOnly>This report is readonly</ReadOnly>
                            ) : (
                                <StyledReportActions
                                    report={report}
                                    reportKey={reportKey}
                                    onReportKeyChange={handleReportKeyChange}
                                    prepareCommit={api.prepareCommit}
                                    revokeChanges={() => {
                                        api.revokeChanges();
                                        clearReportActions();
                                    }}
                                    disabled={api.disabled}
                                    isDirty={api.isDirty()}
                                />
                            )}
                        </Footer>
                    )}
                </LayoutVertical>
            </Container>
        );
    }
);

export const Editor = ({
    report,
    reportKey,
    sources,
    onReportViewChange,
    viewId,
    disabled
}: EditorProps) => {
    const { push } = useReportHistory();
    const [, { canViewReport, canEditReport }] = useAuthenticatedUser();

    const reportValidation = useMemo(() => isInvalidReport(report), [report]);

    if (typeof reportValidation === "string") {
        throw new TypeError(reportValidation);
    }

    useEffect(() => {
        if (push && reportKey && reportKey !== EmptyReport.reportKey) {
            push({ reportKey });
        }
    }, [push, reportKey]);

    return canViewReport(report?.owner) ? (
        <DraftEditProvider
            value={report}
            getDataId={r => r?.reportKey}
            extensions={extensions}
            disabled={
                disabled ||
                (report?.reportKey !== EmptyReport.reportKey && !canEditReport(report?.owner))
            }
            removeUndefinedFromModel
        >
            {api =>
                api.data && (
                    <EditorLayout
                        api={api}
                        report={api.data}
                        reportKey={reportKey}
                        sources={sources}
                        onReportViewChange={onReportViewChange}
                        viewId={viewId}
                    />
                )
            }
        </DraftEditProvider>
    ) : (
        <Warning>
            <Alert
                message="Report Permission"
                description="You do not have permission to access this report."
                type="info"
                showIcon
            />
        </Warning>
    );
};
