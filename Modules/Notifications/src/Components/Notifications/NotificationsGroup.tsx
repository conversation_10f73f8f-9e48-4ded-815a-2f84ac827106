import React, { memo, useCallback, useEffect, useState } from "react";
import styled from "styled-components";

import { withBadge, Button } from "PrimitiveComponents/index";

// Recursive component
// eslint-disable-next-line import/no-cycle
import { NotificationGroupedList } from "./NotificationGroupedList";
import { NotificationSummary } from "./NotificationSummary";
import type { NotificationsListProps } from "./types";

const Container = styled(withBadge(styled.div``))`
    &&&&&&&&&&& {
        .ant-badge-count {
            right: var(--spacing--small);
        }
    }
`;

const PlaceHolderContainer = styled.div`
    --indent: 0.6em;
    position: relative;
    z-index: 0;
`;

const PlaceHolder = styled.div`
    height: 1em;
    background-color: var(--notification__full__background-color);
    border-radius: var(--notification__full__border-radius);
    box-shadow: var(--notification__box-shadow--secondary);
    position: relative;
    z-index: 2;
    margin-top: -0.3em;
    margin-left: var(--indent);
    margin-right: var(--indent);
`;

const Close = styled(Button)`
    &&&&&& {
        margin: var(--spacing--small) 0 0 auto;
    }
`;

export const NotificationsGroup = memo(
    ({
        data,
        onSelect,
        selectedId,
        className,
        onActionComplete,
        ...rest
    }: NotificationsListProps) => {
        const hasSelectedId = data?.some(i => i.id === selectedId);
        const [isGrouped, setIsGrouped] = useState(!hasSelectedId);

        const notifications =
            isGrouped && data
                ? [data?.find(({ status }) => status !== "Inactive") || data?.[0]]
                : data;

        useEffect(() => {
            if (hasSelectedId) {
                setIsGrouped(false);
            }
        }, [hasSelectedId]);

        const handleCollapse = async () => {
            if (hasSelectedId) {
                onSelect(undefined);
            }

            setIsGrouped(true);
        };

        const handleSelect = useCallback<NotificationsListProps["onSelect"]>(
            (id, container) => {
                if (isGrouped) {
                    setIsGrouped(false);
                } else {
                    return onSelect(id, container);
                }

                return undefined;
            },
            [onSelect, isGrouped]
        );

        return (
            notifications?.length > 0 && (
                <Container
                    badge={isGrouped && data?.length > 1 && data?.length}
                    badgeType="default"
                    className={className}
                >
                    {isGrouped ? (
                        <NotificationGroupedList
                            data={notifications}
                            onSelect={handleSelect}
                            emphasised={isGrouped}
                        >
                            <NotificationSummary
                                notification={notifications?.[0]}
                                data={data}
                                onSelect={handleSelect}
                                emphasised={isGrouped}
                                onActionComplete={onActionComplete}
                                {...rest}
                            />
                        </NotificationGroupedList>
                    ) : (
                        <NotificationGroupedList
                            data={notifications}
                            onSelect={handleSelect}
                            selectedId={selectedId}
                            emphasised={isGrouped}
                            onActionComplete={onActionComplete}
                        />
                    )}

                    {!isGrouped && data?.length > 0 && (
                        <Close
                            onClick={handleCollapse}
                            type="ghost"
                            size="x-small"
                        >
                            Close
                        </Close>
                    )}

                    {isGrouped && data?.length > 1 && (
                        <PlaceHolderContainer>
                            <PlaceHolder />
                            {data?.length > 2 && (
                                <PlaceHolder
                                    style={{
                                        marginLeft: "calc(var(--indent) * 2)",
                                        marginRight: "calc(var(--indent) * 2)",
                                        zIndex: 1
                                    }}
                                />
                            )}
                        </PlaceHolderContainer>
                    )}
                </Container>
            )
        );
    }
);
