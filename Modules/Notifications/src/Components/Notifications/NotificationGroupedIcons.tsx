import React from "react";
import styled, { css } from "styled-components";

import { orderedUniq } from "Utilities/index";
import { Typography, LayoutHorizontalGroup } from "PrimitiveComponents/index";
import { CategoryLabel } from "Components/index";

import type { NotificationData } from "./types";

const Container = styled.div<{ columns: number }>`
    display: grid;
    gap: var(--spacing--small);

    ${({ columns }) => css`
        grid-template-columns: repeat(${columns}, minmax(0, 1fr));
        grid-template-rows: repeat(${columns}, minmax(0, 1fr));
    `}
`;

type NotificationGroupedIconsProps = {
    data: NotificationData[];
};

export const NotificationGroupedIcons = ({ data }: NotificationGroupedIconsProps) => {
    const allCategories = orderedUniq(
        data?.filter(({ status }) => status !== "Inactive")?.map(({ categoryType }) => categoryType)
    );
    const categories = allCategories?.slice(0, 4);
    const columns = categories?.length > 1 ? 2 : 1;

    return (
        <Container columns={columns}>
            {categories?.map((item, index) =>
                categories.length > 1 &&
                index === categories.length - 1 &&
                allCategories.length > categories.length ? (
                    <LayoutHorizontalGroup
                        key={item.value}
                        alignItems="center"
                        justifyContent="center"
                    >
                        <Typography.Text
                            fontSize="x-small"
                            muted
                        >
                            <span>＋{allCategories.length - categories.length + 1}</span>
                        </Typography.Text>
                    </LayoutHorizontalGroup>
                ) : (
                    <CategoryLabel
                        key={item.value}
                        categoryType={item.value}
                        includeTypeLabel={false}
                        responsive
                        hasTooltip
                    />
                )
            )}
        </Container>
    );
};
