import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import { <PERSON><PERSON><PERSON><PERSON>, ThemeIterator, LayoutVertical } from "PrimitiveComponents/index";

import { NotificationSamplesAdmin } from "../../Development";
import { Notification } from "../Notification";

<Meta title="Notifications/Notification Examples" component={Notification} />

export const Template = () => (
    <LayoutVertical spacing="xx-large">
        <ThemeIterator>
            {() => (
                <ThemeContainer flex>
                    <NotificationSamplesAdmin/>
                </ThemeContainer>
            )}
        </ThemeIterator>
    </LayoutVertical>
);

# Notification

<Description of={Notification} />

<Canvas>
    <Story
        name="Notification Examples"
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes of={Notification} sort="requiredFirst" />
