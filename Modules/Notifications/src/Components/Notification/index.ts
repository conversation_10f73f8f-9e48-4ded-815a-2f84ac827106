import { ComponentPreview } from "./Components/ComponentPreview";

import { MacroObservation } from "./MacroObservation";
import { Breach } from "./Breach";

type Notifications = {
    ComponentPreview: typeof ComponentPreview;

    MacroObservation: typeof MacroObservation;
    Breach: typeof Breach;
};

const Notification = {} as Notifications;

Notification.ComponentPreview = ComponentPreview;

Notification.MacroObservation = MacroObservation;
Notification.Breach = Breach;

export { Notification };
