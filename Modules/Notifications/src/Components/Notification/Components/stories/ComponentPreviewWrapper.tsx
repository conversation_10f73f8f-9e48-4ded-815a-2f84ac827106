import React from "react";

import { type Workflow } from "Contracts/index";
import { withFixtures } from "TransportGateway/index";
import { type ComponentFactoryType } from "Components/index";

import { ComponentPreview } from "../ComponentPreview";

import { macroObservationFixture } from "./fixtures/macro-observation";
import { breach } from "./fixtures/breach";
import { reason } from "./fixtures/reason";

const Preview = withFixtures(ComponentPreview, {
    ...macroObservationFixture,
    ...breach,
    ...reason
});

type ComponentPreviewWrapperProps = {
    componentType: ComponentFactoryType;
    additionalComponentType: ComponentFactoryType;
    notificationWidth?: string | number;
};

export const ComponentPreviewWrapper = ({
    componentType,
    additionalComponentType,
    notificationWidth
}: ComponentPreviewWrapperProps) => (
    <Preview
        id={42}
        type={componentType}
        notificationWidth={notificationWidth}
        defaultAdditionalComponent={0}
        notification={
            {
                additionalComponents: [
                    {
                        id: "1",
                        componentType: additionalComponentType,
                        componentProps: {
                            id: 42
                        },
                        label: "Why am I seeing this?",
                        icon: "info",
                        type: "Icon",
                        children: {
                            items: [
                                {
                                    id: "1.1",
                                    component: "PrimitiveComponents.SkeletonText",
                                    componentProps: {
                                        rows: 6
                                    },
                                    label: "Test 1",
                                    icon: "info",
                                    type: "Icon"
                                },
                                {
                                    id: "1.2",
                                    component: "PrimitiveComponents.SkeletonText",
                                    componentProps: {
                                        rows: 6
                                    },
                                    label: "Test 2",
                                    icon: "info",
                                    type: "Icon"
                                },
                                {
                                    id: "1.3",
                                    component: "PrimitiveComponents.SkeletonText",
                                    componentProps: {
                                        rows: 6
                                    },
                                    label: "Test 3",
                                    icon: "info",
                                    type: "Icon"
                                }
                            ]
                        }
                    },
                    {
                        id: "2",
                        component: "PrimitiveComponents.SkeletonText",
                        componentProps: {
                            rows: 6
                        },
                        label: "Test 1",
                        icon: "info",
                        type: "Icon"
                    }
                ]
            } as unknown as Workflow.Notifications.Notification
        }
    />
);
