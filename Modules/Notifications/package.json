{"name": "@tradinglabs/notifications", "scripts": {"start": "raid-build start", "build": "yarn build-app", "build-app": "raid-build build", "build-typescript": "yarn g:tsc && yarn make-types", "make-types": "yarn run -T make-federated-types", "test:ci": "jest --passWithNoTests --ci --detectOpenHandles", "lint:all": "yarn lint:eslint && yarn lint:stylelint", "lint:eslint": "node ../../scripts/eslint/runEslint.js --paths='./src/**/*.+(js|jsx|ts|tsx)'", "lint:stylelint": "node ../../scripts/stylelint/runStylelint.js --paths='./src/**/*.+(ts|tsx)'"}, "dependencies": {"@tradinglabs/analytics": "workspace:*", "@tradinglabs/client-api": "workspace:*", "@tradinglabs/components": "workspace:*", "@tradinglabs/contracts": "workspace:*", "@tradinglabs/grid": "workspace:*", "@tradinglabs/primitive-components": "workspace:*", "@tradinglabs/schema": "workspace:*", "@tradinglabs/theming": "workspace:*", "@tradinglabs/utilities": "workspace:*", "classnames": "2.3.2", "lodash": "~4.17.21", "moment": "2.29.4", "react": "18.2.0", "react-dom": "18.2.0", "styled-components": "5.3.11"}, "devDependencies": {"@tradinglabs/raid-build": "workspace:*"}}