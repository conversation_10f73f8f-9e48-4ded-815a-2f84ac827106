import React from "react";
import styled from "styled-components";

import { BaseStyles } from "Theming/index";
import {
    TickerTooltipProvider,
    withLazyComponent,
    withStandardErrorBoundary,
    withAppRestart,
} from "PrimitiveComponents/index";
import { GatewayProvider } from "TransportGateway/index";
import { ClientApiProvider } from "ClientApi/index";
import { getSearchParams } from "Utilities/index";
import { withFeatureProvider } from "ConnectedComponents/index";

const ContextMenuTicker = withLazyComponent(
    () => import("ConnectedComponents/index"),
    "ContextMenuTicker",
);
const CompanySummary = withLazyComponent(
    () => import("CompanyPage/CompanySummary"),
    "CompanySummary",
);

const LaunchPadClientApiProvider = styled(ClientApiProvider)`
    height: 100vh;
`;

const LaunchPad = withFeatureProvider(() => {
    const searchParams = getSearchParams<{
        launchPadEnabled?: boolean;
    }>(window.location.search);

    return <LaunchPadClientApiProvider launchPadEnabled={searchParams.launchPadEnabled ?? true} />;
});

export const App = withAppRestart(
    withStandardErrorBoundary(() => (
        <BaseStyles
            theme="dashboard-dark"
            global
            sandboxed={false}
        >
            <GatewayProvider includeServiceApiGatewayHub>
                <TickerTooltipProvider
                    ContextMenuComponent={ContextMenuTicker}
                    ToolTipComponent={CompanySummary}
                >
                    <LaunchPad />
                </TickerTooltipProvider>
            </GatewayProvider>
        </BaseStyles>
    )),
);
