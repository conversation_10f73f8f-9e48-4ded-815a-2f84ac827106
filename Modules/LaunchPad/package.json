{"name": "@tradinglabs/launchpad", "license": "UNLICENSED", "version": "1.0.0", "main": "src/index.ts", "scripts": {"start": "raid-build start", "start-openfin": "openfin -l -c ./config/manifest.fin.json", "build": "yarn build-app", "build-app": "raid-build build", "build-typescript": "yarn g:tsc && yarn make-types", "make-types": "yarn run -T make-federated-types", "test:ci": "jest --passWithNoTests --ci --detectOpenHandles", "lint:all": "yarn lint:eslint && yarn lint:stylelint", "lint:all:fix": "yarn eslint --fix && yarn lint:stylelint", "lint:eslint": "node ../../scripts/eslint/runEslint.js --paths='./src/**/*.+(js|jsx|ts|tsx)'", "lint:stylelint": "node ../../scripts/stylelint/runStylelint.js --paths='./src/**/*.+(ts|tsx)'"}, "dependencies": {"@tradinglabs/client-api": "workspace:*", "@tradinglabs/company-page": "workspace:*", "@tradinglabs/components": "workspace:*", "@tradinglabs/connected-components": "workspace:*", "@tradinglabs/primitive-components": "workspace:*", "@tradinglabs/theming": "workspace:*", "@tradinglabs/transport-gateway": "workspace:*", "@tradinglabs/utilities": "workspace:*", "classnames": "2.3.2", "openfin-cli": "^4.0.0", "react": "18.2.0", "react-dom": "18.2.0", "styled-components": "5.3.11"}, "devDependencies": {"@tradinglabs/raid-build": "workspace:*", "@types/react": "18.0.25", "@types/react-dom": "18.0.9", "@types/styled-components": "5.1.29", "typescript": "5.2.2"}}