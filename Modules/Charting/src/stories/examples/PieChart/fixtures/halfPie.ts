const COLORS = [
    "#0088FE",
    "#00C49F",
    "#FFBB28",
    "#FF1919",
    "#AF19FF",
    "#FF8042",
    "#19FF19",
    "#19FFAF"
];

export const data = [
    { name: "Group A", value: 0.34 },
    { name: "Group B", value: 0.15 },
    { name: "Group C", value: 0.18 },
    { name: "Group D", value: 0.08 },
    { name: "Group E", value: 0.25 },
    { name: "Group F", value: 0.12 }
].map((d, i) => ({ ...d, fill: COLORS[i] }));

export const schema = [
    {
        columnId: 0,
        contentType: 9,
        schema: [],
        name: "rowId",
        metaData: {
            hidden: true,
            displayName: "Row Id",
            properties: {}
        }
    },
    {
        columnId: 1,
        contentType: 9,
        schema: [],
        name: "name",
        metaData: {
            displayName: "Name",
            properties: {
                order: 1
            }
        }
    },
    {
        columnId: 2,
        contentType: 8,
        schema: [],
        name: "value",
        metaData: {
            contentTypeForDisplay: "DollarValue",
            displayName: "Value",
            properties: {
                order: 2,
                chartType: "pie",
                chartLegendPropsShow: true,
                chartSeriesPropsDataKey: "value",
                chartSeriesPropsInnerRadius: 80,
                chartSeriesPropsOuterRadius: 100,
                chartSeriesPropsStartAngle: 180,
                chartSeriesPropsEndAngle: 0,
                chartSeriesPropsPaddingAngle: 5,
                chartSeriesPropsCx: "50%",
                chartSeriesPropsCy: "75%",
                chartSeriesPropsStroke: "transparent",
                chartTooltipPropsFormatterType: "number",
                chartTooltipPropsValueFormat: "percent2dp",
                chartTooltipPropsDataKey: "value"
            }
        }
    }
];
