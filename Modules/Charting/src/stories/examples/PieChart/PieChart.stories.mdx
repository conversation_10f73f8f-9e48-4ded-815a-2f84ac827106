import { <PERSON><PERSON>, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import { ThemeIterator, ThemeContainer, Flex } from "PrimitiveComponents/index";

import * as riskPagePie from "./fixtures/riskPagePie";
import * as halfPie from "./fixtures/halfPie";
import * as withLabels from "./fixtures/withLabels";

import { Chart } from "../../..";

<Meta
    title="Charting/Examples/PieChart"
    component={Chart}
/>

export const Template = ({ type, ...args }) => (
    <Flex
        gap="xx-large"
    >
        <ThemeIterator>
            {() => (
                <ThemeContainer flex>
                    <Flex flex="1" direction="column" gap="x-large">
                        <Flex gap="x-large">
                            <Flex w={200} h={200} direction="column">
                                <Chart
                                    {...args}
                                    {...riskPagePie}
                                    chartType="pie"
                                />
                            </Flex>
                            <Flex w={200} h={200} direction="column">
                                <Chart
                                    {...args}
                                    {...withLabels}
                                    chartType="pie"
                                />
                            </Flex>
                        </Flex>
                        <Flex w={435} h={200} direction="column">
                            <Chart
                                {...args}
                                {...halfPie}
                                chartType="pie"
                            />
                        </Flex>
                    </Flex>
                </ThemeContainer>
            )}
        </ThemeIterator>
    </Flex>
);

# PieChart

<Description of={Chart} />

<Canvas>
    <Story
        name="PieChart"
        parameters={{
            controls: {
                disable: false,
                include: []
            }
        }}
        args={{}}
        argTypes={{}}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes
    of={Chart}
    sort="requiredFirst"
/>
