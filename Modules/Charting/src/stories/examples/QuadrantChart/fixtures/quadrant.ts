export const data = [
    { x: 0.4, y: 0.8, z: 200 },
    { x: 0.5, y: -0.2, z: 350 },
    { x: -0.7, y: 0.3, z: 860 },
    { x: -0.25, y: -0.75, z: 460 }
];

export const schema = [
    {
        columnId: 10,
        name: "longDeltaExposure",
        contentType: 8,
        metaData: {
            properties: {
                adapters: [
                    "@addPositiveNegativeFillToDatum(x)",
                    "@addPropertyToDatum(stroke, var(--color__background))"
                ],
                chartZoomAndPanPropsEnabled: true,
                chartType: "bubbleseries",
                chartPropsChartType: "scatter",
                chartPropsMargin: { bottom: 30, left: 30, right: 30, top: 30 },
                chartCartesianGridPropsShow: false,

                chartXAxisPropsShow: true,
                chartXAxisPropsHide: true,
                chartXAxisPropsDataKey: "x",
                chartXAxisPropsDomain: "[-1, 1]",
                chartXAxisPropsName: "Net Exposure $",
                chartXAxisPropsPadding: { left: 10, right: 10 },

                chartYAxisLeftPropsShow: true,
                chartYAxisLeftPropsHide: true,
                chartYAxisLeftPropsDataKey: "y",
                chartYAxisLeftPropsDomain: "[-1, 1]",
                chartYAxisLeftPropsName: "Trading Range",
                chartYAxisLeftPropsPadding: { top: 10, bottom: 10 },

                chartZAxisPropsDataKey: "z",
                chartZAxisPropsName: "Value at Risk",
                chartZAxisPropsRange: "[50, 1000]",
                chartZAxisPropsShow: true,

                chartReferenceLinePropsShow: true,
                chartReferenceLinePropsList: [
                    { x: 0 },
                    { y: 0 },
                    {
                        segment: [
                            { x: 0, y: 1 },
                            { x: 0, y: 1 }
                        ],
                        label: { value: "top", position: "insideBottom", offset: 13 }
                    },
                    {
                        segment: [
                            { x: 0, y: -1 },
                            { x: 0, y: -1 }
                        ],
                        label: { value: "bottom", position: "insideTop", offset: 13 }
                    },
                    {
                        segment: [
                            { x: 1, y: 0 },
                            { x: 1, y: 0 }
                        ],
                        label: { value: "right", position: "insideLeft", offset: 13 }
                    },
                    {
                        segment: [
                            { x: -1, y: 0 },
                            { x: -1, y: 0 }
                        ],
                        label: { value: "left", position: "insideRight", offset: 13 }
                    }
                ],
                chartTooltipPropsSortBy: "x, y, z"
            }
        }
    }
];
