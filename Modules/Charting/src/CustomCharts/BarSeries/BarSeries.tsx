import React from "react";
import { v4 } from "uuid";

import type { FormatterType, MarginsAndPaddings } from "Contracts/index";

import { ChartDefinitionDecorator } from "../../utils";

import { BarSeriesBar } from "./BarSeriesBar";
import { BarSeriesTooltipOverlay } from "./BarSeriesTooltipOverlay";

export const BarSeries: ChartDefinitionDecorator = ({ data, definition, schema, schemaProps }) => {
    const {
        referenceRowsLabelColumnName: seriesLabelColumnName,
        referenceRowsValueColumnName: seriesValueColumnName,
        referenceRowsTotalColumnName: seriesTotalColumnName,
        referenceRowsTotalFormatterType: seriesTotalFormatterType,
        referenceRowsTotalValueFormat: seriesTotalValueFormat
    } = definition?.column?.metaData?.properties ?? {};

    const { chartYAxisLeftPropsPadding, chartYAxisRightPropsPadding } = schemaProps.allProps;
    const { top: topPadding, bottom: bottomPadding } = (chartYAxisLeftPropsPadding ??
        chartYAxisRightPropsPadding ??
        {}) as MarginsAndPaddings;

    const columnName = `barseries.${definition?.column?.name}`;
    const minColumnName = `${columnName}.min`;
    const maxColumnName = `${columnName}.max`;
    const id = `${columnName}.${v4()}`;

    if (!seriesValueColumnName) {
        console.warn(
            `The series '${definition?.column?.name}' does not have a child schema specifing a value chartType`
        );
        return undefined;
    }

    const decoratedData = data?.map(item => ({
        ...item,
        [minColumnName]: item[definition.column.name].reduce(
            (sum: number, row: {}) => sum + Math.min(0, row[seriesValueColumnName]),
            0
        ),
        [maxColumnName]: item[definition.column.name].reduce(
            (sum: number, row: {}) => sum + Math.max(0, row[seriesValueColumnName]),
            0
        )
    }));

    const min = Math.min(...decoratedData.map(row => row[minColumnName]), 0);
    const max = Math.max(...decoratedData.map(row => row[maxColumnName]), 0);

    const chartType = {
        chartType: "barseries",
        seriesValueColumnName,
        seriesLabelColumnName,
        min,
        max,
        sourceDataKey: definition?.column?.name
    };

    return {
        data: decoratedData,
        chartComponentProps: {
            tooltip: {
                content: <BarSeriesTooltipOverlay schema={schema} />
            }
        },
        definition: [
            {
                ...definition,
                chartSeriesType: "bar",
                props: {
                    ...definition?.props,
                    dataKey: minColumnName,
                    id,
                    chartType,
                    stackId: `barseries-${columnName}`,
                    shape: (
                        <BarSeriesBar
                            barType="negative"
                            column={definition?.column}
                            seriesValueColumnName={seriesValueColumnName}
                            seriesTotalColumnName={seriesTotalColumnName}
                            seriesTotalFormatterType={seriesTotalFormatterType as FormatterType}
                            seriesTotalValueFormat={seriesTotalValueFormat}
                            seriesMin={min}
                            seriesMax={max}
                            topPadding={topPadding}
                            bottomPadding={bottomPadding}
                        />
                    )
                }
            },
            {
                ...definition,
                chartSeriesType: "bar",
                props: {
                    ...definition?.props,
                    dataKey: maxColumnName,
                    id,
                    chartType,
                    stackId: `barseries-${columnName}`,
                    shape: (
                        <BarSeriesBar
                            barType="positive"
                            column={definition?.column}
                            seriesValueColumnName={seriesValueColumnName}
                            seriesTotalColumnName={seriesTotalColumnName}
                            seriesTotalFormatterType={seriesTotalFormatterType as FormatterType}
                            seriesTotalValueFormat={seriesTotalValueFormat}
                            seriesMin={min}
                            seriesMax={max}
                            topPadding={topPadding}
                            bottomPadding={bottomPadding}
                        />
                    )
                }
            }
        ]
    };
};
