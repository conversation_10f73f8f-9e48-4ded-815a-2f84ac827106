import React from "react";
import { scaleLinear } from "d3";
import { uniqBy } from "lodash";

import { TooltipOverlay } from "../../RechartsComponents";
import { ExtendedTooltipProps, TooltipPayload } from "../../types";

import { createStackData } from "./createStackData";

type BarSeriesChartType = {
    chartType: string;
    seriesValueColumnName: string;
    seriesLabelColumnName: string;
    sourceDataKey: string;
    min: number;
    max: number;
};

type BarSeriesTooltipOverlayProps = {} & ExtendedTooltipProps<
    number | string | Array<number | string>,
    string,
    BarSeriesChartType
>;

export const BarSeriesTooltipOverlay = ({
    payload,
    active,
    viewBox,
    ...props
}: BarSeriesTooltipOverlayProps) => {
    if (!active) {
        return null;
    }

    const activePayloads = uniqBy(
        payload?.filter(item => item.chartType?.chartType === "barseries"),
        item => item.id
    );
    const restPayload = payload?.filter(item => item.chartType?.chartType !== "barseries");

    const getBarSeriesTooltipPayload = (
        activePayload: TooltipPayload<
            any,
            number | string | Array<number | string>,
            string,
            BarSeriesChartType
        >
    ) => {
        const seriesMin = activePayload.chartType.min;
        const seriesMax = activePayload.chartType.max;
        const yScale = scaleLinear().domain([seriesMin, seriesMax]).range([viewBox.height, 0]);
        const series = activePayload?.payload?.[activePayload.chartType.sourceDataKey] || [];
        const value = yScale.invert(props.coordinate.y);
        const stackedData = createStackData(
            series,
            activePayload.chartType.seriesValueColumnName,
            value < 0 ? "negative" : "positive"
        );
        return stackedData?.[0]?.find(
            stack =>
                (value >= stack[0] && value <= stack[1]) || (value >= stack[1] && value <= stack[0])
        );
    };

    const tooltipPayload = [
        ...(activePayloads || []).map(activePayload => {
            const activeStack = getBarSeriesTooltipPayload(activePayload);
            const { seriesLabelColumnName, seriesValueColumnName, sourceDataKey } =
                activePayload.chartType;

            return (
                activeStack && {
                    ...activePayload,
                    color: undefined,
                    name: activeStack?.data?.[seriesLabelColumnName],
                    dataKey: sourceDataKey,
                    payload: {
                        ...activeStack?.data,
                        ...activePayload?.payload,
                        [sourceDataKey]: activeStack?.data?.[seriesValueColumnName]
                    }
                }
            );
        }),
        ...(restPayload || [])
    ].filter(item => item);

    return (
        (tooltipPayload.length > 0 && (
            <TooltipOverlay
                viewBox={viewBox}
                payload={tooltipPayload}
                active={active}
                {...props}
            />
        )) ||
        null
    );
};
