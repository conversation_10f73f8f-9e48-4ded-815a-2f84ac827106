import React from "react";

export type AxisTickLabelProps = {} & any; // @TODO: build correct types... recharts seems to use any here :(

export const AxisTickLabel = (props: AxisTickLabelProps) => {
    const {
        x,
        y,
        orientation,
        payload,
        visibleTicksCount,
        width: baseWidth,
        height: baseHeight,
        tickFormatter
    } = props;
    const width = orientation === "left" || orientation === "right" ? baseHeight : baseWidth;
    const maxWidth = orientation === "left" || orientation === "right" ? baseWidth : baseHeight;
    const labelWidth =
        orientation === "left" || orientation === "right"
            ? baseWidth // when orientation is left/right, we can stack labels on top of each other
            : width / visibleTicksCount - 10;
    // @TODO: review this logic... :)
    const isOverflowed =
        labelWidth >= baseHeight &&
        orientation !== "left" &&
        orientation !== "right" &&
        x - labelWidth / 2 > baseWidth;
    const transform =
        orientation === "left" || orientation === "right"
            ? `translate(-${baseWidth}px, -50%)`
            : `translate(-50%, 0px)`;
    let textAlign: "center" | "left" | "right" = "center";
    if (orientation === "left") {
        textAlign = "right";
    }
    if (orientation === "right") {
        textAlign = "left";
    }

    if (isOverflowed) {
        return null;
    }

    return (
        <g transform={`translate(${x},${y})`}>
            <foreignObject style={{ overflow: "visible", height: 1, width: 1 }}>
                <div
                    style={{
                        width: labelWidth,
                        lineHeight: "var(--font__line-height--small)",
                        whiteSpace: "nowrap",
                        overflow: "hidden",
                        color: "var(--charts__axis__color)",
                        textOverflow: "ellipsis",
                        ...(labelWidth < baseHeight
                            ? {
                                  maxWidth,
                                  transform: "translate(-100%, -40%) rotate(-45deg)",
                                  transformOrigin: "bottom right",
                                  width: "max-content",
                                  textAlign: "right"
                              }
                            : {
                                  transform,
                                  textAlign
                              })
                    }}
                >
                    {tickFormatter ? tickFormatter(payload.value) : payload.value}
                </div>
            </foreignObject>
        </g>
    );
};
