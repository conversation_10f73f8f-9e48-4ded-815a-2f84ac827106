import styled from "styled-components";

export const TooltipContainer = styled.div`
    &&&&& {
        font-size: clamp(var(--font__size--x-small), 75%, var(--font__size--base));
        color: var(--charts__tooltip__color);
        padding: 1px 0;

        .recharts-tooltip-label {
            font-size: 105%;
            line-height: 1;
            font-weight: bold;
            margin: 0 0 4px 0;
            padding: 0;
        }

        .recharts-tooltip-item-list {
            line-height: var(--font__line-height--small);
            padding: 0;
            margin: 0;
            list-style: none;

            .recharts-tooltip-item {
                padding: 0 0 2px 0;
                display: flex;
                align-items: center;

                &:last-of-type {
                    padding-bottom: 0;
                }

                /* .recharts-tooltip-item-name {
                }

                .recharts-tooltip-item-separator {
                } */

                .recharts-tooltip-item-value {
                    font-weight: bold;
                }

                .recharts-tooltip-item-unit {
                    font-weight: bold;
                }
            }

            > li {
                display: flex;

                > .recharts-tooltip-item-legend {
                    height: 1em;
                    width: 1em;
                    flex-shrink: 0;
                    flex-grow: 0;
                    margin-right: var(--spacing--small, 4px);
                    border: 1px solid var(--border__color--secondary);
                }

                > .recharts-tooltip-item-separator {
                    flex-grow: 1;
                    margin-right: var(--spacing--small, 4px);
                }
            }
        }
    }
`;
