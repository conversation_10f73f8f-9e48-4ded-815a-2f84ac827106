import React from "react";

import { Tooltip } from "PrimitiveComponents/index";
import { SchemaValueTooltip } from "Schema/index";
import { isFunction, isStringType } from "Utilities/index";

import type { ExtendedTooltipProps } from "../../types";
import { useChartContext } from "../../ChartContext";

import { DefaultOverlay } from "./DefaultOverlay";

type TooltipOverlayProps = ExtendedTooltipProps<
    undefined | number | string | Array<number | string>,
    string
>;

const getCoordinates = (props: TooltipOverlayProps) => {
    const { coordinate, schemaProps, viewBox } = props;

    if (!coordinate) return null;

    // If chartTooltipPropsPosition is defined, the tooltip's position becomes fixed and won't change.
    // This mirrors the behavior defined in Recharts' Tooltip API: https://recharts.org/en-US/api/Tooltip#position
    if (schemaProps?.chartTooltipProps?.position) {
        const { x: xBase, y: yBase } = schemaProps.chartTooltipProps.position;

        const x =
            isStringType(xBase) && xBase.includes("%")
                ? (parseFloat(xBase) / 100) * viewBox.width
                : xBase;
        const y =
            isStringType(yBase) && yBase.includes("%")
                ? (parseFloat(yBase) / 100) * viewBox.height
                : yBase;

        return { ...coordinate, ...(x && { x }), ...(y && { y }) };
    }

    return coordinate;
};

export const TooltipOverlay = (
    props: ExtendedTooltipProps<undefined | number | string | Array<number | string>, string>,
) => {
    const context = useChartContext();

    const { active, Content, order, ...rest } = props;
    const coordinate = getCoordinates(props);

    if (!active || !coordinate) return null;

    const dataKey = rest.payload?.[0]?.dataKey;
    const columnSchema = rest.schema?.find(column => column.name === dataKey);

    let result;

    if (columnSchema?.metaData?.properties?.componentTooltip) {
        result = (
            <SchemaValueTooltip
                coordinate={coordinate}
                data={rest.payload?.[0]?.payload}
                context={context}
                columnSchema={columnSchema}
            />
        );
    } else {
        const content = isFunction(Content) ? <Content {...rest} /> : DefaultOverlay(rest);

        if (!content) return null;

        result = (
            <Tooltip
                coordinate={coordinate}
                order={rest.schemaProps?.chartTooltipProps?.order ?? order}
                content={content}
                hideArrow
                offset={15}
            />
        );
    }

    return (
        <div
            style={{
                position: "absolute",
                display: "flex",
                transform: `translate(${coordinate.x + 2}px, ${coordinate.y}px)`,
            }}
        >
            {result}
        </div>
    );
};
