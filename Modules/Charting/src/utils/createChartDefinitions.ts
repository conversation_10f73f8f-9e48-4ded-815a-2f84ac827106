import type { Schema } from "Contracts/index";
import { sortSchemaColumns } from "TransportGateway/index";
import { getFormattedValue, isNumberType, parseReferenceValues } from "Utilities/index";
import type { SchemaContext, SchemaProps } from "Schema/index";

import type { ChartSeriesType } from "../RechartsComponents";
import type { ChartDefinition, ChartComponentProps } from "../types";
import { ChartSeriesEventsApi, getChartSeriesProps } from "./getChartSeriesProps";

type ChartDefinitionDecoration<D = ChartDefinition> = {
    definition?: D;
    data?: any[];
    schema?: Schema;
    chartComponentProps?: ChartComponentProps;
    schemaProps?: SchemaProps;
};

export type ChartDefinitionDecorator = (
    decoratorFactory: ChartDefinitionDecoration
) => void | ChartDefinitionDecoration<ChartDefinition | ChartDefinition[]>;

export const createChartDefinitions = <D>(
    chartId: string | number,
    seriesData: D[],
    seriesSchema: Schema,
    context: SchemaContext<D>,
    decorator: ChartDefinitionDecorator | null,
    schemaProps: SchemaProps,
    api: ChartSeriesEventsApi
) =>
    seriesSchema &&
    sortSchemaColumns(seriesSchema)
        ?.filter(column => column?.metaData?.properties?.chartType)
        ?.reduce(
            ({ data, schema, definitions, chartComponentProps }, column) => {
                const properties = column?.metaData?.properties;

                const chartSeriesProps = getChartSeriesProps({
                    properties,
                    context,
                    api
                });

                const definition = {
                    id: `chart-series-${chartId}-${column.name}`,
                    chartSeriesType: parseReferenceValues(
                        properties?.chartType,
                        seriesData,
                        context
                    ) as ChartSeriesType,
                    column,
                    props: {
                        // Defaults
                        dot: false,
                        isAnimationActive: false,
                        type: "monotone",
                        maxBarSize: 28,
                        dataKey: column.name,

                        // Schema config
                        ...chartSeriesProps,
                        onClick: (datum, index, event) =>
                            chartSeriesProps?.onClick?.({ event, datum }),
                        onContextMenu: (datum, index, event) =>
                            chartSeriesProps?.onContextMenu?.({ event, datum }),

                        // Overrides
                        stackId: properties?.chartStackId,

                        ...(chartSeriesProps?.labelDataKey
                            ? {
                                  label: d => {
                                      const rowValue = d.payload[chartSeriesProps.labelDataKey];
                                      const formattedValue = isNumberType(rowValue)
                                          ? getFormattedValue(rowValue, {
                                                formatterType: chartSeriesProps.labelFormatterType,
                                                valueFormat: chartSeriesProps.labelValueFormat
                                            })
                                          : rowValue;
                                      return formattedValue;
                                  }
                              }
                            : {})
                    }
                };

                if (decorator) {
                    const decorated = decorator({ definition, data, schema, schemaProps });

                    if (decorated) {
                        return {
                            data: decorated.data || data,
                            schema: decorated.schema || schema,
                            definitions: [
                                ...definitions,
                                decorated.definition || definition
                            ].flat(),
                            chartComponentProps: {
                                chartComponentProps,
                                ...decorated.chartComponentProps
                            }
                        };
                    }
                }

                return {
                    data,
                    schema,
                    definitions: [...definitions, definition],
                    chartComponentProps
                };
            },
            {
                data: seriesData,
                schema: seriesSchema,
                definitions: [] as ChartDefinition[],
                chartComponentProps: {} as ChartComponentProps
            }
        );
