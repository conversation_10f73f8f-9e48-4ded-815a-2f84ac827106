import { areCirclesColliding, areRectAndCircleColliding, areRectsColliding } from "../collisions";

describe("areCirclesColliding", () => {
    const circle1 = { x: 0, y: 0, radius: 10 };

    it("should return false if the circles are not colliding", () => {
        const circle2 = { x: 100, y: 100, radius: 5 };
        const result = areCirclesColliding(circle1, circle2);
        expect(result).toBe(false);
    });

    it("should return true if the circles are colliding", () => {
        const circle2 = { x: 10, y: 0, radius: 5 };
        const result = areCirclesColliding(circle1, circle2);
        expect(result).toBe(true);
    });
});

describe("areRectsColliding", () => {
    const rect1 = {
        x: 0,
        y: 0,
        height: 10,
        width: 10
    };

    test("should return true if the rectangles are colliding", () => {
        const rect2 = {
            x: 5,
            y: 5,
            height: 10,
            width: 10
        };
        const result = areRectsColliding(rect1, rect2);
        expect(result).toBe(true);
    });

    test("should return false if the rectangles are not colliding", () => {
        const rect2 = {
            x: 20,
            y: 5,
            height: 10,
            width: 10
        };
        const result = areRectsColliding(rect1, rect2);
        expect(result).toBe(false);
    });
});

describe("areRectAndCircleColliding", () => {
    it("should return true if the circle and rectangle are colliding", () => {
        const rect = { x: 0, y: 0, width: 10, height: 10 };
        const circle = { x: 5, y: 5, radius: 5 };
        expect(areRectAndCircleColliding(rect, circle)).toBe(true);
    });

    it("should return false if the circle and rectangle are not colliding", () => {
        const rect = { x: 0, y: 0, width: 10, height: 10 };
        const circle = { x: 20, y: 20, radius: 5 };
        expect(areRectAndCircleColliding(rect, circle)).toBe(false);
    });

    it("should return true if the circle is completely within the rectangle", () => {
        const rect = { x: 0, y: 0, width: 10, height: 10 };
        const circle = { x: 5, y: 5, radius: 2 };
        expect(areRectAndCircleColliding(rect, circle)).toBe(true);
    });

    it("should return true if the circle and rectangle touch but do not overlap", () => {
        const rect = { x: 0, y: 0, width: 10, height: 10 };
        const circle = { x: 10, y: 10, radius: 1 };
        expect(areRectAndCircleColliding(rect, circle)).toBe(true);
    });
});
