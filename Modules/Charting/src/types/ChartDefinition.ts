import { ComponentProps } from "react";

import type { GetFormattedValueOptions } from "Utilities/index";
import type { ColumnDto, FormatterType } from "Contracts/index";

import type {
    ChartSeriesType,
    ChartSeriesTypes,
    ReferenceLine,
    LabelProps,
} from "../RechartsComponents";

export type ChartDefinitionGradient = {
    gradientType?: "minmax" | "linear";
    gradientColorMin?: string;
    gradientColorMax?: string;
    gradientOpacity?: number;
    gradientOpacityMax?: number;
    gradientOpacityMin?: number;
    gradientOffsetMax?: number;
    gradientOffsetMin?: number;
};

export type ChartDefinition<T extends ChartSeriesType = ChartSeriesType> = {
    chartSeriesType: T;
    column: ColumnDto;
    id?: string;
    props: {
        dataKey: string;
        stackId?: string;
    } & ComponentProps<(typeof ChartSeriesTypes)[T]> &
        ChartDefinitionGradient;
};

export type ReferenceLineDefinition = {
    id?: string;
    data: any[];
    props: Omit<ComponentProps<typeof ReferenceLine>, "label"> & {
        label: Omit<LabelProps, "value"> & {
            value?: string;
        };
    };
};

export type PriceMarkerDefinition = {
    id: string;
    data: any;
    column: ColumnDto;
    props: {
        dataKey?: string;
        dataKeyFallback?: string;

        borderColor?: string;
        borderRadius?: number;
        borderWidth?: number;
        fontFamily?: string;
        fontSize?: string;
        marginLeft?: number;
        negativeColor?: string;
        neutralColor?: string;
        padding?: number;
        positionOrder?: number;
        positionPriority?: number;
        positiveColor?: string;
        textColor?: string;
    } & GetFormattedValueOptions;
};
