import { TooltipPayload } from "./TooltipPayload";

export type SeriesShapeProps<D = {}> = {
    fill: string;
    stroke: string;
    x: number;
    y: number;
    width: number;
    height: number;
    value: [number, number];
    payload: D;
    name: string;
    background: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    tooltipPayload: TooltipPayload<D>[];
    tooltipPosition: {
        x: number;
        y: number;
    };
} & D;
