import styled from "styled-components";

export const ChartContainer = styled.div`
    &&&&& {
        font-size: clamp(var(--font__size--x-small), 75%, var(--font__size--base));
        color: var(--color--tertiary);
        cursor: inherit !important;

        .recharts-cartesian-axis-tick-value {
            fill: var(--charts__axis__color);
        }

        .recharts-cartesian-axis-tick-line,
        .recharts-cartesian-axis-line {
            stroke: var(--border__color--secondary);
        }

        .recharts-cartesian-axis.hide-line > .recharts-cartesian-axis-line {
            opacity: 0;
        }

        &.selectable {
            .recharts-layer.recharts-bar,
            .recharts-layer.recharts-area {
                cursor: pointer;
                pointer-events: bounding-box;
            }
        }

        .recharts-default-tooltip {
            padding: var(--spacing--small) var(--spacing--medium);
            box-shadow: 8px 0 20px 5px #00000063;
            border: 0;
            border-radius: var(--border__radius--small);
            color: var(--color--overlay);
            background-color: var(--color__background--overlay);
        }

        .anchor-middle {
            text-anchor: middle;
        }
    }
`;
