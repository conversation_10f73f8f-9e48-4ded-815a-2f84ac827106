import React, { ComponentType } from "react";

import { HOC } from "PrimitiveComponents/index";

import type { ChartProps } from "../Chart";
import { Brush, type BrushProps } from "../Components/Brush";

type WithBrushProps = {
    BrushComponent?: typeof Brush;
    brushStartIndex?: BrushProps["startIndex"];
    brushEndIndex?: BrushProps["endIndex"];
    brushOnChange?: BrushProps["onChange"];
    brushOnChartDataChange?: BrushProps["onChartDataChange"];
    schemaProps?: BrushProps["schemaProps"];
};

export const withBrush = <P extends ChartProps>(Component: ComponentType<P & WithBrushProps>) => {
    const WrappedComponent = ({
        children,
        data,
        schemaProps,
        width,

        BrushComponent = Brush,
        brushStartIndex,
        brushEndIndex,
        brushOnChange,
        brushOnChartDataChange,

        ...props
    }: P & WithBrushProps) => (
        <Component
            {...(props as P)}
            data={data}
            schemaProps={schemaProps}
            width={width}
        >
            {schemaProps?.allProps?.chartBrushPropsShow &&
                BrushComponent({
                    data,
                    schemaProps,
                    chartWidth: width,
                    startIndex: brushStartIndex,
                    endIndex: brushEndIndex,
                    onChange: brushOnChange,
                    onChartDataChange: brushOnChartDataChange,
                })}
            {children}
        </Component>
    );

    return HOC("withBrush", WrappedComponent, Component);
};
