import React, { ComponentType, useMemo } from "react";
import moment from "moment";

import { HOC } from "PrimitiveComponents/index";

import type { ChartProps } from "../Chart";

import type { ChartSchemaProps } from "./withChartSchemaProps";

const getDateInterval = (
    minTickWidth: number,
    seriesIntervalWidth: number,
    hasDate: boolean
): [number, moment.unitOfTime.DurationConstructor] => {
    const interval = Math.ceil(minTickWidth / seriesIntervalWidth);

    if (hasDate && interval > 2) {
        return [Math.ceil(interval / 4), "M"];
    }

    return [interval, hasDate ? "w" : "M"];
};

type Props = {
    schemaProps?: ChartSchemaProps;
};

/**
 * Control the axis ticks when date formatted.
 * @description
 * The date axis intervals are based on:
 *   * Chart width
 *   * Series length
 *   * Date format - with or without the date component (D MMM YY vs MMM YYY)
 *   * YAxis height (inline vs rotated)

 * The calculation attempts to show the interval in the following order:
 *   * With date (D) interval:
 *     * 1 week
 *     * 2 weeks
 *     * 1 month
 *     * x months
 *   * Without date (D) interval:
 *     * x months
 */
export const withDateAxisTicks = <P extends ChartProps>(Component: ComponentType<P & Props>) => {
    const WrappedComponent = ({ schemaProps, ...props }: P & Props) => {
        const { data, width } = props;

        const yAxisDateProps = useMemo(() => {
            const {
                formatterType,
                ticksMode = "derived",
                valueFormat = "D MMM YY",
                height = 60
            } = schemaProps?.chartXAxisProps?.props ?? {};
            if (
                data?.length > 0 &&
                schemaProps?.chartXAxisProps &&
                formatterType === "date" &&
                ticksMode === "derived"
            ) {
                const first = data[0]?.[schemaProps.chartXAxisProps.column.name];
                const firstDate = first && moment(first);
                const last = data[data.length - 1]?.[schemaProps.chartXAxisProps.column.name];
                const lastDate = last && moment(last);
                const hasDate = (valueFormat?.toString() as string)
                    ?.trim()
                    ?.toUpperCase()
                    ?.startsWith("D");

                const minTickWidth = height >= 60 ? 30 : 72;
                const seriesLength = lastDate?.diff(firstDate, hasDate ? "w" : "M");
                const seriesIntervalWidth = data.length && width / seriesLength;

                const dateInterval = getDateInterval(minTickWidth, seriesIntervalWidth, hasDate);

                const allTicks = data.reduce<{ ticks: string[]; date: moment.Moment }>(
                    (acc, row) => {
                        if (row[schemaProps.chartXAxisProps.column.name]) {
                            const date = new Date(row[schemaProps.chartXAxisProps.column.name]);

                            if (date.valueOf() >= acc.date.valueOf()) {
                                const nextDate = moment(date);
                                nextDate.add(...dateInterval);

                                return {
                                    ticks: [...acc.ticks, date.toISOString().substring(0, 10)],
                                    date: nextDate
                                };
                            }
                        }
                        return acc;
                    },
                    { ticks: [], date: firstDate }
                );

                return {
                    interval: 0,
                    ticks: allTicks.ticks,
                    // @TODO: should this be fixed? it also need to ensure the is enought left/right margin for labels
                    height,
                    valueFormat
                };
            }

            return undefined;
        }, [data, schemaProps.chartXAxisProps, width]);

        const decoratedSchemaProps: ChartSchemaProps = useMemo(
            () =>
                yAxisDateProps && {
                    ...schemaProps,
                    chartXAxisProps: {
                        ...schemaProps?.chartXAxisProps,
                        props: {
                            ...schemaProps?.chartXAxisProps?.props,
                            ...yAxisDateProps
                        }
                    }
                },
            [schemaProps, yAxisDateProps]
        );

        return (
            <Component
                {...(props as P)}
                schemaProps={decoratedSchemaProps || schemaProps}
            />
        );
    };

    return HOC("withDateAxisTicks", WrappedComponent, Component);
};
