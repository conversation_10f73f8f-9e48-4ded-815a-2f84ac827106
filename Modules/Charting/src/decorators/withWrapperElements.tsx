import React, { ComponentType } from "react";
import styled from "styled-components";
import classNames from "classnames";

import { HOC, LayoutContainer, NoData } from "PrimitiveComponents/index";

const Container = styled.section`
    &&&&&& {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        position: relative;

        &.no-data {
            filter: grayscale(1);
        }

        > header {
            &:not(:empty) {
                margin-bottom: var(--spacing--medium);
            }
        }

        > footer {
            &:not(:empty) {
                margin-top: var(--spacing--medium);
            }
        }
    }
`;

const NoChartData = styled(NoData)`
    position: absolute;
    inset: 0;
`;

type Props<D> = {
    data?: D[];
};
type WrapperProps = {
    noDataLabel?: string;
};

export const withWrapperElements = <P extends Props<D>, D extends {} = {}>(
    Component: ComponentType<P>
) => {
    const WrappedComponent = ({ data, noDataLabel, ...props }: P & WrapperProps) => (
        <Container className={classNames({ "no-data": !data?.length })}>
            <LayoutContainer>
                <Component
                    {...(props as P)}
                    data={data}
                />
                {!data?.length && <NoChartData noDataLabel={noDataLabel} />}
            </LayoutContainer>
        </Container>
    );

    return HOC("withWrapperElements", WrappedComponent, Component);
};
