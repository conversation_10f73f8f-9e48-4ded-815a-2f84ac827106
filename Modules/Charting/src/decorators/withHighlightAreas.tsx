import React, { ComponentType, type ReactNode } from "react";
import { Customized, Rectangle } from "recharts";

import { HOC, Typography, Flex } from "PrimitiveComponents/index";
import { getPrefixedProps } from "Utilities/index";

import type { ChartSchemaProps } from "./withChartSchemaProps";
import { getTextMeasures } from "../utils";

const HighlightAreas = props => {
    const { data, offset, orderedTooltipTicks, xKey, keys: keysString } = props;

    if (!data || !keysString) return null;

    const keys = keysString.split(",").map(k => k.trim());

    const keyProps = keys.reduce(
        (acc, k) => ({
            ...acc,
            [k]: getPrefixedProps(`${k}Props`, props, data),
        }),
        {},
    );

    const highlightAreaRow = keys.flatMap(key => {
        let inProgress = false;
        const areas = [];

        data.forEach((datum, index) => {
            if (datum[key]) {
                if (!inProgress) {
                    areas.push({ key, ...keyProps[key], data: [] });
                }
                areas[areas.length - 1].data.push({
                    ...datum,
                    originalIndex: index,
                });
                inProgress = true;
            } else {
                inProgress = false;
            }
        });

        return areas;
    });

    const highlightArea = highlightAreaRow.map(el => {
        const first = el.data[0];
        const last = el.data[el.data.length - 1];
        const xFirstValue = first[xKey];
        const xLastValue = last[xKey];
        const xFirstIndex = first.originalIndex;
        const xLastIndex = last.originalIndex;
        const xFirst = orderedTooltipTicks[xFirstIndex].coordinate;
        const xLast = orderedTooltipTicks[xLastIndex].coordinate;
        const width = xLast - xFirst;
        const labelWidth = getTextMeasures(el.label, "10px").width;
        return { ...el, xFirstValue, xLastValue, xFirst, xLast, width, labelWidth };
    });

    return (
        <>
            {Object.entries(keyProps).map(([key, { color }]: [string, { color: string }]) => (
                <defs key={key}>
                    <linearGradient
                        id={key}
                        x1="0%"
                        y1="0%"
                        x2="0%"
                        y2="100%"
                    >
                        <stop
                            stopColor={color}
                            offset="0%"
                            stopOpacity="0.3"
                        />
                        <stop
                            stopColor={color}
                            offset="100%"
                            stopOpacity="0.01"
                        />
                    </linearGradient>
                </defs>
            ))}

            {highlightArea.map(({ key, width, xFirst, label, labelWidth }, index) => {
                const rectKey = `${key}-${index}`;
                const x = xFirst;
                const y = offset.top;
                return (
                    <g key={rectKey}>
                        <Rectangle
                            x={x}
                            y={y}
                            height={offset.height}
                            width={width}
                            fill={`url(#${key})`}
                        />

                        {labelWidth < width && (
                            <foreignObject
                                x={x}
                                y={y}
                                width={width}
                                height={15}
                            >
                                <Flex justify="center">
                                    <Typography.Text
                                        align="center"
                                        ellipsis
                                        style={{ fontSize: 10 }}
                                    >
                                        {label}
                                    </Typography.Text>
                                </Flex>
                            </foreignObject>
                        )}
                    </g>
                );
            })}
        </>
    );
};

export const withHighlightAreas = <
    P extends {
        schemaProps?: ChartSchemaProps;
        children?: ReactNode;
    },
>(
    Component: ComponentType<P>,
) => {
    const WrappedComponent = ({ children, ...rest }: P) => {
        const { schemaProps } = rest;
        const { chartHighlightAreasProps } = schemaProps;
        return (
            <Component {...(rest as P)}>
                {children}

                <Customized
                    component={HighlightAreas}
                    {...chartHighlightAreasProps}
                />
            </Component>
        );
    };

    return HOC("withHighlightAreas", WrappedComponent, Component);
};
