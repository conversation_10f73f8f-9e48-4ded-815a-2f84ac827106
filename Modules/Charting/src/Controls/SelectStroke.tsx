import React from "react";
import styled from "styled-components";
import classnames from "classnames";

import { type ControlProps, with<PERSON><PERSON><PERSON>, withPop<PERSON>, Flex } from "PrimitiveComponents/index";
import { toArray } from "Utilities/index";

import type { StrokeType } from "../types";
import { Stroke } from "../Components";

import { SelectSeriesColor } from "./SelectSeriesColor";
import { SelectSeriesDashes } from "./SelectSeriesDashes";
import { SelectSeriesWidth } from "./SelectSeriesWidth";

import { type ChartSeriesType } from "../RechartsComponents";

type SelectStrokeProps = ControlProps<
    StrokeType,
    {
        chartType: ChartSeriesType;
    }
>;

const Container = withPopover(styled(Flex)`
    padding: var(--input__padding, 2px 6px);

    .stroke__color:not(.stroke__color--auto) {
        border-radius: 1em;
        height: 1em;
        width: 1em;
        flex-shrink: 0;
    }
`);

export const SelectStroke = withLabel(
    ({ chartType, disabled, onBlur, onChange, readOnly, required, value }: SelectStrokeProps) => (
        <Container
            alignItems="center"
            direction="row"
            gap="medium"
            popoverTitle="Stroke"
            popoverContent={
                <Flex
                    direction="column"
                    gap="large"
                >
                    <SelectSeriesColor
                        chartType={chartType}
                        value={value?.color}
                        onChange={color => onChange?.({ ...value, color: toArray(color)?.[0] })}
                        onBlur={() => onBlur?.(value)}
                        disabled={disabled}
                        readOnly={readOnly}
                        required={required}
                    />
                    <SelectSeriesDashes
                        value={value?.dash}
                        onChange={dash => onChange?.({ ...value, dash: toArray(dash)?.[0] })}
                        onBlur={() => onBlur?.(value)}
                        disabled={disabled}
                        readOnly={readOnly}
                        required={required}
                        color={value?.color}
                    />
                    <SelectSeriesWidth
                        value={value?.width}
                        onChange={width => onChange?.({ ...value, width: toArray(width)?.[0] })}
                        onBlur={() => onBlur?.(value)}
                        disabled={disabled}
                        readOnly={readOnly}
                        required={required}
                        color={value?.color}
                        dash={value?.dash}
                    />
                </Flex>
            }
        >
            <Flex alignItems="center">
                <div
                    className={classnames("stroke__color", {
                        "stroke__color--auto": value?.color === "auto"
                    })}
                    style={
                        value?.color !== "auto"
                            ? { backgroundColor: `var(--${value?.color})` }
                            : undefined
                    }
                />
                {value?.color === "auto" ? "Auto" : <>&nbsp;</>}
            </Flex>

            <Flex>
                <Stroke stroke={value} />
            </Flex>
        </Container>
    )
);
