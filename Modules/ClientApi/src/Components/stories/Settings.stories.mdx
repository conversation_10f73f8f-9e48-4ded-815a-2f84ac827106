import { Meta, Story, Canvas, ArgTypes, Description } from "@storybook/addon-docs";

import { LayoutHorizontal, LayoutContainer } from "PrimitiveComponents/index";
import { BaseStyles } from "Theming/index";
import { GlobalFixtures } from "@mocks/api-gateway/src/mocks";

import { ClientApiProvider } from "../../ApiProvider";

import { Settings } from "../Settings";

<Meta
    title="ClientApi/Settings"
    component={Settings}
/>

export const Template = args => (
    <LayoutHorizontal
        columns={2}
        gap="xx-large"
    >
        <BaseStyles
            theme="dashboard-dark"
            global={false}
        >
            <GlobalFixtures>
                <ClientApiProvider>
                    <LayoutContainer
                        background="overlay-content"
                        margin="large"
                        minWidth="74em"
                        minHeight="90vh"
                        width="fit-content"
                        flex
                        style={{ flexGrow: 1 }}
                    >
                        <Settings v2 {...args} />
                    </LayoutContainer>
                </ClientApiProvider>
            </GlobalFixtures>
        </BaseStyles>
    </LayoutHorizontal>
);

# Settings

<Description of={Settings} />

<Canvas>
    <Story
        name="Settings"
        parameters={{}}
        args={{}}
    >
        {Template.bind({})}
    </Story>
</Canvas>

<ArgTypes
    of={Settings}
    sort="requiredFirst"
/>
