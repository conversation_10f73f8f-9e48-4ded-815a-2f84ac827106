#!/usr/bin/env node
/* eslint-disable no-undef */
/* eslint-disable no-cond-assign */
/* eslint-disable prefer-regex-literals */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable no-unreachable-loop */
/* eslint-disable consistent-return */
/* eslint-disable no-plusplus */

/**
 * @see https://github.com/touk/federated-types
 */
const path = require("path");
const fs = require("fs");
const findNodeModules = require("find-node-modules");
const ts = require("typescript");

const formatHost = {
    getCurrentDirectory: ts.sys.getCurrentDirectory,
    getNewLine: () => ts.sys.newLine
};

function reportDiagnostic(diagnostic) {
    console.warn(
        `[WARN] TS-${diagnostic.code}: ${
            diagnostic.file?.fileName || "unknown file"
        } - ${ts.flattenDiagnosticMessageText(diagnostic.messageText, formatHost.getNewLine())}`
    );
}

const [nodeModules] = findNodeModules({ cwd: process.argv[1], relative: false });

const getArg = argName => {
    const argIndex = process.argv.indexOf(argName);
    return argIndex !== -1 ? process.argv[argIndex + 1] : null;
};

const outDirArg = getArg("--outputDir");

const outputDir = outDirArg
    ? path.resolve("./", outDirArg)
    : path.resolve(nodeModules, "@types/__federated_types/");

const configPathArg = getArg("--config");
const configPath = configPathArg ? path.resolve(configPathArg) : null;

const findFederationConfig = base => {
    const files = fs.readdirSync(base);
    const queue = [];

    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const newBase = path.join(base, file);
        if (file === "federation.config.json") {
            return path.resolve("./", newBase);
        }
        if (fs.statSync(newBase).isDirectory() && !newBase.includes("node_modules")) {
            queue.push(newBase);
        }
    }

    for (let i = 0; i < queue.length; i++) {
        return findFederationConfig(queue[i]);
    }
};

if (configPath && !fs.existsSync(configPath)) {
    throw new TypeError(`Unable to find a provided config: ${configPath}`);
}

const federationConfigPath = configPath || findFederationConfig("./");

if (federationConfigPath === undefined) {
    throw new TypeError("[ERROR] Unable to find a federation.config.json file in this package");
}

console.log(`Using config file: ${federationConfigPath}`);

const federationConfig = require(federationConfigPath);
const compileFiles = Object.values(federationConfig.exposes);
const compileKeys = Object.keys(federationConfig.exposes);
const outFile = path.resolve(outputDir, `${federationConfig.name}.d.ts`);

function getModuleDeclareName(exposeName) {
    // windows paths 🤦
    return path.join(federationConfig.name, exposeName).replace(/[\\/]/g, "/");
}

try {
    if (fs.existsSync(outFile)) {
        fs.unlinkSync(outFile);
    }

    // write the typings file
    const program = ts.createProgram(compileFiles, {
        outFile,
        declaration: true,
        emitDeclarationOnly: true,
        skipLibCheck: true,
        jsx: "react",
        esModuleInterop: true
    });

    const { emitSkipped, diagnostics } = program.emit();

    diagnostics.forEach(reportDiagnostic);

    if (emitSkipped) {
        throw new TypeError("No types emitted");
    }

    let typing = fs.readFileSync(outFile, { encoding: "utf8", flag: "r" });

    const moduleRegex = RegExp(/declare module "(.*)"/, "g");
    const moduleNames = [];

    while ((execResults = moduleRegex.exec(typing)) !== null) {
        moduleNames.push(execResults[1]);
    }

    moduleNames.forEach(name => {
        // exposeName - relative name of exposed component (if not found - just take moduleName)
        const [exposeName = name, ...aliases] = compileKeys.filter(key =>
            federationConfig.exposes[key].replace(/\.(ts|js)x?$/, "").endsWith(name)
        );
        // CHANGE: Add a check for the module without '/index' so that it will correctly replace imported types
        const regexWithoutIndex = RegExp(`"${name.replace(/\/index$/, "")}"`, "g");

        const moduleDeclareName = getModuleDeclareName(exposeName);

        // language=TypeScript
        const createAliasModule = name => `
             declare module "${getModuleDeclareName(name)}" {
                 export * from "${moduleDeclareName}"
             }
         `;

        const regexModule = RegExp(`module "${name}"`, "g");
        const regexExport = RegExp(`export (.*?) from "${name}"`, "g");
        const regexModuleImport = RegExp(`import (.*?) from "${name}"`, "g");

        typing = [
            typing
                .replace(regexModule, `module "${moduleDeclareName}"`)
                .replace(regexExport, `export $1 from "${moduleDeclareName}"`)
                .replace(regexModuleImport, (importClause, i) => {
                    // @TODO: review this properly when we have time!
                    // Components modules clashes with internal exports to Components/index :(
                    // This check is here to attempt to ensure imported modules from Components module are retained and correct types are exported from the fed module typings
                    if (name === "Components/index") {
                        return importClause;
                    }

                    return `import ${i} from "${moduleDeclareName}"`;
                })
                // CHANGE: change import
                .replace(regexWithoutIndex, `"${moduleDeclareName}"`),
            ...aliases.map(createAliasModule)
        ].join("\n");
    });

    console.log("writing typing file:", outFile);

    fs.writeFileSync(outFile, typing);

    // if we are writing to the node_modules/@types directory, add a package.json file
    if (outputDir.includes(path.join("node_modules", "@types"))) {
        const packageJsonPath = path.resolve(outputDir, "package.json");

        if (!fs.existsSync(packageJsonPath)) {
            console.debug("writing package.json:", packageJsonPath);
            fs.copyFileSync(path.resolve(__dirname, "typings.package.tmpl.json"), packageJsonPath);
        } else {
            console.debug(packageJsonPath, "already exists");
        }
    } else {
        console.debug("not writing to node modules, dont need a package.json");
    }

    // write/update the index.d.ts file
    const indexPath = path.resolve(outputDir, "index.d.ts");
    const importStatement = `export * from './${federationConfig.name}';`;

    if (!fs.existsSync(indexPath)) {
        console.log("creating index.d.ts file");
        fs.writeFileSync(indexPath, `${importStatement}\n`);
    } else {
        console.log("updating index.d.ts file");
        const contents = fs.readFileSync(indexPath);
        if (!contents.includes(importStatement)) {
            fs.writeFileSync(indexPath, `${contents}${importStatement}\n`);
        }
    }

    console.debug("Success!");
} catch (e) {
    console.error(`[ERROR]`, e.message || e);
    process.exit(1);
}
