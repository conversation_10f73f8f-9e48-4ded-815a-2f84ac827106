param (
    [string] $serverUrl,
    [string] $branch,
    [string] $tcUserId,
    [string] $tcPassword
)

$ErrorActionPreference = "Stop"

$rootDir = (Resolve-Path -Path ".//").Path;

$componentPrefix = "Component/"
$modulePrefix = "Modules/"

$modifiedModules = [Array]@()
$modifiedComponents = [Array]@()
$proxyChanged = $false

$cachedPackageNames = @{}

$authToken=[Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("${tcUserId}:${tcPassword}"))

function Get-PackageName-From-ChangedFile {
    param (
        [string]$changedFile,
        [string]$prefix
    )

    $afterPrefix = ($changedFile -split $prefix)[1]
    $indexOfSlash = $afterPrefix.IndexOf("/")

    $moduleOrComponent = $prefix + $afterPrefix.Substring(0, $indexOfSlash)
    
    if ($cachedPackageNames[$moduleOrComponent]) {
        ## return false because we've already flagged this package as modified
        return $false
    }

    try {
        $packagePath = $rootDir + $moduleOrComponent + "/package.json"
        $packageName = Get-Content $packagePath -Raw | ConvertFrom-Json | Select-Object -ExpandProperty name
        $cachedPackageNames.Add($moduleOrComponent, $packageName)
    
        return $packageName
    }
    catch {
        return $rootDir + $moduleOrComponent
    }
}

function CheckAndExitOnError {
    param(
        [string]$errorMsg
    )

    if ($LASTEXITCODE -ne 0) {
        "##teamcity[buildStatus status='FAILURE' text='$errorMsg']"

        Write-Error $errorMsg
        [System.Environment]::Exit($LASTEXITCODE)
    }
}

function GetFileChanges {
    $getChangesUrl = "${serverUrl}/httpAuth/app/rest/builds?locator=branch:(${branch}),buildType:(id:Firefly_JsClient_BuildAllJsPackages),count:-1&fields=build(id,changes(change(files(file(file)))))"

    $headers = @{
        "Authorization" = "Basic $authToken"
        "Accept" = "application/json"
    }

    Write-Host "Finding branch file changes..."
    
    try {
        $response = Invoke-RestMethod -Uri $getChangesUrl -Headers $headers
    	return $response.build | ForEach-Object { $_.changes.change.files.file.file }
    }
	catch {
    	CheckAndExitOnError -errorMsg "Failed to get file changes"
    }
}

$fileChanges = GetFileChanges

if ($fileChanges.Length -eq 0) {
    $LASTEXITCODE = 1
    CheckAndExitOnError -errorMsg "0 changed files found, re run build"
}

Write-Host "Found $($fileChanges.Length) changed files."

$fileChanges `
    | Where-Object { ![string]::IsNullOrEmpty($_) }`
    | ForEach-Object {
        $changedFile = $_.ToString()

        ## module file change
        if ($changedFile.Contains($modulePrefix)) {
            ## ignore root Modules/ file change e.g Modules/package.json
            if (($changedFile -split "/").Length -eq 2) {
                return
            }

            $module = Get-PackageName-From-ChangedFile $changedFile $modulePrefix
            if (!$module) {
                return
            }
            $modifiedModules += $module
            return
        }

        ## any other file change means we need to build everything for raidproxy
        $proxyChanged = $true

        if ($changedFile.Contains($componentPrefix)){
            $component = Get-PackageName-From-ChangedFile $changedFile $componentPrefix
            if (!$component) {
                return
            }
            $modifiedComponents += $component
        }
    }

if ($modifiedModules.Count -eq 0) {
    Write-Host "No module changes found. Skipping modules."
} else {
    Write-Host "Found $($modifiedModules.Count) module(s) which have been modified:"
    Write-Host $modifiedModules
    
    Write-Host "Building libraries..."
    yarn build-libraries
    CheckAndExitOnError -errorMsg "Failed to compile TS in libraries"
    
    Write-Host "Building modified modules..."

    $workspaces = ($modifiedModules | ForEach-Object { "--include '$_'" }) -join " "
    Invoke-Expression "yarn workspaces foreach $workspaces --parallel --topological --interlaced run build"
    CheckAndExitOnError -errorMsg "Failed to build modules"
}

if (!$proxyChanged) {
    Write-Host "No proxy changes found. Skipping components and proxy build"
} else {
    Write-Host "Building Components and RaidProxy..."
    yarn check:all
    CheckAndExitOnError -errorMsg "Failed to build JS.Clients"
}

Write-Host "Successfully built JS Client changes."
Write-Host "Done."