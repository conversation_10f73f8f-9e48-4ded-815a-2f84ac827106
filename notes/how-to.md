# accounts

- remote: `W10HSV6SK3`
- pw: `C@rbon876`
- email: `<EMAIL>`
- chatgpt
  - user: `<EMAIL>`
  - pw: `Doozy2-Carpentry4-Maroon7-Confusing7-Underuse9-Outmatch1`


# code

## style
- tailwind alike [link](/GlobalStyles.ts:40) 

## schema form
- schema form component [link](Modules/Schema/src/Components/SchemaForm/SchemaForm.tsx:193)
- schema layout mapping [link](Modules/Schema/src/Components/SchemaForm/Decorators/withLayout.tsx:55) 
- schema parsing (resolving) [link](Modules/Schema/src/Components/SchemaForm/Decorators/withResolvedSchema.tsx:25) 

## summary chart
- firefly-shared-controls base component [link](legacy/Library/firefly-shared-controls/src/Common/Components/SummaryChart/SummaryChart.tsx:415)   
- ComponentLibrary wrapper [link](legacy/Modules/ComponentLibrary/src/Components/SummaryChart/SummaryChart.tsx:76)
- ViewServer connected [link](legacy/Modules/ComponentLibrary/src/Components/SummaryChart/container/CompanyPageSummaryChartContainer.ts:54)
- Gateway connected [link](legacy/Modules/ComponentLibrary/src/Components/SummaryChart/container/CompanyPageSummaryChartGatewayContainer.tsx:12)

## fetch
- via hook view server
  - search for useInvokeViewserverCommand
- via hook api gateway
  ```tsx
  import { useInvokeApiGatewayCommand } from "TransportGateway/index";
  
  const { invoke } = useInvokeApiGatewayCommand();
  
  const getInstrumentId = useCallback(async () => {
      const res = await invoke("reference-data.ticker-instrumentid", {
          bbgTicker,
      });
      return res.data?.[0]?.instrumentId;
  }, [bbgTicker, invoke]);
  ```
- with command as parameter  
  ```tsx
  withCommandStateFactory(
      RedFlagsStampViewServerBase,
      ({ isIndex }: RedFlagsStampViewServerProps) =>
          createConfig({
              props: reportSubscriptionCommandSpec(
                  isIndex ? INDEX_REPORT : EQUITY_REPORT,
                  ({ bbgTicker }) => (bbgTicker ? { bbgTicker } : undefined),
                  (data, props, schema) => ({ data, schema }),
                  "ProjectionNode"
              )
          })
  )
  ```
  
- from handler
```ts
export const AxiomaRiskFactorChartsViewServer = withCommandState(
    AxiomaRiskFactorChartsViewServerInner,
    createConfig({
        relativeTicker: requestResponseCommandSpec(
            "RaidData_GetFundsRequest_GetFundsResponse_RequestResponse_Handler",
            ({ instrumentId }: { instrumentId: string }) => ({}),
            data => {
                return {
                    test: data,
                };
            },
        ),
    }),
);
```
  
## viewserver report template

### in a template
```tsx
<ReportTemplate
  reportKey="AC_FundamentalValuation"
  templateName="EV / Sales"
  parameterValues={{
    BBGTicker: identifier,
    DateFrame: parameterValues.Months,
  }}
/>
```

### in the repo
```tsx
<ReportEngineAppStandalone
    reportKey="DSP_ShortInterest"
    templateName="Short Interest"
    config={{
        ParameterValues: {
            BBGTicker: bbgTicker,
            DateFrame: dateFrame
        }
    }}
    maxItems={10000}
/>
  ```

## order-form
- positioning and dimensions [link](legacy/Component/OrderForm/src/model.ts:33) 
- which component to render in a panel [link](legacy/Component/OrderForm/src/components/TicketCanvas/TicketCanvas.tsx:43) 
  
## raid proxy routes
[link](legacy/Proxy/RaidProxy/src/Root/Routes.tsx) 

# components

## RiskBudgetTableViewServer
![](.how-to_images/d8cf81d3.png)

## ChangedValueBar
![](.how-to_images/6fcaf2e3.png)

# docs
- lodash [link](https://lodash.com/docs/4.17.15) 
- svg 
  - tools
    - svg to react: [link](https://www.svgviewer.dev/svg-to-react-jsx)
    - svg editor: [link](https://editor.method.ac/) 
    - optmise svg: [link](https://svgomg.net/)
  - icons
    - icomoon [link](https://icomoon.io/app/#/select) 
    - heroicons [link](https://heroicons.com/outline) 
    - lucide [link](https://lucide.dev/icons/) 

# open fin

## spin local version

### in one shell
```shell
cd Modules/LaunchPad
yarn start
```

### in another shell
```shell
cd Modules/LaunchPad
yarn start-openfin
```

## mock notifications

### programmatically
- use the webstorm shelf `--- HACK --- 1000 notifications`
- or check these files: 
  - packages\@mocks\api-gateway\src\services\notification\notification.ts
  - packages\@mocks\api-gateway\src\services\notification\notifications.ts

### create rules and trigger real notifications
<details open><summary>Image</summary>

![](./.how-to_images/e3709793.png)
</details>

### toast notifications
- use the webstorm shelf `--- HACK --- context menu with notification triggers`


# how to 

## RaidAll

### run locally
1. from the route of the repo
  ```zsh
  cd Web/Raid.Web
  dotnet run
  ```
2. visit [link](https://localhost:65001/) 

### run raid2 locally
1. Add script tag at `Web/Raid.Web/Views/Dashboard/Index.cshtml:46`
```html
<script src="/Content/Modules/Firefly.JS.Modules.RaidReactContainer/raid.js"></script>
  ```
2. in `ermes-trading-labs` run
```bash
bun hack-raid1.ts
```
3. check to which js-client is pointing [link](hack-raid1.ts:5)
4. recompile raid2 modules after changes. e.g:
```bash
yarn workspace @tradinglabs/react-raid-container build
```

### ARCHIVED: run chrome with CORS disabled 
- run `cd "C:\Program Files\Google\Chrome\Application\"`
- run `./chrome.exe --disable-web-security --user-data-dir="C:\chrome-no-cors-session"`
- replace `@jsModulePath` with `http://localhost:8080/`
- <script src="/Content/JavaScript/Bundles/raid.js"></script>


https://localhost:65001/static/Modules/Firefly.JS.Modules.RaidReactContainer/antd-967e27faa32dd3f9d29e.js
https://stage.raidllp.com/static/Modules/Firefly.JS.Modules.RaidReactContainer/antd-967e27faa32dd3f9d29e.js


## MyGet
Not used anymore, now using GitHub packages: https://fireflylabs.slack.com/archives/C0141UV9WCF/p1722588907787569

## Permission issues
permission issues with NODE_EXTRA_CA_CERTS: https://fireflylabs.slack.com/archives/C0141UV9WCF/p1722589938855869

## yarn link
```zsh
YARN_IGNORE_PATH=1 yarn link

yarn link "@tradinglabs/component-library"                                                      
yarn link "@tradinglabs/utilities"
yarn link "@tradinglabs/theming"
yarn link "@tradinglabs/connected-components"
yarn link "@tradinglabs/primitive-components"
yarn link "@tradinglabs/theming"
yarn link "@tradinglabs/utilities"
yarn link "@tradinglabs/contracts"
yarn link "@tradinglabs/components"
yarn link "@tradinglabs/client-api"
yarn link "@tradinglabs/layouts"
yarn link "@tradinglabs/raid"
yarn link "@tradinglabs/charting"
yarn link "@tradinglabs/grid"
yarn link "@tradinglabs/schema"
yarn link "@tradinglabs/notifications"
yarn link "@reddeer/firefly-shared-controls"
```

## grid
### format cell
- `properties: { displayType: "ChangeValue" }`
- declared: `search for "const DisplayType"`
- used: `search for "const schemaDisplayTypeFactory"`
### format row style
- style by row
  - enable it in the schema`properties: { rowClassRules: "group-border-top" }`
  - if styling is not already created, add it into: `Modules/PrimitiveComponents/src/Components/GridBase/styles/Grid.styled.ts:101`
  - add it via dataKey in the datum `group-border-top: true`
- style by column: `properties: { cellClass: "text-highlighted" }`
### sorting
  - `sort: "asc" | "desc"`
### display value with a little rect with gradient
```json5
{
    contentTypeForDisplay: "ValueWithScoreValueGradient",
    properties: {
        cellRendererPropsDataKey: "score",
        cellRendererPropsMin: -100,
        cellRendererPropsMax: 100,
    }
}
```
### display value with a little rect with gradient
```json5
{
    contentTypeForDisplay: "Notional",
    properties: {
        cellRenderer: "PrimitiveComponents.ProgressBar",
        cellRendererPropsMinLabel: "",
        cellRendererPropsMaxLabel: "#ref(score)",
        cellRendererPropsShowLabels: true,
    },
}
```
### display a component
```json5
{
    properties: {
        componentValue: "PrimitiveComponents.ProgressBar",
        componentPropsMinLabel: "#ref(verbalLevel)",
        componentPropsMaxLabel: "#ref(numericLevel)",
        componentPropsShowLabels: true,
        componentPropsValueLabel: "#ref(.)",
        componentPropsMinDescription: "Verbal Level",
        componentPropsMaxDescription: "Numeric Level",
    },
}
```

## chart
- legend:
  - add schema `chartLegendPropsShow: true`
  - to center add prop on component `labelAlign="center"`
- format tooltip:
  - `properties: { chartTooltipPropsFormatterType: "bigValue", chartTooltipPropsValueFormat: "$.1f" }`
    - chartTooltipPropsFormatterType: `bigValue` or `date` or `number`
      - if it is `bigValue`, then chartTooltipPropsValueFormat:
        - can be `$.0f` or `$.1f` or any based on d3 format: `https://github.com/d3/d3-format`
      - if it is `number`, then chartTooltipPropsValueFormat:
        - can be `number2dp` or `dollarMax2dp` or any in: `Modules/Utilities/src/formatNumber/numberFormats.ts:6`
    - chartTooltipPropsValueFormat for bubble chart it also set the color: `Modules/Charting/src/CustomCharts/BubbleSeries/BubbleSeriesTooltipContent.tsx:76`
- date format x axis: `chartXAxisPropsParseFormat: "YYYY-MM-DD-HH.mm.ss"`

## hacking stage/uat/prod
- `yarn start` from `legacy/Proxy/RaidProxy/`
- enable redirect of `RaidReactContainer` in Chrome extension as `8080` and whatever module needed

## open layouts in standalone mode
- append `?componentName=Layouts.Layout&layoutName=SectionAxiomaRisk`

## Raid Insights
<details open><summary>Image</summary>

![](./.how-to_images/img_1.png)
</details>


- to spin the page run `yarn workspace @tradinglabs/react-raid-container start-local --select-env`
- you probably need to compile `yarn workspace @reddeer/risk-utilisation-js-client build` to see the changes
- visit `http://localhost:8080/#/raid-insights/RiskUtilisation?lookback=ThreeMonthsRolling`

## Portfolio rules admin
<details open><summary>Image</summary>

![](./.how-to_images/img_2.png)
</details>

- to spin the page run `yarn workspace @reddeer/risk-utilisation-js-client start --select-env`

## mock notifications
- check the commented code in these files:
  - packages\@mocks\api-gateway\src\services\notification\notification.ts
  - packages\@mocks\api-gateway\src\services\notification\notifications.ts
- check stashed code `--- HACK --- notification mock`

## components config
- to add modal on click, check:
  - `Modules/Components/src/Components/Components/ComponentsConfig/CompanyPageSummaryChart.ts`
  - `Modules/Components/src/Components/Components/ComponentsConfig/TradeImpact.ts`

## run api-gateway locally
- BE
  - from visual studio
  - open repo `Projects\api-gateway-01`
  - edit `Firefly.Backbone.ApiGateway\appsettings.json`
    - replace with first lines with:
    ```
        "ConnectionString": "rabbitmq://raidstagemqlb",
        "Username": "apiuser",
        "Password": "apiuser",
        "Environment": "Dev",
        "Transport": "gRPC",
    ```
  - run api-gateway-01 repo from visual studio and click `Run` action in the toolbar
- FE
  - run `raid` FE and select local api-gateway

## routing
### api-gateway routing
`Firefly.Backbone.ApiGateway\Controllers\DashboardController.cs`
### client side routing
have a look at [link](https://github.com/firefly-trading-labs/Firefly.Backbone.JS.Client/blob/36fff58c34c14d66cf3b4820fc817b5110c5249c/Modules/Raid/src/Components/RaidNavigationProvider/RaidNavigationProvider.tsx#L114) 

## Json Schema

### Matt handover 17 Sep
- Raid2 > Admin/Library > Shows all the tokens that drives the token components (ATM Chart  and Grid)
- Raid2 > Admin/Custom Rules > Component Panel (at the bottom of the mid panel)

<details open><summary>Image</summary>

![](./.how-to_images/matt-handover-17-sep.png)
</details>

<details open><summary>Image</summary>

![](./.how-to_images/matt-handover-17-sep_1.png)
</details>

### SchemaForm code
- types [link](Modules/Schema/src/Components/SchemaForm/types.ts:286) 
- handle changes and keep the state of the form [link](Modules/PrimitiveComponents/src/Components/DraftEdit/hooks/useDraftEdit.ts:35)
- map to real components [link](Modules/Schema/src/Components/SchemaForm/ComponentFactory.ts:64)

#### examples
- increment decimal allowed in number inputs [link](Modules/Schema/src/Components/SchemaForm/types.ts:132) 

### grid code
- map collection to schema [link](Modules/ConnectedComponents/src/ApiGateway/Components/TokenGrid/utils.ts:75)
  - map display type to schema [link](Modules/ConnectedComponents/src/ApiGateway/Components/TokenGrid/decorators/withDisplayType.ts:54) 

## viewserver rust
<details open><summary>Image</summary>

![](.how-to_images/d23b284a.png)
</details>

# misc
- kill node tasks, from PowerShell: `taskkill /f /im node.exe`
- shadcn certificate error:
  - source: https://github.com/shadcn-ui/ui/issues/4867
  - from PowerShell: `$env:NODE_TLS_REJECT_UNAUTHORIZED=0`

# impersonate
`yarn workspace @tradinglabs/raid start --select-env --impersonate=350` 
## Ids
- `Ermes Colella` 485
- `Hilton Nathanson` 9
- `Daniel Taylor` 523
- `Oliver Nokes` 409
- `Gareth Wilshaw` 531
- `Lee Barnard` 566
- `Bem Meduoye` 352
- `Wayne Cavanagh` 425
- `Robert Loynes` 549
