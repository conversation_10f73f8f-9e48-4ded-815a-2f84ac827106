import { Store, createStore, applyMiddleware } from "redux";
import { connectionMiddleware } from "../middleware";
import { IRootState, rootReducer } from "../reducers";
import {IServerConnection} from "@tradinglabs/viewserver-core";

export function configureStore(connection: IServerConnection, initialState?: IRootState): Store<IRootState> {
  const middleware = applyMiddleware(connectionMiddleware(connection));
  connection.connect();

  const store = createStore(rootReducer as any, initialState as any, middleware) as Store<
  IRootState
  >;

  if (module.hot) {
    module.hot.accept("../reducers", () => {
      const nextReducer = require("../reducers");
      store.replaceReducer(nextReducer);
    });
  }

  return store;
}
