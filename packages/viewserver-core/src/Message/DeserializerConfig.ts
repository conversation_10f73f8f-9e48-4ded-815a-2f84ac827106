import { PagingOptions } from './PagingOptions';
import { ServerUri } from './ServerUri';
import { CommandHeaderDto } from './CommandHeaderDto';

export class DeserializerConfig {
    public Handler?: string;
    public CommandKey?: string;
    public PayLoad?: any;
    public Pagination?: PagingOptions;
    public Peer?: ServerUri;
    public ResubscribeTimespanSeconds?: number;
    public Headers?: CommandHeaderDto[];
    public ResetDataOnConnectionLost?: boolean;
    public ShouldKeepRetryingIfConnectionOrCommandFails?: boolean;
    public SessionCreationTimeoutSeconds?: number;
    public SwallowReconnectExceptions?: boolean;
}