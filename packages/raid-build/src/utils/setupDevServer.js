const { resolve } = require("path");
const { existsSync } = require("fs");

const { createModuleConfig } = require("../federated.dev.config");
const federatedConfig = require("../federated.config");

const {
    getProfile,
    selectProfile,
    getDevServerProxyConfig,
} = require("./config");
const { getToken } = require("./token");

const setupDevServer = async ({
    selectEnv = false,
    genToken = false,
    noToken = false,
    proxyFile = undefined,
    moduleConfigOptions = undefined,
    impersonateUserID = undefined,
}) => {
    try {
        const currentProfile = getProfile();
        const profile =
            selectEnv || !currentProfile
                ? await selectProfile()
                : currentProfile;

        if (!profile) {
            console.log("No profiles defined in 'config/env.profiles.js'");
            process.exit(1);
        }

        console.log(`#######################################################################"
Selected profile: ${profile.name}

    run 'yarn start --select-env' to choose another environment profile
    run 'yarn start --impersonate=<user id>' to generate a token to impersonate a user
#######################################################################`);

        const moduleDevConfig = await createModuleConfig(
            process.cwd(),
            moduleConfigOptions,
        );

        const [token, viewseverToken] = await Promise.all([
            getToken(
                "API_TOKEN",
                profile.env.ENDPOINT_API_TOKEN,
                genToken || currentProfile?.id !== profile.id,
                !noToken,
                impersonateUserID,
            ),
            profile.env.ENDPOINT_VIEWSERVER_TOKEN &&
                getToken(
                    "VIEWSERVER_TOKEN",
                    profile.env.ENDPOINT_VIEWSERVER_TOKEN,
                    genToken || currentProfile?.id !== profile.id,
                    !noToken,
                    impersonateUserID,
                ),
        ]);

        if (!noToken && profile.env.ENDPOINT_API_TOKEN !== "-NONE-" && !token) {
            process.exit(1);
        }

        // backward compatibility...
        process.env.API_TOKEN = token;

        process.env.IMPERSONATION_USER_ID = impersonateUserID;

        const activeProxyFile = proxyFile || "devServerProxy.js";
        const resolvedProxyPath = resolve(process.cwd(), activeProxyFile);

        if (proxyFile && !existsSync(resolvedProxyPath)) {
            throw new Error(`Proxy file not found at ${resolvedProxyPath}.`);
        }

        const proxy = {
            // Default dev proxy config
            ...(await getDevServerProxyConfig(
                undefined,
                federatedConfig,
                {
                    api: token,
                    viewserver: viewseverToken || token,
                },
                profile.env,
            )),
            // Package dev proxy if specified
            ...(await getDevServerProxyConfig(
                resolvedProxyPath,
                federatedConfig,
                {
                    api: token,
                    viewserver: viewseverToken || token,
                },
                profile.env,
            )),
        };

        return {
            proxy,
            token,
            viewseverToken: viewseverToken || token,
            moduleDevConfig,
            profile,
        };
    } catch (ex) {
        if (ex.message === "Canceled") {
            process.exit(1);
        } else {
            throw ex;
        }
    }

    return undefined;
};

module.exports = {
    setupDevServer,
};
