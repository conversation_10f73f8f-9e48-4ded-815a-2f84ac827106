import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../createMockingMiddleware";

export const riskPageDriversBetRollingPerformance = {
    "risk-page.drivers-bet-rolling-performance": (): MockHandler => ({
        decorateSchema: () => ({
            date: {
                displayName: "Date",
                properties: {
                    adapters:
                        "@toData(generateMissingDataPointsInPeriod('date', 'today', 'yyyy-MM-dd', '1D', props.datePreset, true))",
                    chartCartesianGridPropsShow: true,
                    chartReferenceLinePropsShow: true,
                    chartLegendPropsShow: false,
                    chartPropsMargin: { right: 60, top: 4, bottom: 4 },

                    chartTooltipPropsValueColumnName: "date",
                    chartTooltipPropsLabelFormatterType: "date",
                    chartTooltipPropsLabelValueFormat: "ddd, Do MMM YYYY"
                }
            },
            value: {
                displayName: "Rolling Performance",
                properties: {
                    chartType: "area",

                    chartSeriesPropsConnectNulls: true,
                    chartSeriesPropsGradientType: "minmax",

                    chartTooltipPropsFormatterType: "number",
                    chartTooltipPropsValueFormat: "number4dp"
                }
            }
        })
    })
};
