import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../createMockingMiddleware";

export const riskPageRiskDecompositionSummary = {
    "risk-page.risk-decomposition-summary": (): MockHandler => ({
        decorateSchema: () => ({
            name: {
                sortable: true,
                displayName: "Risk Decomposition",
                properties: {
                    flex: 1,
                    adapters: [
                        {
                            if: "eq(props.type, 'summary')",
                            adapters: "@prefixByCondition(' » ', 'name', datum.isChild)"
                        },
                        {
                            if: "eq(props.type, 'details')",
                            adapters: "@riskDecompositionDetailsAdapter()"
                        }
                    ]
                }
            },
            before: {
                sortable: true,
                contentTypeForDisplay: "ChangeValue",
                properties: {
                    flex: 1,
                    valueFormat: "percent1dp"
                }
            },
            after: {
                sortable: true,
                contentTypeForDisplay: "ChangeValue",
                properties: {
                    flex: 1,
                    valueFormat: "percent1dp"
                }
            },
            exposure: {
                sortable: true,
                displayName: "Exp ($)",
                contentTypeForDisplay: "ChangeValue",
                properties: {
                    order: 3,
                    flex: 1,
                    formatterType: "bigValue",
                    valueFormat: "$.1f"
                }
            },
            children: {
                schema: {
                    name: {
                        sortable: true,
                        displayName: "#context(selectedRowId) Decomposition",
                        properties: {
                            flex: 2,

                            componentTooltip: "ConnectedComponents.BetChartsTooltip",
                            componentTooltipPropsShow: "@calc[#ref(withTooltip) ? true : false]",
                            componentTooltipPropsWidth: 400,
                            componentTooltipPropsFactor: "#ref(name)",
                            componentTooltipPropsSyncId: "#ref(rowId)",
                            componentTooltipPropsDatePreset: "6M",
                            componentTooltipPropsPlacement: "left"
                        }
                    },
                    rsi: {
                        sortable: true,
                        contentTypeForDisplay: "Notional",
                        properties: {
                            flex: 1,
                            maxWidth: 50,
                            valueFormat: "number1dp"
                        }
                    },
                    before: {
                        sortable: true,
                        contentTypeForDisplay: "ChangeValue",
                        properties: {
                            flex: 1,
                            maxWidth: 100,
                            valueFormat: "percent1dp"
                        }
                    },
                    after: {
                        sortable: true,
                        contentTypeForDisplay: "ChangeValue",
                        properties: {
                            flex: 2,

                            cellRenderer: "ComponentLibrary.RangeCell",
                            cellRendererPropsShowPositiveIndicator: true,
                            cellRendererPropsHighlightPositiveValue: true,
                            aggregate: "minmax",
                            valueFormat: "percent1dp",

                            componentTooltip: "ConnectedComponents.RiskNowContributionTooltip",
                            componentTooltipPropsShow: "@calc[#ref(withTooltip) ? true : false]",
                            componentTooltipPropsFactorName: "#ref(name)",
                            componentTooltipPropsFastTrade: "#context(fastTrade)",
                            componentTooltipPropsDatePreset: "#context(datePreset)",
                            componentTooltipPropsWidth: 400,
                            componentTooltipPropsGridHeight: 195
                        }
                    },
                    exposureOneSD: {
                        contentTypeForDisplay: "ChangeValue",
                        properties: {
                            flex: 2,
                            formatterType: "bigValue",
                            valueFormat: "$.2f",

                            componentTooltip: "ConnectedComponents.RiskNowContributionTooltip",
                            componentTooltipPropsShow: "@calc[#ref(withTooltip) ? true : false]",
                            componentTooltipPropsFactorName: "#ref(name)",
                            componentTooltipPropsFastTrade: "#context(fastTrade)",
                            componentTooltipPropsDatePreset: "#context(datePreset)",
                            componentTooltipPropsWidth: 400,
                            componentTooltipPropsGridHeight: 195
                        }
                    },
                    exposure: {
                        sortable: true,
                        contentTypeForDisplay: "ChangeValue",
                        properties: {
                            flex: 1,
                            maxWidth: 75,

                            formatterType: "bigValue",
                            valueFormat: "$.1f",

                            componentTooltip: "ConnectedComponents.RiskNowExposureTooltip",
                            componentTooltipPropsShow: "@calc[#ref(withTooltip) ? true : false]",
                            componentTooltipPropsFactorName: "#ref(name)",
                            componentTooltipPropsFastTrade: "#context(fastTrade)",
                            componentTooltipPropsDatePreset: "#context(datePreset)",
                            componentTooltipPropsWidth: 400,
                            componentTooltipPropsGridHeight: 195
                        }
                    }
                }
            }
        })
    })
};
