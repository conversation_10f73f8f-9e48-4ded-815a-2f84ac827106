import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../createMockingMiddleware";

export const riskPageTraderRiskMismatch = {
    "risk-page.trader-risk-mismatch": (): MockHandler => ({
        decorateSchema: () => ({
            trader: {
                sortable: true,
                properties: {
                    flex: 1,
                    initialSort: "asc"
                }
            },
            traderId: {
                hidden: true
            },
            mismatchType: {
                displayName: "Mismatch",
                sortable: true,
                properties: {
                    flex: 2
                }
            },
            previousDifference: {
                displayName: "Prev Diff",
                sortable: true,
                contentTypeForDisplay: "Notional",
                properties: {
                    flex: 1,
                    valueFormat: "percent1dp1m"
                }
            },
            currentDifference: {
                displayName: "Curr Diff",
                sortable: true,
                contentTypeForDisplay: "Notional",
                properties: {
                    flex: 1,
                    valueFormat: "percent1dp1m",
                    adapters: [
                        "@toData(reject(propSatisfies(gt(props.selectedCurrentDifference), 'currentDifference')))",
                        "@toData(flt(propSatisfies(includesReversed(props.selectedTraders), 'traderId')))",
                        "@toData(flt(propSatisfies(includesReversed(props.selectedMismatchTypes), 'mismatchType')))"
                    ]
                }
            }
        })
    })
};
