import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../createMockingMiddleware";

export const riskPageAxiomaGroupedAttribution = {
    "risk-page.axioma-grouped-attribution": (): MockHandler => ({
        decorateSchema: () => ({
            return: {
                displayName: "Attribution (Alloc bps)",
                sortable: "@calc[eq('#context(type)', 'group')]",
                properties: {
                    order: 7,
                    adapters: "@toData(reject(propSatisfies(eq('Total'), 'name')))",
                    chartPropsChartType: "treemapHierarchical",
                    chartPropsIdKey: "rowId",
                    chartPropsLabelKey: "name",
                    chartPropsColorKey: "return",
                    chartPropsSizeKey: "absoluteReturn",
                    chartPropsFormatterType: "bigValue",
                    chartPropsValueFormat: "$.1f"
                }
            }
        })
    })
};
