import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../createMockingMiddleware";

export const riskPageAxiomaSummarySeries = {
    "risk-page.axioma-summary-series": (): MockHandler => ({
        decorateSchema: () => ({
            date: {
                displayName: "Date",
                properties: {
                    chartPropsMargin: { left: 0 },
                    chartCartesianGridPropsShow: true,
                    chartReferenceLinePropsShow: true,

                    chartLegendPropsShow: true,

                    chartXAxisPropsShow: true,
                    chartXAxisPropsFormatterType: "date",
                    chartXAxisPropsInterval: "preserveEnd",
                    chartXAxisPropsHeight: 25,

                    chartYAxisLeftPropsShow: true,
                    chartYAxisLeftPropsValueFormat: "percent2dp",

                    chartTooltipPropsValueColumnName: "date",
                    chartTooltipPropsLabelFormatterType: "date",
                    chartTooltipPropsLabelValueFormat: "ddd, Do MMM YYYY",
                    chartTooltipPropsZIndexModifier: "2"
                }
            },
            total: {
                properties: {
                    chartType: "line",
                    chartTooltipPropsFormatterType: "number",
                    chartTooltipPropsValueFormat: "percent4dp",
                    chartSeriesPropsStroke: "var(--charts__series__line__1)",
                    chartSeriesPropsStrokeWidth: "2px"
                }
            },
            specific: {
                properties: {
                    chartType: "line",
                    chartTooltipPropsFormatterType: "number",
                    chartTooltipPropsValueFormat: "percent4dp",
                    chartSeriesPropsStroke: "var(--charts__series__line__6)",
                    chartSeriesPropsStrokeWidth: "2px"
                }
            },
            factor: {
                properties: {
                    chartType: "line",
                    chartTooltipPropsFormatterType: "number",
                    chartTooltipPropsValueFormat: "percent4dp",
                    chartSeriesPropsStroke: "var(--charts__series__line__7)",
                    chartSeriesPropsStrokeWidth: "2px"
                }
            }
        })
    })
};
