import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../createMockingMiddleware";

export const riskPageRiskWidgetAxiomaReturnAttribution = {
    "risk-page.risk-widget.axioma-return-attribution": (): MockHandler => ({
        decorateSchema: () => ({
            total: {
                displayName: " ",
                properties: {
                    stampComponentName: "Components.ThreeValuesStamp",
                    stampPropsTitle: "1D EU Attribution",
                    stampPropsFirstValueKey: "total",
                    stampPropsSecondValueKey: "specific",
                    stampPropsThirdValueKey: "factor",
                    stampPropsModalContentComponentName: "Layouts.LayoutAttributionAndPnL",

                    colorValue: 0,
                    formatterType: "bigValue",
                    valueFormat: "$.1f",
                    defaultValue: "-"
                }
            },
            specific: {
                displayName: "Specific",
                properties: {
                    formatterType: "bigValue",
                    valueFormat: "$.1f",
                    defaultValue: "-"
                }
            },
            factor: {
                displayName: "Factor",
                properties: {
                    formatterType: "bigValue",
                    valueFormat: "$.1f",
                    defaultValue: "-"
                }
            }
        })
    })
};
