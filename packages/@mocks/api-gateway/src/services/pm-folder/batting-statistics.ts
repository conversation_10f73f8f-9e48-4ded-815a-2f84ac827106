import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../createMockingMiddleware";

export const pmBattingStatistics = {
    "pm-folder.batting-statistics": (): Mock<PERSON>andler => ({
        decorateSchema: () => ({
            hitRatio: {
                contentTypeForDisplay: "Notional",
                properties: {
                    valueFormat: "percent0dp",
                    componentValue: "PrimitiveComponents.Dial",
                    componentPropsSize: 100,
                    componentTooltip: "Components.ValueSeries",
                    componentTooltipPropsData: "#context(seriesData)",
                    componentTooltipPropsLabelColumnName: "shortDescription"
                }
            },
            battingPayoff: {
                displayName: "Payoff",
                contentTypeForDisplay: "Notional",
                properties: {
                    valueFormat: "number2dp",
                    componentTooltip: "Components.ValueSeries",
                    componentTooltipPropsData: "#context(seriesData)",
                    componentTooltipPropsLabelColumnName: "shortDescription"
                }
            },
            sluggingRatio: {
                contentTypeForDisplay: "Notional",
                properties: {
                    valueFormat: "number2dp",
                    componentTooltip: "Components.ValueSeries",
                    componentTooltipPropsData: "#context(seriesData)",
                    componentTooltipPropsLabelColumnName: "shortDescription"
                }
            },
            riskAdjustedReturn: {
                displayName: "Specific Risk",
                contentTypeForDisplay: "Notional",
                properties: {
                    valueFormat: "percent0dp",
                    componentValue: "PrimitiveComponents.Dial",
                    componentPropsSize: 100,
                    componentTooltip: "Components.ValueSeries",
                    componentTooltipPropsData: "#context(seriesData)",
                    componentTooltipPropsLabelColumnName: "shortDescription"
                }
            },
            latestCapitalUtilisation: {
                displayName: "Capital Utilisation",
                contentTypeForDisplay: "Notional",
                properties: {
                    valueFormat: "percent0dp",
                    componentValue: "PrimitiveComponents.ValueRange",
                    componentPropsLow: "#ref(minCapitalUtilisation)",
                    componentPropsAvg: "#ref(meanCapitalUtilisation)",
                    componentPropsHigh: "#ref(maxCapitalUtilisation)",
                    componentPropsCurrent: "#ref(.)",
                    componentPropsValueFormat: "percent1dp",
                    componentTooltip: "Components.ValueSeries",
                    componentTooltipPropsData: "#context(seriesData)",
                    componentTooltipPropsLabelColumnName: "shortDescription",
                    componentTooltipPropsValueColumnName: "meanCapitalUtilisationUnlevered"
                }
            },
            meanCapitalUtilisationUnlevered: {
                contentTypeForDisplay: "Notional",
                properties: {
                    valueFormat: "percent0dp"
                }
            },
            averageFactorPercentageOfTotalRisk: {
                displayName: "Risk",
                contentTypeForDisplay: "Notional",
                properties: {
                    componentValue: "PrimitiveComponents.Dial",
                    componentPropsSize: 100,
                    componentPropsLabel: "Factor",
                    componentPropsAltLabel: "Specific",
                    valueFormat: "percent0dp"
                }
            }
        })
        // decorateData: data => {
        //     return data?.map(item => ({
        //         ...item,
        //         hitRatio: 0.55,
        //         sluggingRatio: 1.22,
        //         battingPayoff: 1.54,
        //         riskAdjustedReturn: 0.25,
        //         meanCapitalUtilisation: 0.73,
        //         minCapitalUtilisation: 0.7,
        //         maxCapitalUtilisation: 0.8
        //     }));
        // }
    })
};
