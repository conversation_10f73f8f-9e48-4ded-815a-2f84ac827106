import { ApiGatewayService } from "@tradinglabs/contracts";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../createMockingMiddleware";

export const launchPadSectionTableHandler = {
    "launch-pad.section-table": (): MockHandler<ApiGatewayService.Dashboard.SectionsResponse> => ({
        decorateData: data => {
            // @TODO: remove
            // Example response with settings:
            return (
                data && [
                    ...data.filter(option => option.rowId !== "settings"),
                    {
                        rowId: "settings",
                        type: "Option",
                        name: "Settings",
                        url: "menu",
                        icon: "user",
                        isDisabled: false,
                        componentName: "ClientApi.Settings",
                        action: {
                            actionType: "Popout.Open",
                            label: "Settings",
                            actionParams: {
                                componentName: "ClientApi.Settings",
                                componentProps: {
                                    v2: true,
                                },
                                containerProps: {
                                    id: "ClientApi.Settings",
                                    title: "Settings",
                                    hasPadding: false,
                                    popoutType: "overlay",
                                    minWidth: "74em",
                                    minHeight: "75vh",
                                    maxHeight: "74em",
                                },
                            },
                        },
                        sections: [
                            // If user has permission to impersonate:
                            {
                                rowId: "ImpersonateUser",
                                parentRowId: "settings",
                                type: "Option",
                                name: "Impersonate User",
                                icon: "impersonate",
                                componentName: "ConnectedComponents.ImpersonateUser",
                            },
                        ],
                    },
                ]
            );
        },
    }),

    "notification.active-message-count":
        (): MockHandler<ApiGatewayService.Notification.ActiveMessageCountResponse> => ({
            decorateData: () => [
                {
                    rowId: "1",
                    laneId: 1,
                    count: 23,
                    isPrimary: true,
                },
                {
                    rowId: "2",
                    laneId: 2,
                    count: 32,
                    isPrimary: false,
                },
            ],
        }),
};
