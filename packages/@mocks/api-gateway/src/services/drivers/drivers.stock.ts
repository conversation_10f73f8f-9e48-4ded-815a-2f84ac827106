import type { MockHand<PERSON> } from "../../createMockingMiddleware";

export const driversStockHandler = {
    "drivers.stock-chart": (): MockHandler => ({
        decorateSchema: () => ({
            factorDescription: {
                properties: {
                    chartPropsStackOffset: "sign",
                    chartYAxisLeftPropsValueFormat: "dollarMax2dp",
                    chartTooltipPropsValueFormat: "dollarMax2dp",
                    chartCartesianGridPropsShow: true,
                    chartReferenceLinePropsShow: true,
                    chartYAxisLeftPropsShow: true,
                    chartXAxisPropsShow: true,
                    chartXAxisPropsHeight: 85,
                    chartYAxisLeftPropsWidth: 60
                }
            },
            totalExposureOneSdMove: {
                properties: {
                    chartType: "bar",
                    referenceRowsValueColumnName: "totalExposureOneSdMove",
                    referenceRowsLabelColumnName: "totalExposureOneSdMove"
                }
            }
        })
    })
};
