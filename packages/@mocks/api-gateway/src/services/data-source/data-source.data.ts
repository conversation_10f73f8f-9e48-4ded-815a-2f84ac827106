import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../createMockingMiddleware";

export const dataSourceData = {
    "type=<EMAIL>": (): MockHandler => ({
        decorateSchema: () => ({
            overrides: {
                dollarValue: {
                    contentTypeForDisplay: "Notional",
                    displayName: "$ Value",
                    properties: {
                        chartType: "pie",

                        chartSeriesPropsDataKey: "dollarValue",
                        chartSeriesPropsNameKey: "sector",
                        chartSeriesPropsInnerRadius: 75,
                        chartSeriesPropsOuterRadius: 90,
                        chartSeriesPropsPaddingAngle: 2,
                        chartSeriesPropsStroke: "transparent",

                        chartTooltipPropsTitle: "#ref(status)",
                        // chartTooltipPropsSortBy: "dollarValue",

                        chartTooltipPropsFormatterType: "number",
                        chartTooltipPropsValueFormat: "dollar2dp",
                        chartTooltipPropsDisableColor: true,

                        formatterType: "number",
                        valueFormat: "dollar2dp",
                        cellRendererPropsShowPositiveIndicator: false,
                    },
                },
            },

            schema: [
                {
                    columnId: 101,
                    name: "totalDollarFilled",
                    contentType: 9,
                    metaData: {
                        contentTypeForDisplay: "Notional",
                        properties: {
                            component: "Components.DialCenteredValue",
                            componentPropsValue: "#context(totalDollarFilled)",
                            componentPropsDataKey: "totalDollarFilled",
                            componentPropsDescription: "Total $ Value",
                            componentPropsSize: 110,
                            formatterType: "bigValue",
                            valueFormat: "$.1f",
                        },
                    },
                },
                {
                    columnId: 102,
                    name: "dollarValuePercent",
                    contentType: 9,
                    metaData: {
                        contentTypeForDisplay: "ChangeValuePercentage",
                        displayName: "%",
                        properties: {
                            chartTooltipPropsFormatterType: "percent",
                            chartTooltipPropsValueFormat: "percent1dp",
                            chartTooltipPropsDisableColor: true,

                            colorValue: 0,
                            cellRendererPropsShowPositiveIndicator: false,
                        },
                    },
                },
            ],
        }),
    }),

    "type=<EMAIL>": (): MockHandler => ({
        decorateSchema: () => ({
            overrides: {
                dollarValue: {
                    contentTypeForDisplay: "Notional",
                    displayName: "$ Value",
                    properties: {
                        formatterType: "bigValue",
                        valueFormat: "$.2f",
                        cellRendererPropsShowPositiveIndicator: false,
                    },
                },
            },
        }),
    }),

    "data-source.data": (): MockHandler => ({}),
};
