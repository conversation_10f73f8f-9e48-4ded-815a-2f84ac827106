import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../createMockingMiddleware";

export const pdpbetaAdjustedTotalSpreadTilt = {
    "pdp.beta-adjusted-total-spread-tilt": (): MockHandler => ({
        decorateSchema: () => ({
            date: {
                properties: {
                    chartPropsStackOffset: "sign",
                    chartCartesianGridPropsShow: true,
                    chartReferenceLinePropsShow: true,
                    chartLegendPropsShow: true,

                    chartYAxisLeftPropsValueFormat: "percent1dp",
                    chartYAxisLeftPropsShow: true,
                    chartYAxisLeftPropsWidth: 50,

                    chartXAxisPropsShow: true,
                    chartXAxisPropsFormatterType: "date",

                    chartTooltipPropsValueFormat: "percent2dp",
                    chartTooltipPropsValueColumnName: "date",
                    chartTooltipPropsLabelFormatterType: "date",
                    chartTooltipPropsLabelValueFormat: "ddd, <PERSON> MMM YYYY"
                }
            },
            betaAdjustedSpreadOnAttribution: {
                properties: {
                    chartType: "line",
                    chartSeriesPropsStroke: "var(--charts__series__line__7)"
                }
            },
            gmvReturn: {
                properties: {
                    chartType: "line",
                    chartSeriesPropsStroke: "var(--charts__series__line__8)"
                }
            },
            betaAdjTiltOnAttribution: {
                properties: {
                    chartType: "line",
                    chartSeriesPropsStroke: "var(--charts__series__line__3)"
                }
            }
        })
    })
};
