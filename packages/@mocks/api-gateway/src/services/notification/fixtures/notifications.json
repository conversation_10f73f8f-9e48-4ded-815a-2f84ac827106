[{"id": 9890, "rowId": 9889, "status": "Active", "categoryType": "Indicator", "generatedOn": "2023-09-28T13:00:47.1379605+00:00", "actionedText": "No Longer In Breach", "title": "Offside after 90 days", "level": "Info", "type": "ToDo", "contents": "Offside(-0.13%) after 90 days, max allowed size 1.0% _($1.0m)_ vs current size 1.55% ($1.5m), please reduce ", "namedEntities": [{"rowId": "ERF FP Equity:BloombergTicker", "identifier": "ERF FP Equity", "identifierType": "Bloomberg Ticker"}, {"rowId": "My Game Plan:Game Plan Name", "identifier": "My Game Plan", "identifierType": "Game Plan Name"}, {"rowId": "VOD LN Equity:BloombergTicker", "identifier": "VOD LN Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "48910:GamePlan", "identifier": "48910", "entityType": "Game Plan"}, {"rowId": "573058:<PERSON><PERSON><PERSON>", "identifier": "573058", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1842:PortfolioRuleId", "identifier": "1842", "entityType": "Portfolio Rule Id"}, {"rowId": "473:Trader", "identifier": "473", "entityType": "Trader"}, {"rowId": "13:Group", "identifier": "13", "entityType": "Group"}, {"rowId": "Offside(-0.13%) after 90 days, max allowed size 1.0% _($1.0m)_ vs current size 1.55% ($1.5m), please reduce :Description", "identifier": "Offside(-0.13%) after 90 days, max allowed size 1.0% ($1.0m)_ vs current size 1.55% ($1.5m), please reduce ", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "ERF FP Equity:BloombergTicker", "identifier": "ERF FP Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "WW_TRIP:Strategy", "identifier": "WW_TRIP", "entityType": "Strategy"}], "actions": [], "categorySubType": "Indicator", "component": "PrimitiveComponents.Image", "componentProps": {}, "traderId": 473, "traderInitials": "WW", "displayTraderInitials": true}, {"id": 119890, "rowId": 119889, "status": "Active", "categoryType": "Trading/Jockeying", "generatedOn": "2023-09-27T00:06:52.0268316+00:00", "title": "Added to position 1 days ago when -0.06% ROA offside. Add not working (-0.15%), need to cut add to get back to position of -279567 shares", "level": "<PERSON><PERSON>", "type": "ToDo", "namedEntities": [{"rowId": "BBVA SM Equity:BloombergTicker", "identifier": "BBVA SM Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "48910:GamePlan", "identifier": "48910", "entityType": "Game Plan"}, {"rowId": "568730:<PERSON><PERSON><PERSON>", "identifier": "568730", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "2066:PortfolioRuleId", "identifier": "2066", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "13:Group", "identifier": "13", "entityType": "Group"}, {"rowId": "Added to position 1 days ago when -0.06% ROA offside. Add not working (-0.15%), need to cut add to get back to position of -279567 shares:Description", "identifier": "Added to position 1 days ago when -0.06% ROA offside. Add not working (-0.15%), need to cut add to get back to position of -279567 shares", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "BBVA SM Equity:BloombergTicker", "identifier": "BBVA SM Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_SCAT:Strategy", "identifier": "SK_SCAT", "entityType": "Strategy"}, {"rowId": "2023-09-26:TradeDate", "identifier": "2023-09-26", "entityType": "Trade Date"}], "actions": [], "categorySubType": "Trading/Jockeying", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {}}, {"id": 1535, "rowId": 1535, "status": "Active", "categoryType": "Trading/Jockeying", "generatedOn": "2023-09-27T00:06:52.0268313+00:00", "title": "Added to position 3 days ago when -0.21% ROA offside. Add not working (-2.17%), need to cut add to get back to position of 34991 shares", "level": "<PERSON><PERSON>", "type": "ToDo", "namedEntities": [{"rowId": "BABA US Equity:BloombergTicker", "identifier": "BABA US Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "48920:GamePlan", "identifier": "48920", "entityType": "Game Plan"}, {"rowId": "568727:<PERSON><PERSON><PERSON>", "identifier": "568727", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "2066:PortfolioRuleId", "identifier": "2066", "entityType": "Portfolio Rule Id"}, {"rowId": "473:Trader", "identifier": "473", "entityType": "Trader"}, {"rowId": "11:Group", "identifier": "11", "entityType": "Group"}, {"rowId": "Max size 1.0% _($1.0m)_ if initiate within **5 days** of upcoming numbers:Description", "identifier": "Max size 1.0% _($1.0m)_ if initiate within **5 days** of upcoming numbers", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "BABA US Equity:BloombergTicker", "identifier": "BABA US Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "WW_CHAMP:Strategy", "identifier": "WW_CHAMP", "entityType": "Strategy"}], "actions": [], "categorySubType": "Portfolio Construction & Market Awareness", "component": "PrimitiveComponents.Image", "componentProps": {}, "traderId": 473, "traderInitials": "ABC", "displayTraderInitials": false}, {"id": 889, "rowId": 889, "status": "Active", "categoryType": "Commentary", "generatedOn": "2023-08-01T07:00:47.1379605+00:00", "title": "Offside after 90 days", "level": "Info", "type": "ToDo", "contents": "Offside(-0.13%) after 90 days, max allowed size 1.0% _($1.0m)_ vs current size 1.55% ($1.5m), please reduce ", "namedEntities": [{"rowId": "Offside after 90 days:Commentary", "identifier": "Offside after 90 days", "identifierType": "Commentary"}], "entities": [{"rowId": "573058:<PERSON><PERSON><PERSON>", "identifier": "573058", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1842:PortfolioRuleId", "identifier": "1842", "entityType": "Portfolio Rule Id"}, {"rowId": "473:Trader", "identifier": "473", "entityType": "Trader"}, {"rowId": "13:Group", "identifier": "13", "entityType": "Group"}, {"rowId": "Added to position 3 days ago when -0.21% ROA offside. Add not working (-2.17%), need to cut add to get back to position of 34991 shares:Description", "identifier": "Added to position 3 days ago when -0.21% ROA offside. Add not working (-2.17%), need to cut add to get back to position of 34991 shares", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Positive", "entityType": "Severity"}, {"rowId": "Offside after 90 days:Commentary", "identifier": "Offside after 90 days", "identifierType": "Commentary"}, {"rowId": "SK_LFUND:Strategy", "identifier": "SK_LFUND", "entityType": "Strategy"}, {"rowId": "2023-09-22:TradeDate", "identifier": "2023-09-22", "entityType": "Trade Date"}], "actions": [], "categorySubType": "Trading/Jockeying", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {}}, {"id": 1545, "rowId": 1545, "status": "Active", "categoryType": "Trading/Jockeying", "generatedOn": "2023-09-27T07:28:14.9130466+00:00", "title": "Offside on return on allocation(-0.02%) after > 40 days(46), exit position", "level": "<PERSON><PERSON>", "type": "ToDo", "namedEntities": [{"rowId": "RIO LN Equity:BloombergTicker", "identifier": "RIO LN Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "48920:GamePlan", "identifier": "48920", "entityType": "Game Plan"}, {"rowId": "568759:<PERSON><PERSON><PERSON>", "identifier": "568759", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1898:PortfolioRuleId", "identifier": "1898", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "13:Group", "identifier": "13", "entityType": "Group"}, {"rowId": "Offside on return on allocation(-0.02%) after > 40 days(46), exit position:Description", "identifier": "Offside on return on allocation(-0.02%) after > 40 days(46), exit position", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "RIO LN Equity:BloombergTicker", "identifier": "RIO LN Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_LCAT:Strategy", "identifier": "SK_LCAT", "entityType": "Strategy"}], "actions": [], "categorySubType": "Trading/Jockeying", "traderId": 434, "component": "PrimitiveComponents.Image", "componentProps": {}, "traderInitials": "SK", "displayTraderInitials": true}, {"id": 1547, "rowId": 1547, "status": "Active", "categoryType": "Trading/Jockeying", "generatedOn": "2023-09-27T07:28:14.9077995+00:00", "title": "Max size 0.75%($2.3m) for offside short that is making 6m high or offside long that is making 6m low, vs current size 1.11%($3.3m), please reduce", "level": "<PERSON><PERSON>", "type": "ToDo", "namedEntities": [{"rowId": "KNEBV FH Equity:BloombergTicker", "identifier": "KNEBV FH Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "568761:<PERSON><PERSON><PERSON>", "identifier": "568761", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1900:PortfolioRuleId", "identifier": "1900", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "11:Group", "identifier": "11", "entityType": "Group"}, {"rowId": "Max size 0.75%($2.3m) for offside short that is making 6m high or offside long that is making 6m low, vs current size 1.11%($3.3m), please reduce:Description", "identifier": "Max size 0.75%($2.3m) for offside short that is making 6m high or offside long that is making 6m low, vs current size 1.11%($3.3m), please reduce", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "KNEBV FH Equity:BloombergTicker", "identifier": "KNEBV FH Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_LFUND:Strategy", "identifier": "SK_LFUND", "entityType": "Strategy"}], "actions": [], "categorySubType": "Sourcing & Ideation", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {}}, {"id": 1549, "rowId": 1549, "status": "Active", "categoryType": "Commentary", "generatedOn": "2023-09-27T07:28:14.9130499+00:00", "title": "Offside on return on allocation(-0.03%) after > 40 days(200), exit position", "level": "<PERSON><PERSON>", "type": "ToDo", "namedEntities": [{"rowId": "Offside on return on allocation(-0.03%) after > 40 days(200), exit position:Commentary", "identifier": "Offside on return on allocation(-0.03%) after > 40 days(200), exit position", "identifierType": "Commentary"}], "entities": [{"rowId": "48910:GamePlan", "identifier": "48910", "entityType": "Game Plan"}, {"rowId": "568758:<PERSON><PERSON><PERSON>", "identifier": "568758", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1898:PortfolioRuleId", "identifier": "1898", "entityType": "Portfolio Rule Id"}, {"rowId": "473:Trader", "identifier": "473", "entityType": "Trader"}, {"rowId": "11:Group", "identifier": "11", "entityType": "Group"}, {"rowId": "Max size 1.0 % _($1.0m)_ if initiate within **5 days** of upcoming numbers:Description", "identifier": "Max size 1.0% _($1.0m)_ if initiate within **5 days** of upcoming numbers", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "Offside on return on allocation(-0.03%) after > 40 days(200), exit position:Commentary", "identifier": "Offside on return on allocation(-0.03%) after > 40 days(200), exit position", "identifierType": "Commentary"}, {"rowId": "WW_CHAMP:Strategy", "identifier": "WW_CHAMP", "entityType": "Strategy"}], "actions": [], "categorySubType": "Portfolio Construction & Market Awareness", "component": "PrimitiveComponents.Image", "componentProps": {}, "traderInitials": "SK", "displayTraderInitials": true}, {"id": 19889, "rowId": 19889, "status": "Active", "categoryType": "Trading/Jockeying", "generatedOn": "2023-08-01T07:00:47.1379605+00:00", "actionedText": "Not Breached", "title": "Offside after 90 days", "level": "<PERSON><PERSON>", "type": "ToDo", "contents": "Offside(-0.13%) after 90 days, max allowed size 1.0% _($1.0m)_ vs current size 1.55% ($1.5m), please reduce ", "namedEntities": [{"rowId": "ERF FP Equity:BloombergTicker", "identifier": "ERF FP Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "573058:<PERSON><PERSON><PERSON>", "identifier": "573058", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1842:PortfolioRuleId", "identifier": "1842", "entityType": "Portfolio Rule Id"}, {"rowId": "473:Trader", "identifier": "473", "entityType": "Trader"}, {"rowId": "13:Group", "identifier": "13", "entityType": "Group"}, {"rowId": "Offside on return on allocation(-0.03%) after > 40 days(200), exit position:Description", "identifier": "Offside on return on allocation(-0.03%) after > 40 days(200), exit position", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "ERF FP Equity:BloombergTicker", "identifier": "ERF FP Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_LCAT:Strategy", "identifier": "SK_LCAT", "entityType": "Strategy"}], "actions": [], "categorySubType": "Trading/Jockeying", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {}}, {"id": 1550, "rowId": 1550, "status": "Active", "categoryType": "Trading/Jockeying", "generatedOn": "2023-09-27T07:28:14.913047+00:00", "title": "Offside on return on allocation(-0.03%) after > 40 days(121), exit position", "level": "<PERSON><PERSON>", "type": "ToDo", "componentType": "CompanySummary", "namedEntities": [{"rowId": "RBI AV Equity:BloombergTicker", "identifier": "RBI AV Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "568757:<PERSON><PERSON><PERSON>", "identifier": "568757", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1898:PortfolioRuleId", "identifier": "1898", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "13:Group", "identifier": "13", "entityType": "Group"}, {"rowId": "Offside on return on allocation(-0.03%) after > 40 days(121), exit position:Description", "identifier": "Offside on return on allocation(-0.03%) after > 40 days(121), exit position", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "RBI AV Equity:BloombergTicker", "identifier": "RBI AV Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_LHEDGE:Strategy", "identifier": "SK_LHEDGE", "entityType": "Strategy"}], "actions": [], "categorySubType": "Trading/Jockeying", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {}}, {"id": 1554, "rowId": 1554, "status": "Active", "categoryType": "Trading/Jockeying", "generatedOn": "2023-09-27T08:13:43.1384823+00:00", "title": "Added to position 3 days ago when -0.06% ROA offside. Add not working (-0.17%), need to cut add to get back to position of -517755 shares", "level": "<PERSON><PERSON>", "type": "ToDo", "namedEntities": [{"rowId": "AIBG ID Equity:BloombergTicker", "identifier": "AIBG ID Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "568815:<PERSON><PERSON><PERSON>", "identifier": "568815", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "2066:PortfolioRuleId", "identifier": "2066", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "13:Group", "identifier": "13", "entityType": "Group"}, {"rowId": "Added to position 3 days ago when -0.06% ROA offside. Add not working (-0.17%), need to cut add to get back to position of -517755 shares:Description", "identifier": "Added to position 3 days ago when -0.06% ROA offside. Add not working (-0.17%), need to cut add to get back to position of -517755 shares", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "AIBG ID Equity:BloombergTicker", "identifier": "AIBG ID Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_SFUND:Strategy", "identifier": "SK_SFUND", "entityType": "Strategy"}, {"rowId": "2023-09-22:TradeDate", "identifier": "2023-09-22", "entityType": "Trade Date"}], "actions": [], "categorySubType": "Trading/Jockeying", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {}}, {"id": 1556, "rowId": 1556, "status": "Active", "categoryType": "Trading/Jockeying", "generatedOn": "2023-09-27T11:37:16.0321219+00:00", "title": "Max size 0.75%($2.3m) when initiating short on 6m high or long on 6m low", "level": "<PERSON><PERSON>", "type": "ToDo", "namedEntities": [{"rowId": "SIE GY Equity:BloombergTicker", "identifier": "SIE GY Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "568818:<PERSON><PERSON><PERSON>", "identifier": "568818", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1899:PortfolioRuleId", "identifier": "1899", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "11:Group", "identifier": "11", "entityType": "Group"}, {"rowId": "Max size 0.75%($2.3m) when initiating short on 6m high or long on 6m low:Description", "identifier": "Max size 0.75%($2.3m) when initiating short on 6m high or long on 6m low", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "SIE GY Equity:BloombergTicker", "identifier": "SIE GY Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_LCAT:Strategy", "identifier": "SK_LCAT", "entityType": "Strategy"}], "actions": [], "categorySubType": "Sourcing & Ideation", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {}}, {"id": 1557, "rowId": 1557, "status": "Active", "categoryType": "Risk", "generatedOn": "2023-09-27T12:44:16.2851337+00:00", "title": "Max size 0.75%($2.3m) for offside short that is making 6m high or offside long that is making 6m low, vs current size 1.32%($4.0m), please reduce", "level": "<PERSON><PERSON>", "type": "ToDo", "namedEntities": [{"rowId": "LONN SW Equity:BloombergTicker", "identifier": "LONN SW Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "48910:GamePlan", "identifier": "48910", "entityType": "Game Plan"}, {"rowId": "568819:<PERSON><PERSON><PERSON>", "identifier": "568819", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1900:PortfolioRuleId", "identifier": "1900", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "11:Group", "identifier": "11", "entityType": "Group"}, {"rowId": "Max size 0.75%($2.3m) for offside short that is making 6m high or offside long that is making 6m low, vs current size 1.32%($4.0m), please reduce:Description", "identifier": "Max size 0.75%($2.3m) for offside short that is making 6m high or offside long that is making 6m low, vs current size 1.32%($4.0m), please reduce", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "LONN SW Equity:BloombergTicker", "identifier": "LONN SW Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_LFUND:Strategy", "identifier": "SK_LFUND", "entityType": "Strategy"}], "actions": [], "categorySubType": "Sourcing & Ideation", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {}}, {"id": 1562, "rowId": 1562, "status": "Inactive", "categoryType": "Portfolio Construction & Market Awareness", "generatedOn": "2023-09-27T14:08:18.7147531+00:00", "title": "Max size 0.75%($2.3m) for offside short that is making 6m high or offside long that is making 6m low, vs current size 0.75%($2.3m), please reduce", "level": "<PERSON><PERSON>", "type": "ToDo", "namedEntities": [{"rowId": "KER FP Equity:BloombergTicker", "identifier": "KER FP Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "568834:<PERSON><PERSON><PERSON>", "identifier": "568834", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1900:PortfolioRuleId", "identifier": "1900", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "11:Group", "identifier": "11", "entityType": "Group"}, {"rowId": "Max size 0.75%($2.3m) for offside short that is making 6m high or offside long that is making 6m low, vs current size 0.75%($2.3m), please reduce:Description", "identifier": "Max size 0.75%($2.3m) for offside short that is making 6m high or offside long that is making 6m low, vs current size 0.75%($2.3m), please reduce", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "KER FP Equity:BloombergTicker", "identifier": "KER FP Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_LCAT:Strategy", "identifier": "SK_LCAT", "entityType": "Strategy"}], "actions": [], "categorySubType": "Sourcing & Ideation", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {}}, {"id": 1563, "rowId": 1563, "status": "Active", "categoryType": "Trading/Jockeying", "generatedOn": "2023-09-27T14:08:18.7147491+00:00", "title": "Stop loss of 5% for long making 6m low or short making 6m high (-9.94%), exit position", "level": "<PERSON><PERSON>", "type": "ToDo", "namedEntities": [{"rowId": "KER FP Equity:BloombergTicker", "identifier": "KER FP Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "568835:<PERSON><PERSON><PERSON>", "identifier": "568835", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1901:PortfolioRuleId", "identifier": "1901", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "13:Group", "identifier": "13", "entityType": "Group"}, {"rowId": "Stop loss of 5% for long making 6m low or short making 6m high (-9.94%), exit position:Description", "identifier": "Stop loss of 5% for long making 6m low or short making 6m high (-9.94%), exit position", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "KER FP Equity:BloombergTicker", "identifier": "KER FP Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_LCAT:Strategy", "identifier": "SK_LCAT", "entityType": "Strategy"}], "actions": [], "defaultAction": {"actionParams": {"commandName": "gateway/api/workflowinstance/try-set-status", "commandParams": {"WorkflowInstanceIds": [14327], "Status": "Acknowledged"}}, "label": "Acknowledge", "level": "secondary", "order": 1, "actionType": "Command.Invoke"}, "categorySubType": "Trading/Jockeying", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {"caption": "As discussed any long making 6m low or short making 6m high has a tight stop of 5% to ensure not getting stuck in positions that are not working. Exit "}}, {"id": 1565, "rowId": 1565, "status": "Active", "categoryType": "Trading/Jockeying", "generatedOn": "2023-09-27T16:01:11.640542+00:00", "title": "Max size 0.75%($2.3m) for offside short that is making 6m high or offside long that is making 6m low, vs current size 0.99%($3.0m), please reduce", "level": "<PERSON><PERSON>", "type": "ToDo", "namedEntities": [{"rowId": "DIS US Equity:BloombergTicker", "identifier": "DIS US Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "568840:<PERSON><PERSON><PERSON>", "identifier": "568840", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1900:PortfolioRuleId", "identifier": "1900", "entityType": "Portfolio Rule Id"}, {"rowId": "461:Trader", "identifier": "461", "entityType": "Trader"}, {"rowId": "SK:Trader Initials", "identifier": "SK", "entityType": "Trader Initials"}, {"rowId": "11:Group", "identifier": "11", "entityType": "Group"}, {"rowId": "Max size 0.75%($2.3m) for offside short that is making 6m high or offside long that is making 6m low, vs current size 0.99%($3.0m), please reduce:Description", "identifier": "Max size 0.75%($2.3m) for offside short that is making 6m high or offside long that is making 6m low, vs current size 0.99%($3.0m), please reduce", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "DIS US Equity:BloombergTicker", "identifier": "DIS US Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_LCAT:Strategy", "identifier": "SK_LCAT", "entityType": "Strategy"}], "actions": [], "categorySubType": "Sourcing & Ideation", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {}}, {"id": 1567, "rowId": 1567, "status": "Active", "categoryType": "Trading/Jockeying", "generatedOn": "2023-09-27T18:01:14.4355709+00:00", "title": "Stop loss of 5% for long making 6m low or short making 6m high (42.10%), exit position", "level": "<PERSON><PERSON>", "type": "ToDo", "namedEntities": [{"rowId": "8306 JP 12/07/23 P1100 Equity:BloombergTicker", "identifier": "8306 JP 12/07/23 P1100 Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "48911:GamePlan", "identifier": "48911", "entityType": "Game Plan"}, {"rowId": "568844:<PERSON><PERSON><PERSON>", "identifier": "568844", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1901:PortfolioRuleId", "identifier": "1901", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "13:Group", "identifier": "13", "entityType": "Group"}, {"rowId": "Stop loss of 5% for long making 6m low or short making 6m high (42.10%), exit position:Description", "identifier": "Stop loss of 5% for long making 6m low or short making 6m high (42.10%), exit position", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "8306 JP 12/07/23 P1100 Equity:BloombergTicker", "identifier": "8306 JP 12/07/23 P1100 Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_LOPT:Strategy", "identifier": "SK_LOPT", "entityType": "Strategy"}], "actions": [], "categorySubType": "Trading/Jockeying", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {"caption": "As discussed any long making 6m low or short making 6m high has a tight stop of 5% to ensure not getting stuck in positions that are not working. Exit "}}, {"id": 1574, "rowId": 1574, "status": "Active", "categoryType": "Trading/Jockeying", "generatedOn": "2023-09-28T07:00:20.034624+00:00", "title": "Offside on return on allocation(-0.02%) after > 40 days(122), exit position", "level": "<PERSON><PERSON>", "type": "ToDo", "namedEntities": [{"rowId": "RBI AV Equity:BloombergTicker", "identifier": "RBI AV Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "584024:<PERSON><PERSON><PERSON>", "identifier": "584024", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1898:PortfolioRuleId", "identifier": "1898", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "13:Group", "identifier": "13", "entityType": "Group"}, {"rowId": "Offside on return on allocation(-0.02%) after > 40 days(122), exit position:Description", "identifier": "Offside on return on allocation(-0.02%) after > 40 days(122), exit position", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "RBI AV Equity:BloombergTicker", "identifier": "RBI AV Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_LHEDGE:Strategy", "identifier": "SK_LHEDGE", "entityType": "Strategy"}], "actions": [], "categorySubType": "Trading/Jockeying", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {}}, {"id": 1575, "rowId": 1575, "status": "Active", "categoryType": "Trading/Jockeying", "generatedOn": "2023-09-28T07:00:20.0270941+00:00", "title": "Offside on return on allocation(-0.03%) after > 40 days(53), exit position", "level": "Info", "type": "ToDo", "namedEntities": [{"rowId": "DOCS LN Equity:BloombergTicker", "identifier": "DOCS LN Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "584023:<PERSON><PERSON><PERSON>", "identifier": "584023", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1898:PortfolioRuleId", "identifier": "1898", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "13:Group", "identifier": "13", "entityType": "Group"}, {"rowId": "Offside on return on allocation(-0.03%) after > 40 days(53), exit position:Description", "identifier": "Offside on return on allocation(-0.03%) after > 40 days(53), exit position", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "DOCS LN Equity:BloombergTicker", "identifier": "DOCS LN Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_LFUND:Strategy", "identifier": "SK_LFUND", "entityType": "Strategy"}], "actions": [], "categorySubType": "Trading/Jockeying", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {}}, {"id": 1576, "rowId": 1576, "status": "Active", "categoryType": "Indicator", "generatedOn": "2023-09-28T07:00:20.0270951+00:00", "title": "Offside on return on allocation(-0.12%) after > 40 days(201), exit position", "level": "Info", "type": "ToDo", "namedEntities": [{"rowId": "KER FP Equity:BloombergTicker", "identifier": "KER FP Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "584025:<PERSON><PERSON><PERSON>", "identifier": "584025", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1898:PortfolioRuleId", "identifier": "1898", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "13:Group", "identifier": "13", "entityType": "Group"}, {"rowId": "Offside on return on allocation(-0.12%) after > 40 days(201), exit position:Description", "identifier": "Offside on return on allocation(-0.12%) after > 40 days(201), exit position", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "KER FP Equity:BloombergTicker", "identifier": "KER FP Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_LCAT:Strategy", "identifier": "SK_LCAT", "entityType": "Strategy"}], "actions": [], "categorySubType": "Trading/Jockeying", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {}}, {"id": 1583, "rowId": 1583, "status": "Active", "categoryType": "Risk", "generatedOn": "2023-09-28T07:04:10.9517901+00:00", "title": "Max size 0.75%($2.3m) for offside short that is making 6m high or offside long that is making 6m low, vs current size 0.78%($2.3m), please reduce", "level": "Info", "type": "ToDo", "namedEntities": [{"rowId": "KER FP Equity:BloombergTicker", "identifier": "KER FP Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "48911:GamePlan", "identifier": "48911", "entityType": "Game Plan"}, {"rowId": "584061:<PERSON><PERSON><PERSON>", "identifier": "584061", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1900:PortfolioRuleId", "identifier": "1900", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "11:Group", "identifier": "11", "entityType": "Group"}, {"rowId": "Max size 0.75%($2.3m) for offside short that is making 6m high or offside long that is making 6m low, vs current size 0.78%($2.3m), please reduce:Description", "identifier": "Max size 0.75%($2.3m) for offside short that is making 6m high or offside long that is making 6m low, vs current size 0.78%($2.3m), please reduce", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "KER FP Equity:BloombergTicker", "identifier": "KER FP Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_LCAT:Strategy", "identifier": "SK_LCAT", "entityType": "Strategy"}], "actions": [], "categorySubType": "Sourcing & Ideation", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {}}, {"id": 1584, "rowId": 1584, "status": "Active", "categoryType": "Risk", "generatedOn": "2023-09-28T07:04:10.9515385+00:00", "title": "Stop loss of 5% for long making 6m low or short making 6m high (114.61%), exit position", "level": "Info", "type": "ToDo", "namedEntities": [{"rowId": "ERF FP Equity:BloombergTicker", "identifier": "ERF FP Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "584062:<PERSON><PERSON><PERSON>", "identifier": "584062", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1901:PortfolioRuleId", "identifier": "1901", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "13:Group", "identifier": "13", "entityType": "Group"}, {"rowId": "Stop loss of 5% for long making 6m low or short making 6m high (114.61%), exit position:Description", "identifier": "Stop loss of 5% for long making 6m low or short making 6m high (114.61%), exit position", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "ERF FP Equity:BloombergTicker", "identifier": "ERF FP Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_SCAT:Strategy", "identifier": "SK_SCAT", "entityType": "Strategy"}], "actions": [], "categorySubType": "Trading/Jockeying", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {"caption": "As discussed any long making 6m low or short making 6m high has a tight stop of 5% to ensure not getting stuck in positions that are not working. Exit "}}, {"id": 1585, "rowId": 1585, "status": "Active", "categoryType": "Sourcing & Ideation", "generatedOn": "2023-09-28T07:04:10.9519724+00:00", "title": "Stop loss of 5% for long making 6m low or short making 6m high (-10.01%), exit position", "level": "Info", "type": "ToDo", "namedEntities": [{"rowId": "KER FP Equity:BloombergTicker", "identifier": "KER FP Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "584063:<PERSON><PERSON><PERSON>", "identifier": "584063", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1901:PortfolioRuleId", "identifier": "1901", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "13:Group", "identifier": "13", "entityType": "Group"}, {"rowId": "Stop loss of 5% for long making 6m low or short making 6m high (-10.01%), exit position:Description", "identifier": "Stop loss of 5% for long making 6m low or short making 6m high (-10.01%), exit position", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "KER FP Equity:BloombergTicker", "identifier": "KER FP Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_LCAT:Strategy", "identifier": "SK_LCAT", "entityType": "Strategy"}], "actions": [], "categorySubType": "Trading/Jockeying", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {"caption": "As discussed any long making 6m low or short making 6m high has a tight stop of 5% to ensure not getting stuck in positions that are not working. Exit "}}, {"id": 1586, "rowId": 1586, "status": "Inactive", "categoryType": "Trading/Jockeying", "generatedOn": "2023-09-28T07:19:26.8652163+00:00", "title": "Stop loss of 5% for long making 6m low or short making 6m high (-11.67%), exit position", "level": "Hard", "type": "ToDo", "namedEntities": [{"rowId": "ISS DC Equity:BloombergTicker", "identifier": "ISS DC Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "584079:<PERSON><PERSON><PERSON>", "identifier": "584079", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1901:PortfolioRuleId", "identifier": "1901", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "13:Group", "identifier": "13", "entityType": "Group"}, {"rowId": "Stop loss of 5% for long making 6m low or short making 6m high (-11.67%), exit position:Description", "identifier": "Stop loss of 5% for long making 6m low or short making 6m high (-11.67%), exit position", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "ISS DC Equity:BloombergTicker", "identifier": "ISS DC Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_LFUND:Strategy", "identifier": "SK_LFUND", "entityType": "Strategy"}], "actions": [], "defaultAction": {"actionParams": {"commandName": "gateway/api/workflowinstance/try-set-status", "commandParams": {"WorkflowInstanceIds": [14327], "Status": "Acknowledged"}}, "label": "Acknowledge", "level": "secondary", "order": 1, "actionType": "Command.Invoke"}, "categorySubType": "Trading/Jockeying", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {"caption": "As discussed any long making 6m low or short making 6m high has a tight stop of 5% to ensure not getting stuck in positions that are not working. Exit "}}, {"id": 1587, "rowId": 1587, "status": "Active", "categoryType": "Trading/Jockeying", "generatedOn": "2023-09-28T07:19:26.8380619+00:00", "title": "Max size 0.75%($2.3m) when initiating short on 6m high or long on 6m low", "level": "Hard", "type": "ToDo", "namedEntities": [{"rowId": "ISS DC Equity:BloombergTicker", "identifier": "ISS DC Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "584078:<PERSON><PERSON><PERSON>", "identifier": "584078", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1899:PortfolioRuleId", "identifier": "1899", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "11:Group", "identifier": "11", "entityType": "Group"}, {"rowId": "Max size 0.75%($2.3m) when initiating short on 6m high or long on 6m low:Description", "identifier": "Max size 0.75%($2.3m) when initiating short on 6m high or long on 6m low", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "ISS DC Equity:BloombergTicker", "identifier": "ISS DC Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_LFUND:Strategy", "identifier": "SK_LFUND", "entityType": "Strategy"}], "actions": [], "categorySubType": "Sourcing & Ideation", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {}}, {"id": 0, "rowId": 1591, "status": "Active", "categoryType": "Information", "generatedOn": "2023-09-28T08:26:08.7115932+00:00", "title": "Max size 0.75%($2.3m) when initiating short on 6m high or long on 6m low", "level": "Hard", "type": "ToDo", "namedEntities": [{"rowId": "NESN SW Equity:BloombergTicker", "identifier": "NESN SW Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "584083:<PERSON><PERSON><PERSON>", "identifier": "584083", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1899:PortfolioRuleId", "identifier": "1899", "entityType": "Portfolio Rule Id"}, {"rowId": "434:Trader", "identifier": "434", "entityType": "Trader"}, {"rowId": "11:Group", "identifier": "11", "entityType": "Group"}, {"rowId": "Max size 0.75%($2.3m) when initiating short on 6m high or long on 6m low:Description", "identifier": "Max size 0.75%($2.3m) when initiating short on 6m high or long on 6m low", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "NESN SW Equity:BloombergTicker", "identifier": "NESN SW Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "SK_LFUND:Strategy", "identifier": "SK_LFUND", "entityType": "Strategy"}], "actions": [], "categorySubType": "Short Interest", "traderId": 434, "traderInitials": "SK", "component": "PrimitiveComponents.Image", "componentProps": {}, "contents": "Offside(-0.13%) after 90 days, max allowed size 1.0% _($1.0m)_ vs current size 1.55% ($1.5m), please reduce"}, {"id": 3762, "rowId": "3762", "status": "Active", "categoryType": "Trading/Jockeying", "generatedOn": "2023-11-30T08:00:10.3895143+00:00", "title": "Offside after 40 days", "level": "<PERSON><PERSON>", "type": "ToDo", "contents": "Offside on return on allocation(-0.11% on gameplan) after > 40 days(42), exit position", "namedEntities": [{"rowId": "ALIV SS Equity:BloombergTicker", "identifier": "ALIV SS Equity", "identifierType": "Bloomberg Ticker"}], "entities": [{"rowId": "48911:GamePlan", "identifier": "48911", "entityType": "Game Plan"}, {"rowId": "577005:<PERSON><PERSON><PERSON>", "identifier": "577005", "entityType": "<PERSON><PERSON><PERSON>"}, {"rowId": "1898:PortfolioRuleId", "identifier": "1898", "entityType": "Portfolio Rule Id"}, {"rowId": "11:Trader", "identifier": "11", "entityType": "Trader"}, {"rowId": "SK:Trader Initials", "identifier": "JS", "entityType": "__Trader Initials"}, {"rowId": "13:Group", "identifier": "13", "entityType": "Group"}, {"rowId": "Offside on return on allocation(-0.11% on gameplan) after > 40 days(42), exit position:Description", "identifier": "Offside on return on allocation(-0.11% on gameplan) after > 40 days(42), exit position", "entityType": "Description"}, {"rowId": "Warning:Severity", "identifier": "Warning", "entityType": "Severity"}, {"rowId": "ALIV SS Equity:BloombergTicker", "identifier": "ALIV SS Equity", "entityType": "Bloomberg Ticker"}, {"rowId": "5940:Raid InstrumentId", "identifier": "5940", "entityType": "__Raid InstrumentId"}, {"rowId": "SK_SFUND:Strategy", "identifier": "SK_SFUND", "entityType": "Strategy"}], "actions": [{"actionParams": {"type": "url", "url": "/backbone/raid/clients/v2#/trade-ticket?traderId=434&fasttrade=%5bbs%3aB%5d+%5bq%3a39013%5d+%5bt%3aALIV+SS+Equity%5d+%5bst%3aSK_SFUND%5d", "containerProps": {"title": "Trade Ticket"}}, "label": "Cut All", "level": "secondary", "order": 1, "actionType": "Window.Open"}], "defaultAction": {"actionParams": {"commandName": "gateway/api/workflowinstance/try-set-status", "commandParams": {"WorkflowInstanceIds": [14327], "Status": "Acknowledged"}}, "label": "Acknowledge", "level": "secondary", "order": 1, "actionType": "Command.Invoke"}, "categorySubType": "Trading/Jockeying", "traderId": 434, "traderInitials": "MDu", "displayTraderInitials": true, "fundGroup": "", "componentType": "CompanySummary", "componentProps": {"id": 588596}, "additionalComponents": [{"componentId": "1", "componentType": "CompanySummary", "type": "Icon", "children": {"items": [{"componentId": "1.1", "component": "PrimitiveComponents.Dial", "componentProps": {"value": 0.43, "size": 60}, "label": "Why am I seeing this? (3)"}, {"componentId": "1.2", "component": "PrimitiveComponents.Dial", "componentProps": {"value": 0.43, "size": 60}, "label": "Why am I seeing this? (3)"}]}, "selectByDefault": false}, {"componentId": "2", "componentType": "PosEx", "componentProps": {"id": 12}, "label": "Macro", "icon": "info", "type": "Icon", "children": {"items": [{"componentId": "2.2", "componentType": "CompanySummary"}, {"componentId": "2.3", "componentType": "DriversBreakdownChart"}, {"componentId": "2.4", "componentType": "DriversBreakdownChart", "componentProps": {"showRisk": true, "riskModelNames": ["Theme"]}, "children": {"items": [{"componentId": "2.4.1", "componentType": "CompanyPage"}]}}]}}, {"componentId": "3", "componentType": "DriversBreakdownChart", "componentProps": {"id": 7477}, "label": "Why am I seeing this?", "icon": "info", "type": "Icon", "children": {"items": [{"componentId": "3.1", "componentType": "PosEx"}]}}, {"componentId": "4", "componentType": "TradingAnalytics", "selectByDefault": false, "children": {"items": [{"componentId": "4.2", "componentType": "CompanySummary"}, {"componentId": "4.5", "componentType": "TradeTicket"}, {"componentId": "4.3", "componentType": "DriversBreakdownChart"}, {"componentId": "4.4", "componentType": "DriversBreakdownChart", "componentProps": {"riskModelNames": ["Theme"]}}]}}]}]