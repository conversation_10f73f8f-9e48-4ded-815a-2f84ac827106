import { v4 } from "uuid";

import type { Workflow } from "Contracts/index";

export const NOTIFICATION_WITH_DEEP_LINK_TO_NOTIFICATIONS = {
    id: 123,
    generatedOn: new Date().toISOString() as any,
    categoryType: "Trading/Jockeying",
    categorySubType: "Indicator",
    level: "Warn",
    source: "xxx",
    status: "Active",
    title: "Test",
    contents: "Message",
    componentType: "CompanySummary",
    entities: [
        {
            identifier: "SMG US Equity",
            entityType: "Bloomberg Ticker",
        },
    ],
    namedEntities: [
        {
            identifier: "SMG US Equity",
            identifierType: "Bloomberg Ticker",
        },
    ],
    defaultAction: {
        level: "primary",
        order: 0,
        label: "LaunchPad (selected 3762)",
        icon: "todo",
        actionType: "LaunchPad.Open",
        actionParams: {
            section: "notification_lane_1",
            id: 3762,
        },
    },

    actions: [
        {
            level: "primary",
            order: 0,
            label: "LaunchPad (selected 3762)",
            icon: "todo",
            actionType: "LaunchPad.Open",
            actionParams: {
                section: "notification_lane_1",
                id: 3762,
            },
        },
    ],
} as Workflow.Notifications.Notification;

export const SIMPLE_NOTIFICATION_WITH_TICKER = {
    id: 124,
    generatedOn: "2023-05-03T12:34:44.333223+00:00",
    categoryType: "Process",
    categorySubType: "Portfolio Construction & Market Awareness",
    level: "Info",
    source: "xxx",
    status: "Active",
    title: "Test",
    contents: "Message",
} as Workflow.Notifications.Notification;

export const STICKY_NOTIFICATION_WITH_REQUIRED_ACTION = {
    id: 125,
    generatedOn: "2023-05-03T12:34:44.333223+00:00",
    categoryType: "Process",
    categorySubType: "Portfolio Construction & Market Awareness",
    level: "Hard",
    source: "xxx",
    status: "Inactive",
    title: "Test",
    contents: "Message",
    entities: [
        {
            identifier: "SMG US Equity",
            entityType: "Bloomberg Ticker",
        },
        {
            identifier: "My Game Plan",
            entityType: "Game Plan Name",
        },
        {
            identifier: "DOM SS Equity",
            entityType: "Bloomberg Ticker",
        },
    ],
    namedEntities: [
        {
            identifier: "SMG US Equity",
            identifierType: "Bloomberg Ticker",
        },
        {
            identifier: "My Game Plan",
            identifierType: "Game Plan Name",
        },
        {
            identifier: "DOM SS Equity",
            identifierType: "Bloomberg Ticker",
        },
    ],
    actions: [
        {
            level: "primary",
            order: 0,
            label: "Open Game Plan",
            actionType: "Window.Open",
            actionParams: {
                componentName: "Layouts.SectionGamePlan",
                componentProps: {
                    gamePlanId: 19926,
                    traderId: 391,
                    showChecklist: true,
                    showTrades: false,
                },
                containerProps: {
                    title: "Game Plan",
                },
            },
        },
        {
            level: "secondary",
            order: 1,
            label: "Acknowledge",
            actionType: "Command.Invoke",
            actionParams: {
                commandName: "filters.txxxxxxraders",
                commandParams: {
                    p1: 2,
                },
            },
        },
    ],
} as Workflow.Notifications.Notification;

export const NEW_NOTIFICATION = {
    id: v4() as any,
    generatedOn: new Date().toISOString() as any,
    categoryType: "Indicator",
    categorySubType: "Sub type",
    level: "Warn",
    source: "xxx",
    status: "Active",
    title: "Test",
    contents: "Message",
    componentType: "CompanySummary",
    entities: [
        {
            identifier: "SMG US Equity",
            entityType: "Bloomberg Ticker",
        },
    ],
    namedEntities: [
        {
            identifier: "SMG US Equity",
            identifierType: "Bloomberg Ticker",
        },
    ],
    defaultAction: {
        confirmMessage: "Are you aure?",
        actionParams: {
            commandName: "gateway/api/workflowinstance/try-set-status",
            commandParams: {
                WorkflowInstanceIds: [14327],
                Status: "Acknowledged",
            },
        },
        label: "Acknowledge",
        level: "secondary",
        order: 1,
        actionType: "Command.Invoke",
    },
    // defaultAction2: {
    //     level: "primary",
    //     order: 0,
    //     label: "LaunchPad (selected 3762)",
    //     icon: "todo",
    //     actionType: "LaunchPad.Open",
    //     actionParams: {
    //         section: "notification_lane_1",
    //         id: 3762
    //     }
    // },

    actions: [
        {
            level: "primary",
            order: 0,
            label: "LaunchPad (selected 3762)",
            icon: "todo",
            actionType: "LaunchPad.Open",
            actionParams: {
                section: "notification_lane_1",
                id: 3762,
            },
        },
    ],
} as Workflow.Notifications.Notification;
