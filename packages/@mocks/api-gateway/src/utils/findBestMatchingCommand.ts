export const findBestMatchingCommand = (
    strings: string[],
    command: string,
    payload: Record<string, unknown>
): string | undefined =>
    strings.reduce(
        (acc, str: string) => {
            const normalizedValue = str?.replace(/\//g, ".");
            if (
                normalizedValue.endsWith(command) ||
                new RegExp(`^${normalizedValue}$`).test(command)
            ) {
                const keyValuePairs = str
                    .split("@")[0]
                    .split("&")
                    .map(s => s.split("=") as [string, string]);

                const score = keyValuePairs.reduce((scoreAcc, [key, value]) => {
                    if (payload?.[key] && payload[key] === value) {
                        return scoreAcc + 1;
                    }
                    return scoreAcc;
                }, 1);

                if (score >= acc.score) {
                    return { score, value: str };
                }
            }

            return acc;
        },
        { score: 0, value: undefined }
    ).value;
